﻿## disable-inactive-accounts for QHR CORP by <PERSON>
## !! Please see confluence document before running or editing this script! !!  https://confluence.qhrtech.com/display/DCO/Automatic+Disable+of+AD+Accounts+-+QHR+CORP

###### Set these variables
$csvExcludePath = "C:\Scripts\disable-inactive-ad-users\excluded-users.csv" #Users set here will be excluded from being disabled
$log = "C:\Scripts\disable-inactive-ad-users\logs\$(get-date -f yyyy-MM-dd)-disable-inactive-accounts-list.log" # Log Path 
$recentlyDisabledAccounts = "C:\Scripts\disable-inactive-ad-users\recently-disabled-accounts-list.csv" # This is overwritten each time script is run. Used only to send email attachment of disabled users and if recently disabled users need to be enabled in again

######


$users = @() #Clear users
$list = ""  #Clear list
$i = 0


#See if log can be written
try { 
    write-host "$(get-date -f g) - Starting Script" | out-file $log -Append -ErrorAction stop
    "$(get-date -f g) - Starting Script"
}
catch { 
    "Unable to write to log $log, stopping script"

    #email alert that log can't be written as to and script won't be run.
    $warningmessage = "<h4>WARNING:  disable-inactive-accounts.ps1 will not run as log can't be generated to $log</h4>"

    break
}

# Generate list of active AD accounts that have not been logged into for 45 days or longer
"$(get-date -f g) - Generating list of active AD accounts that have not been logged into for 45 days or longer" | out-file $log -Append
"$(get-date -f g) - Generating list of active AD accounts that have not been logged into for 45 days or longer"
try{
    $list = Search-ADAccount -UsersOnly -AccountInactive -TimeSpan 45.00:00:00 | Where {$_.Enabled} | Sort SamAccountName  | Get-ADUser -Prop DisplayName | Select SamAccountName, Name
}
catch{
    "$(get-date -f g) - ERROR: can't generate list of active AD accounts that have not been logged into for 45 days or longer. Stopping script" | out-file $log -Append
    "$(get-date -f g) - ERROR: can't generate list of active AD accounts that have not been logged into for 45 days or longer. Stopping script"
    "" | out-file $log -Append
    break
}


#import CSV exclude user list
try {
    "$(get-date -f g) - Importing user exclude CSV from $csvExcludePath" | out-file $log -Append
    $users = import-csv $csvExcludePath
    $numberOfUsersToExclude = $users.count
    "$(get-date -f g) - Successful import of CSV user exclude config, $numberOfUsersToExclude users were imported" | out-file $log -Append
}
catch{
    "ERROR: can't read csv file in $csvExcludePath. Stopping Script"
    "$(get-date -f g) - ERROR: can't read csv file in $csvExcludePath. Stopping Script" | out-file $log -Append
    "" | out-file $log -Append
    break
}

#remove excluded users from list
"$(get-date -f g) - Removing users in exclusion list" | out-file $log -Append
try {
    foreach($user in $users) {
        $list = $list | ? {$_.SamAccountName -notlike $user.SamAccountName} #Exclude users in excell sheet by SamAccountName
    }
}
catch {
    "ERROR: can't exclude users in $csvExlcudePath. Stopping Script"
    "$(get-date -f g) - ERROR: can't exclude users in $csvExcludePath. Stopping Script" | out-file $log -Append
    "" | out-file $log -Append
}

"$(get-date -f g) -v- Users disabled -v-" | out-file $log -Append
"$(get-date -f g) -v- Users disabled -v-"


#disable all accounts left in the list and log them (disable line commented out in this version of the script)
try {
    foreach($member in $list) {
        if($i -eq 0) { 
        $memberSamName = $member.samAccountName
        
        #Disable-ADAccount -Identity $memberSamName -WhatIf #don't actually disable
        "$memberSamName" | out-file $log -Append
        "$memberSamName"
        }
        #$i++
    }
}
catch {
    "$(get-date -f g) Error: Can't disable accounts. Might be permissions issue. Stopping Script" | out-file $log -Append
    "$(get-date -f g) Error: Can't disable accounts. Might be permissions issue. Stopping Script" 

}


"$(get-date -f g) -^- Finished Disabling User Accounts -^-" | out-file $log -Append
"$(get-date -f g) -^- Finished Disabling User Accounts -^-" 

#Export list to CSV to use in email alert
$list | export-csv $recentlyDisabledAccounts -NoTypeInformation
"$(get-date -f g) - Created email csv attachment with recently disabled users" | out-file $log -Append



"$(get-date -f g) - Script Complete" | out-file $log -Append
"$(get-date -f g) - Script Complete"
"" | out-file $log -Append

