﻿Import-Module ActiveDirectory
 
# All of the properties you'd like to pull from Get-ADUser
$properties=@(
    'givenName',
    'sn',
#    'sAMAccountType',
    'mail',
    'objectSid',
    'department',
    'title'
    'mobile'
    'extensionAttribute10'
    'extensionAttribute11'
    'description',
    'userPrincipalName',
    'lastLogon',
    'sAMAccountName'
    )
 
 
# All of the expressions you want with all of the filtering .etc you'd like done to them
$expressions=@(
    @{Expression={$_.givenName};Label="First Name"},
    @{Expression={$_.sn};Label="Last Name"},
#    @{Expression={$_.sAMAccountType};Label="Account Type"},
    @{Expression={$_.mail};Label="Email Address"},
   @{Expression={$_.department};Label="Department"}
  @{Expression={$_.title};Label="Title"}
    @{Expression={$_.mobile};Label="Mobile"}
    @{Expression={$_.extensionAttribute10};Label="Extension attribute 10"}
    @{Expression={$_.extensionAttribute10};Label="Extension attribute 11"}
    @{Expression={$_.description};Label="Description"},
    @{Expression={[DateTime]::FromFileTime([Int64]::Parse($_.lastLogon))};Label="Last Log-on Date"},
    @{Expression={$_.userPrincipalName};Label="Full User Logon"},
    @{Expression={$_.objectSid};Label="Unique Account ID"},
    @{Expression={$_.sAMAccountName};Label="User Logon"}
    )
 
$path_to_file = "C:\scripts\user-report_$((Get-Date).ToString('yyyy-MM-dd_hh-mm-ss')).csv"
 
Get-ADUser -Filter '(enabled -eq $true)' -Properties $properties | select $expressions | Export-CSV $path_to_file -notypeinformation -Encoding UTF8

