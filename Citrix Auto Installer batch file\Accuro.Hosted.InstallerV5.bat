@echo off
echo "Accuro: ASP/Accuro Cloud Installer"
echo

echo "Creating directory C:\QHR"
mkdir C:\QHRTechSoftware
cd C:\QHRTechSoftware\

%SystemRoot%\explorer.exe "C:\QHRTechSoftware\"

echo "Downloading CitrixWorkspace-19.12.5000.exe"
powershell -Command "Invoke-webrequest https://www.cloudwerx.com/update/files/citrix/CitrixWorkspace-19.12.5000.exe -outfile C:\QHRTechSoftware\CitrixWorkspace-19.12.5000.exe"
echo "Done"

echo "Downloading ScrewDriversClient_6.6.1.17374_x64.msi"
powershell -Command "Invoke-webrequest https://www.cloudwerx.com/update/files/ScrewDriversClient_6.6.1.17374_x64.msi -outfile C:\QHRTechSoftware\ScrewDriversClient_6.6.1.17374_x64.msi"
echo "Done"

echo "Downloading cloudwerx-setup.zip"
powershell -Command "Invoke-webrequest https://www.cloudwerx.com/update/files/cloudwerx-setup.zip -outfile C:\QHRTechSoftware\cloudwerx-setup.zip"
echo "Done"

echo "Unzipping cloudwerx-setup.zip"
powershell -Command "Expand-Archive cloudwerx-setup.zip -Destinationpath C:\QHRTechSoftware"
echo "Done"

timeout /t 10

echo "Installing CitrixWorkspace-19.12.5000.exe"
"C:\QHRTechSoftware\CitrixWorkspace-19.12.5000.exe" /silent 
echo "Done"

timeout /t 15

echo "Installing ScrewDriversClient_6.6.1.17374_x64.msi"
msiexec /i "C:\QHRTechSoftware\ScrewDriversClient_6.6.1.17374_x64.msi" /qn 
echo "Done"

timeout /t 120

echo "Installing cloudwerx-plugin.exe"
"C:\QHRTechSoftware\cloudwerx-setup.exe" /S
echo "Done"

timeout /t 8

echo "Deleting directory C:\QHRTechSoftware and the contents within."
cd C:\
RMDIR "C:\QHRTechSoftware" /S /Q
echo "Done"

echo "Opening your default web browser to the Accuro sign in page"
start https://citrix-ca.cloudwerx.com/vpn/index.html
start https://accuro.cloud

echo "Opening ScrewDrivers Client Control Panel v6"
"C:\Program Files\Tricerat\Simplify Printing\ScrewDrivers Client v6\Client Control Panel.exe"
echo "Done"
pause