﻿$programName = "Oracle VM VirtualBox 6.1.34"
$registryPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"

$keypath = $null
$keyExists = $false
#The first section will get the uninstall string so we can uninstall the app later


try{
$uninstallString = (Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*") | Where-Object {$_.DisplayName -eq $programName} | Select-Object -ExpandProperty UninstallString -ErrorAction Continue
if ($uninstallString -clike "*MsiExec*")
{
$inputString =  $uninstallString
}
elseif($uninstallString[1] -clike "*MsiExec*")
{
$inputString = $uninstallString[1]
}
$inputString = $inputString -replace '(\{)(.*)(\})', ' $2'

# Split the input string by the space character
$splitString = $inputString.Split(' ', 2)

# Assign the values to the variables
$Msi = $splitString[0]
$convertedString = $splitString[1]
}catch{Write-Output "Error, can't find uninstall string"}



# The second section will delete all of the registry keys.


try{
    
    # Get all subkeys under the specified registry path
    $subKeys = Get-ChildItem -Path $registryPath


    # Iterate through each subkey
    foreach ($subKey in $subKeys) 
    {
    

    # Check if the DisplayName value exists and matches the specified value
    if ($subkey.GetValue("DisplayName") -eq $programName) 
        {
        $keypath = $subKey.PSPath
        # Remove the subkey and all its subkeys recursively
        Remove-Item -Path $subkey.PSPath -Recurse -Force 
        
        
        }
     }
    
    }catch{Write-Output "Problem removing the registry entry"}


if ($keypath)
{
$keyExists = Test-Path -LiteralPath $keypath
}

#Now we can try to uninstall the app, then check and see if it exists anywhere.

if ($convertedString) {
    # Uninstall the program silently
    try{
    $convertedString = $convertedString -replace "/I", "/X"
    Start-Process  $Msi -ArgumentList $convertedString ,  "/quiet", "/norestart" -Wait -ErrorAction SilentlyContinue
    }catch{Write-Output "Problem running the Uninstall for:  $Msi $convertedString "}
    
    



    # Check if the uninstallation was successful
    $uninstalledProgram = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -eq $programName}
    
    if ($uninstalledProgram -eq $null -and !$keyExists) {
        Write-Output "The program '$programName' was uninstalled successfully. Exit 0"
        Exit 0
    } else {
        Write-Output "Failed to uninstall the program '$programName' or remove registry key. Exit 1"
        Exit 1
    }
} else {
    Write-Output "The program '$programName' is not installed or could not be found. Exit 0"
    Exit 0
}
