@echo off

@echo Attempting to terminate Microsoft Teams...
@taskkill /im ms-teams.exe /f 
@if errorlevel 1 @echo Microsoft Teams process was not found. It may not be running.
@if errorlevel 0 @echo Successfully terminated Microsoft Teams.

@echo Deleting Microsoft Teams cache...
@del /q /s "%appdata%\Microsoft\Teams\*.*" 
@echo Cache files deleted.

@echo Deleting Microsoft Teams directories...
@for /d %%x in ("%appdata%\Microsoft\Teams\*") do (
    @echo Deleting directory: %%x
    @rd /s /q "%%x"
)
@echo Directories deleted.

@echo Starting Microsoft Teams...
@for /d %%x in ("C:\Program Files\WindowsApps\MSTeams_*") do (
    @echo Starting: %%x\ms-teams.exe
    @start "" "%%x\ms-teams.exe"
)

@echo Waiting for 30 seconds...
@timeout /t 30 /nobreak
@echo Done waiting.

@echo Script completed. Microsoft Teams has been restarted with a cleared cache.


