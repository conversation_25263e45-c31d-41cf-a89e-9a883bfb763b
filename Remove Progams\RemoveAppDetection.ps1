﻿$programName = "Oracle VM VirtualBox 6.1.34"


$installedPrograms = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -eq $programName}

if ($installedPrograms) {
    Write-Output "The program '$programName' is installed."
    Exit 1
} else {
    Write-Output "The program '$programName' is not installed."



    $RegPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Microsoft Help Viewer 1.1"
    $RegEntryDeleted = !(Test-Path $RegPath) 
    if($RegEntryDeleted)
    {
    Write-Output "The registry entry for '$programName' is not installed."
    Exit 0
    }
    Else{
    Write-Output "The program '$programName' is installed."
    Exit 1}
}