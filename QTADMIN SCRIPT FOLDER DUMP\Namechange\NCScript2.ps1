﻿#Script Name - Name Change Script
#Description - This script will take names as an input and change an employee's accounts to reflect that new name.

#Prerequisite
#Install-Module MSOnline
#Install-Module ActiveDirectory


# Import the Active Directory module
Import-Module ActiveDirectory


# Function to get user input
function Get-UserInput {
    $global:oldUsername = Read-Host "Please enter the old username of the employee, EX: 'john.doe'"
    $global:newUsername = Read-Host "Please enter the new username of the employee, EX: 'jane.dee'"
    $global:oldUpn = $oldUsername + "@QHRtech.com"
    $global:newUpn = $newUsername + "@QHRtech.com"
    return $oldUsername, $newUsername
}

# Function to update Active Directory user information
function Update-ADUserInfo($oldUsername, $newUsername) {
    
    # Find the user based on the old username
    $user = Get-ADUser -Filter {SamAccountName -eq $oldUsername}

    # Update the username (samaccountname) and SMTP address
    Set-ADUser -Identity $user.SamAccountName -EmailAddress $newUpn
    Set-ADUser -Identity $user.SamAccountName -SamAccountName $newUsername

    #Retrieve the user again with the new sam account name
    $global:user = Get-ADUser -Filter {SamAccountName -eq $newUsername}

    # Update the user's principal name if necessary
    if ($user.UserPrincipalName -eq $oldUpn) {
        Set-ADUser -Identity $newUsername -UserPrincipalName $newUpn
    }
}

#Function for setting proxy addresses and removing proxy addresses
function Set-ProxyAddress($newUsername, $oldUpn){
    $user = Get-ADUser $newUsername

    $SMTP0 = "@QHRtech.com"
    $smtp1 = "@qhrtech.mail.onmicrosoft.com"
    $smtp2 = ".<EMAIL>"
    $smtp3 = ".<EMAIL>"
    $smtp4 = ".<EMAIL>"
    $smtp5 = ".<EMAIL>"
    $smtp6 = ".<EMAIL>"
    $smtp7 = "@medeoprovider2.com"
    $smtp8 = "@medeoassistant.com"
    $smtp9 = "@medeo.ca"
    $smtp10 = "@medeohealth.com"
    $smtp11 = "@QHRtechnologies.com"
    $smtp12 = "@OptiMedSoftware.com"
    $smtp13 = ".<EMAIL>"
    $Smtp ='smtp:'
    $SMTPPrimary ='SMTP:'
    
            $fname=$($user.givenname)
            $lname=$($user.surname)
        $proxyaddresses0=$SMTPPrimary + $fname + '.' +$lname + $SMTP0
        $proxyaddresses1=$Smtp + $fname + '.' +$lname + $smtp1
            $proxyaddresses2=$Smtp + $fname + '.' +$lname + $smtp2
            $proxyaddresses3=$Smtp + $fname + '.' +$lname + $smtp3
            $proxyaddresses4=$Smtp + $fname + '.' +$lname + $smtp4
        $proxyaddresses5=$Smtp + $fname + '.' +$lname + $smtp5
            $proxyaddresses6=$Smtp + $fname + '.' +$lname + $smtp6
            $proxyaddresses7=$Smtp + $fname + '.' +$lname + $smtp7
        $proxyaddresses8=$Smtp + $fname + '.' +$lname + $smtp8
            $proxyaddresses9=$Smtp + $fname + '.' +$lname + $smtp9
            $proxyaddresses10=$Smtp + $fname + '.' +$lname + $smtp10
        $proxyaddresses11=$Smtp + $fname + '.' +$lname + $smtp11
            $proxyaddresses12=$Smtp + $fname + '.' +$lname + $smtp12
            $proxyaddresses13=$Smtp + $fname + '.' + $lname + $smtp13
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses0}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses1}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses2}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses3}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses4}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses5}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses6}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses7}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses8}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses9}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses10}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses11}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses12}
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses13}

        #Removing Old primary SMTP proxy address
        $user.proxyAddresses.Remove("SMTP:$oldUpn")

        #Derive Display Name from Username
        $first, $second = $newUsername.Split(".")
        $first = $first.Substring(0, 1).ToUpper() + $first.Substring(1)
        $second = $second.Substring(0, 1).ToUpper() + $second.Substring(1)
        $newDisplayName = $first + " " + $second

        Set-ADUser -instance $User -DisplayName $newDisplayName
    }


# Function to display updated user details
function Get-UpdatedUserDetails($newUsername) {
    Get-ADUser -Identity $newUsername -Properties SamAccountName, EmailAddress, UserPrincipalName
}

# Main script starts here

$oldUsername, $newUsername = Get-UserInput 

if (![string]::IsNullOrEmpty($oldUsername) -and ![string]::IsNullOrEmpty($newUsername)) {
    Update-ADUserInfo $oldUsername $newUsername
} else {
    Write-Error " Can not call 'Update-ADUserInfo' At least one of the usernames is empty or not a valid string."
}


if (![string]::IsNullOrEmpty($oldUpn) -and ![string]::IsNullOrEmpty($newUsername)) {
    Set-ProxyAddress $newUsername $oldUpn
} else {
    Write-Error " Can not call 'Set-ProxyAddress' At least one of the usernames is empty or not a valid string."
}

if (![string]::IsNullOrEmpty($newUsername)) {
    Get-UpdatedUserDetails $newUsername
} else {
    Write-Error " Can not call 'Get-UpdatedUserDetails' At least one of the usernames is empty or not a valid string."
}


