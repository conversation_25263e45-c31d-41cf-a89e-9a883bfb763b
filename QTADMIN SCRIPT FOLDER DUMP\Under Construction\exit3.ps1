﻿# Import necessary modules
Import-Module ActiveDirectory
Import-Module ExchangeOnlineManagement
Import-Module AzureAD

# Function to Revoke Exchange Online Sessions
function Revoke-ExchangeSessions {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    # Revoke Exchange Online sessions
    $mailbox = Get-Mailbox -Identity $exitedUser -ErrorAction SilentlyContinue
    if ($mailbox) {
        # Remove all permissions on the mailbox
        Get-MailboxPermission -Identity $exitedUser | Where-Object { $_.User -ne "NT AUTHORITY\SELF" } | ForEach-Object {
            Remove-MailboxPermission -Identity $exitedUser -User $_.User -AccessRights FullAccess -Confirm:$false
        }
        Write-Host "Revoked Exchange Online sessions for '$exitedUser'"
    } else {
        Write-Host "No mailbox found for '$exitedUser'"
    }
}

# Function to Disable User Account in AD
function Disable-ADUser {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    $user = Get-ADUser -Identity $exitedUser -ErrorAction SilentlyContinue
    if ($user) {
        Disable-ADAccount -Identity $exitedUser
        Write-Host "Disabled AD account for '$exitedUser'"
    } else {
        Write-Host "No AD user found for '$exitedUser'"
    }
}

# Function to Disable Mailbox
function Disable-Mailbox {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    try {
        # Use the correct parameter if -Identity is not recognized
        Disable-Mailbox -Identity $exitedUser -Confirm:$false
        Write-Host "Disabled mailbox for '$exitedUser'"
    } catch {
        Write-Host "Error disabling mailbox for '$exitedUser': $_"
    }
}

# Function to Hide Email Address from Exchange Address Book
function Hide-EmailFromAddressBook {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    try {
        # Use the correct parameter if -HiddenFromAddressListsEnabled is not recognized
        Set-Mailbox -Identity $exitedUser -HiddenFromAddressListsEnabled $true
        Write-Host "Hidden '$exitedUser' from the Exchange Address Book"
    } catch {
        Write-Host "Error hiding email address for '$exitedUser': $_"
    }
}

# Function to Move User and Computer to a Specific OU
function Move-UserAndComputer {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser,
        [Parameter(Mandatory=$true)]
        [string]$TargetOU
    )
    $user = Get-ADUser -Identity $exitedUser -ErrorAction SilentlyContinue
    if ($user) {
        Move-ADObject -Identity $user.DistinguishedName -TargetPath $TargetOU
        Write-Host "Moved '$exitedUser' to '$TargetOU'"
    } else {
        Write-Host "No user account found for '$exitedUser'"
    }

    $computer = Get-ADComputer -Filter { Name -eq $exitedUser } -ErrorAction SilentlyContinue
    if ($computer) {
        Move-ADObject -Identity $computer.DistinguishedName -TargetPath $TargetOU
        Write-Host "Moved computer '$exitedUser' to '$TargetOU'"
    } else {
        Write-Host "No computer account found for '$exitedUser'"
    }
}

# Function to Update Group Membership
function Update-GroupMembership {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    $noAccessGroup = "NoAccess"
    $qHREmployeeGroup = "QHEmployee"
    
    # Remove user from all local AD groups
    $userGroups = Get-ADUser -Identity $exitedUser -Properties MemberOf -ErrorAction SilentlyContinue | Select-Object -ExpandProperty MemberOf
    foreach ($group in $userGroups) {
        Remove-ADGroupMember -Identity $group -Members $exitedUser -Confirm:$false
        Write-Host "Removed '$exitedUser' from '$group'"
    }

    # Add user to 'NoAccess' group
    try {
        $group = Get-ADGroup -Identity $noAccessGroup -ErrorAction Stop
        Add-ADGroupMember -Identity $noAccessGroup -Members $exitedUser
        Write-Host "Added '$exitedUser' to '$noAccessGroup'"
    } catch {
        Write-Host "Error adding '$exitedUser' to '$noAccessGroup': $_"
    }
    
    # Add user to 'QHEmployee' distribution list
    try {
        $distributionGroup = Get-DistributionGroup -Identity $qHREmployeeGroup -ErrorAction Stop
        Add-DistributionGroupMember -Identity $qHREmployeeGroup -Member $exitedUser
        Write-Host "Added '$exitedUser' to '$qHREmployeeGroup'"
    } catch {
        Write-Host "Error adding '$exitedUser' to '$qHREmployeeGroup': $_"
    }
}

# Function to Remove User from Azure AD Groups
function Remove-UserFromAzureADGroups {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    try {
        # Connect to Azure AD if not already connected
        if (-not (Get-AzureADSignedInUser -ErrorAction SilentlyContinue)) {
            Connect-AzureAD
        }
        $user = Get-AzureADUser -ObjectId $exitedUser -ErrorAction Stop
        $groups = Get-AzureADUserMembership -ObjectId $user.ObjectId
        foreach ($group in $groups) {
            Remove-AzureADGroupMember -ObjectId $group.ObjectId -MemberId $user.ObjectId
            Write-Host "Removed '$exitedUser' from Azure AD group '$($group.DisplayName)'"
        }
    } catch {
        Write-Host "Error removing '$exitedUser' from Azure AD groups: $_"
    }
}

# Main Script Execution
# Prompt for the username
$userName = Read-Host -Prompt "Enter the username"

Connect-ExchangeOnline -UserPrincipalName $userName

# Prompt for the username to disable and remove attributes
$exitedUser = Read-Host -Prompt "Enter the username to be exited"

# Revoke Exchange sessions
Revoke-ExchangeSessions -exitedUser $exitedUser

# Disable user account in AD
Disable-ADUser -exitedUser $exitedUser

# Disable mailbox
Disable-Mailbox -exitedUser $exitedUser

# Hide email address from Exchange Address Book
Hide-EmailFromAddressBook -exitedUser $exitedUser

# Move user and computer to the 'Graveyard/Disabled Users' OU
$targetOU = "OU=Disabled Users,OU=Graveyard,DC=quadranthr,DC=com"
Move-UserAndComputer -exitedUser $exitedUser -TargetOU $targetOU

# Update group memberships
Update-GroupMembership -exitedUser $exitedUser

# Remove user from Azure AD groups
Remove-UserFromAzureADGroups -exitedUser $exitedUser

# Disconnect from Exchange Online
Disconnect-ExchangeOnline -Confirm:$false

Write-Host "User account '$exitedUser' has been processed for termination."
