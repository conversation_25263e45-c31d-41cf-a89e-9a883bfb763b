﻿Try {
  $connected = Get-mailbox <EMAIL>
  Write-Host "Already Connected to Exchange Online!`n`n" -ForegroundColor Green
}
Catch {
  $LiveCred = Get-Credential <EMAIL>

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
Connect-MsolService -Credential $LiveCred
 }
 $roomArray = @()


$rooms = Get-Mailbox -RecipientTypeDetails RoomMailbox -Resultsize Unlimited | Where {$_.HiddenFromAddressListsEnabled -eq $False} | select PrimarySmtpAddress
$roomArray += $rooms.primarySmtpaddress
$roomArray[1]