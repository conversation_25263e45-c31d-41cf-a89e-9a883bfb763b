$servers =  get-content c:\scripts\corpserverlist.txt
foreach ($server in $servers)
{
try{
$OSName = Get-WmiObject win32_operatingsystem -ComputerName $server -ErrorAction Stop | select Caption -expa Caption 
}
Catch [System.Management.Automation.RuntimeException]
{
Write-Output "unable to reach $server"
continue
}
$Kbs = $null
#$server 
#$OSName 
if($OSName -match "Microsoft Windows 10 Enterprise")
{
    $kbs = @('KB4534306','KB4528760','KB4534273','KB4534293','KB4534276','KB4534271')
}
if($OSName -match "2008 R2")
{
Continue
}
if($OSName -match "2008 SP2")
{
   Continue
}
if($OSName -match "2012 R2")
{
    $kbs = @('KB4534309','kb4534297')
}
if($OSName -match "2012")
{
    $kbs = @('KB4534309','kb4534297')
}
if($OSName -match "2016")
{
    $kbs = @('KB4534271')
}
if($OSName -match "2019")
{
    $kbs = @('KB4534273')
}
$Patched = $true
 foreach ($kb in $Kbs) 
{ 
Try {
	$result = Get-Hotfix -ComputerName $server -id $kb  -ErrorAction Stop | Format-Table -Wrap -AutoSize
if ($result)
    {
    #Write-Output "$kb is Installed on $server" 
    }
    }
Catch [System.Management.Automation.RuntimeException]
    {	#Write-Output "$kb Not Installed on $server" 
	$Patched = $false
    }
    }
if ($Patched -eq $true) {
    $Comment = "$server Patched"
}
else {
    $Comment = "$server NOT Patched"
}
$Comment
}