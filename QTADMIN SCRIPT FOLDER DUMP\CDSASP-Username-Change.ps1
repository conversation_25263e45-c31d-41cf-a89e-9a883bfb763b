﻿Import-Module ActiveDirectory

### Edit before running script ###
$usersToExclude = @("optimedlabs","sftp-baliarzadeh","sftp-emrdev","sftp-lkinsella","sftp-migration","sftp-optidev","sftp-wrhaa","alanztest","amehlx","briantest","dylantest","felixtest","kevantest","nicolatest","skonkin!")
$OUlocation = "OU=Users,OU=OPTIADM - Optimed Vendor Accounts,OU=Vendor Service Accounts,OU=CDS Administration,OU=CDS ASP,DC=asp,DC=cloudwerxdata,DC=com"
$domainName = "@asp.cloudwerxdata.com"
$csvSaveLocation = "c:\temp"
##################################################################################################


$saveObject = @()
$userstoclone = Get-ADUser -SearchBase "OU=Test,OU=Information Systems,OU=QHR Technologies Inc,DC=QuadrantHR,DC=com" -Properties Description, DisplayName -Filter * | where ({$usersToExclude -notcontains $_.SamAccountName}) | where {$_.SamAccountName -notmatch "qhr-"}

#Displays users before clone
foreach ($name in $userstoclone) {
    write-host $name.UserPrincipalName -ForegroundColor yellow
}

#Asks if you want to clone listed users
$changeUsers = read-host "Clone listed users? y/n"

if ($changeUsers -eq "y") {


    foreach ($user in $userstoclone) {


        #$defaultPassword = $user.GivenName.ToUpper().Substring(0,1) + $user.Surname.ToLower().Substring(0,1) + "123!*"

        $defaultPassword = "Qhr!" + -join (1..9  | Get-Random -Count 5)
        $newuser= "qhr-" + $user.SamAccountName
        $userToCopyFrom = $user
        $displayName = $user.Name + " " + "(QHR)"

        #sets users to copy from
        $instance = Get-ADUser $userToCopyFrom
        #creates new user
        New-ADUser -SamAccountName $newuser -UserPrincipalName "$newuser$domainName" -Name $displayName -Instance $instance -AccountPassword (ConvertTo-SecureString -AsPlainText $defaultPassword -Force) -Path $OUlocation
        #copies old user groups to new user groups
        Get-ADUser -Identity $userToCopyFrom -Properties memberof | Select-Object -ExpandProperty memberof | Add-ADGroupMember -Members $newuser 
        #copies old user description to new user description
        Set-ADUser -Identity $newuser -Description $userToCopyFrom.Description -DisplayName $userToCopyFrom.DisplayName

        write-host "User: $newuser" -ForegroundColor Magenta
        write-host "Password: $defaultPassword" -ForegroundColor Magenta
        write-host " "

        
        $saveObject += New-Object -TypeName psobject -Property  @{Name = $newuser; TempPass = $defaultPassword}
        


    }
    write-host "Users have been cloned" -ForegroundColor Green


    $saveObject | Export-Csv "$csvSaveLocation\ClonedUsersOutput.csv" -NoTypeInformation
}

else { 

    write-host "No users were cloned as yes was not selected" -ForegroundColor Yellow
    break;

}