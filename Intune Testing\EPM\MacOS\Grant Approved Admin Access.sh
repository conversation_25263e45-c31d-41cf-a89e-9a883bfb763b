#!/bin/zsh
#=================================================================================
# SCRIPT: Grant Approved Admin Access (Final v4.4)
#=================================================================================
log() { echo "$(date '+%Y-%m-%d %H:%M:%S') | $1"; }
log "=== SCRIPT START: Grant Approved Admin Access (v4.4) ==="
JAMF_BINARY="/usr/local/bin/jamf"; DSEDIT_GROUP="/usr/sbin/dseditgroup"; STAT_CMD="/usr/bin/stat"; LAUNCHCTL_CMD="/bin/launchctl"
jamfProURL="https://qhrtech.jamfcloud.com"; apiClientID="fe4d6778-a92d-4895-9cb8-772347f2b161"; apiClientSecret="qtMGn-mHe6BiWZxPTbE85YuBTw6KLHQU7IobRxc-7OAseqShG3iInQaxR8wluOfZ"
ACTION_EA_NAME="Temp Admin - Action Status"; DURATION_EA_NAME="Temp Admin - Approved Duration (Minutes)"; DURATION_EA_ID="2"
consoleUser=$($STAT_CMD -f%Su /dev/console); log "Identified console user: '${consoleUser}'"
apiTokenResponse=$(/usr/bin/curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" --data-urlencode "client_id=${apiClientID}" --data-urlencode "client_secret=${apiClientSecret}" --data-urlencode "grant_type=client_credentials" "${jamfProURL}/api/oauth/token")
bearerToken=$(echo "$apiTokenResponse" | /usr/bin/plutil -extract "access_token" raw -); if [[ -z "$bearerToken" ]]; then log "ERROR: Could not retrieve bearer token."; exit 1; fi
serialNumber=$(/usr/sbin/system_profiler SPHardwareDataType | /usr/bin/awk '/Serial Number/{print $4}')
computerDetails=$(/usr/bin/curl -s -H "Authorization: Bearer ${bearerToken}" -H "accept: application/json" "${jamfProURL}/api/v1/computers-inventory?filter=hardware.serialNumber==%22${serialNumber}%22")
minified_json=$(echo "${computerDetails}" | tr -d ' \n\r')
computerID=$(echo "${minified_json}" | sed -n 's/.*"results":\[{"id":"\([^"]*\)".*/\1/p')
duration_minutes=$(echo "${minified_json}" | sed -n 's/.*"definitionId":"'"${DURATION_EA_ID}"'".*"values":\["\([^"]*\)"\].*/\1/p')
if [[ -z "$computerID" ]] || ! [[ "$computerID" =~ ^[0-9]+$ ]]; then log "ERROR: Could not determine this computer's Jamf Pro ID."; exit 1; fi; log "This computer's Jamf Pro ID is: ${computerID}"
if ! [[ "${duration_minutes}" =~ ^[0-9]+$ ]]; then log "WARN: Extracted duration is not a valid number. Defaulting to 15 minutes."; duration_minutes=15; fi
duration_seconds=$(( duration_minutes * 60 )); log "Final Duration: ${duration_minutes} minute(s)."

$DSEDIT_GROUP -o edit -a "$consoleUser" -t user admin; log "SUCCESS: User '${consoleUser}' added to admin group."
message="You have been granted local administrator access for ${duration_minutes} minute(s). Your access will be automatically revoked."; $JAMF_BINARY displayMessage -message "$message"

revoke_script_path="/private/var/tmp/revoke_temporary_admin.sh"; plist_path="/Library/LaunchDaemons/com.qhrtech.revokeadmin.plist"
# CORRECTED: The revocation script now uses the comprehensive dscl commands.
cat > "$revoke_script_path" <<EOF
#!/bin/zsh
# Get user's UUID for reliable demotion
userUUID=\$(/usr/bin/dscl . -read "/Users/<USER>" GeneratedUID | /usr/bin/awk '{print \$2}')
# Attempt removal from both group membership types
/usr/bin/dscl . -delete /Groups/admin GroupMembers "\$userUUID"
/usr/bin/dscl . -delete /Groups/admin GroupMembership "$consoleUser"
# Notify the user
/usr/local/bin/jamf displayMessage -message "Your temporary administrator access for ${duration_minutes} minute(s) has expired and your privileges have been returned to standard."
# Clean up
rm -f "$plist_path" "$revoke_script_path"
exit 0
EOF
chmod 755 "$revoke_script_path" && chown root:wheel "$revoke_script_path"
cat > "$plist_path" <<EOF
<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd"><plist version="1.0"><dict><key>Label</key><string>com.qhrtech.revokeadmin</string><key>ProgramArguments</key><array><string>/bin/zsh</string><string>$revoke_script_path</string></array><key>StartInterval</key><integer>$duration_seconds</integer><key>RunAtLoad</key><false/></dict></plist>
EOF
chown root:wheel "$plist_path" && chmod 644 "$plist_path"
$LAUNCHCTL_CMD bootout system "$plist_path" 2>/dev/null; $LAUNCHCTL_CMD bootstrap system "$plist_path"

api_payload="<?xml version=\"1.0\" encoding=\"UTF-8\"?><computer><extension_attributes><extension_attribute><name>${ACTION_EA_NAME}</name><value></value></extension_attribute><extension_attribute><name>${DURATION_EA_NAME}</name><value></value></extension_attribute></extension_attributes></computer>"
http_status=$(/usr/bin/curl -s -o /dev/null -w "%{http_code}" -X PUT -H "Authorization: Bearer ${bearerToken}" -H "Content-Type: application/xml" -d "$api_payload" "${jamfProURL}/JSSResource/computers/id/${computerID}")
if [[ "$http_status" == "201" ]]; then log "SUCCESS: API call to clear EAs returned status ${http_status}."; else log "ERROR: API call to clear EAs failed with HTTP status ${http_status}."; exit 1; fi
log "=== SCRIPT END ==="; exit 0