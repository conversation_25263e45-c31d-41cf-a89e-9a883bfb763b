﻿#Title: GetAppsGraphAPI.ps1
#Description: This script was made to be able to pull information on applications installed on devices in the Intune device list.
#It can pull things like the email address of the employee, device name, ID, app names, app versions, if multiple versions are installed. and some more properties.

#Author: <PERSON><PERSON><PERSON>
#Version: 1.0

#PreReqs
#Make sure you have microsoft graph api permissions on your DA Account. This can be obtained from https://developer.microsoft.com/en-us/graph/graph-explorer
#Install the Microsoft.Graph.Intune module if not already installed
#Install-Module -Name Microsoft.Graph.Intune

#Import / Install these modules as well
Import-Module Microsoft.Graph.Applications
Import-Module Microsoft.Graph.Authentication
Import-Module Microsoft.Graph.DeviceManagement
Import-Module Microsoft.Graph.Intune

#How to use 
#First, change the $AppName and $CSVPath to be specific to your new report.
#Then Test importing the modules needed by copying the above import lines into the powershell window
#If all PreReqs are met, run the script.
#Gather your data out of the pop-up window or CSV File when the script has cycled through all apps. This may take up to 45 minutes to complete.

Disconnect-MgGraph
Connect-MgGraph

$CsvPath = "C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\GetAppsGraphAPI\userswithnotepadinstalled.csv"
$AppName = "*notepad++*"

# Filter expression to retrieve devices with SQL Server 2016 installed

# Get a list of all devices
$ManagedDevices = Get-MgDeviceManagementManagedDevice -Filter "managedDeviceOwnerType eq 'company'"

# Define the custom object for output
$deviceAppInfo = @()
$customObject = [PSCustomObject]@{
    EmailAddress = ""
    DeviceName = ""
    LastSyncDateTime = ""
    Id = ""
    AppVersion = ""
}
$i = 1
# Loop through each device to get the app info
foreach ($device in $ManagedDevices) {
    $deviceApps = Get-MgDeviceManagementManagedDeviceDetectedApp -All -ManagedDeviceId $device.Id
    Write-Host $i
    # Check if the app is installed on this device
    $app = $deviceApps | Where-Object {$_.displayName -ilike $AppName}
    if ($app) {
        $appversion = $app.Version | Out-String
        # Create a new custom object for this device and app
        $customObject = [PSCustomObject]@{
            EmailAddress = $device.EmailAddress
            DeviceName = $device.deviceName
            LastSyncDateTime = $device.lastSyncDateTime
            Id = $device.Id
            AppVersion = $appversion
        }
        
        # Add the custom object to the deviceAppInfo array
        $deviceAppInfo += $customObject
        $customObject | Export-Csv -Path $CsvPath -Append -NoTypeInformation
        Write-host $device.deviceName ' Done, ' $app.DisplayName 'has version ' $app.Version.ToString()
    }
    $i = $i +1
}

# Output the custom object using Out-GridView
$deviceAppInfo | Out-GridView
