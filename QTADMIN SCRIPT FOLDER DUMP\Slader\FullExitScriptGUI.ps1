<#
.SYNOPSIS
Provides a GUI to perform comprehensive user termination actions across Entra ID, Active Directory, and Exchange Online,
with placeholders for Slack, 1Pass<PERSON>, Zscaler, and GitHub.

.DESCRIPTION
This script presents a simple Windows Form GUI for initiating user offboarding. Input one or more
User Principal Names (UPNs). Clicking "Run Full Exit" performs the following for each user:

Entra ID / Microsoft Graph / Exchange Online Actions (Order Changed):
1. Finds the Entra ID user object.
2. Converts the user's mailbox to a Shared Mailbox (if applicable and EXO connected).
3. Disables the Entra ID user account (`accountEnabled = $false`).
4. Revokes all Microsoft Entra ID refresh tokens (`Revoke-MgUserSignInSession`).
5. Removes all assigned licenses.
6. Finds and disables the user's registered devices in Entra ID.
7. Removes user from specified Entra ID groups (excluding defined exceptions and skipping mail-enabled/synced/dynamic groups).

Local Active Directory Actions:
1. Finds the AD user object based on UPN.
2. Disables the AD account (`Disable-ADAccount`).
3. Sets the AD account to expire at the end of the current day (`Set-ADAccountExpiration`).
4. Logs and clears Title, Department, Company attributes.
5. Logs Manager, stores Manager's name (firstname.lastname) in extensionAttribute10 (and verifies), and clears Manager attribute.
6. Resets the AD account password once with a random password.
7. Adds the user to a specified 'no access' group (if found).
8. Sets the user's primary group to the 'no access' group using its primaryGroupToken (if found and user added, USE WITH CAUTION).
9. Removes the user from all other AD groups based on MemberOf attribute (excluding only the 'no access' group).
10. Hides the user from the Exchange Global Address List (GAL) by setting msExchHideFromAddressLists=True.
11. Moves the AD user object to the specified Disabled Users OU.

Logging:
- Actions, successes, and failures are logged verbosely to the GUI and console.
- A permanent log file is created for each run in C:\temp\Helpdesk\FullExitAuditLogs\.

API Placeholders:
- Includes commented-out sections for integrating other systems.

.PARAMETER UserList
A string containing User Principal Names (UPNs), typically separated by newlines or commas. Entered via the GUI text box.

.EXAMPLE
1. **Configure $adDisabledUserOU variable first.**
2. Ensure prerequisites (Modules, Permissions, Log Directory, Correct Group Names) are met.
3. Run the script: .\FullExitScriptGUI.ps1 (Authentications will happen first).
4. Enter UPNs like '<EMAIL>', '<EMAIL>' into the top text box.
5. Click "Run Full Exit".
6. Observe the log output in the GUI/console and review the permanent log file.

.NOTES
Author: Slader Sheppard (incorporating Gemini suggestions)
Date: 2025-04-07 (Corrected Version 15 - Full Exit)
Requires:
 - PowerShell 5.1 or later
 - .NET Framework (for Windows Forms)
 - Microsoft Graph PowerShell SDK (`Microsoft.Graph.Authentication`, `Microsoft.Graph.Users`, `Microsoft.Graph.Users.Actions`, `Microsoft.Graph.DirectoryObjects`, `Microsoft.Graph.Groups`)
 - Active Directory PowerShell Module (RSAT)
 - Exchange Online Management V3 Module (`ExchangeOnlineManagement`)
Permissions:
 - Entra ID/Graph: User.ReadWrite.All, Directory.ReadWrite.All, Directory.AccessAsUser.All, AuditLog.Read.All, GroupMember.ReadWrite.All, Group.Read.All. **IMPORTANT:** Removing members from all types of Entra groups may require higher Azure AD roles. License management also requires appropriate admin roles.
 - Active Directory: Rights to find users/groups/OUs, disable accounts, set expiration, clear/set attributes (Title, Dept, Company, Manager, extAttr10), set password, manage group membership, modify user attributes (msExchHideFromAddressLists, primaryGroupID), move user objects. **Crucially, requires specific Write permission for extensionAttribute10.**
 - Exchange Online: Typically 'Exchange Administrator' role or equivalent permissions for Get/Set-Mailbox.
 - File System: Write access to C:\temp\Helpdesk\FullExitAuditLogs\
 - External APIs: Appropriate credentials/tokens/keys for Slack, 1Password, Zscaler, GitHub.
Ensure the account running the script has the necessary permissions across all integrated systems.
CRITICAL: Review and update placeholder variables like group names and $adDisabledUserOU before use.
TEST THOROUGHLY in a non-production environment.
#>

#region Prerequisites Check
#Write-Host "Checking for required PowerShell modules..." -ForegroundColor Yellow
$ErrorActionPreference = 'Stop' # Make sure script stops on module check errors

$requiredModules = @{
    "Microsoft.Graph.Authentication" = "Install-Module Microsoft.Graph.Authentication -Scope CurrentUser -Force"
    "Microsoft.Graph.Users"          = "Install-Module Microsoft.Graph.Users -Scope CurrentUser -Force"
    "Microsoft.Graph.Users.Actions"  = "Install-Module Microsoft.Graph.Users.Actions -Scope CurrentUser -Force" # Needed for Set-MgUserLicense
    "Microsoft.Graph.DirectoryObjects" = "Install-Module Microsoft.Graph.DirectoryObjects -Scope CurrentUser -Force"
    "Microsoft.Graph.Groups"         = "Install-Module Microsoft.Graph.Groups -Scope CurrentUser -Force" # Needed for group management
    "ActiveDirectory"                = "Install-WindowsFeature RSAT-AD-PowerShell (Windows Server) or Add RSAT via Optional Features (Windows Client)"
    "ExchangeOnlineManagement"       = "Install-Module ExchangeOnlineManagement -Scope CurrentUser -Force"
}
$missingModules = @{}

foreach ($moduleName in $requiredModules.Keys) {
    try {
        if (-not (Get-Module -ListAvailable -Name $moduleName)) {
            $missingModules[$moduleName] = $requiredModules[$moduleName]
        }
    } catch {
        Write-Warning "Could not check for module '$moduleName'. Error: $($_.Exception.Message)"
        Write-Warning "Ensure PowerShell execution policy allows script execution (e.g., RemoteSigned)."
        if (-not $missingModules.ContainsKey($moduleName)) {
             $missingModules[$moduleName] = $requiredModules[$moduleName]
        }
    }
}

if ($missingModules.Count -gt 0) {
    Write-Warning "----------------------------------------------------------------"
    Write-Warning "The following required modules appear to be missing:"
    $missingModules.GetEnumerator() | ForEach-Object { Write-Warning "- $($_.Name) `n  (Installation Hint: $($_.Value))" }
    Write-Warning "----------------------------------------------------------------"
    Write-Error "Script cannot continue without required modules. Please install them and re-run."
    Read-Host "Press Enter to exit"
    exit
}
else {
    #Write-Host "Required modules found." -ForegroundColor Green
}
$ErrorActionPreference = 'Continue' # Set back to default

# Import necessary modules silently
Import-Module Microsoft.Graph.Authentication -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.Users -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.Users.Actions -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.DirectoryObjects -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.Groups -ErrorAction SilentlyContinue
Import-Module ActiveDirectory -ErrorAction SilentlyContinue
Import-Module ExchangeOnlineManagement -ErrorAction SilentlyContinue
#endregion Prerequisites Check

#region Global Variables & Configuration

# --- !! IMPORTANT: Configure these variables for your environment !! ---
$logFilePathBase = "C:\temp\Helpdesk\FullExitAuditLogs"
# --- Target OU for disabled users (Distinguished Name format) ---
# --- Verify this DN is correct for your AD structure ---
$adDisabledUserOU = "OU=Disabled Users,OU=Graveyard,DC=QuadrantHR,DC=com"
# --- Ensure this group name is exactly correct in AD ---
$adNoAccessGroupName = "no access"
# --- Define AD groups to EXCLUDE from removal (by Name) ---
# Only excluding the 'no access' group per user request
$adGroupExclusionNames = @(
    $adNoAccessGroupName
)
# --- Corrected based on screenshot ---
$entraFormerUsersGroupName = "QHR Former Employees" # Exact name of the Entra group to potentially add later (used as exclusion here)
$entraGroupExclusionNames = @( # Entra Groups/Object IDs to NOT remove the user from
     $entraFormerUsersGroupName
     # Add other Entra group names or IDs if needed
)
$githubOrgName = "QHRTech" # Your GitHub Organization name

#endregion Global Variables & Configuration

# --- Moved Authentication BEFORE GUI Load ---
#region Connect To Services
#Write-Host "Attempting initial service connections..." -ForegroundColor Cyan

$graphConnected = $false
$exoConnected = $false
$global:fullLogPath = $null # Initialize log path variable

# --- Initialize Log File (Moved here to log connection attempts) ---
try {
    if (-not (Test-Path -Path $logFilePathBase -PathType Container)) {
        #Write-Host "Creating log directory: $logFilePathBase" -ForegroundColor Yellow
        New-Item -Path $logFilePathBase -ItemType Directory -Force | Out-Null
    }
    $timestampFile = Get-Date -Format "yyyy-MM-dd HH-mm-ss" # Changed timestamp format
    $global:fullLogPath = Join-Path -Path $logFilePathBase -ChildPath "FullExitScript_$timestampFile.log"
    Add-Content -Path $global:fullLogPath -Value "[$([DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))] [Action] Log file initialized: $global:fullLogPath"
} catch {
    Write-Warning "Error initializing log file path '$logFilePathBase'. Logging to file disabled. Error: $($_.Exception.Message)"
    $global:fullLogPath = $null
}

# Define Write-Log here temporarily for connection logging if GUI isn't loaded
function Write-InitialLog ($Message, $Type = "Info") {
    $timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    $logEntry = "[$timestamp] [$Type] $Message"
    switch ($Type) {
        "Info"    { #Write-Host $logEntry -ForegroundColor Gray
         }
        "Success" { #Write-Host $logEntry -ForegroundColor Green
         }
        "Warning" { #Write-Host $logEntry -ForegroundColor Yellow
         }
        "Error"   { #Write-Error $logEntry
         }
        "Action"  { #Write-Host $logEntry -ForegroundColor Cyan
         }
        default   { #Write-Host $logEntry
         }
    }
    if ($null -ne $global:fullLogPath) { try { Add-Content -Path $global:fullLogPath -Value $logEntry } catch { Write-Warning "Failed to write initial log to file."} }
}

Write-InitialLog "Full Exit process script started." "Action"
Write-InitialLog ("-" * 70) "Info"

# Connect Graph
Write-InitialLog "Connecting to Microsoft Graph..." "Action"
$requiredScopes = @("User.ReadWrite.All", "Directory.ReadWrite.All", "Directory.AccessAsUser.All", "AuditLog.Read.All", "GroupMember.ReadWrite.All", "Group.Read.All")
try {
    $currentContext = Get-MgContext -ErrorAction SilentlyContinue
    $missingScopes = $requiredScopes | Where-Object {$currentContext.Scopes -notcontains $_}
    if ($null -eq $currentContext -or $missingScopes) {
         Disconnect-MgGraph -ErrorAction SilentlyContinue
         # NOTE: The "Welcome to Microsoft Graph!" message appears on first connect in a session and cannot be suppressed via parameters in current SDK versions.
         Connect-MgGraph -Scopes $requiredScopes -ErrorAction Stop -NoWelcome
         $currentContext = Get-MgContext
         if ($null -eq $currentContext) { Throw "Failed to establish connection to Microsoft Graph after connect attempt." }
    }
    Write-InitialLog "Microsoft Graph connection successful." "Success"
    $graphConnected = $true
} catch { Write-InitialLog "Failed to connect to Microsoft Graph. Error: $($_.Exception.Message). Entra ID actions will be skipped." "Error" }
Write-InitialLog ("-" * 70) "Info"

# Connect Exchange Online
Write-InitialLog "Connecting to Exchange Online..." "Action"
try {
    $msalPath = [System.IO.Path]::GetDirectoryName((Get-Module ExchangeOnlineManagement).Path);
    Add-Type -Path "$msalPath\Microsoft.IdentityModel.Abstractions.dll";
    Add-Type -Path "$msalPath\Microsoft.Identity.Client.dll";
    [Microsoft.Identity.Client.IPublicClientApplication] $application = [Microsoft.Identity.Client.PublicClientApplicationBuilder]::Create("fb78d390-0c51-40cd-8e17-fdbfab77341b").WithDefaultRedirectUri().Build();
    $result = $application.AcquireTokenInteractive([string[]]"https://outlook.office365.com/.default").ExecuteAsync().Result;
    Connect-ExchangeOnline -AccessToken $result.AccessToken -UserPrincipalName $result.Account.Username -ShowBanner:$false -ErrorAction Stop 
    Write-InitialLog "Exchange Online connection successful." "Success"
    $exoConnected = $true
} catch { Write-InitialLog "Failed to connect to Exchange Online. Error: $($_.Exception.Message). Mailbox actions will be skipped." "Error" }
Write-InitialLog ("-" * 70) "Info"

#endregion Connect To Services


#region Windows Forms GUI Setup
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Define GUI elements (form, labels, textboxes, button)
$form = New-Object System.Windows.Forms.Form
$form.Text = 'Full User Exit Automation Tool'
$form.Size = New-Object System.Drawing.Size(700, 650)
$form.StartPosition = 'CenterScreen'
$form.FormBorderStyle = 'FixedDialog'
$form.MaximizeBox = $false
$form.MinimizeBox = $true

$labelInput = New-Object System.Windows.Forms.Label
$labelInput.Location = New-Object System.Drawing.Point(10, 10)
$labelInput.Size = New-Object System.Drawing.Size(660, 20)
$labelInput.Text = 'Enter User Principal Names (UPNs - one per line or comma-separated):'
$form.Controls.Add($labelInput)

$textBoxInput = New-Object System.Windows.Forms.TextBox
$textBoxInput.Location = New-Object System.Drawing.Point(10, 35)
$textBoxInput.Size = New-Object System.Drawing.Size(660, 80)
$textBoxInput.Multiline = $true
$textBoxInput.ScrollBars = 'Vertical'
$textBoxInput.Font = New-Object System.Drawing.Font("Consolas", 9)
$textBoxInput.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$form.Controls.Add($textBoxInput)

$buttonTerminate = New-Object System.Windows.Forms.Button
$buttonTerminate.Location = New-Object System.Drawing.Point(275, 125)
$buttonTerminate.Size = New-Object System.Drawing.Size(150, 30)
$buttonTerminate.Text = 'Run Full Exit'
$buttonTerminate.Font = New-Object System.Drawing.Font("Segoe UI", 9, [System.Drawing.FontStyle]::Bold)
$buttonTerminate.Anchor = [System.Windows.Forms.AnchorStyles]::Top
$form.Controls.Add($buttonTerminate)

$labelLog = New-Object System.Windows.Forms.Label
$labelLog.Location = New-Object System.Drawing.Point(10, 170)
$labelLog.Size = New-Object System.Drawing.Size(660, 20)
$labelLog.Text = 'Log Output:'
$form.Controls.Add($labelLog)

$textBoxLog = New-Object System.Windows.Forms.TextBox
$textBoxLog.Location = New-Object System.Drawing.Point(10, 195)
$textBoxLog.Size = New-Object System.Drawing.Size(660, 400)
$textBoxLog.Multiline = $true
$textBoxLog.ScrollBars = 'Vertical'
$textBoxLog.ReadOnly = $true
$textBoxLog.Font = New-Object System.Drawing.Font("Consolas", 8)
$textBoxLog.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$form.Controls.Add($textBoxLog)
#endregion Windows Forms GUI Setup

#region Helper Functions (Logging, Random Password)

# --- Logging Function (Now includes File Logging) ---
# Define the *full* Write-Log function now that $textBoxLog exists
function Write-Log ($Message, $Type = "Info") {
    $timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    $logEntry = "[$timestamp] [$Type] $Message"

    # Write to Console Host
    switch ($Type) {
        "Info"    { #Write-Host $logEntry -ForegroundColor Gray
         }
        "Success" { #Write-Host $logEntry -ForegroundColor Green
         }
        "Warning" { #Write-Host $logEntry -ForegroundColor Yellow
         }
        "Error"   { #Write-Error $logEntry
         }
        "Action"  { #Write-Host $logEntry -ForegroundColor Cyan
         }
        "Section" { #Write-Host $logEntry -ForegroundColor Magenta
         }
        default   { #Write-Host $logEntry
         }
    }

    # Append to GUI Log Box
    if ($textBoxLog -and $textBoxLog.IsHandleCreated) {
         try {
            if ($textBoxLog.InvokeRequired) {
                $textBoxLog.Invoke([System.Action]{ $textBoxLog.AppendText("$logEntry`r`n") })
            } else { $textBoxLog.AppendText("$logEntry`r`n") }
         } catch { Write-Warning "Failed to write log to GUI text box: $($_.Exception.Message)" }
    }

    # Append to File Log (Check if path is set)
    if ($null -ne $global:fullLogPath) {
        try {
            $logDir = Split-Path -Path $global:fullLogPath -Parent
            if (-not (Test-Path -Path $logDir -PathType Container)) {
                New-Item -Path $logDir -ItemType Directory -Force | Out-Null
            }
            Add-Content -Path $global:fullLogPath -Value $logEntry
        } catch {
            Write-Warning "Failed to write log to file '$global:fullLogPath'. Error: $($_.Exception.Message)"
        }
    }
}

# --- Random Password Generation ---
function New-RandomPassword {
    param(
        [int]$Length = 16
    )
    $lower = 'abcdefghijklmnopqrstuvwxyz'
    $upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    $digits = '0123456789'
    $special = '!@#$%^&*()-_=+[]{}|;:,.<>?'
    $passwordChars = @()
    $passwordChars += Get-Random -InputObject ($lower.ToCharArray())
    $passwordChars += Get-Random -InputObject ($upper.ToCharArray())
    $passwordChars += Get-Random -InputObject ($digits.ToCharArray())
    $passwordChars += Get-Random -InputObject ($special.ToCharArray())
    $allChars = $lower + $upper + $digits + $special
    for ($i = $passwordChars.Count; $i -lt $Length; $i++) {
        $passwordChars += Get-Random -InputObject ($allChars.ToCharArray())
    }
    return (($passwordChars | Get-Random -Count $passwordChars.Count) -join '')
}

# --- Log Separator ---
function Write-LogSeparator {
    Write-Log ("-" * 70) "Info"
}

#endregion Helper Functions (Logging, Random Password)

#region Main Logic - Button Click Event Handler
$buttonTerminate.Add_Click({
    $buttonTerminate.Enabled = $false
    $buttonTerminate.Text = 'Processing...'
    $form.Cursor = [System.Windows.Forms.Cursors]::WaitCursor
    $textBoxLog.Clear() # Clear GUI log for new run

    # Log file path was already set before GUI loaded
    Write-Log "Starting processing from GUI button click." "Action"
    Write-LogSeparator

    # --- Process User List ---
    $userInput = $textBoxInput.Text.Trim()
    if ([string]::IsNullOrWhiteSpace($userInput)) {
        Write-Log "User input field is empty. No users to process." "Warning"
    } else {
        $userPrincipalNames = $userInput -split '[\r\n,]+' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

        if ($userPrincipalNames.Count -eq 0) {
             Write-Log "No valid User Principal Names found after parsing input." "Warning"
        } else {
             Write-Log "Processing the following UPNs: $($userPrincipalNames -join ', ')" "Action"

             # --- Pre-fetch AD 'No Access' Group Info ---
             $adNoAccessGroupObject = $null
             $adNoAccessGroupDN = $null # Store DN for exclusion
             try {
                 $adNoAccessGroupObject = Get-ADGroup -Filter { Name -eq $adNoAccessGroupName } -Properties primaryGroupToken, SID -ErrorAction Stop
                 if ($adNoAccessGroupObject) {
                    $adNoAccessGroupDN = $adNoAccessGroupObject.DistinguishedName
                    Write-Log "Successfully found AD '$adNoAccessGroupName' group (DN: $adNoAccessGroupDN)" "Info"
                 } else { Throw "AD Group '$adNoAccessGroupName' not found using Filter." }
             } catch { Write-Log "Failed to find the required AD group '$adNoAccessGroupName'. Error: $($_.Exception.Message). AD group actions requiring it will fail." "Error" }
             Write-LogSeparator

             # --- Pre-fetch DNs for AD Exclusion Groups ---
             $adExclusionGroupDNs = @()
             if ($adNoAccessGroupDN) { $adExclusionGroupDNs += $adNoAccessGroupDN }
             # Add any other custom exclusions by Name by looking up their DNs
             # Removed lookup for "Domain Users" per request
             foreach ($customExclusionName in ($adGroupExclusionNames | Where-Object {$_ -ne $adNoAccessGroupName})) {
                 try {
                     $customGroup = Get-ADGroup -Filter { Name -eq $customExclusionName } -ErrorAction Stop
                     if ($customGroup) { $adExclusionGroupDNs += $customGroup.DistinguishedName }
                     else { Write-Log "Warning: Could not find custom exclusion group '$customExclusionName' by name to get DN." "Warning" }
                 } catch { Write-Log "Warning: Error finding custom exclusion group '$customExclusionName'. Error: $($_.Exception.Message)" "Warning" }
             }
             $adExclusionGroupDNs = $adExclusionGroupDNs | Select-Object -Unique # Ensure unique DNs
             Write-Log "AD Groups to Exclude from removal (by DN): $($adExclusionGroupDNs -join '; ')" "Info"
             Write-LogSeparator

            # --- Check if Target OU Exists ---
            $targetOUExists = $false
            if (-not [string]::IsNullOrEmpty($adDisabledUserOU)) {
                Write-Log "Checking if target OU '$adDisabledUserOU' exists..." "Info"
                try {
                    if (Get-ADOrganizationalUnit -Identity $adDisabledUserOU -ErrorAction Stop) {
                        Write-Log "Target OU '$adDisabledUserOU' found." "Success"
                        $targetOUExists = $true
                    }
                } catch {
                    Write-Log "Target OU '$adDisabledUserOU' not found or inaccessible. Error: $($_.Exception.Message). User move will be skipped." "Error"
                }
            } else {
                 Write-Log "Target OU path (`$adDisabledUserOU`) is not defined. User move will be skipped." "Warning"
            }
            Write-LogSeparator


             # --- Loop Through Each User ---
             foreach ($upn in $userPrincipalNames) {
                 Write-Log "========== Starting Processing for User: $upn ==========" "Section"
                 $Error.Clear()

                 $mgUser = $null
                 $adUser = $null # Store the initial AD user object
                 $userId = $null
                 $samAccountName = $null
                 $adUserDistinguishedName = $null # Store DN for Move-ADObject

                 # == Find Entra User First ==
                 if ($graphConnected) {
                     try {
                         Write-Log "Finding Entra user '$upn'..." "Info"
                         $mgUser = Get-MgUser -Filter "UserPrincipalName eq '$upn'" -ConsistencyLevel eventual -ErrorAction SilentlyContinue `
                            -Select Id, UserPrincipalName, AccountEnabled, DisplayName, Mail, AssignedLicenses
                         if ($null -eq $mgUser) {
                             Write-Log "Filter did not find '$upn', trying Search..." "Warning"
                             $mgUser = Get-MgUser -Search "UserPrincipalName:$upn" -ConsistencyLevel eventual -ErrorAction SilentlyContinue -Top 1 `
                                -Select Id, UserPrincipalName, AccountEnabled, DisplayName, Mail, AssignedLicenses
                             if ($null -ne $mgUser -and $mgUser.UserPrincipalName -ne $upn) { Write-Log "Search found '$($mgUser.UserPrincipalName)', but UPN doesn't match '$upn'. Skipping Entra processing." "Warning"; $mgUser = $null }
                         }
                         if ($mgUser) {
                            Write-Log "Found Entra user '$($mgUser.UserPrincipalName)' (ID: $($mgUser.Id))" "Success"
                            $userId = $mgUser.Id
                         } else { Write-Log "Entra user '$upn' not found." "Warning" }
                     } catch { Write-Log "Error finding Entra user '$upn'. Error: $($_.Exception.Message)" "Error" }
                 } else { Write-Log "Skipping Entra user search because Graph connection failed." "Warning" }
                 Write-LogSeparator


                 # == Exchange Online Actions (Moved Earlier) ==
                 Write-Log "--- Exchange Online Actions ---" "Section"
                 if ($exoConnected) {
                     if ($null -ne $mgUser -and -not([string]::IsNullOrEmpty($mgUser.Mail))) {
                         $userMail = $mgUser.Mail
                         Write-Log "  Attempting to convert mailbox '$userMail' to Shared..." "Action"
                         try {
                            $mailbox = Get-Mailbox -Identity $userMail -ErrorAction SilentlyContinue
                            if ($null -ne $mailbox -and $mailbox.RecipientTypeDetails -ne 'SharedMailbox') {
                                Set-Mailbox -Identity $userMail -Type Shared -ErrorAction Stop
                                Write-Log "  Successfully converted mailbox '$userMail' to Shared." "Success"
                            } elseif ($null -ne $mailbox -and $mailbox.RecipientTypeDetails -eq 'SharedMailbox') {
                                Write-Log "  Mailbox '$userMail' is already a Shared Mailbox." "Info"
                            } else {
                                Write-Log "  Mailbox '$userMail' not found or is not a type that can be converted." "Warning"
                            }
                         } catch { Write-Log "  Failed to convert mailbox '$userMail' to Shared. Error: $($_.Exception.Message)" "Error" }
                     } else { Write-Log "  Skipping mailbox conversion because Entra user/mail property was not found for '$upn'." "Warning" }
                 } else { Write-Log "Skipping Exchange Online actions because connection failed." "Warning" }
                 Write-LogSeparator


                 # == Entra ID / Graph Actions (Continued) ==
                 Write-Log "--- Entra ID / Graph Actions (Continued) ---" "Section"
                 if ($graphConnected -and $mgUser) {
                     try {
                         # Disable Entra User
                         if ($mgUser.AccountEnabled -eq $true) {
                             Write-Log "  Attempting to disable Entra account..." "Action"
                             try { Update-MgUser -UserId $userId -AccountEnabled:$false -ErrorAction Stop; Write-Log "  Successfully disabled Entra account." "Success" }
                             catch { Write-Log "  Failed to disable Entra account. Error: $($_.Exception.Message)" "Error" }
                         } else { Write-Log "  Entra account is already disabled." "Info" }

                         # Revoke Sessions
                         Write-Log "  Attempting to revoke Entra sign-in sessions..." "Action"
                         try { Revoke-MgUserSignInSession -UserId $userId -ErrorAction Stop; Write-Log "  Successfully revoked Entra sessions." "Success" }
                         catch {
                             if ($_.Exception.Message -match 'User has no valid session|invalid refresh token') { Write-Log "  No active/valid Entra sessions found to revoke." "Info" }
                             else { Write-Log "  Failed to revoke Entra sessions. Error: $($_.Exception.Message)" "Error" }
                         }

                         # Remove Licenses
                         Write-Log "  Attempting to remove all assigned Entra licenses..." "Action"
                         try {
                             $licenses = Get-MgUserLicenseDetail -UserId $userId -ErrorAction Stop
                             if ($licenses.Count -gt 0) {
                                 $skuIdsToRemove = $licenses.SkuId
                                 Write-Log "    Found $($licenses.Count) license SKUs to remove: $($skuIdsToRemove -join ', ')" "Info"
                                 $licenseParams = @{ AddLicenses = @{}; RemoveLicenses = $skuIdsToRemove }
                                 Set-MgUserLicense -UserId $userId -BodyParameter $licenseParams -ErrorAction Stop
                                 Write-Log "  Successfully removed assigned licenses." "Success"
                             } else { Write-Log "  No assigned licenses found for user." "Info" }
                         } catch {
                             Write-Log "  Failed to remove licenses. Error: $($_.Exception.Message)" "Error"
                             Write-Log "  Check required permissions/roles (e.g., License Administrator)." "Warning"
                         }

                         # Disable Entra Devices
                         Write-Log "  Attempting to find/disable Entra registered devices..." "Action"
                         try {
                             $Error.Clear(); $Devices = $null; $getDeviceError = $null
                             $Devices = Get-MgUserRegisteredDevice -UserId $userId -ErrorAction SilentlyContinue -All
                             $getDeviceError = $Error[0]
                             if ($null -ne $Devices -and $Devices.Count -gt 0) {
                                 Write-Log "    Found $($Devices.Count) Entra registered device(s)." "Info"
                                 foreach ($Device in $Devices) {
                                     Write-Log "    Processing Device ID: $($Device.Id), Name: $($Device.DisplayName), Enabled: $($Device.AccountEnabled)" "Info"
                                     if ($Device.PSObject.Properties.Name -contains 'AccountEnabled' -and $Device.AccountEnabled -ne $false) {
                                         Write-Log "      Attempting to disable Entra device ID '$($Device.Id)'..." "Action"
                                         try { Update-MgDevice -DeviceId $Device.Id -AccountEnabled:$false -ErrorAction Stop; Write-Log "      Successfully disabled Entra device ID '$($Device.Id)'." "Success" }
                                         catch { Write-Log "      Failed to disable Entra device ID '$($Device.Id)'. Error: $($_.Exception.Message)" "Error" }
                                     } elseif ($Device.AccountEnabled -eq $false) { Write-Log "    Device ID '$($Device.Id)' is already disabled." "Info" }
                                     else { Write-Log "    Device ID '$($Device.Id)' AccountEnabled status unknown." "Warning" }
                                 }
                             } elseif ($null -eq $Devices -and $getDeviceError -ne $null) {
                                 if ($getDeviceError.CategoryInfo.Reason -eq 'ResourceNotFoundException' -or $getDeviceError.Exception.Message -match 'Resource .* does not exist') { Write-Log "    User not found when querying Entra devices." "Warning" }
                                 elseif ($getDeviceError.Exception.Message -match 'No application to sign out from.') { Write-Log "    Querying Entra devices failed with unusual error: $($getDeviceError.Exception.Message)." "Warning" }
                                 else { Write-Log "    Error querying Entra devices: $($getDeviceError.Exception.Message)" "Error" }
                                 $Error.Clear()
                             } else { Write-Log "    No Entra registered devices found." "Info" }
                         } catch { Write-Log "  Unexpected error during Entra device processing: $($_.Exception.Message)" "Error" }

                         # Remove Entra Group Memberships (excluding exceptions)
                         Write-Log "  Attempting to remove user from Entra groups (excluding specified)..." "Action"
                         try {
                             $entraMemberships = Get-MgUserMemberOf -UserId $userId -All -ErrorAction Stop
                             $entraGroups = $entraMemberships | Where-Object {$_.AdditionalProperties.Keys -contains '@odata.type' -and $_.AdditionalProperties['@odata.type'] -eq '#microsoft.graph.group'}
                             $resolvedExclusionIds = @()
                             Write-Log "    Resolving exclusion group names to IDs..." "Info"
                             foreach ($exclusionName in $entraGroupExclusionNames) {
                                 try {
                                     $group = Get-MgGroup -Filter "displayName eq '$exclusionName'" -Select Id -ErrorAction SilentlyContinue
                                     if ($group) { $resolvedExclusionIds += $group.Id; Write-Log "      Resolved '$exclusionName' to ID '$($group.Id)'." "Info" }
                                     else { Write-Log "      Could not resolve Entra exclusion group name '$exclusionName' to an ID." "Warning" }
                                 } catch { Write-Log "      Error resolving Entra exclusion group '$exclusionName'. Error: $($_.Exception.Message)" "Warning" }
                             }
                             $entraGroupsToRemove = $entraGroups | Where-Object { $resolvedExclusionIds -notcontains $_.Id -and $entraGroupExclusionNames -notcontains $_.AdditionalProperties['displayName'] }

                             if ($entraGroupsToRemove.Count -gt 0) {
                                 Write-Log "    Found $($entraGroupsToRemove.Count) Entra group(s) to process for removal." "Info"
                                 foreach ($groupMembershipObject in $entraGroupsToRemove) {
                                     $groupId = $groupMembershipObject.Id
                                     $groupDisplayName = $groupMembershipObject.AdditionalProperties['displayName']
                                     Write-Log "    Processing Entra group '$($groupDisplayName)' (ID: $($groupId))..." "Info"
                                     $groupDetails = $null
                                     try {
                                         $groupDetails = Get-MgGroup -GroupId $groupId -Property Id, DisplayName, MailEnabled, SecurityEnabled, GroupTypes, OnPremisesSyncEnabled -ErrorAction Stop
                                     } catch { Write-Log "      Failed to get details for Entra group '$groupDisplayName' (ID: $groupId). Skipping removal attempt. Error: $($_.Exception.Message)" "Error"; continue }

                                     if ($groupDetails.GroupTypes -contains 'DynamicMembership') { Write-Log "      Skipping removal from '$($groupDisplayName)' (Dynamic Group)." "Warning"; continue }
                                     if ($groupDetails.MailEnabled -eq $true -and $groupDetails.SecurityEnabled -eq $true) { Write-Log "      Skipping removal from '$($groupDisplayName)' (Mail-Enabled Security Group, likely synced)." "Warning"; continue }
                                     if ($groupDetails.AdditionalProperties.Keys -contains 'onPremisesSyncEnabled' -and $groupDetails.AdditionalProperties['onPremisesSyncEnabled'] -eq $true) { Write-Log "      Skipping removal from '$($groupDisplayName)' (Synced from On-Premises AD)." "Warning"; continue }
                                     if ($groupDetails.MailEnabled -eq $true -and $groupDetails.SecurityEnabled -eq $false) { Write-Log "      Skipping removal from '$($groupDisplayName)' (Distribution List)." "Warning"; continue }

                                     Write-Log "      Attempting removal from Entra group '$($groupDisplayName)' (ID: $($groupId))..." "Action"
                                     try {
                                         Remove-MgGroupMemberByRef -GroupId $groupId -DirectoryObjectId $userId -ErrorAction Stop
                                         Write-Log "      Successfully removed user from Entra group '$($groupDisplayName)'." "Success"
                                     } catch {
                                         if ($_.Exception.Message -match 'Authorization_RequestDenied' -or $_.Exception.Message -match 'Insufficient privileges') { Write-Log "      Failed to remove user from Entra group '$($groupDisplayName)'. Error: Insufficient privileges. Check Azure AD Role assignments." "Error" }
                                         else { Write-Log "      Failed to remove user from Entra group '$($groupDisplayName)'. Error: $($_.Exception.Message)" "Error" }
                                     }
                                 } # End foreach group to remove
                             } else { Write-Log "    No Entra groups found to remove user from (after exclusions and type checks)." "Info" }
                         } catch { Write-Log "  Failed to get or process Entra group memberships. Error: $($_.Exception.Message)" "Error" }

                     } catch { Write-Log "An UNEXPECTED error occurred during Entra ID actions for '$upn': $($_.Exception.Message)" "Error" }
                 } else { Write-Log "Skipping Entra ID actions because Graph connection failed or user not found." "Warning" }
                 Write-LogSeparator

                 # == Local Active Directory Actions ==
                 Write-Log "--- Local Active Directory Actions ---" "Section"
                 try {
                     Write-Log "Finding AD user for UPN '$upn'..." "Info"
                     $adUser = Get-ADUser -Filter { UserPrincipalName -eq $upn } -Properties SamAccountName, Enabled, msExchHideFromAddressLists, primaryGroupID, AccountExpirationDate, Title, Department, Company, Manager, extensionAttribute10, MemberOf, DistinguishedName -ErrorAction SilentlyContinue
                     if ($null -ne $adUser) {
                         $samAccountName = $adUser.SamAccountName
                         $adUserDistinguishedName = $adUser.DistinguishedName # Store DN for Move action later
                         Write-Log "Found AD user '$samAccountName' (DN: $adUserDistinguishedName)" "Success"

                         # Disable AD Account
                         if ($adUser.Enabled -eq $true) {
                             Write-Log "  Attempting to disable AD account '$samAccountName'..." "Action"
                             try { Disable-ADAccount -Identity $samAccountName -ErrorAction Stop; Write-Log "  Successfully disabled AD account '$samAccountName'." "Success" }
                             catch { Write-Log "  Failed to disable AD account '$samAccountName'. Error: $($_.Exception.Message)" "Error" }
                         } else { Write-Log "  AD account '$samAccountName' is already disabled." "Info" }

                         # Set Account Expiration to End of Today
                         Write-Log "  Attempting to set AD account expiration for '$samAccountName' to end of today..." "Action"
                         try {
                            $expiryDate = (Get-Date).Date.AddDays(1).AddSeconds(-1)
                            if ($null -eq $adUser.AccountExpirationDate -or $adUser.AccountExpirationDate -gt (Get-Date)) {
                                Set-ADAccountExpiration -Identity $samAccountName -DateTime $expiryDate -ErrorAction Stop
                                Write-Log "  Successfully set AD account expiration date to $($expiryDate.ToString('yyyy-MM-dd HH:mm:ss'))." "Success"
                            } else { Write-Log "  AD account expiration date is already set to '$($adUser.AccountExpirationDate)' or earlier. Not changing." "Info" }
                         } catch { Write-Log "  Failed to set AD account expiration for '$samAccountName'. Error: $($_.Exception.Message)" "Error" }

                         # Clear Attributes and Handle Manager
                         Write-Log "  Processing AD Attributes (Title, Dept, Company, Manager -> extAttr10)..." "Action"
                         Write-Warning "  Verify account running script has Write permissions for extensionAttribute10 on user objects!"
                         $managerNameFormatted = $null
                         $originalManagerDN = $adUser.Manager
                         $attributesToClear = @('Title', 'Department', 'Company')

                         if (-not [string]::IsNullOrEmpty($adUser.Title)) { Write-Log "    Current Title: $($adUser.Title)" "Info"} else { Write-Log "    Current Title: <empty>" "Info" }
                         if (-not [string]::IsNullOrEmpty($adUser.Department)) { Write-Log "    Current Department: $($adUser.Department)" "Info"} else { Write-Log "    Current Department: <empty>" "Info" }
                         if (-not [string]::IsNullOrEmpty($adUser.Company)) { Write-Log "    Current Company: $($adUser.Company)" "Info"} else { Write-Log "    Current Company: <empty>" "Info" }

                         if (-not [string]::IsNullOrEmpty($originalManagerDN)) {
                             Write-Log "    Current Manager DN: $originalManagerDN" "Info"
                             Write-Log "    Attempting to retrieve manager details..." "Info"
                             try {
                                 $managerObj = Get-ADUser -Identity $originalManagerDN -Properties GivenName, Surname -ErrorAction Stop
                                 if ($managerObj -and -not([string]::IsNullOrEmpty($managerObj.GivenName)) -and -not([string]::IsNullOrEmpty($managerObj.Surname)) ) {
                                     $managerNameFormatted = "$($managerObj.GivenName).$($managerObj.Surname)"
                                     Write-Log "    Formatted manager name '$managerNameFormatted' for extensionAttribute10." "Info"
                                     $attributesToClear += 'Manager'
                                 } else { Write-Log "    Retrieved manager object but couldn't get first/last name. Manager attribute will be cleared, extensionAttribute10 not set." "Warning"; $attributesToClear += 'Manager' }
                             } catch { Write-Log "    Failed to retrieve manager details for DN '$originalManagerDN'. Error: $($_.Exception.Message). Manager attribute will be cleared, extensionAttribute10 not set." "Error"; $attributesToClear += 'Manager' }
                         } else { Write-Log "    No manager attribute set for user '$samAccountName'." "Info" }

                         $setParams = @{ Identity = $samAccountName }
                         $replaceHash = @{}
                         if ($managerNameFormatted) {
                             $replaceHash.Add('extensionAttribute10', $managerNameFormatted)
                         } elseif (-not [string]::IsNullOrEmpty($adUser.extensionAttribute10)) {
                             $attributesToClear += 'extensionAttribute10'
                         }
                         if ($attributesToClear.Count -gt 0) { $setParams.Add('Clear', $attributesToClear) }
                         if ($replaceHash.Keys.Count -gt 0) { $setParams.Add('Replace', $replaceHash) }

                         if ($setParams.Keys.Count -gt 1) {
                             try {
                                 Write-Log "    Attempting to update attributes..." "Action"
                                 Set-ADUser @setParams -ErrorAction Stop
                                 Write-Log "    Successfully submitted attribute updates (Clear=$($attributesToClear -join ', '); Replace=$($replaceHash.Keys -join ', '))" "Success"

                                 Write-Log "    Verifying extensionAttribute10..." "Info"
                                 Start-Sleep -Seconds 2
                                 $verifyUser = Get-ADUser $samAccountName -Properties extensionAttribute10
                                 $currentExtAttr10Value = $verifyUser.extensionAttribute10
                                 if ($managerNameFormatted) {
                                     if ($currentExtAttr10Value -eq $managerNameFormatted) { Write-Log "      Verified extensionAttribute10 is set correctly to '$managerNameFormatted'." "Success" }
                                     else { Write-Log "      VERIFICATION FAILED: extensionAttribute10 is currently '$($currentExtAttr10Value)' after attempting to set it to '$managerNameFormatted'." "Warning" }
                                 } else {
                                     if ([string]::IsNullOrEmpty($currentExtAttr10Value)) { Write-Log "      Verified extensionAttribute10 is clear." "Success" }
                                     else { Write-Log "      VERIFICATION FAILED: extensionAttribute10 still has value '$($currentExtAttr10Value)' after attempting to clear it." "Warning" }
                                 }
                             } catch { Write-Log "    Failed to clear/update AD attributes for '$samAccountName'. Error: $($_.Exception.Message)" "Error" }
                         } else { Write-Log "    No attribute changes required for Title, Dept, Company, Manager, or extAttr10." "Info" }
                         Write-LogSeparator

                         # Reset AD Password (Once)
                         Write-Log "  Attempting to reset AD password for '$samAccountName'..." "Action"
                         try {
                             $newPassword1 = New-RandomPassword
                             Set-ADAccountPassword -Identity $samAccountName -Reset -NewPassword (ConvertTo-SecureString -AsPlainText $newPassword1 -Force) -ErrorAction Stop
                             Write-Log "  Successfully reset AD password." "Success"
                         } catch { Write-Log "  Failed to reset AD password for '$samAccountName'. Error: $($_.Exception.Message)" "Error" }

                         Write-LogSeparator

                         # Add to "No Access" Group (if group exists)
                         if ($null -ne $adNoAccessGroupObject) {
                            Write-Log "  Attempting to add AD user '$samAccountName' to group '$adNoAccessGroupName'..." "Action"
                            try {
                                if (-not (Get-ADGroupMember -Identity $adNoAccessGroupObject -ErrorAction SilentlyContinue | Where-Object {$_.SamAccountName -eq $samAccountName})) {
                                    Add-ADGroupMember -Identity $adNoAccessGroupObject -Members $samAccountName -ErrorAction Stop
                                    Write-Log "  Successfully added AD user '$samAccountName' to group '$adNoAccessGroupName'." "Success"
                                } else { Write-Log "  User '$samAccountName' is already a member of '$adNoAccessGroupName'." "Info" }
                            } catch { Write-Log "  Failed to add AD user '$samAccountName' to group '$adNoAccessGroupName'. Error: $($_.Exception.Message)" "Error" }
                         } else { Write-Log "  Skipping add to AD group '$adNoAccessGroupName' because group object was not found." "Warning" }

                         # Set Primary Group to "No Access" (if group exists and user is member)
                         if ($null -ne $adNoAccessGroupObject -and $adNoAccessGroupObject.primaryGroupToken) {
                             $isMember = $false
                             try {
                                 if (Get-ADGroupMember -Identity $adNoAccessGroupObject -ErrorAction SilentlyContinue | Where-Object {$_.SamAccountName -eq $samAccountName}) { $isMember = $true }
                             } catch { Write-Log "    Error checking membership of '$samAccountName' in '$adNoAccessGroupName' before setting primary group." "Warning"}

                             if ($isMember) {
                                 $noAccessToken = $adNoAccessGroupObject.primaryGroupToken
                                 $currentUserPrimaryGroup = (Get-ADUser $samAccountName -Properties primaryGroupID).primaryGroupID
                                 if ($currentUserPrimaryGroup -ne $noAccessToken) {
                                    Write-Log "  Attempting to set AD primary group for '$samAccountName' to '$adNoAccessGroupName' (Token: $noAccessToken)..." "Action"
                                    try {
                                         Set-ADUser -Identity $samAccountName -Replace @{primaryGroupID = $noAccessToken} -ErrorAction Stop
                                         Write-Log "  Successfully set AD primary group to '$adNoAccessGroupName'." "Success"
                                     } catch { Write-Log "  Failed to set AD primary group for '$samAccountName'. Error: $($_.Exception.Message)" "Error" }
                                 } else { Write-Log "  AD primary group is already set to '$adNoAccessGroupName'." "Info"}
                             } else { Write-Log "  Skipping set AD primary group because user '$samAccountName' is not confirmed member of '$adNoAccessGroupName'." "Warning" }
                         } else { Write-Log "  Skipping set AD primary group because '$adNoAccessGroupName' object or token was not found." "Warning" }

                         # Remove Other AD Groups (Using MemberOf, excluding only 'no access' DN)
                         Write-Log "  Attempting to remove AD user '$samAccountName' from other groups (using MemberOf, excluding specified DNs)..." "Action"
                         try {
                             $adUserForGroups = Get-ADUser -Identity $samAccountName -Properties MemberOf -ErrorAction Stop
                             $groupsToRemoveDNs = $adUserForGroups.MemberOf | Where-Object { $adExclusionGroupDNs -notcontains $_ }

                             if ($groupsToRemoveDNs.Count -gt 0) {
                                 Write-Log "    Found $($groupsToRemoveDNs.Count) AD group DN(s) to remove user from." "Info"
                                 foreach ($groupDN in $groupsToRemoveDNs) {
                                     Write-Log "    Removing user '$samAccountName' from AD group '$($groupDN)'..." "Action"
                                     try { Remove-ADGroupMember -Identity $groupDN -Members $adUserForGroups -Confirm:$false -ErrorAction Stop; Write-Log "    Successfully removed user from AD group '$($groupDN)'." "Success" }
                                     catch { Write-Log "    Failed to remove user '$samAccountName' from AD group '$($groupDN)'. Error: $($_.Exception.Message)" "Error" }
                                 }
                             } else { Write-Log "    No other AD groups found in MemberOf attribute to remove (after exclusions)." "Info" }
                         } catch { Write-Log "  Failed to get/process AD group memberships via MemberOf for '$samAccountName'. Error: $($_.Exception.Message)" "Error" }

                         # Hide from GAL
                         $adUserFinalCheck = Get-ADUser -Identity $samAccountName -Properties msExchHideFromAddressLists -ErrorAction SilentlyContinue
                         if ($adUserFinalCheck -and $adUserFinalCheck.msExchHideFromAddressLists -ne $true) {
                            Write-Log "  Attempting to hide AD user '$samAccountName' from GAL..." "Action"
                            try { Set-ADUser -Identity $samAccountName -Replace @{msExchHideFromAddressLists=$true} -ErrorAction Stop; Write-Log "  Successfully set msExchHideFromAddressLists=True for '$samAccountName'." "Success" }
                            catch { Write-Log "  Failed to set msExchHideFromAddressLists for '$samAccountName'. Error: $($_.Exception.Message)" "Error" }
                         } else { Write-Log "  AD user '$samAccountName' is already hidden from GAL or could not be re-verified." "Info" }

                     } else { Write-Log "AD user not found for UPN '$upn'. Skipping AD actions." "Warning" }
                 } catch { Write-Log "An UNEXPECTED error occurred during AD actions for '$upn': $($_.Exception.Message)" "Error" }
                 Write-LogSeparator

                 # == External System Placeholders ==
                 # ... (Placeholders remain the same) ...
                 Write-Log "--- Slack Actions (Placeholder) ---" "Section"
                 Write-Log "Slack integration not implemented." "Info"
                 Write-LogSeparator
                 Write-Log "--- 1Password Actions (Placeholder) ---" "Section"
                 Write-Log "1Password integration via CLI/API not implemented." "Info"
                 Write-LogSeparator
                 Write-Log "--- Zscaler Session Termination via NetBox (Placeholder) ---" "Section"
                 Write-Log "Zscaler session termination via NetBox API not implemented." "Info"
                 Write-LogSeparator
                 Write-Log "--- GitHub Organization Removal (Placeholder) ---" "Section"
                 Write-Log "GitHub organization removal not implemented." "Info"
                 Write-LogSeparator

                 # == Move AD User Object (Final Step for User) ==
                 Write-Log "--- Move AD User Action ---" "Section"
                 if ($adUser -ne $null -and $targetOUExists -eq $true) {
                     # Check if user is already in the target OU
                     if ($adUser.DistinguishedName -notlike "*,$adDisabledUserOU") {
                         Write-Log "  Attempting to move AD user '$samAccountName' to OU '$adDisabledUserOU'..." "Action"
                         try {
                             Move-ADObject -Identity $adUser.DistinguishedName -TargetPath $adDisabledUserOU -ErrorAction Stop
                             Write-Log "  Successfully moved AD user '$samAccountName' to '$adDisabledUserOU'." "Success"
                         } catch {
                             Write-Log "  Failed to move AD user '$samAccountName' to '$adDisabledUserOU'. Error: $($_.Exception.Message)" "Error"
                         }
                     } else {
                         Write-Log "  AD User '$samAccountName' is already in the target OU '$adDisabledUserOU'." "Info"
                     }
                 } elseif ($adUser -eq $null) {
                     Write-Log "  Skipping move AD user because user was not found in AD." "Warning"
                 } else { # Target OU doesn't exist
                     Write-Log "  Skipping move AD user because target OU '$adDisabledUserOU' was not found or inaccessible." "Warning"
                 }
                 Write-LogSeparator


                 Write-Log "========== Finished Processing for User: $upn ==========`n" "Section"

             } # End foreach user loop
        }
    } # End else for non-empty user input

    Write-LogSeparator
    Write-Log "Full Exit process completed." "Action"

    # Re-enable button and reset cursor safely
    if ($form -and $form.IsHandleCreated) {
        $form.Invoke([System.Action]{
            if ($buttonTerminate -and -not $buttonTerminate.IsDisposed) { $buttonTerminate.Enabled = $true; $buttonTerminate.Text = 'Run Full Exit'}
            if ($form -and -not $form.IsDisposed) { $form.Cursor = [System.Windows.Forms.Cursors]::Default }
        })
    }
})
#endregion Main Logic - Button Click Event Handler

#region Show GUI
$form.Add_Shown({
    if ($form -and -not $form.IsDisposed) { $form.Activate() }
    if ($textBoxInput -and -not $textBoxInput.IsDisposed) { $textBoxInput.Select() }
})

# Log GUI Initialization AFTER the full Write-Log function is defined
Write-Log "GUI Initialized. Ready for input. Current Time: $(Get-Date)" "Info"
if ($PSVersionTable.PSEdition -ne 'Desktop' -and $host.Name -match 'Integrated Console'){
    Write-Warning "Host may require STA mode. If GUI fails/hangs, try: powershell.exe -Sta -File `"$($MyInvocation.MyCommand.Path)`""
}
[System.Windows.Forms.Application]::EnableVisualStyles()

try {
    [void]$form.ShowDialog()
} finally {
    if($null -ne $form -and -not $form.IsDisposed) {
        # Use the full Write-Log function here
        Write-Log "GUI Form closing and disposing." "Info"
        $form.Dispose()
    }
}

# Disconnect services silently
Disconnect-MgGraph | Out-Null
if ($exoConnected) {
    #Write-Host "Disconnecting from Exchange Online..." -ForegroundColor Yellow
    Disconnect-ExchangeOnline -Confirm:$false -ErrorAction SilentlyContinue
}
#endregion Show GUI

Write-Host "Script finished. Time: $(Get-Date)"

