# Specify the users you want to delete
$usersToDelete = @("user1", "user2", "user3")

# Path to the log file
$logFilePath = "C:\DeleteUserLog.txt"

# Clear the log file if it exists
Clear-Content -Path $logFilePath -ErrorAction SilentlyContinue

# Loop through each user to delete
foreach ($user in $usersToDelete) {
    try {
        # Check if user exists
        if (Get-LocalUser -Name $user -ErrorAction SilentlyContinue) {
            # Delete the user
            Remove-LocalUser -Name $user -ErrorAction Stop
            # Log success
            $logMessage = "User '$user' deleted successfully."
            Add-Content -Path $logFilePath -Value $logMessage
            Write-Output $logMessage
        } else {
            # Log failure if user doesn't exist
            $logMessage = "User '$user' does not exist."
            Add-Content -Path $logFilePath -Value $logMessage
            Write-Output $logMessage
        }
    } catch {
        # Log failure if any error occurs during deletion
        $errorMessage = $_.Exception.Message
        $logMessage = "Failed to delete user '$user'. Error: $errorMessage"
        Add-Content -Path $logFilePath -Value $logMessage
        Write-Output $logMessage
    }
}

# Display log contents
Get-Content -Path $logFilePath