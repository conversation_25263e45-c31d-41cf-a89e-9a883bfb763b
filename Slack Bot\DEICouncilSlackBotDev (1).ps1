<#
Author - <PERSON><PERSON><PERSON> 
Description - This application allows for employees of the DEI_Council to post to the slack channel for the team.
The GUI will take an image and plain text to send to the slack API. It also has a section to take in Slack block kit syntax for rich messages.

<EMAIL>

#>



<#
How to update
Get uriSlack from https://api.slack.com/apps/A04A8G6PHPU/incoming-webhooks?
$tok From https://api.slack.com/apps/A04A8G6PHPU/oauth?
$Channel is from the slack channel link

#>


#-------------------------------------------------------------#
#----Initial Declarations-------------------------------------#
#-------------------------------------------------------------#
Write-Host "Checking for PSSlack module..."
if (-not (Get-Module PSSlack -ListAvailable)) {
    try {
        Write-Host "PSSlack module not found. Installing..."
        Install-Module PSSlack -Scope CurrentUser -Force -Repository PSGallery
        Write-Host "PSSlack module installed successfully."
    } catch {
        Write-Host "Error installing PSSlack module:"
        Write-Error $_  
        exit  
    }
}

Import-Module PSSlack


$global:File = $null

$uriSlack = "*********************************************************************************"
$tok = "*********************************************************"
$date = Get-Date 
#$outpath = "$env:USERPROFILE\OneDrive - QHR Technologies\Desktop\SlackbotOutput.txt"
$Channel = "C04A87TNNUA"


Add-Type -AssemblyName PresentationCore, PresentationFramework



<# extra XAML
<Image Source="https://www.clipartmax.com/png/middle/199-1998466_slack-icon-slack-logo.png" />

#>

#Code for main application
$Xaml = @"

<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" Width="900" Height="400"  MinHeight="400" MinWidth="800">
     <Grid Name="LayoutRoot" Background="#41a0e8">
    <TextBox HorizontalAlignment="Center" Width="500" TextWrapping="Wrap"  AcceptsReturn="True" VerticalScrollBarVisibility="Visible" Margin="150,174,148,34" FontFamily="Tahoma" ToolTip="Enter Text for the message" Name="enteredtext"></TextBox>
    <TextBlock VerticalAlignment="Top" TextWrapping="Wrap" Text="DEI Council Slack" Margin="128,21,0,0" FontFamily="Tahoma Bold" FontWeight="Bold" FontSize="36" Width="350" Height="70" Foreground="#470874" Focusable="False"></TextBlock>
    <Button Content="Attachment" ToolTip="Select a file to upload" HorizontalAlignment="Right" VerticalAlignment="Top" Width="120" Margin="29,106,0,0" Name="SelectJPG" FontFamily="Tahoma" FontSize="18"></Button>
    <Button Content="Slack Block Kit" ToolTip="Copy block kit syntax from Slack" HorizontalAlignment="Right" VerticalAlignment="Top" Width="150" Margin="29,200,0,0" Name="UseBlockKit" FontFamily="Tahoma" FontSize="18"></Button>
    <TextBlock HorizontalAlignment="Center" VerticalAlignment="Top" TextWrapping="Wrap" Text="File Path Will Appear Here" Margin="100,105,0,0" Name="filePath" Background="#ffffff" Width="400" Height="25" FontFamily="Tahoma"></TextBlock>
    <Button Content="Send" HorizontalAlignment="Right" VerticalAlignment="Bottom" Width="100" Margin="684,225,0,0" Name="Send" Height="100" FontSize="24" FontFamily="Tahoma" Background="#22fc10" BorderBrush="#000000" ToolTip="Press to send to Slack"></Button>

        
               
      <Ellipse Height = "100" 
            Width = "200" 
            HorizontalAlignment = "Left" 
            VerticalAlignment = "Top"
            Margin = "20">
				
            <Ellipse.Fill>
               <ImageBrush ImageSource = "https://www.howtogeek.com/wp-content/uploads/2019/06/slack_logo.png" /> 
            </Ellipse.Fill> 
         </Ellipse> 

         <Ellipse Height = "100" 
            Width = "100" 
            HorizontalAlignment = "Left" 
            VerticalAlignment = "Bottom"
            Margin = "20">
				
            <Ellipse.Fill>
               <ImageBrush ImageSource = "https://pbs.twimg.com/profile_images/573572378707394560/XWEL11gp_400x400.png" /> 
            </Ellipse.Fill> 
         </Ellipse> 


  </Grid>
</Window>

"@


#Code for Block kit window
$BlockXaml = @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" Width="600" Height="00"  MinHeight="400" MinWidth="400">
<Grid Name="LayoutBlock" Background="#41a0e8">
     <TextBlock VerticalAlignment="Top" TextWrapping="Wrap" Text="Slack Block Kit" Margin="128,21,0,0" FontFamily="Tahoma Bold" FontWeight="Bold" FontSize="24" Width="350" Height="70" Foreground="#470874" Focusable="False"></TextBlock>
     <TextBox HorizontalAlignment="Center" VerticalAlignment="Center" AcceptsReturn="True" VerticalScrollBarVisibility="Visible" Width="400" Height="400" TextWrapping="Wrap" Margin="10,150,10,10" FontFamily="Tahoma" ToolTip="Enter Block Kit syntax" Name="BlockKitText"></TextBox>
     <Button Content="Send" HorizontalAlignment="Right" VerticalAlignment="Bottom" Width="100" Margin="5,5,5,5" Name="BlockSend" Height="100" FontSize="24" FontFamily="Tahoma" Background="#22fc10" BorderBrush="#000000" ToolTip="Press to get entry ready to send"></Button>

 </Grid>
</Window>
"@

#-------------------------------------------------------------#
#----Control Event Handlers-----------------------------------#
#-------------------------------------------------------------#



Function Get-FileName($initialDirectory)
{
    [System.Reflection.Assembly]::LoadWithPartialName("System.windows.forms") | Out-Null
    
    $OpenFileDialog = New-Object System.Windows.Forms.OpenFileDialog
    $OpenFileDialog.initialDirectory = $initialDirectory
    $OpenFileDialog.filter = "Media (*.jpeg, *.mov, *.gif)| *.jp*g;*.mov;*.gif"
    $OpenFileDialog.ShowDialog() | Out-Null
    $filepath.Text = $OpenFileDialog.FileName
    $global:File = $OpenFileDialog.FileName
    $OpenFileDialog.FileName
    $global:File
    $slackbot.UpdateLayout()
}

Function File-Upload {
    Write-Host 'Uploading: ' + $global:File
    try {
        # Use a hashtable for the body to include both file and initial_comment
        $body = @{
            Token   = $tok
            Channel = "bot-creation"
            Content = (Get-Item $global:File).OpenRead()
            filename = (Split-Path $global:File -Leaf)
            initial_comment = "Image uploaded by DEI Council Slack Bot"
        }

        $response = Invoke-WebRequest -Uri 'https://slack.com/api/files.upload' -Method POST -Body $body -ContentType 'multipart/form-data' 

        if ($response.StatusCode -eq 200) {
            $responseJson = $response.Content | ConvertFrom-Json
            if ($responseJson.ok) {
                Write-Host "File uploaded successfully."
            } else {
                Write-Host "File upload failed. Error: $($responseJson.error)"
            }
        } else {
            Write-Host "File upload request failed with status code: $($response.StatusCode)"
        }
    } catch {
        Write-Host (Get-Date) ": File upload to Slack failed with an unexpected error: $_"
    }
}


Function Send-Textmsg {

     write-host 'plain message'
     $body = ConvertTo-Json @{
    text = $enteredtext.Text
}

    try {
        Invoke-RestMethod -uri $uriSlack -Method Post -body $body -ContentType 'application/json' 
    } catch {
        Write-Host ($date) ": Text to Slack went wrong..."
    }
    $slackbot.UpdateLayout()
}

Function Send-Richmsg {
   $BKmsg = $BlockKitText.Text
   write-host 'rich message'
    $BKmsg
    

    try {
        Invoke-RestMethod -uri $uriSlack -Method Post -body $BKmsg -ContentType 'application/json'
       #Invoke-WebRequest  -uri $uriSlack -Method Post -body $BKmsg -ContentType 'application/json' 
       
    }
    catch {
        Write-Host ($date) ": Update to Slack went wrong..."  
    }

}


#endregion

#-------------------------------------------------------------#
#----Script Execution-----------------------------------------#
#-------------------------------------------------------------#

$slackbot = [Windows.Markup.XamlReader]::Parse($Xaml)
$BlockInput = [Windows.Markup.XamlReader]::Parse($BlockXaml)

[xml]$xml = $Xaml
$xml.SelectNodes("//*[@Name]") | ForEach-Object { Set-Variable -Name $_.Name -Value $slackbot.FindName($_.Name) } 

[xml]$bxml = $BlockXaml
$bxml.SelectNodes("//*[@Name]") | ForEach-Object { Set-Variable -Name $_.Name -Value $BlockInput.FindName($_.Name) } 


$SelectJPG.Add_Click({Get-FileName('%HOMEPATH%') $this $_})
$Send.Add_Click({Send-Textmsg $this $_})
$Send.Add_Click({File-Upload $this $_})

$UseBlockKit.Add_Click({$BlockInput.ShowDialog()})
$BlockSend.Add_Click({Send-Richmsg $this $_ })

$slackbot.ShowDialog()
$slackbot.UpdateLayout()




