<?xml version="1.0" encoding="utf-8"?>
<policyDefinitions revision="7.1" schemaVersion="1.0">
  <policyNamespaces>
    <target prefix="firefox" namespace="Mozilla.Policies.Firefox7.1"/>
    <using prefix="Mozilla" namespace="Mozilla.Policies4.8"/>
  </policyNamespaces>
  <resources minRequiredRevision="7.1"/>
  <supportedOn>
    <definitions>
      <definition name="UNSUPPORTED" displayName="$(string.UNSUPPORTED)"/>
      <definition name="SUPPORTED_WINXPSP2" displayName="$(string.SUPPORTED_WINXPSP2)"/>
      <definition name="SUPPORTED_FF60" displayName="$(string.SUPPORTED_FF60)"/>
      <definition name="SUPPORTED_FF60ESR" displayName="$(string.SUPPORTED_FF60ESR)"/>
      <definition name="SUPPORTED_FF62" displayName="$(string.SUPPORTED_FF62)"/>
      <definition name="SUPPORTED_FF63" displayName="$(string.SUPPORTED_FF63)"/>
      <definition name="SUPPORTED_FF64" displayName="$(string.SUPPORTED_FF64)"/>
      <definition name="SUPPORTED_FF66" displayName="$(string.SUPPORTED_FF66)"/>
      <definition name="SUPPORTED_FF67" displayName="$(string.SUPPORTED_FF67)"/>
      <definition name="SUPPORTED_FF68" displayName="$(string.SUPPORTED_FF68)"/>
      <definition name="SUPPORTED_FF6801" displayName="$(string.SUPPORTED_FF6801)"/>
      <definition name="SUPPORTED_FF68ESR" displayName="$(string.SUPPORTED_FF68ESR)"/>
      <definition name="SUPPORTED_FF69" displayName="$(string.SUPPORTED_FF69)"/>
      <definition name="SUPPORTED_FF70" displayName="$(string.SUPPORTED_FF70)"/>
      <definition name="SUPPORTED_FF71" displayName="$(string.SUPPORTED_FF71)"/>
      <definition name="SUPPORTED_FF72" displayName="$(string.SUPPORTED_FF72)"/>
      <definition name="SUPPORTED_FF73" displayName="$(string.SUPPORTED_FF73)"/>
      <definition name="SUPPORTED_FF74" displayName="$(string.SUPPORTED_FF74)"/>
      <definition name="SUPPORTED_FF75" displayName="$(string.SUPPORTED_FF75)"/>
      <definition name="SUPPORTED_FF75_ONLY" displayName="$(string.SUPPORTED_FF75_ONLY)"/>
      <definition name="SUPPORTED_FF76" displayName="$(string.SUPPORTED_FF76)"/>
      <definition name="SUPPORTED_FF76_ONLY" displayName="$(string.SUPPORTED_FF76_ONLY)"/>
      <definition name="SUPPORTED_FF77" displayName="$(string.SUPPORTED_FF77)"/>
      <definition name="SUPPORTED_FF77_ONLY" displayName="$(string.SUPPORTED_FF77_ONLY)"/>
      <definition name="SUPPORTED_FF78" displayName="$(string.SUPPORTED_FF78)"/>
      <definition name="SUPPORTED_FF79" displayName="$(string.SUPPORTED_FF79)"/>
      <definition name="SUPPORTED_FF80" displayName="$(string.SUPPORTED_FF80)"/>
      <definition name="SUPPORTED_FF81" displayName="$(string.SUPPORTED_FF81)"/>
      <definition name="SUPPORTED_FF82" displayName="$(string.SUPPORTED_FF82)"/>
      <definition name="SUPPORTED_FF83" displayName="$(string.SUPPORTED_FF83)"/>
      <definition name="SUPPORTED_FF84" displayName="$(string.SUPPORTED_FF84)"/>
      <definition name="SUPPORTED_FF85" displayName="$(string.SUPPORTED_FF85)"/>
      <definition name="SUPPORTED_FF86" displayName="$(string.SUPPORTED_FF86)"/>
      <definition name="SUPPORTED_FF88" displayName="$(string.SUPPORTED_FF88)"/>
      <definition name="SUPPORTED_FF89" displayName="$(string.SUPPORTED_FF89)"/>
      <definition name="SUPPORTED_FF90" displayName="$(string.SUPPORTED_FF90)"/>
      <definition name="SUPPORTED_FF91" displayName="$(string.SUPPORTED_FF91)"/>
      <definition name="SUPPORTED_FF95" displayName="$(string.SUPPORTED_FF95)"/>
      <definition name="SUPPORTED_FF96" displayName="$(string.SUPPORTED_FF96)"/>
      <definition name="SUPPORTED_FF96_ONLY" displayName="$(string.SUPPORTED_FF96_ONLY)"/>
      <definition name="SUPPORTED_FF97" displayName="$(string.SUPPORTED_FF97)"/>
      <definition name="SUPPORTED_FF98_ONLY" displayName="$(string.SUPPORTED_FF98_ONLY)"/>
      <definition name="SUPPORTED_FF99" displayName="$(string.SUPPORTED_FF99)"/>
      <definition name="SUPPORTED_FF100" displayName="$(string.SUPPORTED_FF100)"/>
      <definition name="SUPPORTED_FF101" displayName="$(string.SUPPORTED_FF101)"/>
      <definition name="SUPPORTED_FF102" displayName="$(string.SUPPORTED_FF102)"/>
      <definition name="SUPPORTED_FF104" displayName="$(string.SUPPORTED_FF104)"/>
      <definition name="SUPPORTED_FF105" displayName="$(string.SUPPORTED_FF105)"/>
      <definition name="SUPPORTED_FF106" displayName="$(string.SUPPORTED_FF106)"/>
      <definition name="SUPPORTED_FF107" displayName="$(string.SUPPORTED_FF107)"/>
      <definition name="SUPPORTED_FF108" displayName="$(string.SUPPORTED_FF108)"/>
      <definition name="SUPPORTED_FF109" displayName="$(string.SUPPORTED_FF109)"/>
      <definition name="SUPPORTED_FF110" displayName="$(string.SUPPORTED_FF110)"/>
      <definition name="SUPPORTED_FF112_ONLY" displayName="$(string.SUPPORTED_FF112_ONLY)"/>
      <definition name="SUPPORTED_FF113_ONLY" displayName="$(string.SUPPORTED_FF113_ONLY)"/>
      <definition name="SUPPORTED_FF114" displayName="$(string.SUPPORTED_FF114)"/>
      <definition name="SUPPORTED_FF118" displayName="$(string.SUPPORTED_FF118)"/>
      <definition name="SUPPORTED_FF120" displayName="$(string.SUPPORTED_FF120)"/>
      <definition name="SUPPORTED_FF121" displayName="$(string.SUPPORTED_FF121)"/>
      <definition name="SUPPORTED_FF122" displayName="$(string.SUPPORTED_FF122)"/>
      <definition name="SUPPORTED_FF123" displayName="$(string.SUPPORTED_FF123)"/>
      <definition name="SUPPORTED_FF124" displayName="$(string.SUPPORTED_FF124)"/>
      <definition name="SUPPORTED_FF124_ONLY" displayName="$(string.SUPPORTED_FF124_ONLY)"/>
      <definition name="SUPPORTED_FF125" displayName="$(string.SUPPORTED_FF125)"/>
      <definition name="SUPPORTED_FF126_ONLY" displayName="$(string.SUPPORTED_FF126_ONLY)"/>
      <definition name="SUPPORTED_FF127_ONLY" displayName="$(string.SUPPORTED_FF127_ONLY)"/>
      <definition name="SUPPORTED_FF128" displayName="$(string.SUPPORTED_FF128)"/>
      <definition name="SUPPORTED_FF129" displayName="$(string.SUPPORTED_FF129)"/>
      <definition name="SUPPORTED_FF130" displayName="$(string.SUPPORTED_FF130)"/>
      <definition name="SUPPORTED_FF130_ONLY" displayName="$(string.SUPPORTED_FF130_ONLY)"/>
      <definition name="SUPPORTED_FF131" displayName="$(string.SUPPORTED_FF131)"/>
      <definition name="SUPPORTED_FF137_ONLY" displayName="$(string.SUPPORTED_FF137_ONLY)"/>
      <definition name="SUPPORTED_FF138_ONLY" displayName="$(string.SUPPORTED_FF138_ONLY)"/>
    </definitions>
  </supportedOn>
  <categories>
    <category displayName="$(string.firefox)" name="firefox">
      <parentCategory ref="Mozilla:Cat_Mozilla"/>
    </category>
    <category displayName="$(string.Authentication_group)" name="Authentication">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Popups_group)" name="Popups">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Cookies_group)" name="Cookies">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Addons_group)" name="Addons">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Flash_group)" name="Flash">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Bookmarks_group)" name="Bookmarks">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Homepage_group)" name="Homepage">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Certificates_group)" name="Certificates">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Extensions_group)" name="Extensions">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Search_group)" name="Search">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Permissions_group)" name="Permissions">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.Camera_group)" name="Camera">
      <parentCategory ref="Permissions"/>
    </category>
    <category displayName="$(string.Microphone_group)" name="Microphone">
      <parentCategory ref="Permissions"/>
    </category>
    <category displayName="$(string.Location_group)" name="Location">
      <parentCategory ref="Permissions"/>
    </category>
    <category displayName="$(string.Notifications_group)" name="Notifications">
      <parentCategory ref="Permissions"/>
    </category>
    <category displayName="$(string.Autoplay_group)" name="Autoplay">
      <parentCategory ref="Permissions"/>
    </category>
    <category displayName="$(string.VirtualReality_group)" name="VirtualReality">
      <parentCategory ref="Permissions"/>
    </category>
    <category displayName="$(string.Preferences_group)" name="Preferences">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.SanitizeOnShutdown_group)" name="SanitizeOnShutdown">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.TrackingProtection_group)" name="TrackingProtection">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.DNSOverHTTPS_group)" name="DNSOverHTTPS">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.UserMessaging_group)" name="UserMessaging">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.DisabledCiphers_group)" name="DisabledCiphers">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.EncryptedMediaExtensions_group)" name="EncryptedMediaExtensions">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.PDFjs_group)" name="PDFjs">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.PictureInPicture_group)" name="PictureInPicture">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.ProxySettings_group)" name="ProxySettings">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.SecurityDevices_group)" name="SecurityDevices">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.FirefoxSuggest_group)" name="FirefoxSuggest">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.ContentAnalysis_group)" name="ContentAnalysis">
      <parentCategory ref="firefox"/>
    </category>
    <category displayName="$(string.InterceptionPoints_group)" name="InterceptionPoints">
      <parentCategory ref="ContentAnalysis"/>
    </category>
    <category displayName="$(string.InterceptionPoints_Clipboard_group)" name="InterceptionPoints_Clipboard">
      <parentCategory ref="InterceptionPoints"/>
    </category>
    <category displayName="$(string.InterceptionPoints_DragAndDrop_group)" name="InterceptionPoints_DragAndDrop">
      <parentCategory ref="InterceptionPoints"/>
    </category>
  </categories>
  <policies>
    <policy name="AppAutoUpdate" class="Both" displayName="$(string.AppAutoUpdate)" explainText="$(string.AppAutoUpdate_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="AppAutoUpdate">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF75"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Authentication_SPNEGO" class="Both" displayName="$(string.Authentication_SPNEGO)" key="Software\Policies\Mozilla\Firefox\Authentication\SPNEGO" explainText="$(string.Authentication_SPNEGO_Explain)" presentation="$(presentation.Authentication)">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Authentication" key="Software\Policies\Mozilla\Firefox\Authentication\SPNEGO" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Authentication_Delegated" class="Both" displayName="$(string.Authentication_Delegated)" key="Software\Policies\Mozilla\Firefox\Authentication\Delegated" explainText="$(string.Authentication_Delegated_Explain)" presentation="$(presentation.Authentication)">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Authentication" key="Software\Policies\Mozilla\Firefox\Authentication\Delegated" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Authentication_NTLM" class="Both" displayName="$(string.Authentication_NTLM)" key="Software\Policies\Mozilla\Firefox\Authentication\NTLM" explainText="$(string.Authentication_NTLM_Explain)" presentation="$(presentation.Authentication)">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Authentication" key="Software\Policies\Mozilla\Firefox\Authentication\NTLM" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Authentication_AllowNonFQDN" class="Both" displayName="$(string.Authentication_AllowNonFQDN)" key="Software\Policies\Mozilla\Firefox\Authentication\AllowNonFQDN" explainText="$(string.Authentication_AllowNonFQDN_Explain)" presentation="$(presentation.Authentication_AllowNonFQDN)">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <boolean id="Authentication_AllowNonFQDN_NTLM" key="Software\Policies\Mozilla\Firefox\Authentication\AllowNonFQDN" valueName="NTLM">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="Authentication_AllowNonFQDN_SPNEGO" key="Software\Policies\Mozilla\Firefox\Authentication\AllowNonFQDN" valueName="SPNEGO">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
      </elements>
    </policy>
    <policy name="Authentication_AllowProxies" class="Both" displayName="$(string.Authentication_AllowProxies)" key="Software\Policies\Mozilla\Firefox\Authentication\AllowProxies" explainText="$(string.Authentication_AllowProxies_Explain)" presentation="$(presentation.Authentication_AllowProxies)">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <elements>
        <boolean id="Authentication_AllowProxies_NTLM" key="Software\Policies\Mozilla\Firefox\Authentication\AllowProxies" valueName="NTLM">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="Authentication_AllowProxies_SPNEGO" key="Software\Policies\Mozilla\Firefox\Authentication\AllowProxies" valueName="SPNEGO">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
      </elements>
    </policy>
    <policy name="Authentication_Locked" class="Both" displayName="$(string.Authentication_Locked)" explainText="$(string.Authentication_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Authentication" valueName="Locked">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF71"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Authentication_PrivateBrowsing" class="Both" displayName="$(string.Authentication_PrivateBrowsing)" explainText="$(string.Authentication_PrivateBrowsing_Explain)" key="Software\Policies\Mozilla\Firefox\Authentication" valueName="PrivateBrowsing">
      <parentCategory ref="Authentication"/>
      <supportedOn ref="SUPPORTED_FF77"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="BlockAboutAddons" class="Both" displayName="$(string.BlockAboutAddons)" explainText="$(string.BlockAboutAddons_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="BlockAboutAddons">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="BlockAboutConfig" class="Both" displayName="$(string.BlockAboutConfig)" explainText="$(string.BlockAboutConfig_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="BlockAboutConfig">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="BlockAboutProfiles" class="Both" displayName="$(string.BlockAboutProfiles)" explainText="$(string.BlockAboutProfiles_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="BlockAboutProfiles">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="BlockAboutSupport" class="Both" displayName="$(string.BlockAboutSupport)" explainText="$(string.BlockAboutSupport_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="BlockAboutSupport">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="CaptivePortal" class="Both" displayName="$(string.CaptivePortal)" explainText="$(string.CaptivePortal_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="CaptivePortal">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF67"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Certificates_ImportEnterpriseRoots" class="Both" displayName="$(string.Certificates_ImportEnterpriseRoots)" explainText="$(string.Certificates_ImportEnterpriseRoots_Explain)" key="Software\Policies\Mozilla\Firefox\Certificates" valueName="ImportEnterpriseRoots">
      <parentCategory ref="Certificates"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Certificates_Install" class="Both" displayName="$(string.Certificates_Install)" explainText="$(string.Certificates_Install_Explain)" key="Software\Policies\Mozilla\Certificates" presentation="$(presentation.Certificates_Install)">
      <parentCategory ref="Certificates"/>
      <supportedOn ref="SUPPORTED_FF64"/>
      <elements>
        <list id="Certificates_Install" key="Software\Policies\Mozilla\Firefox\Certificates\Install" valuePrefix="" expandable="true"/>
      </elements>
    </policy>
    <policy name="Cookies_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.Cookies_Allow_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Cookies\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Cookies_AllowSession" class="Both" displayName="$(string.AllowSession)" explainText="$(string.Cookies_AllowSession_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF79"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Cookies\AllowSession" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Cookies_Block" class="Both" displayName="$(string.Block)" explainText="$(string.Cookies_Block_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Cookies\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Cookies_Default" class="Both" displayName="$(string.Cookies_Default)" explainText="$(string.Cookies_Default_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" valueName="Default">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Cookies_AcceptThirdParty" class="Both" displayName="$(string.Cookies_AcceptThirdParty)" explainText="$(string.Cookies_AcceptThirdParty_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" presentation="$(presentation.Cookies_AcceptThirdParty)">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <enum id="Cookies_AcceptThirdParty" valueName="AcceptThirdParty">
          <item displayName="$(string.Cookies_AcceptThirdParty_All)">
            <value>
              <string>always</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_AcceptThirdParty_None)">
            <value>
              <string>never</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_AcceptThirdParty_FromVisited)">
            <value>
              <string>from-visited</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Cookies_ExpireAtSessionEnd" class="Both" displayName="$(string.Cookies_ExpireAtSessionEnd)" explainText="$(string.Cookies_ExpireAtSessionEnd_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" valueName="ExpireAtSessionEnd">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Cookies_RejectTracker" class="Both" displayName="$(string.Cookies_RejectTracker)" explainText="$(string.Cookies_RejectTracker_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" valueName="RejectTracker">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF64"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Cookies_Locked" class="Both" displayName="$(string.Cookies_Locked)" explainText="$(string.Cookies_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" valueName="Locked">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Cookies_Behavior" class="Both" displayName="$(string.Cookies_Behavior)" explainText="$(string.Cookies_Behavior_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" presentation="$(presentation.Cookies_Behavior)">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF95"/>
      <elements>
        <enum id="Cookies_Behavior" valueName="Behavior">
          <item displayName="$(string.Cookies_Behavior_Accept)">
            <value>
              <string>accept</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_RejectForeign)">
            <value>
              <string>reject-foreign</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_Reject)">
            <value>
              <string>reject</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_LimitForeign)">
            <value>
              <string>limit-foreign</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_RejectTracker)">
            <value>
              <string>reject-tracker</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_RejectTrackerAndPartitionForeign)">
            <value>
              <string>reject-tracker-and-partition-foreign</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Cookies_BehaviorPrivateBrowsing" class="Both" displayName="$(string.Cookies_BehaviorPrivateBrowsing)" explainText="$(string.Cookies_BehaviorPrivateBrowsing_Explain)" key="Software\Policies\Mozilla\Firefox\Cookies" presentation="$(presentation.Cookies_BehaviorPrivateBrowsing)">
      <parentCategory ref="Cookies"/>
      <supportedOn ref="SUPPORTED_FF95"/>
      <elements>
        <enum id="Cookies_BehaviorPrivateBrowsing" valueName="BehaviorPrivateBrowsing">
          <item displayName="$(string.Cookies_Behavior_Accept)">
            <value>
              <string>accept</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_RejectForeign)">
            <value>
              <string>reject-foreign</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_Reject)">
            <value>
              <string>reject</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_LimitForeign)">
            <value>
              <string>limit-foreign</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_RejectTracker)">
            <value>
              <string>reject-tracker</string>
            </value>
          </item>
          <item displayName="$(string.Cookies_Behavior_RejectTrackerAndPartitionForeign)">
            <value>
              <string>reject-tracker-and-partition-foreign</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Camera_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.Camera_Allow_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Camera"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Camera\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Camera_Block" class="Both" displayName="$(string.Block)" explainText="$(string.Camera_Block_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Camera"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Camera\Permissions\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Camera_BlockNewRequests" class="Both" displayName="$(string.Camera_BlockNewRequests)" explainText="$(string.Camera_BlockNewRequests_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Camera" valueName="BlockNewRequests">
      <parentCategory ref="Camera"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Camera_Locked" class="Both" displayName="$(string.Camera_Locked)" explainText="$(string.Camera_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Camera" valueName="Locked">
      <parentCategory ref="Camera"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Microphone_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.Microphone_Allow_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Microphone"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Microphone\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Microphone_Block" class="Both" displayName="$(string.Block)" explainText="$(string.Microphone_Block_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Microphone"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Microphone\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Microphone_BlockNewRequests" class="Both" displayName="$(string.Microphone_BlockNewRequests)" explainText="$(string.Microphone_BlockNewRequests_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Microphone" valueName="BlockNewRequests">
      <parentCategory ref="Microphone"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Microphone_Locked" class="Both" displayName="$(string.Microphone_Locked)" explainText="$(string.Microphone_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Microphone" valueName="Locked">
      <parentCategory ref="Microphone"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Location_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.Location_Allow_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Location"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Location\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Location_Block" class="Both" displayName="$(string.Block)" explainText="$(string.Location_Block_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Location"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Location\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Location_BlockNewRequests" class="Both" displayName="$(string.Location_BlockNewRequests)" explainText="$(string.Location_BlockNewRequests_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Location" valueName="BlockNewRequests">
      <parentCategory ref="Location"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Location_Locked" class="Both" displayName="$(string.Location_Locked)" explainText="$(string.Location_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Location" valueName="Locked">
      <parentCategory ref="Location"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Notifications_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.Notifications_Allow_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Notifications"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Notifications\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Notifications_Block" class="Both" displayName="$(string.Block)" explainText="$(string.Notifications_Block_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Notifications"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Notifications\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Notifications_BlockNewRequests" class="Both" displayName="$(string.Notifications_BlockNewRequests)" explainText="$(string.Notifications_BlockNewRequests_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Notifications" valueName="BlockNewRequests">
      <parentCategory ref="Notifications"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Notifications_Locked" class="Both" displayName="$(string.Notifications_Locked)" explainText="$(string.Notifications_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Notifications" valueName="Locked">
      <parentCategory ref="Notifications"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Autoplay_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.Autoplay_Allow_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Autoplay"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Autoplay\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Autoplay_Block" class="Both" displayName="$(string.Block)" explainText="$(string.Autoplay_Block_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="Autoplay"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\Autoplay\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Autoplay_Locked" class="Both" displayName="$(string.Autoplay_Locked)" explainText="$(string.Autoplay_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Autoplay" valueName="Locked">
      <parentCategory ref="Autoplay"/>
      <supportedOn ref="SUPPORTED_FF76"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Autoplay_Default" class="Both" displayName="$(string.Autoplay_Default)" explainText="$(string.Autoplay_Default_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\Autoplay" presentation="$(presentation.Autoplay_Default)">
      <parentCategory ref="Autoplay"/>
      <supportedOn ref="SUPPORTED_FF76"/>
      <elements>
        <enum id="Autoplay_Default" valueName="Default">
          <item displayName="$(string.AllowAudioVideo)">
            <value>
              <string>allow-audio-video</string>
            </value>
          </item>
          <item displayName="$(string.BlockAudio)">
            <value>
              <string>block-audio</string>
            </value>
          </item>
          <item displayName="$(string.BlockAudioVideo)">
            <value>
              <string>block-audio-video</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="VirtualReality_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.VirtualReality_Allow_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="VirtualReality"/>
      <supportedOn ref="SUPPORTED_FF80"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\VirtualReality\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="VirtualReality_Block" class="Both" displayName="$(string.Block)" explainText="$(string.VirtualReality_Block_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions" presentation="$(presentation.Permissions)">
      <parentCategory ref="VirtualReality"/>
      <supportedOn ref="SUPPORTED_FF80"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\Permissions\VirtualReality\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="VirtualReality_BlockNewRequests" class="Both" displayName="$(string.VirtualReality_BlockNewRequests)" explainText="$(string.VirtualReality_BlockNewRequests_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\VirtualReality" valueName="BlockNewRequests">
      <parentCategory ref="VirtualReality"/>
      <supportedOn ref="SUPPORTED_FF80"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="VirtualReality_Locked" class="Both" displayName="$(string.VirtualReality_Locked)" explainText="$(string.VirtualReality_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Permissions\VirtualReality" valueName="Locked">
      <parentCategory ref="VirtualReality"/>
      <supportedOn ref="SUPPORTED_FF80"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DefaultDownloadDirectory" class="Both" displayName="$(string.DefaultDownloadDirectory)" explainText="$(string.DefaultDownloadDirectory_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="DefaultDownloadDirectory" expandable="true"/>
      </elements>
    </policy>
    <policy name="DownloadDirectory" class="Both" displayName="$(string.DownloadDirectory)" explainText="$(string.DownloadDirectory_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="DownloadDirectory" expandable="true"/>
      </elements>
    </policy>
    <policy name="DNSOverHTTPS_Enabled" class="Both" displayName="$(string.DNSOverHTTPS_Enabled)" explainText="$(string.DNSOverHTTPS_Enabled_Explain)" key="Software\Policies\Mozilla\Firefox\DNSOverHTTPS" valueName="Enabled">
      <parentCategory ref="DNSOverHTTPS"/>
      <supportedOn ref="SUPPORTED_FF63ESR"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DNSOverHTTPS_ProviderURL" class="Both" displayName="$(string.DNSOverHTTPS_ProviderURL)" explainText="$(string.DNSOverHTTPS_ProviderURL_Explain)" key="Software\Policies\Mozilla\Firefox\DNSOverHTTPS" presentation="$(presentation.String)">
      <parentCategory ref="DNSOverHTTPS"/>
      <supportedOn ref="SUPPORTED_FF63ESR"/>
      <elements>
        <text id="String" valueName="ProviderURL"/>
      </elements>
    </policy>
    <policy name="DNSOverHTTPS_Locked" class="Both" displayName="$(string.DNSOverHTTPS_Locked)" explainText="$(string.DNSOverHTTPS_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\DNSOverHTTPS" valueName="Locked">
      <parentCategory ref="DNSOverHTTPS"/>
      <supportedOn ref="SUPPORTED_FF63ESR"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DNSOverHTTPS_ExcludedDomains" class="Both" displayName="$(string.DNSOverHTTPS_ExcludedDomains)" explainText="$(string.DNSOverHTTPS_ExcludedDomains_Explain)" key="Software\Policies\Mozilla\Firefox\DNSOverHTTPS" presentation="$(presentation.List)">
      <parentCategory ref="DNSOverHTTPS"/>
      <supportedOn ref="SUPPORTED_FF75"/>
      <elements>
        <list id="List" key="Software\Policies\Mozilla\Firefox\DNSOverHTTPS\ExcludedDomains" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="DNSOverHTTPS_Fallback" class="Both" displayName="$(string.DNSOverHTTPS_Fallback)" explainText="$(string.DNSOverHTTPS_Fallback_Explain)" key="Software\Policies\Mozilla\Firefox\DNSOverHTTPS" valueName="Fallback">
      <parentCategory ref="DNSOverHTTPS"/>
      <supportedOn ref="SUPPORTED_FF124"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableAppUpdate" class="Both" displayName="$(string.DisableAppUpdate)" explainText="$(string.DisableAppUpdate_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableAppUpdate">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableBuiltinPDFViewer" class="Both" displayName="$(string.DisableBuiltinPDFViewer)" explainText="$(string.DisableBuiltinPDFViewer_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableBuiltinPDFViewer">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableDefaultBrowserAgent" class="Both" displayName="$(string.DisableDefaultBrowserAgent)" explainText="$(string.DisableDefaultBrowserAgent_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableDefaultBrowserAgent">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF75"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableDeveloperTools" class="Both" displayName="$(string.DisableDeveloperTools)" explainText="$(string.DisableDeveloperTools_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableDeveloperTools">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableFeedbackCommands" class="Both" displayName="$(string.DisableFeedbackCommands)" explainText="$(string.DisableFeedbackCommands_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableFeedbackCommands">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableFirefoxAccounts" class="Both" displayName="$(string.DisableFirefoxAccounts)" explainText="$(string.DisableFirefoxAccounts_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableFirefoxAccounts">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableFirefoxScreenshots" class="Both" displayName="$(string.DisableFirefoxScreenshots)" explainText="$(string.DisableFirefoxScreenshots_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableFirefoxScreenshots">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableFirefoxStudies" class="Both" displayName="$(string.DisableFirefoxStudies)" explainText="$(string.DisableFirefoxStudies_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableFirefoxStudies">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableForgetButton" class="Both" displayName="$(string.DisableForgetButton)" explainText="$(string.DisableForgetButton_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableForgetButton">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableFormHistory" class="Both" displayName="$(string.DisableFormHistory)" explainText="$(string.DisableFormHistory_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableFormHistory">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="HardwareAcceleration" class="Both" displayName="$(string.HardwareAcceleration)" explainText="$(string.HardwareAcceleration_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="HardwareAcceleration">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableMasterPasswordCreation" class="Both" displayName="$(string.DisableMasterPasswordCreation)" explainText="$(string.DisableMasterPasswordCreation_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableMasterPasswordCreation">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisablePasswordReveal" class="Both" displayName="$(string.DisablePasswordReveal)" explainText="$(string.DisablePasswordReveal_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisablePasswordReveal">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF71"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisablePocket" class="Both" displayName="$(string.DisablePocket)" explainText="$(string.DisablePocket_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisablePocket">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisablePrivateBrowsing" class="Both" displayName="$(string.DisablePrivateBrowsing)" explainText="$(string.DisablePrivateBrowsing_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisablePrivateBrowsing">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableProfileImport" class="Both" displayName="$(string.DisableProfileImport)" explainText="$(string.DisableProfileImport_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableProfileImport">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableProfileRefresh" class="Both" displayName="$(string.DisableProfileRefresh)" explainText="$(string.DisableProfileRefresh_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableProfileRefresh">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableSafeMode" class="Both" displayName="$(string.DisableSafeMode)" explainText="$(string.DisableSafeMode_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableSafeMode">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableSetDesktopBackground" class="Both" displayName="$(string.DisableSetDesktopBackground)" explainText="$(string.DisableSetDesktopBackground_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableSetDesktopBackground">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableSystemAddonUpdate" class="Both" displayName="$(string.DisableSystemAddonUpdate)" explainText="$(string.DisableSystemAddonUpdate_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableSystemAddonUpdate">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableTelemetry" class="Both" displayName="$(string.DisableTelemetry)" explainText="$(string.DisableTelemetry_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableTelemetry">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisplayBookmarksToolbar" class="Both" displayName="$(string.DisplayBookmarksToolbar)" explainText="$(string.DisplayBookmarksToolbar_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisplayBookmarksToolbar">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisplayBookmarksToolbar_Enum" class="Both" displayName="$(string.DisplayBookmarksToolbar_Enum)" explainText="$(string.DisplayBookmarksToolbar_Enum_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.DisplayBookmarksToolbar)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF109"/>
        <elements>
        <enum id="DisplayBookmarksToolbar" valueName="DisplayBookmarksToolbar">
          <item displayName="$(string.DisplayBookmarksToolbar_Always)">
            <value>
              <string>always</string>
            </value>
          </item>
          <item displayName="$(string.DisplayBookmarksToolbar_Never)">
            <value>
              <string>never</string>
            </value>
          </item>
          <item displayName="$(string.DisplayBookmarksToolbar_NewTab)">
            <value>
              <string>newtab</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="DisplayMenuBar" class="Both" displayName="$(string.DisplayMenuBar)" explainText="$(string.DisplayMenuBar_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisplayMenuBar">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisplayMenuBar_Enum" class="Both" displayName="$(string.DisplayMenuBar_Enum)" explainText="$(string.DisplayMenuBar_Enum_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.DisplayMenuBar)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF73"/>
        <elements>
        <enum id="DisplayMenuBar" valueName="DisplayMenuBar">
          <item displayName="$(string.DisplayMenuBar_Always)">
            <value>
              <string>always</string>
            </value>
          </item>
          <item displayName="$(string.DisplayMenuBar_Never)">
            <value>
              <string>never</string>
            </value>
          </item>
          <item displayName="$(string.DisplayMenuBar_Default_On)">
            <value>
              <string>default-on</string>
            </value>
          </item>
          <item displayName="$(string.DisplayMenuBar_Default_Off)">
            <value>
              <string>default-off</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Extensions_Install" class="Both" displayName="$(string.Extensions_Install)" key="Software\Policies\Mozilla\Firefox\Extensions\Install" explainText="$(string.Extensions_Install_Explain)" presentation="$(presentation.Extensions)">
      <parentCategory ref="Extensions"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Extensions" key="Software\Policies\Mozilla\Firefox\Extensions\Install" valuePrefix="" expandable="true"/>
      </elements>
    </policy>
    <policy name="Extensions_Uninstall" class="Both" displayName="$(string.Extensions_Uninstall)" key="Software\Policies\Mozilla\Firefox\Extensions\Uninstall" explainText="$(string.Extensions_Uninstall_Explain)" presentation="$(presentation.Extensions)">
      <parentCategory ref="Extensions"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Extensions" key="Software\Policies\Mozilla\Firefox\Extensions\Uninstall" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Extensions_Locked" class="Both" displayName="$(string.Extensions_Locked)" key="Software\Policies\Mozilla\Firefox\Extensions\Locked" explainText="$(string.Extensions_Locked_Explain)" presentation="$(presentation.Extensions)">
      <parentCategory ref="Extensions"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Extensions" key="Software\Policies\Mozilla\Firefox\Extensions\Locked" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="ExtensionSettings" class="Both" displayName="$(string.ExtensionSettings)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.ExtensionSettings_Explain)" presentation="$(presentation.ExtensionSettings)">
      <parentCategory ref="Extensions"/>
      <supportedOn ref="SUPPORTED_FF69"/>
      <elements>
        <multiText id="ExtensionSettings" valueName="ExtensionSettings" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="ExtensionSettingsOneLine" class="Both" displayName="$(string.ExtensionSettingsOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.ExtensionSettings_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="Extensions"/>
      <supportedOn ref="SUPPORTED_FF69"/>
      <elements>
        <text id="JSONOneLine" valueName="ExtensionSettings" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="NoDefaultBookmarks" class="Both" displayName="$(string.NoDefaultBookmarks)" explainText="$(string.NoDefaultBookmarks_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="NoDefaultBookmarks">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DontCheckDefaultBrowser" class="Both" displayName="$(string.DontCheckDefaultBrowser)" explainText="$(string.DontCheckDefaultBrowser_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DontCheckDefaultBrowser">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ExtensionUpdate" class="Both" displayName="$(string.ExtensionUpdate)" explainText="$(string.ExtensionUpdate_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="ExtensionUpdate">
      <parentCategory ref="Extensions"/>
      <supportedOn ref="SUPPORTED_FF67"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="LegacyProfiles" class="Both" displayName="$(string.LegacyProfiles)" explainText="$(string.LegacyProfiles_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="LegacyProfiles">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="LegacySameSiteCookieBehaviorEnabled" class="Both" displayName="$(string.LegacySameSiteCookieBehaviorEnabled)" explainText="$(string.LegacySameSiteCookieBehaviorEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="LegacySameSiteCookieBehaviorEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF96_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="LegacySameSiteCookieBehaviorEnabledForDomainList" class="Both" displayName="$(string.LegacySameSiteCookieBehaviorEnabledForDomainList)" explainText="$(string.LegacySameSiteCookieBehaviorEnabledForDomainList_Explain)" key="Software\Policies\Mozilla\Firefox\LegacySameSiteCookieBehaviorEnabledForDomainList" presentation="$(presentation.LegacySameSiteCookieBehaviorEnabledForDomainList)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF96_ONLY"/>
      <elements>
        <list id="LegacySameSiteCookieBehaviorEnabledForDomainList" key="Software\Policies\Mozilla\Firefox\LegacySameSiteCookieBehaviorEnabledForDomainList" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="LocalFileLinks" class="Both" displayName="$(string.LocalFileLinks)" explainText="$(string.LocalFileLinks_Explain)" key="Software\Policies\Mozilla\Firefox\LocalFileLinks" presentation="$(presentation.LocalFileLinks)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68ESR"/>
      <elements>
        <list id="LocalFileLinks" key="Software\Policies\Mozilla\Firefox\LocalFileLinks" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="NetworkPrediction" class="Both" displayName="$(string.NetworkPrediction)" explainText="$(string.NetworkPrediction_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="NetworkPrediction">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF67"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="NewTabPage" class="Both" displayName="$(string.NewTabPage)" explainText="$(string.NewTabPage_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="NewTabPage">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="OfferToSaveLogins" class="Both" displayName="$(string.OfferToSaveLogins)" explainText="$(string.OfferToSaveLogins_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="OfferToSaveLogins">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="OfferToSaveLoginsDefault" class="Both" displayName="$(string.OfferToSaveLoginsDefault)" explainText="$(string.OfferToSaveLoginsDefault_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="OfferToSaveLoginsDefault">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PopupBlocking_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.PopupBlocking_Allow_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Popups"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\PopupBlocking\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="PopupBlocking_Default" class="Both" displayName="$(string.PopupBlocking_Default)" explainText="$(string.PopupBlocking_Default_Explain)" key="Software\Policies\Mozilla\Firefox\PopupBlocking" valueName="Default">
      <parentCategory ref="Popups"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PopupBlocking_Locked" class="Both" displayName="$(string.PopupBlocking_Locked)" explainText="$(string.PopupBlocking_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\PopupBlocking" valueName="Locked">
      <parentCategory ref="Popups"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="InstallAddonsPermission_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.InstallAddonsPermission_Allow_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Addons"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\InstallAddonsPermission\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="InstallAddonsPermission_Default" class="Both" displayName="$(string.InstallAddonsPermission_Default)" explainText="$(string.InstallAddonsPermission_Default_Explain)" key="Software\Policies\Mozilla\Firefox\InstallAddonsPermission" valueName="Default">
      <parentCategory ref="Addons"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="CustomizeFirefoxHome" class="Both" displayName="$(string.FirefoxHome)" explainText="$(string.FirefoxHome_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.FirefoxHome)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <boolean id="FirefoxHome_Search" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="Search">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_TopSites" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="TopSites">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_SponsoredTopSites" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="SponsoredTopSites">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_Highlights" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="Highlights">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_Pocket" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="Pocket">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_SponsoredPocket" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="SponsoredPocket">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_Snippets" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="Snippets">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
        <boolean id="FirefoxHome_Locked" key="Software\Policies\Mozilla\Firefox\FirefoxHome" valueName="Locked">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
      </elements>
    </policy>
    <policy name="FlashPlugin_Allow" class="Both" displayName="$(string.Allow)" explainText="$(string.FlashPlugin_Allow_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Flash"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\FlashPlugin\Allow" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="FlashPlugin_Block" class="Both" displayName="$(string.Block)" explainText="$(string.FlashPlugin_Block_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Permissions)">
      <parentCategory ref="Flash"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="Permissions" key="Software\Policies\Mozilla\Firefox\FlashPlugin\Block" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="FlashPlugin_Default" class="Both" displayName="$(string.FlashPlugin_Default)" explainText="$(string.FlashPlugin_Default_Explain)" key="Software\Policies\Mozilla\Firefox\FlashPlugin" valueName="Default">
      <parentCategory ref="Flash"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="FlashPlugin_Locked" class="Both" displayName="$(string.FlashPlugin_Locked)" explainText="$(string.FlashPlugin_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\FlashPlugin" valueName="Locked">
      <parentCategory ref="Flash"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="OverrideFirstRunPage" class="Both" displayName="$(string.OverrideFirstRunPage)" explainText="$(string.OverrideFirstRunPage_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.OverridePage)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="OverridePage" valueName="OverrideFirstRunPage"/>
      </elements>
    </policy>
    <policy name="OverridePostUpdatePage" class="Both" displayName="$(string.OverridePostUpdatePage)" explainText="$(string.OverridePostUpdatePage_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.OverridePage)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="OverridePage" valueName="OverridePostUpdatePage"/>
      </elements>
    </policy>
    <policy name="PromptForDownloadLocation" class="Both" displayName="$(string.PromptForDownloadLocation)" explainText="$(string.PromptForDownloadLocation_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="PromptForDownloadLocation">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <!-- Alphabetization is based on name, so had to add P -->
    <policy name="P_DisableSecurityBypass_InvalidCertificate" class="Both" displayName="$(string.DisableSecurityBypass_InvalidCertificate)" explainText="$(string.DisableSecurityBypass_InvalidCertificate_Explain)" key="Software\Policies\Mozilla\Firefox\DisableSecurityBypass" valueName="InvalidCertificate">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <!-- Alphabetization is based on name, so had to add P -->
    <policy name="P_DisableSecurityBypass_SafeBrowsing" class="Both" displayName="$(string.DisableSecurityBypass_SafeBrowsing)" explainText="$(string.DisableSecurityBypass_SafeBrowsing_Explain)" key="Software\Policies\Mozilla\Firefox\DisableSecurityBypass" valueName="SafeBrowsing">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="HomepageURL" class="Both" displayName="$(string.HomepageURL)" explainText="$(string.HomepageURL_Explain)" key="Software\Policies\Mozilla\Firefox\Homepage" presentation="$(presentation.HomepageURL)">
      <parentCategory ref="Homepage"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="HomepageURL" valueName="URL" required="true"/>
        <boolean id="HomepageLocked" key="Software\Policies\Mozilla\Firefox\Homepage" valueName="Locked">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
      </elements>
    </policy>
    <policy name="HomepageAdditional" class="Both" displayName="$(string.HomepageAdditional)" explainText="$(string.HomepageAdditional_Explain)" key="Software\Policies\Mozilla\Firefox\Homepage" presentation="$(presentation.HomepageAdditional)">
      <parentCategory ref="Homepage"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="HomepageAdditional" key="Software\Policies\Mozilla\Firefox\Homepage\Additional" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="HomepageStartPage" class="Both" displayName="$(string.HomepageStartPage)" explainText="$(string.HomepageStartPage_Explain)" key="Software\Policies\Mozilla\Firefox\Homepage" presentation="$(presentation.StartPage)">
      <parentCategory ref="Homepage"/>
      <supportedOn ref="SUPPORTED_FF64"/>
      <elements>
        <enum id="StartPage" valueName="StartPage">
          <item displayName="$(string.None)">
            <value>
              <string>none</string>
            </value>
          </item>
          <item displayName="$(string.Homepage)">
            <value>
              <string>homepage</string>
            </value>
          </item>
          <item displayName="$(string.PreviousSession)">
            <value>
              <string>previous-session</string>
            </value>
          </item>
          <item displayName="$(string.HomepageLocked)">
            <value>
              <string>homepage-locked</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Homepage_ShowHomeButton" class="Both" displayName="$(string.Homepage_ShowHomeButton)" explainText="$(string.Homepage_ShowHomeButton_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="ShowHomeButton">
      <parentCategory ref="Homepage"/>
      <supportedOn ref="SUPPORTED_FF88"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="A_BookmarksOneLine" class="Both" displayName="$(string.BookmarksOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Bookmarks_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF95"/>
      <elements>
        <text id="JSONOneLine" valueName="Bookmarks" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="A_Bookmarks" class="Both" displayName="$(string.Bookmarks)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Bookmarks_Explain)" presentation="$(presentation.JSON)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF95"/>
      <elements>
        <multiText id="JSON" valueName="Bookmarks" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="Bookmark01" class="Both" displayName="$(string.Bookmark01)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\1" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Bookmark02" class="Both" displayName="$(string.Bookmark02)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\10" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Bookmark03" class="Both" displayName="$(string.Bookmark03)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\11" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Bookmark04" class="Both" displayName="$(string.Bookmark04)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\12" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Bookmark05" class="Both" displayName="$(string.Bookmark05)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\13" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark06" class="Both" displayName="$(string.Bookmark06)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\14" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark07" class="Both" displayName="$(string.Bookmark07)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\15" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark08" class="Both" displayName="$(string.Bookmark08)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\16" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark09" class="Both" displayName="$(string.Bookmark09)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\17" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark10" class="Both" displayName="$(string.Bookmark10)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\18" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark11" class="Both" displayName="$(string.Bookmark11)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\19" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark12" class="Both" displayName="$(string.Bookmark12)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\2" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark13" class="Both" displayName="$(string.Bookmark13)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\20" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark14" class="Both" displayName="$(string.Bookmark14)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\21" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark15" class="Both" displayName="$(string.Bookmark15)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\22" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark16" class="Both" displayName="$(string.Bookmark16)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\23" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark17" class="Both" displayName="$(string.Bookmark17)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\24" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark18" class="Both" displayName="$(string.Bookmark18)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\25" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark19" class="Both" displayName="$(string.Bookmark19)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\26" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark20" class="Both" displayName="$(string.Bookmark20)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\27" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark21" class="Both" displayName="$(string.Bookmark21)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\28" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark22" class="Both" displayName="$(string.Bookmark22)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\29" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark23" class="Both" displayName="$(string.Bookmark23)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\3" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark24" class="Both" displayName="$(string.Bookmark24)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\30" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark25" class="Both" displayName="$(string.Bookmark25)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\31" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark26" class="Both" displayName="$(string.Bookmark26)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\32" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark27" class="Both" displayName="$(string.Bookmark27)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\33" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark28" class="Both" displayName="$(string.Bookmark28)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\34" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark29" class="Both" displayName="$(string.Bookmark29)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\35" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark30" class="Both" displayName="$(string.Bookmark30)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\36" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark31" class="Both" displayName="$(string.Bookmark31)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\37" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark32" class="Both" displayName="$(string.Bookmark32)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\38" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark33" class="Both" displayName="$(string.Bookmark33)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\39" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark34" class="Both" displayName="$(string.Bookmark34)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\4" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark35" class="Both" displayName="$(string.Bookmark35)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\40" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark36" class="Both" displayName="$(string.Bookmark36)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\41" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark37" class="Both" displayName="$(string.Bookmark37)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\42" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark38" class="Both" displayName="$(string.Bookmark38)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\43" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark39" class="Both" displayName="$(string.Bookmark39)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\44" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark40" class="Both" displayName="$(string.Bookmark40)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\45" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark41" class="Both" displayName="$(string.Bookmark41)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\46" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark42" class="Both" displayName="$(string.Bookmark42)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\47" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark43" class="Both" displayName="$(string.Bookmark43)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\48" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark44" class="Both" displayName="$(string.Bookmark44)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\49" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark45" class="Both" displayName="$(string.Bookmark45)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\5" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark46" class="Both" displayName="$(string.Bookmark46)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\50" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark47" class="Both" displayName="$(string.Bookmark47)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\6" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark48" class="Both" displayName="$(string.Bookmark48)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\7" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark49" class="Both" displayName="$(string.Bookmark49)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\8" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
	<policy name="Bookmark50" class="Both" displayName="$(string.Bookmark50)" explainText="$(string.Bookmark_Explain)" key="Software\Policies\Mozilla\Firefox\Bookmarks\9" presentation="$(presentation.Bookmark)">
      <parentCategory ref="Bookmarks"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="BookmarkTitle" valueName="Title" required="true"/>
        <text id="BookmarkURL" valueName="URL" required="true"/>
        <text id="BookmarkFolder" valueName="Folder"/>
        <text id="BookmarkFavicon" valueName="Favicon"/>
        <enum id="BookmarkPlacement" valueName="Placement">
          <item displayName="$(string.BookmarkPlacementToolbar)">
            <value>
              <string>toolbar</string>
            </value>
          </item>
          <item displayName="$(string.BookmarkPlacementMenu)">
            <value>
              <string>menu</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="PasswordManagerEnabled" class="Both" displayName="$(string.PasswordManagerEnabled)" explainText="$(string.PasswordManagerEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="PasswordManagerEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PasswordManagerExceptions" class="Both" displayName="$(string.PasswordManagerExceptions)" explainText="$(string.PasswordManagerExceptions_Explain)" key="Software\Policies\Mozilla\Firefox\PasswordManagerExceptions" presentation="$(presentation.List)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF101"/>
      <elements>
        <list id="List" key="Software\Policies\Mozilla\Firefox\PasswordManagerExceptions" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="Proxy_Locked" class="Both" displayName="$(string.Proxy_Locked)" explainText="$(string.Proxy_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" valueName="Locked">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Proxy_ConnectionType" class="Both" displayName="$(string.Proxy_ConnectionType)" explainText="$(string.Proxy_ConnectionType_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" presentation="$(presentation.Proxy_ConnectionType)">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <enum id="Proxy_ConnectionType" valueName="Mode">
          <item displayName="$(string.NoProxy)">
            <value>
              <string>none</string>
            </value>
          </item>
          <item displayName="$(string.SystemProxy)">
            <value>
              <string>system</string>
            </value>
          </item>
          <item displayName="$(string.ManualProxy)">
            <value>
              <string>manual</string>
            </value>
          </item>
          <item displayName="$(string.AutoDetectProxy)">
            <value>
              <string>autoDetect</string>
            </value>
          </item>
          <item displayName="$(string.AutoConfigProxy)">
            <value>
              <string>autoConfig</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Proxy_HTTPProxy" class="Both" displayName="$(string.Proxy_HTTPProxy)" explainText="$(string.Proxy_HTTPProxy_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" presentation="$(presentation.Proxy_HTTPProxy)">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="Proxy_HTTPProxy" valueName="HTTPProxy"/>
      </elements>
    </policy>
    <policy name="Proxy_UseHTTPProxyForAllProtocols" class="Both" displayName="$(string.Proxy_UseHTTPProxyForAllProtocols)" explainText="$(string.Proxy_UseHTTPProxyForAllProtocols_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" valueName="UseHTTPProxyForAllProtocols">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Proxy_SSLProxy" class="Both" displayName="$(string.Proxy_SSLProxy)" explainText="$(string.Proxy_SSLProxy_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" presentation="$(presentation.Proxy_SSLProxy)">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="Proxy_SSLProxy" valueName="SSLProxy"/>
      </elements>
    </policy>
    <policy name="Proxy_SOCKSProxy" class="Both" displayName="$(string.Proxy_SOCKSProxy)" explainText="$(string.Proxy_SOCKSProxy_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" presentation="$(presentation.Proxy_SOCKSProxy)">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="Proxy_SOCKSProxy" valueName="SOCKSProxy"/>
        <enum id="Proxy_SOCKSVersion" valueName="SOCKSVersion">
          <item displayName="$(string.SOCKSVersion4)">
            <value>
              <decimal value="4"/>
            </value>
          </item>
          <item displayName="$(string.SOCKSVersion5)">
            <value>
              <decimal value="5"/>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="Proxy_AutoConfigURL" class="Both" displayName="$(string.Proxy_AutoConfigURL)" explainText="$(string.Proxy_AutoConfigURL_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" presentation="$(presentation.Proxy_AutoConfigURL)">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="Proxy_AutoConfigURL" valueName="AutoConfigURL"/>
      </elements>
    </policy>
    <policy name="Proxy_Passthrough" class="Both" displayName="$(string.Proxy_Passthrough)" explainText="$(string.Proxy_Passthrough_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" presentation="$(presentation.Proxy_Passthrough)">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <text id="Proxy_Passthrough" valueName="Passthrough"/>
      </elements>
    </policy>
    <policy name="Proxy_AutoLogin" class="Both" displayName="$(string.Proxy_AutoLogin)" explainText="$(string.Proxy_AutoLogin_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" valueName="AutoLogin">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="Proxy_UseProxyForDNS" class="Both" displayName="$(string.Proxy_UseProxyForDNS)" explainText="$(string.Proxy_UseProxyForDNS_Explain)" key="Software\Policies\Mozilla\Firefox\Proxy" valueName="UseProxyForDNS">
      <parentCategory ref="ProxySettings"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="SearchBar" class="Both" displayName="$(string.SearchBar)" explainText="$(string.SearchBar_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.SearchBar)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <enum id="SearchBar" valueName="SearchBar">
          <item displayName="$(string.SearchBar_Unified)">
            <value>
              <string>unified</string>
            </value>
          </item>
          <item displayName="$(string.SearchBar_Separate)">
            <value>
              <string>separate</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="SearchSuggestEnabled" class="Both" displayName="$(string.SearchSuggestEnabled)" explainText="$(string.SearchSuggestEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="SearchSuggestEnabled">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <!-- Alphabetization is based on name, so had to add B -->
    <policy name="B_WebsiteFilter_Block" class="Both" displayName="$(string.WebsiteFilter_Block)" explainText="$(string.WebsiteFilter_Block_Explain)" key="Software\Policies\Mozilla\WebsiteFilter\Block" presentation="$(presentation.WebsiteFilter)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="WebsiteFilter" key="Software\Policies\Mozilla\Firefox\WebsiteFilter\Block" valuePrefix=""/>
      </elements>
    </policy>
    <!-- Alphabetization is based on name, so had to add B -->
    <policy name="B_WebsiteFilter_Exceptions" class="Both" displayName="$(string.WebsiteFilter_Exceptions)" explainText="$(string.WebsiteFilter_Exceptions_Explain)" key="Software\Policies\Mozilla\WebsiteFilter\Exceptions" presentation="$(presentation.WebsiteFilter)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF60"/>
      <elements>
        <list id="WebsiteFilter" key="Software\Policies\Mozilla\Firefox\WebsiteFilter\Exceptions" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="WebsiteFilterOneLine" class="Both" displayName="$(string.WebsiteFilterOneLine)" explainText="$(string.WebsiteFilter_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF114"/>
      <elements>
        <text id="JSONOneLine" valueName="WebsiteFilter" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="WebsiteFilter" class="Both" displayName="$(string.WebsiteFilter)" explainText="$(string.WebsiteFilter_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.JSON)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF114"/>
      <elements>
        <multiText id="JSON" valueName="WebsiteFilter" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="SearchEngines_1" class="Both" displayName="$(string.SearchEngines_1)" explainText="$(string.SearchEngines_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines\Add\1" presentation="$(presentation.SearchEngine)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <text id="SearchEngine_Name" valueName="Name" required="true"/>
        <text id="SearchEngine_URLTemplate" valueName="URLTemplate" required="true"/>
        <enum id="SearchEngine_Method" valueName="Method">
          <item displayName="$(string.SearchEngine_Method_GET)">
            <value>
              <string>GET</string>
            </value>
          </item>
          <item displayName="$(string.SearchEngine_Method_POST)">
            <value>
              <string>POST</string>
            </value>
          </item>
        </enum>
        <text id="SearchEngine_IconURL" valueName="IconURL"/>
        <text id="SearchEngine_Alias" valueName="Alias"/>
        <text id="SearchEngine_Description" valueName="Description"/>
        <text id="SearchEngine_SuggestURLTemplate" valueName="SuggestURLTemplate"/>
        <text id="SearchEngine_PostData" valueName="PostData"/>
        <text id="SearchEngine_Encoding" valueName="Encoding"/>
      </elements>
    </policy>
    <policy name="SearchEngines_2" class="Both" displayName="$(string.SearchEngines_2)" explainText="$(string.SearchEngines_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines\Add\2" presentation="$(presentation.SearchEngine)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <text id="SearchEngine_Name" valueName="Name" required="true"/>
        <text id="SearchEngine_URLTemplate" valueName="URLTemplate" required="true"/>
        <enum id="SearchEngine_Method" valueName="Method">
          <item displayName="$(string.SearchEngine_Method_GET)">
            <value>
              <string>GET</string>
            </value>
          </item>
          <item displayName="$(string.SearchEngine_Method_POST)">
            <value>
              <string>POST</string>
            </value>
          </item>
        </enum>
        <text id="SearchEngine_IconURL" valueName="IconURL"/>
        <text id="SearchEngine_Alias" valueName="Alias"/>
        <text id="SearchEngine_Description" valueName="Description"/>
        <text id="SearchEngine_SuggestURLTemplate" valueName="SuggestURLTemplate"/>
        <text id="SearchEngine_PostData" valueName="PostData"/>
        <text id="SearchEngine_Encoding" valueName="Encoding"/>
      </elements>
    </policy>
    <policy name="SearchEngines_3" class="Both" displayName="$(string.SearchEngines_3)" explainText="$(string.SearchEngines_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines\Add\3" presentation="$(presentation.SearchEngine)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <text id="SearchEngine_Name" valueName="Name" required="true"/>
        <text id="SearchEngine_URLTemplate" valueName="URLTemplate" required="true"/>
        <enum id="SearchEngine_Method" valueName="Method">
          <item displayName="$(string.SearchEngine_Method_GET)">
            <value>
              <string>GET</string>
            </value>
          </item>
          <item displayName="$(string.SearchEngine_Method_POST)">
            <value>
              <string>POST</string>
            </value>
          </item>
        </enum>
        <text id="SearchEngine_IconURL" valueName="IconURL"/>
        <text id="SearchEngine_Alias" valueName="Alias"/>
        <text id="SearchEngine_Description" valueName="Description"/>
        <text id="SearchEngine_SuggestURLTemplate" valueName="SuggestURLTemplate"/>
        <text id="SearchEngine_PostData" valueName="PostData"/>
        <text id="SearchEngine_Encoding" valueName="Encoding"/>
      </elements>
    </policy>
    <policy name="SearchEngines_4" class="Both" displayName="$(string.SearchEngines_4)" explainText="$(string.SearchEngines_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines\Add\4" presentation="$(presentation.SearchEngine)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <text id="SearchEngine_Name" valueName="Name" required="true"/>
        <text id="SearchEngine_URLTemplate" valueName="URLTemplate" required="true"/>
        <enum id="SearchEngine_Method" valueName="Method">
          <item displayName="$(string.SearchEngine_Method_GET)">
            <value>
              <string>GET</string>
            </value>
          </item>
          <item displayName="$(string.SearchEngine_Method_POST)">
            <value>
              <string>POST</string>
            </value>
          </item>
        </enum>
        <text id="SearchEngine_IconURL" valueName="IconURL"/>
        <text id="SearchEngine_Alias" valueName="Alias"/>
        <text id="SearchEngine_Description" valueName="Description"/>
        <text id="SearchEngine_SuggestURLTemplate" valueName="SuggestURLTemplate"/>
        <text id="SearchEngine_PostData" valueName="PostData"/>
        <text id="SearchEngine_Encoding" valueName="Encoding"/>
      </elements>
    </policy>
    <policy name="SearchEngines_5" class="Both" displayName="$(string.SearchEngines_5)" explainText="$(string.SearchEngines_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines\Add\5" presentation="$(presentation.SearchEngine)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <text id="SearchEngine_Name" valueName="Name" required="true"/>
        <text id="SearchEngine_URLTemplate" valueName="URLTemplate" required="true"/>
        <enum id="SearchEngine_Method" valueName="Method">
          <item displayName="$(string.SearchEngine_Method_GET)">
            <value>
              <string>GET</string>
            </value>
          </item>
          <item displayName="$(string.SearchEngine_Method_POST)">
            <value>
              <string>POST</string>
            </value>
          </item>
        </enum>
        <text id="SearchEngine_IconURL" valueName="IconURL"/>
        <text id="SearchEngine_Alias" valueName="Alias"/>
        <text id="SearchEngine_Description" valueName="Description"/>
        <text id="SearchEngine_SuggestURLTemplate" valueName="SuggestURLTemplate"/>
        <text id="SearchEngine_PostData" valueName="PostData"/>
        <text id="SearchEngine_Encoding" valueName="Encoding"/>
      </elements>
    </policy>
    <policy name="SearchEngines_Default" class="Both" displayName="$(string.SearchEngines_Default)" explainText="$(string.SearchEngines_Default_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines" presentation="$(presentation.SearchEngines_Default)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <text id="SearchEngines_Default" valueName="Default" required="true"/>
      </elements>
    </policy>
    <policy name="SearchEngines_PreventInstalls" class="Both" displayName="$(string.SearchEngines_PreventInstalls)" explainText="$(string.SearchEngines_PreventInstalls_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines" valueName="PreventInstalls">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="SearchEngines_Remove" class="Both" displayName="$(string.SearchEngines_Remove)" explainText="$(string.SearchEngines_Remove_Explain)" key="Software\Policies\Mozilla\Firefox\SearchEngines\Remove" presentation="$(presentation.SearchEngines_Remove)">
      <parentCategory ref="Search"/>
      <supportedOn ref="SUPPORTED_FF60ESR"/>
      <elements>
        <list id="SearchEngines_Remove" key="Software\Policies\Mozilla\Firefox\SearchEngines\Remove" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="AppUpdateURL" class="Both" displayName="$(string.AppUpdateURL)" explainText="$(string.AppUpdateURL_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.AppUpdateURL)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF62"/>
      <elements>
        <text id="AppUpdateURL" valueName="AppUpdateURL" required="true"/>
      </elements>
    </policy>
    <policy name="SecurityDevices" class="Both" displayName="$(string.SecurityDevices)" explainText="$(string.SecurityDevices_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.SecurityDevices)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF64"/>
      <elements>
        <list id="SecurityDevices" key="Software\Policies\Mozilla\Firefox\SecurityDevices" valuePrefix="" explicitValue="true" expandable="true"/>
      </elements>
    </policy>
    <policy name="RequestedLocales" class="Both" displayName="$(string.RequestedLocales)" explainText="$(string.RequestedLocales_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.RequestedLocales)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF64"/>
      <elements>
        <list id="RequestedLocales" key="Software\Policies\Mozilla\Firefox\RequestedLocales" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="RequestedLocalesString" class="Both" displayName="$(string.RequestedLocalesString)" explainText="$(string.RequestedLocales_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="RequestedLocales"/>
      </elements>
    </policy>
    <policy name="SSLVersionMin" class="Both" displayName="$(string.SSLVersionMin)" explainText="$(string.SSLVersionMin_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.SSLVersionMin)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF66"/>
      <elements>
        <enum id="SSLVersion" valueName="SSLVersionMin">
          <item displayName="$(string.TLS1)">
            <value>
              <string>tls1</string>
            </value>
          </item>
          <item displayName="$(string.TLS1_1)">
            <value>
              <string>tls1.1</string>
            </value>
          </item>
          <item displayName="$(string.TLS1_2)">
            <value>
              <string>tls1.2</string>
            </value>
          </item>
          <item displayName="$(string.TLS1_3)">
            <value>
              <string>tls1.3</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="SSLVersionMax" class="Both" displayName="$(string.SSLVersionMax)" explainText="$(string.SSLVersionMax_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.SSLVersionMax)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF66"/>
      <elements>
        <enum id="SSLVersion" valueName="SSLVersionMax">
          <item displayName="$(string.TLS1)">
            <value>
              <string>tls1</string>
            </value>
          </item>
          <item displayName="$(string.TLS1_1)">
            <value>
              <string>tls1.1</string>
            </value>
          </item>
          <item displayName="$(string.TLS1_2)">
            <value>
              <string>tls1.2</string>
            </value>
          </item>
          <item displayName="$(string.TLS1_3)">
            <value>
              <string>tls1.3</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="SupportMenu" class="Both" displayName="$(string.SupportMenu)" explainText="$(string.SupportMenu_Explain)" key="Software\Policies\Mozilla\Firefox\SupportMenu" presentation="$(presentation.SupportMenu)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF6801"/>
      <elements>
        <text id="SupportMenuTitle" valueName="Title" required="true"/>
        <text id="SupportMenuURL" valueName="URL" required="true"/>
        <text id="SupportMenuAccessKey" valueName="AccessKey"/>
      </elements>
    </policy>
    <policy name="accessibility_force_disabled" class="Both" displayName="$(string.accessibility_force_disabled)" explainText="$(string.Preferences_Enum_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_accessibility_force_disabled)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <elements>
        <enum id="Preferences_accessibility_force_disabled" valueName="accessibility.force_disabled">
          <item displayName="$(string.Preferences_accessibility_force_disabled_auto)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.Preferences_accessibility_force_disabled_off)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="app_update_auto" class="Both" displayName="$(string.app_update_auto)" explainText="$(string.Preferences_Unsupported_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="app.update.auto">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="UNSUPPORTED"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_bookmarks_autoExportHTML" class="Both" displayName="$(string.browser_bookmarks_autoExportHTML)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.bookmarks.autoExportHTML">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_bookmarks_file" class="Both" displayName="$(string.browser_bookmarks_file)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <elements>
        <text id="Preferences_String" valueName="browser.bookmarks.file" required="true" expandable="true"/>
      </elements>
    </policy>
    <policy name="browser_bookmarks_restore_default_bookmarks" class="Both" displayName="$(string.browser_bookmarks_restore_default_bookmarks)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.bookmarks.restore_default_bookmarks">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_cache_disk_enable" class="Both" displayName="$(string.browser_cache_disk_enable)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.cache.disk.enable">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_fixup_dns_first_for_single_words" class="Both" displayName="$(string.browser_fixup_dns_first_for_single_words)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.fixup.dns_first_for_single_words">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_places_importBookmarksHTML" class="Both" displayName="$(string.browser_places_importBookmarksHTML)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.places.importBookmarksHTML">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_safebrowsing_phishing_enabled" class="Both" displayName="$(string.browser_safebrowsing_phishing_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.safebrowsing.phishing.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_safebrowsing_malware_enabled" class="Both" displayName="$(string.browser_safebrowsing_malware_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.safebrowsing.malware.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_slowStartup_notificationDisabled" class="Both" displayName="$(string.browser_slowStartup_notificationDisabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.slowStartup.notificationDisabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_search_update" class="Both" displayName="$(string.browser_search_update)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.search.update">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_tabs_warnOnClose" class="Both" displayName="$(string.browser_tabs_warnOnClose)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.tabs.warnOnClose">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_taskbar_previews_enable" class="Both" displayName="$(string.browser_taskbar_previews_enable)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.taskbar.previews.enable">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_urlbar_suggest_bookmark" class="Both" displayName="$(string.browser_urlbar_suggest_bookmark)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.urlbar.suggest.bookmark">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_urlbar_suggest_history" class="Both" displayName="$(string.browser_urlbar_suggest_history)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.urlbar.suggest.history">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_urlbar_suggest_openpage" class="Both" displayName="$(string.browser_urlbar_suggest_openpage)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="browser.urlbar.suggest.openpage">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="datareporting_policy_dataSubmissionPolicyBypassNotification" class="Both" displayName="$(string.datareporting_policy_dataSubmissionPolicyBypassNotification)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="datareporting.policy.dataSubmissionPolicyBypassNotification">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="dom_allow_scripts_to_close_windows" class="Both" displayName="$(string.dom_allow_scripts_to_close_windows)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="dom.allow_scripts_to_close_windows">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="dom_disable_window_flip" class="Both" displayName="$(string.dom_disable_window_flip)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="dom.disable_window_flip">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="dom_disable_window_move_resize" class="Both" displayName="$(string.dom_disable_window_move_resize)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="dom.disable_window_move_resize">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="dom_event_contextmenu_enabled" class="Both" displayName="$(string.dom_event_contextmenu_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="dom.event.contextmenu.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="dom_xmldocument_load_enabled" class="Both" displayName="$(string.dom_xmldocument_load_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="dom.xmldocument.load.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68ESR"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="dom_xmldocument_async_enabled" class="Both" displayName="$(string.dom_xmldocument_async_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="dom.xmldocument.async.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68ESR"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="extensions_blocklist_enabled" class="Both" displayName="$(string.extensions_blocklist_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="extensions.blocklist.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="extensions_getAddons_showPane" class="Both" displayName="$(string.extensions_getAddons_showPane)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="extensions.getAddons.showPane">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="geo_enabled" class="Both" displayName="$(string.geo_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="geo.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="intl_accept_languages" class="Both" displayName="$(string.intl_accept_languages)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <elements>
        <text id="Preferences_String" valueName="intl.accept_languages" required="true" expandable="true"/>
      </elements>
    </policy>
    <policy name="media_eme_enabled" class="Both" displayName="$(string.media_eme_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="media.eme.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="media_gmp-gmpopenh264_enabled" class="Both" displayName="$(string.media_gmp-gmpopenh264_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="media.gmp-gmpopenh264.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="media_gmp-widevinecdm_enabled" class="Both" displayName="$(string.media_gmp-widevinecdm_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="media.gmp-widevinecdm.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="network_dns_disableIPv6" class="Both" displayName="$(string.network_dns_disableIPv6)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="network.dns.disableIPv6">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="network_IDN_show_punycode" class="Both" displayName="$(string.network_IDN_show_punycode)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="network.IDN_show_punycode">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="places_history_enabled" class="Both" displayName="$(string.places_history_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="places.history.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="print_save_print_settings" class="Both" displayName="$(string.print_save_print_settings)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="print.save_print_settings">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="security_mixed_content_block_active_content" class="Both" displayName="$(string.security_mixed_content_block_active_content)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="security.mixed_content.block_active_content">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF70"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="security_ssl_errorReporting_enabled" class="Both" displayName="$(string.security_ssl_errorReporting_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="security.ssl.errorReporting.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ui_key_menuAccessKeyFocuses" class="Both" displayName="$(string.ui_key_menuAccessKeyFocuses)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="ui.key.menuAccessKeyFocuses">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="browser_cache_disk_parent_directory" class="Both" displayName="$(string.browser_cache_disk_parent_directory)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="browser.cache.disk.parent_directory" required="true" expandable="true"/>
      </elements>
    </policy>
    <policy name="dom_keyboardevent_keypress_hack_dispatch_non_printable_keys_addl" class="Both" displayName="$(string.dom_keyboardevent_keypress_hack_dispatch_non_printable_keys_addl)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="dom.keyboardevent.keypress.hack.dispatch_non_printable_keys.addl" required="true"/>
      </elements>
    </policy>
    <policy name="dom_keyboardevent_keypress_hack_use_legacy_keycode_and_charcode_addl" class="Both" displayName="$(string.dom_keyboardevent_keypress_hack_use_legacy_keycode_and_charcode_addl)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="dom.keyboardevent.keypress.hack.use_legacy_keycode_and_charcode.addl" required="true"/>
      </elements>
    </policy>
    <policy name="security_default_personal_cert" class="Both" displayName="$(string.security_default_personal_cert)" explainText="$(string.Preferences_Enum_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_security_default_personal_cert)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <enum id="Preferences_security_default_personal_cert" valueName="security.default_personal_cert">
          <item displayName="$(string.Preferences_security_default_personal_cert_Ask_Every_Time)">
            <value>
              <string>Ask Every Time</string>
            </value>
          </item>
          <item displayName="$(string.Preferences_security_default_personal_cert_Select_Automatically)">
            <value>
              <string>Select Automatically</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="browser_newtabpage_activity-stream_default_sites" class="Both" displayName="$(string.browser_newtabpage_activity-stream_default_sites)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF72"/>
      <elements>
        <text id="Preferences_String" valueName="browser.newtabpage.activity-stream.default.sites" required="true"/>
      </elements>
    </policy>
    <policy name="extensions_htmlaboutaddons_recommendations_enabled" class="Both" displayName="$(string.extensions_htmlaboutaddons_recommendations_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="extensions.htmlaboutaddons.recommendations.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF72"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="media_peerconnection_enabled" class="Both" displayName="$(string.media_peerconnection_enabled)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="media.peerconnection.enabled">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF72"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="media_peerconnection_ice_obfuscate_host_addresses_whitelist" class="Both" displayName="$(string.media_peerconnection_ice_obfuscate_host_addresses_whitelist)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="media.peerconnection.ice.obfuscate_host_addresses.whitelist" required="true"/>
      </elements>
    </policy>
    <policy name="media_peerconnection_ice_obfuscate_host_addresses_blocklist" class="Both" displayName="$(string.media_peerconnection_ice_obfuscate_host_addresses_blocklist)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF79"/>
      <elements>
        <text id="Preferences_String" valueName="media.peerconnection.ice.obfuscate_host_addresses.blocklist" required="true"/>
      </elements>
    </policy>
    <policy name="security_osclientcerts_autoload" class="Both" displayName="$(string.security_osclientcerts_autoload)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="security.osclientcerts.autoload">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF72"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="security_tls_hello_downgrade_check" class="Both" displayName="$(string.security_tls_hello_downgrade_check)" explainText="$(string.Preferences_Boolean_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" valueName="security.tls.hello_downgrade_check">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF72"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="widget_content_gtk-theme-override" class="Both" displayName="$(string.widget_content_gtk-theme-override)" explainText="$(string.Preferences_String_Explain)" key="Software\Policies\Mozilla\Firefox\Preferences" presentation="$(presentation.Preferences_String)">
      <parentCategory ref="Preferences"/>
      <supportedOn ref="SUPPORTED_FF68"/>
      <elements>
        <text id="Preferences_String" valueName="widget.content.gtk-theme-override" required="true"/>
      </elements>
    </policy>
    <policy name="A_SanitizeOnShutdown_Cache" class="Both" displayName="$(string.SanitizeOnShutdown_Cache)" explainText="$(string.SanitizeOnShutdown_Cache_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="Cache">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="B_SanitizeOnShutdown_Cookies" class="Both" displayName="$(string.SanitizeOnShutdown_Cookies)" explainText="$(string.SanitizeOnShutdown_Cookies_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="Cookies">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="C_SanitizeOnShutdown_Downloads" class="Both" displayName="$(string.SanitizeOnShutdown_Downloads)" explainText="$(string.SanitizeOnShutdown_Downloads_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="Downloads">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="D_SanitizeOnShutdown_FormData" class="Both" displayName="$(string.SanitizeOnShutdown_FormData)" explainText="$(string.SanitizeOnShutdown_FormData_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="FormData">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="E_SanitizeOnShutdown_History" class="Both" displayName="$(string.SanitizeOnShutdown_History)" explainText="$(string.SanitizeOnShutdown_History_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="History">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="F_SanitizeOnShutdown_Sessions" class="Both" displayName="$(string.SanitizeOnShutdown_Sessions)" explainText="$(string.SanitizeOnShutdown_Sessions_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="Sessions">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="G_SanitizeOnShutdown_SiteSettings" class="Both" displayName="$(string.SanitizeOnShutdown_SiteSettings)" explainText="$(string.SanitizeOnShutdown_SiteSettings_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="SiteSettings">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="H_SanitizeOnShutdown_OfflineApps" class="Both" displayName="$(string.SanitizeOnShutdown_OfflineApps)" explainText="$(string.SanitizeOnShutdown_OfflineApps_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="OfflineApps">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="I_SanitizeOnShutdown_Locked" class="Both" displayName="$(string.SanitizeOnShutdown_Locked)" explainText="$(string.SanitizeOnShutdown_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\SanitizeOnShutdown" valueName="Locked">
      <parentCategory ref="SanitizeOnShutdown"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="A_TrackingProtection_Value" class="Both" displayName="$(string.TrackingProtection_Value)" explainText="$(string.TrackingProtection_Value_Explain)" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection" valueName="Value">
      <parentCategory ref="TrackingProtection"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="B_TrackingProtection_Cryptomining" class="Both" displayName="$(string.TrackingProtection_Cryptomining)" explainText="$(string.TrackingProtection_Cryptomining_Explain)" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection" valueName="Cryptomining">
      <parentCategory ref="TrackingProtection"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="C_TrackingProtection_Fingerprinting" class="Both" displayName="$(string.TrackingProtection_Fingerprinting)" explainText="$(string.TrackingProtection_Fingerprinting_Explain)" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection" valueName="Fingerprinting">
      <parentCategory ref="TrackingProtection"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="D_TrackingProtection_Exceptions" class="Both" displayName="$(string.TrackingProtection_Exceptions)" explainText="$(string.TrackingProtection_Exceptions_Explain)" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection" presentation="$(presentation.TrackingProtection_Exceptions)">
      <parentCategory ref="TrackingProtection"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <elements>
        <list id="TrackingProtection_Exceptions" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection\Exceptions" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="E_TrackingProtection_Locked" class="Both" displayName="$(string.TrackingProtection_Locked)" explainText="$(string.TrackingProtection_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection" valueName="Locked">
      <parentCategory ref="TrackingProtection"/>
      <supportedOn ref="SUPPORTED_FF74"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="F_TrackingProtection_EmailTracking" class="Both" displayName="$(string.TrackingProtection_EmailTracking)" explainText="$(string.TrackingProtection_EmailTracking_Explain)" key="Software\Policies\Mozilla\Firefox\EnableTrackingProtection" valueName="EmailTracking">
      <parentCategory ref="TrackingProtection"/>
      <supportedOn ref="SUPPORTED_FF112"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_WhatsNew" class="Both" displayName="$(string.UserMessaging_WhatsNew)" explainText="$(string.UserMessaging_WhatsNew_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="WhatsNew">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF75_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_ExtensionRecommendations" class="Both" displayName="$(string.UserMessaging_ExtensionRecommendations)" explainText="$(string.UserMessaging_ExtensionRecommendations_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="ExtensionRecommendations">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF75"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_FeatureRecommendations" class="Both" displayName="$(string.UserMessaging_FeatureRecommendations)" explainText="$(string.UserMessaging_FeatureRecommendations_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="FeatureRecommendations">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF75"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_UrlbarInterventions" class="Both" displayName="$(string.UserMessaging_UrlbarInterventions)" explainText="$(string.UserMessaging_UrlbarInterventions_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="UrlbarInterventions">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF75_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_SkipOnboarding" class="Both" displayName="$(string.UserMessaging_SkipOnboarding)" explainText="$(string.UserMessaging_SkipOnboarding_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="SkipOnboarding">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF88"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_MoreFromMozilla" class="Both" displayName="$(string.UserMessaging_MoreFromMozilla)" explainText="$(string.UserMessaging_MoreFromMozilla_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="MoreFromMozilla">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF98_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_FirefoxLabs" class="Both" displayName="$(string.UserMessaging_FirefoxLabs)" explainText="$(string.UserMessaging_FirefoxLabs)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="FirefoxLabs">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF130_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UserMessaging_Locked" class="Both" displayName="$(string.UserMessaging_Locked)" explainText="$(string.UserMessaging_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\UserMessaging" valueName="Locked">
      <parentCategory ref="UserMessaging"/>
      <supportedOn ref="SUPPORTED_FF75"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_DHE_RSA_WITH_AES_128_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_DHE_RSA_WITH_AES_128_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_DHE_RSA_WITH_AES_128_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_DHE_RSA_WITH_AES_256_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_DHE_RSA_WITH_AES_256_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_DHE_RSA_WITH_AES_256_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_RSA_WITH_AES_128_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_RSA_WITH_AES_128_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_RSA_WITH_AES_128_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_RSA_WITH_AES_256_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_RSA_WITH_AES_256_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_RSA_WITH_AES_256_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_RSA_WITH_3DES_EDE_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_RSA_WITH_3DES_EDE_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_RSA_WITH_3DES_EDE_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF76_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_RSA_WITH_AES_128_GCM_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_RSA_WITH_AES_128_GCM_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_RSA_WITH_AES_128_GCM_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF78"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_RSA_WITH_AES_256_GCM_SHA384" class="Both" displayName="$(string.DisabledCiphers_TLS_RSA_WITH_AES_256_GCM_SHA384)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_RSA_WITH_AES_256_GCM_SHA384">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF78"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF97"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF97"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF97"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF97"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF97"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF97"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_CHACHA20_POLY1305_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_CHACHA20_POLY1305_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_CHACHA20_POLY1305_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF138"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_AES_128_GCM_SHA256" class="Both" displayName="$(string.DisabledCiphers_TLS_AES_128_GCM_SHA256)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_AES_128_GCM_SHA256">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF138"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisabledCiphers_TLS_AES_256_GCM_SHA384" class="Both" displayName="$(string.DisabledCiphers_TLS_AES_256_GCM_SHA384)" explainText="$(string.DisabledCiphers_Explain)" key="Software\Policies\Mozilla\Firefox\DisabledCiphers" valueName="TLS_AES_256_GCM_SHA384">
      <parentCategory ref="DisabledCiphers"/>
      <supportedOn ref="SUPPORTED_FF138"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="EncryptedMediaExtensions_Enabled" class="Both" displayName="$(string.EncryptedMediaExtensions_Enabled)" explainText="$(string.EncryptedMediaExtensions_Enabled_Explain)" key="Software\Policies\Mozilla\Firefox\EncryptedMediaExtensions" valueName="Enabled">
      <parentCategory ref="EncryptedMediaExtensions"/>
      <supportedOn ref="SUPPORTED_FF77"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="EncryptedMediaExtensions_Locked" class="Both" displayName="$(string.EncryptedMediaExtensions_Locked)" explainText="$(string.EncryptedMediaExtensions_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\EncryptedMediaExtensions" valueName="Locked">
      <parentCategory ref="EncryptedMediaExtensions"/>
      <supportedOn ref="SUPPORTED_FF77"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PDFjs_Enabled" class="Both" displayName="$(string.PDFjs_Enabled)" explainText="$(string.PDFjs_Enabled_Explain)" key="Software\Policies\Mozilla\Firefox\PDFjs" valueName="Enabled">
      <parentCategory ref="PDFjs"/>
      <supportedOn ref="SUPPORTED_FF77_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PDFjs_EnablePermissions" class="Both" displayName="$(string.PDFjs_EnablePermissions)" explainText="$(string.PDFjs_EnablePermissions_Explain)" key="Software\Policies\Mozilla\Firefox\PDFjs" valueName="EnablePermissions">
      <parentCategory ref="PDFjs"/>
      <supportedOn ref="SUPPORTED_FF77_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PictureInPicture_Enabled" class="Both" displayName="$(string.PictureInPicture_Enabled)" explainText="$(string.PictureInPicture_Enabled_Explain)" key="Software\Policies\Mozilla\Firefox\PictureInPicture" valueName="Enabled">
      <parentCategory ref="PictureInPicture"/>
      <supportedOn ref="SUPPORTED_FF78"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PictureInPicture_Locked" class="Both" displayName="$(string.PictureInPicture_Locked)" explainText="$(string.PictureInPicture_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\PictureInPicture" valueName="Locked">
      <parentCategory ref="PictureInPicture"/>
      <supportedOn ref="SUPPORTED_FF78"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PrimaryPassword" class="Both" displayName="$(string.PrimaryPassword)" explainText="$(string.PrimaryPassword_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="PrimaryPassword">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF79"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="HandlersOneLine" class="Both" displayName="$(string.HandlersOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Handlers_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF78"/>
      <elements>
        <text id="JSONOneLine" valueName="Handlers" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="Handlers" class="Both" displayName="$(string.Handlers)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Handlers_Explain)" presentation="$(presentation.Handlers)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF78"/>
      <elements>
        <multiText id="Handlers" valueName="Handlers" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="PreferencesOneLine" class="Both" displayName="$(string.PreferencesOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Preferences_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF81"/>
      <elements>
        <text id="JSONOneLine" valueName="Preferences" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="Preferences" class="Both" displayName="$(string.Preferences)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Preferences_Explain)" presentation="$(presentation.JSON)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF81"/>
      <elements>
        <multiText id="JSON" valueName="Preferences" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="ManagedBookmarksOneLine" class="Both" displayName="$(string.ManagedBookmarksOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.ManagedBookmarks_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF83"/>
      <elements>
        <text id="JSONOneLine" valueName="ManagedBookmarks" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="ManagedBookmarks" class="Both" displayName="$(string.ManagedBookmarks)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.ManagedBookmarks_Explain)" presentation="$(presentation.JSON)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF83"/>
      <elements>
        <multiText id="JSON" valueName="ManagedBookmarks" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="AllowedDomainsForApps" class="Both" displayName="$(string.AllowedDomainsForApps)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.AllowedDomainsForApps_Explain)" presentation="$(presentation.String)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF89"/>
      <elements>
        <text id="String" valueName="AllowedDomainsForApps"/>
      </elements>
    </policy>
    <policy name="BackgroundAppUpdate" class="Both" displayName="$(string.BackgroundAppUpdate)" explainText="$(string.BackgroundAppUpdate_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="BackgroundAppUpdate">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF90"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="AutoLaunchProtocolsFromOriginsOneLine" class="Both" displayName="$(string.AutoLaunchProtocolsFromOriginsOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.AutoLaunchProtocolsFromOrigins_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF81"/>
      <elements>
        <text id="JSONOneLine" valueName="AutoLaunchProtocolsFromOrigins" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="AutoLaunchProtocolsFromOrigins" class="Both" displayName="$(string.AutoLaunchProtocolsFromOrigins)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.AutoLaunchProtocolsFromOrigins_Explain)" presentation="$(presentation.JSON)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF81"/>
      <elements>
        <multiText id="JSON" valueName="AutoLaunchProtocolsFromOrigins" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="WindowsSSO" class="Both" displayName="$(string.WindowsSSO)" explainText="$(string.WindowsSSO_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="WindowsSSO">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF91"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="UseSystemPrintDialog" class="Both" displayName="$(string.UseSystemPrintDialog)" explainText="$(string.UseSystemPrintDialog_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="UseSystemPrintDialog">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF102"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ExemptDomainFileTypePairsFromFileTypeDownloadWarningsOneLine" class="Both" displayName="$(string.ExemptDomainFileTypePairsFromFileTypeDownloadWarningsOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.ExemptDomainFileTypePairsFromFileTypeDownloadWarnings_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF102"/>
      <elements>
        <text id="JSONOneLine" valueName="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings" class="Both" displayName="$(string.ExemptDomainFileTypePairsFromFileTypeDownloadWarnings)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.ExemptDomainFileTypePairsFromFileTypeDownloadWarnings_Explain)" presentation="$(presentation.JSON)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF102"/>
      <elements>
        <multiText id="JSON" valueName="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="StartDownloadsInTempDirectory" class="Both" displayName="$(string.StartDownloadsInTempDirectory)" explainText="$(string.StartDownloadsInTempDirectory_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="StartDownloadsInTempDirectory">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF102"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="GoToIntranetSiteForSingleWordEntryInAddressBar" class="Both" displayName="$(string.GoToIntranetSiteForSingleWordEntryInAddressBar)" explainText="$(string.GoToIntranetSiteForSingleWordEntryInAddressBar_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="GoToIntranetSiteForSingleWordEntryInAddressBar">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF104"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="AppUpdatePin" class="Both" displayName="$(string.AppUpdatePin)" explainText="$(string.AppUpdatePin_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.String)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF102"/>
      <elements>
        <text id="String" valueName="AppUpdatePin" required="true"/>
      </elements>
    </policy>
    <policy name="DisableThirdPartyModuleBlocking" class="Both" displayName="$(string.DisableThirdPartyModuleBlocking)" explainText="$(string.DisableThirdPartyModuleBlocking_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableThirdPartyModuleBlocking">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF110"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContainersOneLine" class="Both" displayName="$(string.ContainersOneLine)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Containers_Explain)" presentation="$(presentation.JSONOneLine)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF113"/>
      <elements>
        <text id="JSONOneLine" valueName="Containers" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="Containers" class="Both" displayName="$(string.Containers)" key="Software\Policies\Mozilla\Firefox" explainText="$(string.Containers_Explain)" presentation="$(presentation.JSON)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF113"/>
      <elements>
        <multiText id="JSON" valueName="Containers" maxLength="16384"/>
      </elements>
    </policy>
    <policy name="SecurityDevices_Add" class="Both" displayName="$(string.SecurityDevices_Add)" explainText="$(string.SecurityDevices_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.SecurityDevices)">
      <parentCategory ref="SecurityDevices"/>
      <supportedOn ref="SUPPORTED_FF114"/>
      <elements>
        <list id="SecurityDevices" key="Software\Policies\Mozilla\Firefox\SecurityDevices\Add" valuePrefix="" explicitValue="true" expandable="true"/>
      </elements>
    </policy>
    <policy name="SecurityDevices_Delete" class="Both" displayName="$(string.SecurityDevices_Delete)" explainText="$(string.SecurityDevices_Delete_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.SecurityDevices)">
      <parentCategory ref="SecurityDevices"/>
      <supportedOn ref="SUPPORTED_FF114"/>
      <elements>
        <list id="SecurityDevices" key="Software\Policies\Mozilla\Firefox\SecurityDevices\Delete" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="FirefoxSuggest_WebSuggestions" class="Both" displayName="$(string.FirefoxSuggest_WebSuggestions)" explainText="$(string.FirefoxSuggest_WebSuggestions_Explain)" key="Software\Policies\Mozilla\Firefox\FirefoxSuggest" valueName="WebSuggestions">
      <parentCategory ref="FirefoxSuggest"/>
      <supportedOn ref="SUPPORTED_FF118"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="FirefoxSuggest_SponsoredSuggestions" class="Both" displayName="$(string.FirefoxSuggest_SponsoredSuggestions)" explainText="$(string.FirefoxSuggest_SponsoredSuggestions_Explain)" key="Software\Policies\Mozilla\Firefox\FirefoxSuggest" valueName="SponsoredSuggestions">
      <parentCategory ref="FirefoxSuggest"/>
      <supportedOn ref="SUPPORTED_FF118"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="FirefoxSuggest_ImproveSuggest" class="Both" displayName="$(string.FirefoxSuggest_ImproveSuggest)" explainText="$(string.FirefoxSuggest_ImproveSuggest_Explain)" key="Software\Policies\Mozilla\Firefox\FirefoxSuggest" valueName="ImproveSuggest">
      <parentCategory ref="FirefoxSuggest"/>
      <supportedOn ref="SUPPORTED_FF118"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="FirefoxSuggest_Locked" class="Both" displayName="$(string.FirefoxSuggest_Locked)" explainText="$(string.FirefoxSuggest_Locked_Explain)" key="Software\Policies\Mozilla\Firefox\FirefoxSuggest" valueName="Locked">
      <parentCategory ref="FirefoxSuggest"/>
      <supportedOn ref="SUPPORTED_FF118"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PrintingEnabled" class="Both" displayName="$(string.PrintingEnabled)" explainText="$(string.PrintingEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="PrintingEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF120"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ManualAppUpdateOnly" class="Both" displayName="$(string.ManualAppUpdateOnly)" explainText="$(string.ManualAppUpdateOnly_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="ManualAppUpdateOnly">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF88"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="AllowFileSelectionDialogs" class="Both" displayName="$(string.AllowFileSelectionDialogs)" explainText="$(string.AllowFileSelectionDialogs_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="AllowFileSelectionDialogs">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF124_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="AutofillAddressEnabled" class="Both" displayName="$(string.AutofillAddressEnabled)" explainText="$(string.AutofillAddressEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="AutofillAddressEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF125"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="AutofillCreditCardEnabled" class="Both" displayName="$(string.AutofillCreditCardEnabled)" explainText="$(string.AutofillCreditCardEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="AutofillCreditCardEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF125"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="TranslateEnabled" class="Both" displayName="$(string.TranslateEnabled)" explainText="$(string.TranslateEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="TranslateEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF126_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="DisableEncryptedClientHello" class="Both" displayName="$(string.DisableEncryptedClientHello)" explainText="$(string.DisableEncryptedClientHello_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="DisableEncryptedClientHello">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF127_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="PostQuantumKeyAgreementEnabled" class="Both" displayName="$(string.PostQuantumKeyAgreementEnabled)" explainText="$(string.PostQuantumKeyAgreementEnabled_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="PostQuantumKeyAgreementEnabled">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF127_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="HttpsOnlyMode" class="Both" displayName="$(string.HttpsOnlyMode)" explainText="$(string.HttpsOnlyMode_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.HttpsOnlyMode)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF127_ONLY"/>
      <elements>
        <enum id="HttpsOnlyMode" valueName="HttpsOnlyMode">
          <item displayName="$(string.HttpsOnlyMode_Allowed)">
            <value>
              <string>allowed</string>
            </value>
          </item>
          <item displayName="$(string.HttpsOnlyMode_Disallowed)">
            <value>
              <string>disallowed</string>
            </value>
          </item>
          <item displayName="$(string.HttpsOnlyMode_Enabled)">
            <value>
              <string>enabled</string>
            </value>
          </item>
          <item displayName="$(string.HttpsOnlyMode_ForceEnabled)">
            <value>
              <string>force_enabled</string>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="HttpAllowlist" class="Both" displayName="$(string.HttpAllowlist)" explainText="$(string.HttpAllowlist_Explain)" key="Software\Policies\Mozilla\Firefox\HttpAllowlist" presentation="$(presentation.List)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF127_ONLY"/>
      <elements>
        <list id="List" key="Software\Policies\Mozilla\Firefox\HttpAllowlist" valuePrefix=""/>
      </elements>
    </policy>
    <policy name="PrivateBrowsingModeAvailability" class="Both" displayName="$(string.PrivateBrowsingModeAvailability)" explainText="$(string.PrivateBrowsingModeAvailability_Explain)" key="Software\Policies\Mozilla\Firefox" presentation="$(presentation.PrivateBrowsingModeAvailability)">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF130"/>
      <elements>
        <enum id="PrivateBrowsingModeAvailability" valueName="PrivateBrowsingModeAvailability">
          <item displayName="$(string.PrivateBrowsingModeAvailability_0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.PrivateBrowsingModeAvailability_1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.PrivateBrowsingModeAvailability_2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="ContentAnalysis_AgentName" class="Both" displayName="$(string.ContentAnalysis_AgentName)" explainText="$(string.ContentAnalysis_AgentName_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.String)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <text id="String" valueName="AgentName"/>
      </elements>
    </policy>
    <policy name="ContentAnalysis_AgentTimeout" class="Both" displayName="$(string.ContentAnalysis_AgentTimeout)" explainText="$(string.ContentAnalysis_AgentTimeout_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.Number)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <decimal id="Number" valueName="AgentTimeout"/>
      </elements>
    </policy>
    <policy name="ContentAnalysis_AllowUrlRegexList" class="Both" displayName="$(string.ContentAnalysis_AllowUrlRegexList)" explainText="$(string.ContentAnalysis_AllowUrlRegexList_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.String)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <text id="String" valueName="AllowUrlRegexList"/>
      </elements>
    </policy>
    <policy name="ContentAnalysis_BypassForSameTabOperations" class="Both" displayName="$(string.ContentAnalysis_BypassForSameTabOperations)" explainText="$(string.ContentAnalysis_BypassForSameTabOperations_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" valueName="BypassForSameTabOperations">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_ClientSignature" class="Both" displayName="$(string.ContentAnalysis_ClientSignature)" explainText="$(string.ContentAnalysis_ClientSignature_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.String)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <text id="String" valueName="ClientSignature"/>
      </elements>
    </policy>
    <policy name="ContentAnalysis_DefaultResult" class="Both" displayName="$(string.ContentAnalysis_DefaultResult)" explainText="$(string.ContentAnalysis_DefaultResult_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.ContentAnalysis_DefaultResult)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <enum id="ContentAnalysis_DefaultResult" valueName="DefaultResult">
          <item displayName="$(string.ContentAnalysis_DefaultResult_0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.ContentAnalysis_DefaultResult_1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.ContentAnalysis_DefaultResult_2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="ContentAnalysis_DenyUrlRegexList" class="Both" displayName="$(string.ContentAnalysis_DenyUrlRegexList)" explainText="$(string.ContentAnalysis_DenyUrlRegexList_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.String)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <text id="String" valueName="DenyUrlRegexList"/>
      </elements>
    </policy>
    <policy name="ContentAnalysis_Enabled" class="Both" displayName="$(string.ContentAnalysis_Enabled)" explainText="$(string.ContentAnalysis_Enabled_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" valueName="Enabled">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_IsPerUser" class="Both" displayName="$(string.ContentAnalysis_IsPerUser)" explainText="$(string.ContentAnalysis_IsPerUser_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" valueName="IsPerUser">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_PipePathName" class="Both" displayName="$(string.ContentAnalysis_PipePathName)" explainText="$(string.ContentAnalysis_PipePathName_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.String)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <elements>
        <text id="String" valueName="PipePathName"/>
      </elements>
    </policy>
    <policy name="ContentAnalysis_ShowBlockedResult" class="Both" displayName="$(string.ContentAnalysis_ShowBlockedResult)" explainText="$(string.ContentAnalysis_ShowBlockedResult_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" valueName="ShowBlockedResult">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_InterceptionPoints_Clipboard" class="Both" displayName="$(string.ContentAnalysis_InterceptionPoints_Clipboard)" explainText="$(string.ContentAnalysis_InterceptionPoints_Clipboard_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis\InterceptionPoints\Clipboard" valueName="Enabled">
      <parentCategory ref="InterceptionPoints_Clipboard"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly" class="Both" displayName="$(string.ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly)" explainText="$(string.ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis\InterceptionPoints\Clipboard" valueName="PlainTextOnly">
      <parentCategory ref="InterceptionPoints_Clipboard"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_InterceptionPoints_DragAndDrop" class="Both" displayName="$(string.ContentAnalysis_InterceptionPoints_DragAndDrop)" explainText="$(string.ContentAnalysis_InterceptionPoints_DragAndDrop_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis\InterceptionPoints\DragAndDrop" valueName="Enabled">
      <parentCategory ref="InterceptionPoints_DragAndDrop"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly" class="Both" displayName="$(string.ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly)" explainText="$(string.ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis\InterceptionPoints\DragAndDrop" valueName="PlainTextEnabled">
      <parentCategory ref="InterceptionPoints_DragAndDrop"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_InterceptionPoints_FileUpload" class="Both" displayName="$(string.ContentAnalysis_InterceptionPoints_FileUpload)" explainText="$(string.ContentAnalysis_InterceptionPoints_FileUpload_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis\InterceptionPoints\FileUpload" valueName="Enabled">
      <parentCategory ref="InterceptionPoints"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_InterceptionPoints_Print" class="Both" displayName="$(string.ContentAnalysis_InterceptionPoints_Print)" explainText="$(string.ContentAnalysis_InterceptionPoints_Print_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis\InterceptionPoints\Print" valueName="Enabled">
      <parentCategory ref="InterceptionPoints"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
    <policy name="ContentAnalysis_TimeoutResult" class="Both" displayName="$(string.ContentAnalysis_TimeoutResult)" explainText="$(string.ContentAnalysis_TimeoutResult_Explain)" key="Software\Policies\Mozilla\Firefox\ContentAnalysis" presentation="$(presentation.ContentAnalysis_TimeoutResult)">
      <parentCategory ref="ContentAnalysis"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
        <elements>
        <enum id="ContentAnalysis_TimeoutResult" valueName="TimeoutResult">
          <item displayName="$(string.ContentAnalysis_DefaultResult_0)">
            <value>
              <decimal value="0"/>
            </value>
          </item>
          <item displayName="$(string.ContentAnalysis_DefaultResult_1)">
            <value>
              <decimal value="1"/>
            </value>
          </item>
          <item displayName="$(string.ContentAnalysis_DefaultResult_2)">
            <value>
              <decimal value="2"/>
            </value>
          </item>
        </enum>
      </elements>
    </policy>
    <policy name="SkipTermsOfUse" class="Both" displayName="$(string.SkipTermsOfUse)" explainText="$(string.SkipTermsOfUse_Explain)" key="Software\Policies\Mozilla\Firefox" valueName="SkipTermsOfUse">
      <parentCategory ref="firefox"/>
      <supportedOn ref="SUPPORTED_FF138_ONLY"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
  </policies>
</policyDefinitions>
