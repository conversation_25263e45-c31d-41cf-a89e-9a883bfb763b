<#
.SYNOPSIS
    Restore SQL Server and Accuro databases from OneDrive backup locations.

.DESCRIPTION
    This script restores SQL Server databases from OneDrive backup locations to all detected SQL Server instances
    and relocates Accuro installation files from OneDrive directly to `C:\Program Files (x86)`.
    
.NOTES
    Author   : <PERSON><PERSON>
    Date     : 2024-11-14
    Version  : 1.3
    Script Name: RestoreDatabases.ps1
#>

# Logging function
if (-not (Test-Path -Path "C:\Temp")) {
    New-Item -ItemType Directory -Path "C:\Temp" | Out-Null
}

function Write-LogMessage {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [string]$Level = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    # Attempt to write to the log file with retry in case of file lock
    for ($i = 0; $i -lt 5; $i++) {
        try {
            Add-Content -Path "C:\Temp\RestoreDatabases.log" -Value $logEntry
            break
        } catch {
            Start-Sleep -Milliseconds 100
            if ($i -eq 4) {
                Add-Content -Path "C:\Temp\Fallback-RestoreDatabases.log" -Value "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] [ERROR] Could not write to primary log: $($_.Exception.Message)"
            }
        }
    }
}

# Unblock the script to prevent security warnings
if (Test-Path $MyInvocation.MyCommand.Path) {
    try {
        Unblock-File -Path $MyInvocation.MyCommand.Path -ErrorAction SilentlyContinue
        Write-LogMessage "Script unblocked to suppress security warning." -Level "INFO"
    } catch {
        Write-LogMessage "Failed to unblock script: $($_.Exception.Message)" -Level "WARNING"
    }
}

# Get the user's OneDrive folder
function Get-OneDriveFolder {
    try {
        $oneDrivePaths = Get-ChildItem -Path "$env:USERPROFILE" -Directory -Filter "OneDrive*" |
            Where-Object { $_.FullName -like "*OneDrive - QHR Technologies*" }

        if ($oneDrivePaths.Count -eq 1) {
            $oneDrivePath = $oneDrivePaths[0].FullName
            Write-LogMessage "OneDrive folder resolved to: $oneDrivePath" -Level "SUCCESS"
            return $oneDrivePath
        } else {
            throw "Correct OneDrive folder could not be found."
        }
    } catch {
        Write-LogMessage "Error resolving OneDrive folder: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Detect installed SQL Server instances
function Get-SQLInstances {
    try {
        Write-LogMessage "Scanning for installed SQL Server instances..."
        $instances = (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" -ErrorAction Stop).PSChildName

        if (-not $instances) {
            throw "No SQL Server instances found."
        }

        Write-LogMessage "Found SQL Server instances: $($instances -join ', ')"
        return $instances
    } catch {
        Write-LogMessage "No SQL Server instances found or error accessing registry: $($_.Exception.Message)" -Level "WARNING"
        return @()  # Return empty array to continue
    }
}

# Restore SQL databases
function Restore-SQLDatabase {
    param (
        [string]$SqlInstance,
        [string]$DatabaseName,
        [string]$BackupFilePath
    )

    try {
        Write-LogMessage "Restoring database '$DatabaseName' on instance '$SqlInstance' using backup file '$BackupFilePath'..."
        
        Restore-SqlDatabase -ServerInstance $SqlInstance `
                            -Database $DatabaseName `
                            -BackupFile $BackupFilePath `
                            -ReplaceDatabase `
                            -RelocateFile @(
                                @{
                                    LogicalFileName = "$($DatabaseName)_Data";
                                    PhysicalFileName = "C:\Program Files\Microsoft SQL Server\MSSQL\Data\$($DatabaseName)_Data.mdf"
                                },
                                @{
                                    LogicalFileName = "$($DatabaseName)_Log";
                                    PhysicalFileName = "C:\Program Files\Microsoft SQL Server\MSSQL\Data\$($DatabaseName)_Log.ldf"
                                }
                            )

        Write-LogMessage "Restore for '$DatabaseName' completed successfully."
    } catch {
        Write-LogMessage "Error restoring database '$DatabaseName': $($_.Exception.Message)" -Level "ERROR"
    }
}

# Restore Accuro files directly to C:\Program Files (x86)
function Restore-AccuroFiles {
    param (
        [string]$AccuroBackupPath,
        [string]$DestinationPath = "C:\Program Files (x86)"
    )

    try {
        Write-LogMessage "Restoring Accuro files from '$AccuroBackupPath' directly to '$DestinationPath'..."

        # Ensure the destination path exists
        if (-not (Test-Path -Path $DestinationPath)) {
            throw "The destination path '$DestinationPath' is missing. Ensure Program Files (x86) exists."
        }

        # Copy only the contents of Accuro Backups directly to Program Files (x86)
        $accuroFolders = Get-ChildItem -Path $AccuroBackupPath -ErrorAction Stop
        if ($accuroFolders.Count -eq 0) {
            Write-LogMessage "No Accuro backup files found at '$AccuroBackupPath'." -Level "WARNING"
            return
        }

        foreach ($item in $accuroFolders) {
            $dest = Join-Path -Path $DestinationPath -ChildPath $item.Name
            Copy-Item -Path $item.FullName -Destination $dest -Recurse -Force
            Write-LogMessage "Restored '$($item.Name)' to '$DestinationPath'." -Level "SUCCESS"
        }

        Write-LogMessage "All Accuro files successfully restored to Program Files (x86)."
    } catch {
        Write-LogMessage "Error restoring Accuro files: $($_.Exception.Message)" -Level "ERROR"
    }
}

# Main script execution
try {
    # Get OneDrive folder
    $oneDriveFolder = Get-OneDriveFolder

    # Define backup paths
    $sqlBackupPath = Join-Path -Path $oneDriveFolder -ChildPath "SQL Backups"
    $accuroBackupPath = Join-Path -Path $oneDriveFolder -ChildPath "Accuro Backups"

    # Detect SQL Server instances
    $sqlInstances = Get-SQLInstances

    # Restore each SQL database backup to all detected instances
    if ($sqlInstances.Count -eq 0) {
        Write-LogMessage "Skipping SQL restore: No SQL instances detected." -Level "WARNING"
    } else {
        foreach ($instance in $sqlInstances) {
            $backupFiles = Get-ChildItem -Path $sqlBackupPath -Filter "*.bak" -ErrorAction SilentlyContinue
            foreach ($backup in $backupFiles) {
                $databaseName = [System.IO.Path]::GetFileNameWithoutExtension($backup.Name)
                Restore-SQLDatabase -SqlInstance "$env:COMPUTERNAME\$instance" -DatabaseName $databaseName -BackupFilePath $backup.FullName
            }
        }
    }

    # Restore Accuro files directly to Program Files (x86)
    if (Test-Path -Path $accuroBackupPath) {
        Restore-AccuroFiles -AccuroBackupPath $accuroBackupPath
    } else {
        Write-LogMessage "No Accuro backups found at '$accuroBackupPath'." -Level "WARNING"
    }

    Write-LogMessage "All restorations completed successfully!"
    exit 0
} catch {
    Write-LogMessage "An error occurred during execution: $($_.Exception.Message)" -Level "ERROR"
    exit 1
}
