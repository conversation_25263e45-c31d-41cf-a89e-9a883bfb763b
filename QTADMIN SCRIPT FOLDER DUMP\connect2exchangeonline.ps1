﻿# Connect to Office 365 (http://technet.microsoft.com/library/jj151815.aspx#BKMK_connect):
$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
 $LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)
#$LiveCred = Get-Credential $creduser

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
#Connect-MsolService -Credential $LiveCred


# Find email address:
#$findaddr = Read-Host "Enter email address to find"
#$findaddr = 'smtp:' + $findaddr
#Get-mailbox -Identity * | ? {$_.EmailAddresses -like $findaddr} | fl Identity, Emailaddresses
#Get-Contact -Identity * | ? {$_.EmailAddresses -like $findaddr} | fl Identity, Emailaddresses
#Get-User -Identity * | ? {$_.EmailAddresses -like $findaddr} | fl Identity, Emailaddresses
#Get-DistributionGroup | ? {$_.EmailAddresses -like $findaddr} | fl Identity, Emailaddresses 

# Get mailbox info

# Get Calendar info
# get-mailboxfolderpermission -identity email:\calendar | select user,accessrights

# Set Cal Permissions
# Add-MailboxFolderPermission -Identity email:\calendar -AccessRights Editor -user "Display Name"
# OR
# Set-MailboxFolderPermission -Identity email:\calendar -AccessRights Editor -user "Display Name"

# Update Exchange Online GAL
# Update-GlobalAddressList -Identity "Fourth Coffee"

# Check user's access to other mailboxes
# Get-Mailbox -ResultSize Unlimited | Get-MailboxPermission -User <EMAIL> | Format-Table Identity, AccessRights, Deny

# Check Forwarding
# Get-Mailbox <your mailbox identity>| Ft ForwardingSMTPAddress

# Enable Forwarding
# <AUTHOR> <EMAIL> -DeliverToMailboxAndForward $true -ForwardingSMTPAddress <<EMAIL>>

# Disable Forwarding
# <AUTHOR> <EMAIL> -DeliverToMailboxAndForward $false -ForwardingSMTPAddress $null

# Disable Forwarding for an entire OU
# get-aduser -filter * | ? distinguishedname -match medeo | foreach { set-mailbox -Identity $_.userprincipalname -DeliverToMailboxAndForward $false -ForwardingSMTPAddress $null }
