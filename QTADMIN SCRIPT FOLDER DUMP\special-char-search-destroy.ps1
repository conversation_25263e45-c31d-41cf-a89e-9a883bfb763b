﻿$Logfile = '\\rcmmhalligan\c$\Users\mhalligan\OneDrive @ QHR Technologies Inc-\20150417 -renamed-files.txt'
$dirpath = '\\rcmmhalligan\c$\Users\mhalligan\OneDrive @ QHR Technologies Inc-\documents'

$TxtToFind = @('=','#','%') # ,'QHR Accuro Data')
#$TxtToFind = "#"
$UpdatedTxt = ''
$count=0
Write "" | Out-File $Logfile

ForEach($File in (GCI $dirpath -Recurse -ErrorAction SilentlyContinue)){
    If($File.Name|Select-String $TxtToFind -SimpleMatch -Quiet){
        #sleep 3
        $newname = $file.Name
        foreach ($char in $TxtToFind) {
          if ($char -ne '.') {
            $newname = $newname -replace [RegEx]::Escape($char),$UpdatedTxt
          }
          else {  
            #Removes dots, could be scary
            #$newname = $newname -split "\$char",([regex]::matches($newname,"\$char").count) -join ''
          }
        }
        #$newname = $newname -split '\.',([regex]::matches($newname,"\.").count) -join ''
        if ($File.Name -ne $newname) {
            Rename-Item -path $file.PSPath -NewName $newname
            Write "Renamed: .\$($File.FullName.TrimStart($dirpath)) to $newname"
            Write "Renamed: .\$($File.FullName.TrimStart($dirpath)) to $newname" |Out-File $Logfile -append
            $count++
        }
   }
}  
Write "`n$count files renamed" |Out-File $Logfile -Append