<#
.SYNOPSIS
    This script assigns the required Microsoft Graph API permissions to an Azure Logic App's
    System-Assigned Managed Identity for managing Intune EPM elevation requests.

.DESCRIPTION
    The script automates the process of granting 'DeviceManagementConfiguration.Read.All' and
    'DeviceManagementConfiguration.ReadWrite.All' permissions to the specified Logic App.
    This allows the Logic App to securely read pending EPM requests and post approve/deny
    decisions back to Intune without using stored credentials.

.NOTES
    Version: 1.1
    Author: <PERSON><PERSON>
    Prerequisites:
    - PowerShell 7+
    - Microsoft.Graph.Applications module
    - Run by a user with administrative privileges (e.g., Global Administrator)
#>

# 1. --- CONFIGURATION ---
# IMPORTANT: Change this value to the exact name of the Logic App you created in Azure.
# This should match the name you provided in Part 2, Step 5 of the guide (e.g., LA-Intune-EPM-Notifications).
$logicAppName = "LA-Intune-EPM-Notifications"

# 2. --- CONNECT TO MICROSOFT GRAPH ---
# You will be prompted to log in. Use an account with Global Admin or similar privileges.
Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Yellow
try {
    Connect-MgGraph -Scopes "Application.Read.All, AppRoleAssignment.ReadWrite.All"
    Write-Host "Connection successful." -ForegroundColor Green
}
catch {
    Write-Error "Failed to connect to Microsoft Graph. Please ensure you have the correct permissions. Error: $($_.Exception.Message)"
    return
}


# 3. --- FIND THE MANAGED IDENTITY (SERVICE PRINCIPAL) OF YOUR LOGIC APP ---
Write-Host "Finding the Managed Identity for '$logicAppName'..." -ForegroundColor Yellow
$managedIdentity = Get-MgServicePrincipal -Filter "displayName eq '$logicAppName'"
if (-not $managedIdentity) {
    Write-Error "Could not find the Managed Identity. Check the Logic App name and ensure it was created with a System-Assigned Identity enabled."
    return
}
Write-Host "Found Managed Identity with ID: $($managedIdentity.Id)" -ForegroundColor Green

# 4. --- DEFINE THE REQUIRED API PERMISSIONS ---
# These are the specific permissions needed for EPM request management.
$requiredPermissions = @(
    "DeviceManagementConfiguration.Read.All",       # To read EPM requests
    "DeviceManagementConfiguration.ReadWrite.All"   # To approve/deny EPM requests
)

# 5. --- FIND THE 'MICROSOFT GRAPH' ENTERPRISE APPLICATION ---
# This represents the API we are granting permissions to. Its AppId is always the same.
Write-Host "Finding the Microsoft Graph service..." -ForegroundColor Yellow
$graphApi = Get-MgServicePrincipal -Filter "appId eq '00000003-0000-0000-c000-000000000000'"

# 6. --- ASSIGN THE PERMISSIONS ---
Write-Host "Assigning permissions..." -ForegroundColor Yellow
foreach ($permissionName in $requiredPermissions) {
    # Find the specific role (permission) on the Graph API application
    $appRole = $graphApi.AppRoles | Where-Object { ($_.Value -eq $permissionName) -and ($_.AllowedMemberTypes -contains "Application") }
    
    if ($appRole) {
        # Check if the assignment already exists to prevent errors on re-running the script
        $existingAssignment = Get-MgServicePrincipalAppRoleAssignment -ServicePrincipalId $managedIdentity.Id -Filter "appRoleId eq '$($appRole.Id)' and resourceId eq '$($graphApi.Id)'"
        if ($existingAssignment) {
            Write-Host "Permission '$permissionName' is already assigned. Skipping." -ForegroundColor Cyan
        }
        else {
            # Create the new permission assignment
            New-MgServicePrincipalAppRoleAssignment -ServicePrincipalId $managedIdentity.Id -PrincipalId $managedIdentity.Id -ResourceId $graphApi.Id -AppRoleId $appRole.Id
            Write-Host "Successfully assigned permission: $permissionName" -ForegroundColor Green
        }
    }
    else {
        Write-Warning "Could not find the application permission role for '$permissionName'."
    }
}

Write-Host "Script finished." -ForegroundColor Green
Disconnect-MgGraph
