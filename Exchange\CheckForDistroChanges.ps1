<#
.SYNOPSIS
Connects to Microsoft Graph, queries Entra ID Audit Logs for group membership changes, and optionally
performs extended searches in Entra ID user activity and the M365 Unified Audit Log (UAL).

.DESCRIPTION
This script provides a multi-layered approach to auditing group membership changes for SOC audit purposes.
1. Installs/Connects to Microsoft Graph with necessary permissions.
2. Finds the group, determines its type (Dynamic/Static), and displays details.
3. Queries Entra ID Directory Audit Logs for 'Add member to group'/'Remove member from group'.
4. Filters results locally for the target group.
5. Extracts details: Timestamp, Action, Performed By, User Modified, Result, Correlation ID.
6. (-SearchRelatedUserActivity - Default:True) Searches Entra ID Directory Audit Logs for preceding activity targeting the modified user.
7. (-CheckEntitlementManagement - Default:False) Checks if the group is linked to Entitlement Management Access Packages.
8. **NEW:** (-SearchUnifiedAuditLog - Default:False) Searches the M365 Unified Audit Log (UAL) via ExchangeOnline connection
   for *any* activity involving the modified user in the preceding window. Requires EXO connection and UAL permissions.
9. Displays results from each enabled search layer separately.

!!! VERY IMPORTANT WARNINGS !!!
* ENABLING [-SearchUnifiedAuditLog] REQUIRES an active Exchange Online PowerShell connection AND permissions to search the UAL (e.g., 'Audit Logs' role).
* BOTH [-SearchRelatedUserActivity] and [-SearchUnifiedAuditLog] perform additional, potentially large API queries for EACH system-initiated event found.
* Using these switches, especially with large groups or long date ranges/search windows, WILL BE **EXTREMELY SLOW** and consume significant API resources, potentially hitting throttling limits.
* This script provides data points but CANNOT GUARANTEE finding the definitive root cause for all scenarios due to complexity, logging gaps, or asynchronous operations. Manual investigation across portals is often required for SOC certainty.

.PARAMETER GroupName
Mandatory. Display Name or Object ID (GUID) of the group.

.PARAMETER StartDate
Start date for *group change* audit search. Defaults 29 days ago.

.PARAMETER EndDate
End date for *group change* audit search. Defaults now.

.PARAMETER TargetUserName
Optional. UPN or Object ID of a specific user to filter main results for.

.PARAMETER SearchRelatedUserActivity
Optional Switch. [Default:True] Searches Entra ID logs for preceding activity for the modified user. (Recommended)

.PARAMETER UserActivitySearchWindowHours
Optional Int. [Default:6] Hours *before* group change event to search Entra logs for user activity.

.PARAMETER CheckEntitlementManagement
Optional Switch. [Default:False] Checks EM linkage. Requires 'EntitlementManagement.Read.All'.

.PARAMETER SearchUnifiedAuditLog
Optional Switch. [Default:False] !! VERY SLOW / REQUIRES EXO + UAL PERMS !! Searches M365 Unified Audit Log for preceding user activity.

.PARAMETER UnifiedAuditLogSearchWindowHours
Optional Int. [Default:6] Hours *before* group change event to search UAL for user activity.


.EXAMPLE
.\CheckForDistroChanges.ps1 -GroupName "QHR Former Employees"

Description: Default run. Audits group changes, searches related user activity in Entra logs (6hr window).

.EXAMPLE
.\CheckForDistroChanges.ps1 -GroupName "App Users" -SearchRelatedUserActivity:$false -SearchUnifiedAuditLog -UnifiedAuditLogSearchWindowHours 12

Description: Audits group changes, skips Entra user search, searches UAL for user activity in 12hr window prior. (Requires EXO connection + UAL Perms).

.OUTPUTS
Outputs detailed results tables for Group Changes, Related Entra User Activity, EM Context, and optionally Related Unified Audit Log Activity.

.NOTES
Version: 1.10
Author: Slader Sheppard
Date: 2025-04-14
ChangeLog: v1.10 - Added optional -SearchUnifiedAuditLog parameter and logic using Search-UnifiedAuditLog.
                  Added check for EXO connection when UAL search enabled. Added strong warnings.
Requires: Microsoft.Graph, ExchangeOnlineManagement (if using -SearchUnifiedAuditLog)
Permissions: AuditLog.Read.All, Group.Read.All, User.Read.All, Directory.Read.All
             (Optional: EntitlementManagement.Read.All if using -CheckEntitlementManagement)
             (Optional: ExchangeOnline connection + UAL Search Permissions if using -SearchUnifiedAuditLog)
#>
param (
    [Parameter(Mandatory=$true)] [string]$GroupName,
    [Parameter()] [datetime]$StartDate = (Get-Date).AddDays(-29),
    [Parameter()] [datetime]$EndDate = (Get-Date),
    [Parameter()] [string]$TargetUserName,
    [Parameter()] [switch]$SearchRelatedUserActivity = $true,
    [Parameter()] [int]$UserActivitySearchWindowHours = 6,
    [Parameter()] [switch]$CheckEntitlementManagement = $false,
    [Parameter()] [switch]$SearchUnifiedAuditLog = $false,
    [Parameter()] [int]$UnifiedAuditLogSearchWindowHours = 6
)

# --- Script Start ---

# --- Step 1: Prerequisites - Module Installation and Connection ---
Write-Host "--- Step 1: Checking/Installing Microsoft Graph SDK and Connecting ---" -ForegroundColor Cyan
$mainGraphModule = "Microsoft.Graph"; try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 } catch { Write-Warning "TLS 1.2 set failed." }
$module = Get-Module -ListAvailable -Name $mainGraphModule; if (-not $module) { Write-Host "Installing '$mainGraphModule'..." -FG Yellow; try { Install-Module -Name $mainGraphModule -Repo PSGallery -Force -Scope CurrentUser -Confirm:$false -AllowClobber -EA Stop } catch { Write-Error "Install failed: $($_.Exception.Message)" ; return } } else { Write-Host "'$mainGraphModule' installed." -FG Green }
$requiredScopes = @("AuditLog.Read.All", "Group.Read.All", "User.Read.All", "Directory.Read.All"); if ($CheckEntitlementManagement) { $requiredScopes += "EntitlementManagement.Read.All" }
Write-Host "Checking Graph connection..."; $currentSession = Get-MgContext -EA SilentlyContinue; $missingScopes = @()
if ($currentSession) { Write-Host "Graph Connected as '$($currentSession.Account)'." -FG Green; $grantedScopes = $currentSession.Scopes ; $requiredScopes | % { $reqScope = $_; if ($grantedScopes -notcontains $reqScope) { $missingScopes += $reqScope } } } else { Write-Host "Graph Not connected." -FG Yellow ; $missingScopes = $requiredScopes }
if ($missingScopes.Count -gt 0) { Write-Host "Connecting to Graph (Scopes: $($missingScopes -join ', '))..." -FG Yellow; if($CheckEntitlementManagement -and ($missingScopes -contains "EntitlementManagement.Read.All")){ Write-Warning "Requires EntitlementManagement.Read.All scope."}; try { Connect-MgGraph -Scopes $missingScopes -EA Stop } catch { Write-Error "Graph Connect failed: $($_.Exception.Message)" ; return } Write-Host "Graph Connected." -FG Green } else { Write-Host "Graph Connection active." -FG Green }

# Check EXO connection IF UAL Search is requested
$isExoConnected = $false
if ($SearchUnifiedAuditLog) {
    Write-Host "Checking Exchange Online connection (required for -SearchUnifiedAuditLog)..."
    try {
        # Use a lightweight cmdlet that requires EXO connection
        Get-ConnectionInformation -ErrorAction Stop | Out-Null
        $isExoConnected = $true
        Write-Host "Exchange Online Connection Detected." -ForegroundColor Green
    } catch {
        Write-Warning "Exchange Online connection NOT detected or failed check."
        Write-Warning "Unified Audit Log search (-SearchUnifiedAuditLog) will be skipped."
        Write-Warning "Connect using 'Connect-ExchangeOnline' in this console and re-run if needed."
        # We don't exit, just disable the UAL search for this run
        $SearchUnifiedAuditLog = $false
    }
}


# --- Step 2: Resolve Group Details and User IDs ---
Write-Host "`n--- Step 2: Finding Group Details and Target User ---" -ForegroundColor Cyan
$targetGroup = $null; $targetGroupId = $null; $isDynamicGroup = $false; $dynamicGroupRule = "N/A"; $dynamicGroupState = "N/A"
try { Write-Host "Searching group '$GroupName'..."; $props="Id,DisplayName,GroupTypes,MembershipRule,MembershipRuleProcessingState"; $targetGroup = Get-MgGroup -Filter "DisplayName eq '$GroupName'" -Property $props -EA SilentlyContinue; if (-not $targetGroup) { Write-Host "Lookup by ID..."; $targetGroup = Get-MgGroup -GroupId $GroupName -Property $props -EA Stop } elseif ($targetGroup.Count -gt 1) { Write-Warning "Multiple groups match. Using first."; $targetGroup = $targetGroup[0] } $targetGroupId = $targetGroup.Id; Write-Host "Found group: '$($targetGroup.DisplayName)' (ID: $targetGroupId)" -FG Green; if ($targetGroup.GroupTypes -contains "DynamicMembership") { $isDynamicGroup = $true; $dynamicGroupRule = $targetGroup.MembershipRule; $dynamicGroupState = $targetGroup.MembershipRuleProcessingState; Write-Host " Group Type: Dynamic | Rule: $dynamicGroupRule | State: $dynamicGroupState" -FG Yellow } else { Write-Host " Group Type: Assigned (Static)" -FG Green } } catch { Write-Error "Failed find/get group '$GroupName'. Error: $($_.Exception.Message)" ; return }
$targetUserId = $null; if ($PSBoundParameters.ContainsKey('TargetUserName')) { try { Write-Host "Searching user '$TargetUserName'..."; $targetUser = Get-MgUser -Filter "UserPrincipalName eq '$TargetUserName'" -EA SilentlyContinue; if (-not $targetUser) { Write-Host "Lookup by ID..."; $targetUser = Get-MgUser -UserId $TargetUserName -EA Stop } if ($targetUser.Count -gt 1) { Write-Warning "Multiple users match. Using first."; $targetUser = $targetUser[0] } elseif (-not $targetUser) { Write-Error "Could not find user '$TargetUserName'." ; $TargetUserName = $null } else { $targetUserId = $targetUser.Id ; Write-Host "Found user: '$($targetUser.UserPrincipalName)' (ID: $targetUserId)" -FG Green } } catch { Write-Warning "Failed find user '$TargetUserName'. No user filter. Error: $($_.Exception.Message)" ; $TargetUserName = $null } }


# --- Step 3: Query Audit Logs (Broad Query) ---
Write-Host "`n--- Step 3: Querying Entra ID Audit Logs (Broad Query) ---" -ForegroundColor Cyan
$graphStartDate = $StartDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"); $graphEndDate = $EndDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
$filter = "(activityDisplayName eq 'Add member to group' or activityDisplayName eq 'Remove member from group') and (activityDateTime ge $graphStartDate and activityDateTime le $graphEndDate)"
Write-Host "Searching Entra logs from $graphStartDate to $graphEndDate..."; Write-Warning "Retrieving all Add/Remove events & filtering locally..."; Write-Verbose "Filter: $filter"
$allAuditEvents = $null; try { Write-Host "Retrieving Entra audit events..."; $allAuditEvents = Get-MgAuditLogDirectoryAudit -Filter $filter -Sort "activityDateTime desc" -All -EA Stop } catch { Write-Error "Entra Audit Log Query Failed. Error: $($_.Exception.Message)"; return }
if (-not $allAuditEvents) { Write-Host "No Entra Add/Remove member events found." -FG Yellow; return } Write-Host "Retrieved $($allAuditEvents.Count) Entra Add/Remove events."


# --- Step 4: Filter Locally and Process Detailed Results (with Optional Secondary Searches) ---
Write-Host "`n--- Step 4: Filtering Locally & Processing Results (with Optional Secondary Searches) ---" -ForegroundColor Cyan
Write-Host "Filtering locally for group '$($targetGroup.DisplayName)'..."
$filteredEvents = $allAuditEvents | Where-Object { ($_.TargetResources | Where-Object {$_.Id -eq $targetGroupId}) -ne $null }
$eventCount = $filteredEvents.Count; Write-Host "Found $eventCount event(s) for target group."
if ($eventCount -eq 0) { Write-Host "No events found for group '$($targetGroup.DisplayName)'." -FG Yellow; return }

# Display Warnings for Enabled Secondary Searches
if ($SearchRelatedUserActivity) { Write-Warning "Processing $eventCount events. Entra User Activity search ENABLED (Window: $UserActivitySearchWindowHours hr(s))." }
if ($CheckEntitlementManagement) { Write-Warning "Processing $eventCount events. Entitlement Management check ENABLED."}
if ($SearchUnifiedAuditLog) { Write-Warning "Processing $eventCount events. Unified Audit Log search ENABLED (Window: $UnifiedAuditLogSearchWindowHours hr(s)). REQUIRES EXO Connection & Perms."}
if ($SearchRelatedUserActivity -or $CheckEntitlementManagement -or $SearchUnifiedAuditLog) { Write-Warning "!! SECONDARY SEARCHES ENABLED - EXPECT SIGNIFICANTLY INCREASED RUNTIME & API USAGE !!"}

$results = @()
$progressCount = 0
$knownSystemActors = @("Microsoft Substrate Management", "Microsoft Lifecycle Management", "Unknown/System", "Microsoft Entra Connect Sync")

foreach ($event in $filteredEvents) {
    $progressCount++; Write-Progress -Activity "Processing Events for Group '$($targetGroup.DisplayName)'" -Status "Event $progressCount/$eventCount (ID: $($event.Id))" -PercentComplete (($progressCount / $eventCount) * 100)

    # Extract PerformedBy, UserModified (condensed logic)
    $performedBy = $event.InitiatedBy.User.UserPrincipalName; if (-not $performedBy) { $performedBy = $event.InitiatedBy.App.DisplayName }; if (-not $performedBy) { $performedBy = $event.InitiatedBy.User.Id }; if (-not $performedBy) { $performedBy = "Unknown/System" }
    $userModifiedPrincipalName = $null; $userModifiedId = $null; $userModifiedInfo = "Unknown User"
    $userTarget = $event.TargetResources | ? { $_.Id -ne $targetGroupId -and $_.Type -eq 'User' }; if ($userTarget) { $userModifiedPrincipalName = $userTarget.UserPrincipalName; $userModifiedId = $userTarget.Id } else { $modProps = $event.TargetResources | ? {$_.ModifiedProperties} | Select -Expand ModifiedProperties; $memberChange = $modProps | ? {$_.DisplayName -eq 'Group.Members'}; if ($memberChange) { $json = if ($event.ActivityDisplayName -eq 'Add member to group') { $memberChange.NewValue } else { $memberChange.OldValue }; try { $d = $json | ConvertFrom-Json -EA Stop; if ($d -is [array]) { $d = $d[0] }; if ($d.PSObject.Properties.Name -contains 'ID') { $userModifiedId = $d.ID; if ($d.PSObject.Properties.Name -contains 'userPrincipalName') { $userModifiedPrincipalName = $d.userPrincipalName } } } catch { Write-Verbose "Event $($event.Id): JSON parse fail. Err: $($_.Exception.Message)"} } }
    if ($userModifiedPrincipalName) { $userModifiedInfo = $userModifiedPrincipalName } elseif ($userModifiedId) { $userModifiedInfo = $userModifiedId }

    # --- Optional Secondary Searches ---
    $RelatedUserEventsDetails = $null ; $EntitlementManagementContext = "N/A (Check Disabled)"; $UnifiedAuditLogDetails = $null

    # 1. Related User Activity Search (Entra Logs)
    if ($SearchRelatedUserActivity -and ($knownSystemActors -contains $performedBy) -and $userModifiedId) {
        Write-Verbose "Event $($event.Id): Actor system. Searching Entra logs for user '$userModifiedInfo' (ID: $userModifiedId) in prev $UserActivitySearchWindowHours hr(s)..."
        $uaEndTime = $event.ActivityDateTime; $uaStartTime = $uaEndTime.AddHours(-$UserActivitySearchWindowHours); $uaStartTimeUtc = $uaStartTime.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"); $uaEndTimeUtc = $uaEndTime.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        $uaFilter = "(targets/any(t:t/id eq '$userModifiedId')) and (activityDateTime ge $uaStartTimeUtc and activityDateTime le $uaEndTimeUtc) and (id ne '$($event.Id)')"; $relatedUserEvents = $null
        try { $relatedUserEvents = Get-MgAuditLogDirectoryAudit -Filter $uaFilter -Sort "activityDateTime desc" -All -EA SilentlyContinue; if ($relatedUserEvents) { Write-Verbose " Found $($relatedUserEvents.Count) Entra event(s) for user."; $RelatedUserEventsDetails = @(); foreach($relEvent in $relatedUserEvents | Sort ActivityDateTime) { $relActor = $relEvent.InitiatedBy.User.UserPrincipalName; if (-not $relActor) { $relActor = $relEvent.InitiatedBy.App.DisplayName }; if (-not $relActor) { $relActor = "Other System" }; $relTargetDesc = ($relEvent.TargetResources | % { $_.DisplayName -or $_.UserPrincipalName -or $_.Id }) -join ', '; $modPropsInfo = "N/A"; if ($relEvent.ActivityDisplayName -eq 'Update user') { $modPropsList = $relEvent.TargetResources | ? {$_.ModifiedProperties} | Select -Expand ModifiedProperties; $propStrings = @(); foreach ($prop in $modPropsList) { $propStrings += "  $($prop.DisplayName) = $($prop.NewValue ?? '[Removed/Empty]')" }; $modifiedPropsInfo = $propStrings -join [Environment]::NewLine; if (-not $modifiedPropsInfo) {$modifiedPropsInfo = "[Props Not Parsed/Empty]"} }; $RelatedUserEventsDetails += [PSCustomObject]@{ RelTime=$relEvent.ActivityDateTime; RelAction=$relEvent.ActivityDisplayName; RelActor=$relActor; RelResult=$relEvent.Result; RelTarget=$relTargetDesc; RelModProps=$modifiedPropsInfo } } } else { Write-Verbose " No related Entra user activity found."; $RelatedUserEventsDetails = $null } } catch { Write-Warning "Event $($event.Id): Entra User activity search failed. Error: $($_.Exception.Message)"; $RelatedUserEventsDetails = @([PSCustomObject]@{Info='Error searching Entra Logs'}) }
    } elseif ($SearchRelatedUserActivity -and (-not $userModifiedId)) { Write-Warning "Event $($event.Id): Cannot search Entra user activity because UserModified ID unknown." }

    # 2. Entitlement Management Context Check
    if ($CheckEntitlementManagement) { $EntitlementManagementContext = "Group NOT in any AP resources checked."; try { Write-Verbose "Event $($event.Id): Checking EM context..."; $relatedAPs = Get-MgEntitlementManagementAccessPackage -Filter "resources/any(r:r.originId eq '$targetGroupId')" -ExpandProperty resources -Top 1 -EA SilentlyContinue; if ($relatedAPs) { $EntitlementManagementContext = "Group IS LINKED to AP '$($relatedAPs.DisplayName)' (ID: $($relatedAPs.Id))." } } catch { Write-Warning "Event $($event.Id): EM check failed. Error: $($_.Exception.Message)"; $EntitlementManagementContext = "Error during EM check." } }

    # 3. Unified Audit Log Search
    if ($SearchUnifiedAuditLog -and $isExoConnected -and ($knownSystemActors -contains $performedBy) -and $userModifiedId) {
        # Use UPN for UserIds parameter if available, otherwise fall back to ID
        $ualUserIdToSearch = if ($userModifiedPrincipalName) { $userModifiedPrincipalName } else { $userModifiedId }
        Write-Verbose "Event $($event.Id): Actor system. Searching UAL for '$ualUserIdToSearch' in prev $UnifiedAuditLogSearchWindowHours hr(s)..."
        $ualEndTime = $event.ActivityDateTime; $ualStartTime = $ualEndTime.AddHours(-$UnifiedAuditLogSearchWindowHours)
        $ualResults = $null; $UnifiedAuditLogDetails = @([PSCustomObject]@{Info = 'Searching...'}) # Placeholder
        try {
            # Search UAL - Limit results heavily initially to avoid extreme delays/failures
            $ualResults = Search-UnifiedAuditLog -StartDate $ualStartTime -EndDate $ualEndTime -UserIds $ualUserIdToSearch -ResultSize 500 -SessionCommand ReturnLargeSet -ErrorAction Stop
            if ($ualResults) {
                 Write-Verbose " Found $($ualResults.Count) UAL event(s) for user '$ualUserIdToSearch'. Processing first 500..."
                 $UnifiedAuditLogDetails = @()
                 $ualCounter = 0
                 foreach($ualEvent in $ualResults | Sort-Object CreationDate) {
                     # Basic parsing - AuditData structure varies wildly! Requires manual inspection for specifics.
                     $auditDataObj = $null; try { $auditDataObj = $ualEvent.AuditData | ConvertFrom-Json -Depth 5 -ErrorAction SilentlyContinue } catch {}
                     $ualActor = $auditDataObj.UserId -or ($ualEvent.UserIds -join ';') # Actor from AuditData preferred
                     $ualTarget = $auditDataObj.ObjectId -or $auditDataObj.Item -or $auditDataObj.DestinationFileName -or "See AuditData" # Heuristic for target
                     $ualClientIP = $auditDataObj.ClientIP -or $ualEvent.ClientIPAddress

                     $UnifiedAuditLogDetails += [PSCustomObject]@{
                         UALTime = $ualEvent.CreationDate
                         UALOp = $ualEvent.Operations
                         UALService = $auditDataObj.Workload -or $ualEvent.RecordType # Service (Exchange, SharePoint etc)
                         UALActor = $ualActor
                         UALTarget = $ualTarget
                         UALClientIP = $ualClientIP
                         UALAuditData = $ualEvent.AuditData # Include raw data for manual inspection
                     }
                     $ualCounter++
                     # Limit processed results display if too many are returned to keep output manageable
                     if($ualCounter -ge 50) { Write-Warning "Stopped processing UAL results after 50 for event $($event.Id) to keep output concise."; break }
                 }
            } else { Write-Verbose " No relevant UAL events found."; $UnifiedAuditLogDetails = $null }
        } catch { Write-Warning "Event $($event.Id): UAL search failed. Check EXO connection/UAL permissions/parameters. Error: $($_.Exception.Message)"; $UnifiedAuditLogDetails = @([PSCustomObject]@{Info='UAL Search Error'}) }
    } elseif ($SearchUnifiedAuditLog -and (-not $isExoConnected)) { $UnifiedAuditLogDetails = @([PSCustomObject]@{Info='EXO Not Connected'})}
    elseif ($SearchUnifiedAuditLog -and (-not $userModifiedId)) { $UnifiedAuditLogDetails = @([PSCustomObject]@{Info='User ID Unknown'})}
    elseif (-not $SearchUnifiedAuditLog) { $UnifiedAuditLogDetails = $null } # Ensure null if not searched

    # --- End Secondary Searches ---

    $includeEvent = $true; if ($targetUserId) { if (-not $userModifiedId -or $userModifiedId -ne $targetUserId) { $includeEvent = $false } }
    if ($includeEvent) { $results += [PSCustomObject]@{ Time=$event.ActivityDateTime; Action=$event.ActivityDisplayName; PerformedBy=$performedBy; UserModified=$userModifiedInfo; UserModifiedID=$userModifiedId; Result=$event.Result; TargetGroup=$targetGroup.DisplayName; CorrelationId=$event.CorrelationId; EventId=$event.Id; RelatedUserEventsDetails=$RelatedUserEventsDetails; EntitlementManagementContext=$EntitlementManagementContext; UnifiedAuditLogDetails = $UnifiedAuditLogDetails } }

} # End foreach filtered event loop
Write-Progress -Activity "Processing Events" -Completed

# --- Step 5: Display Results and Context ---
Write-Host "`n--- Step 5: Audit Log Results for Group Membership Changes ---" -ForegroundColor Cyan
Write-Host "---------------------------------------------------------------"
if ($results.Count -gt 0) {
    $resultCount = $results.Count; Write-Host "Displaying $resultCount main audit event(s) for group '$($targetGroup.DisplayName)'." -FG Green; if ($TargetUserName) { Write-Host "Filtered for target user '$TargetUserName'." -FG Green}
    # Main Table - Keep concise
    $results | Select-Object Time, Action, PerformedBy, UserModified, Result, EntitlementManagementContext | Format-Table -AutoSize

    # Related Entra User Activity Details
    $eventsWithEntraDetails = $results | Where-Object {$_.RelatedUserEventsDetails -ne $null -and $_.RelatedUserEventsDetails.Count -gt 0}
    if ($SearchRelatedUserActivity -and $eventsWithEntraDetails) {
        Write-Host "`n--- Related Entra User Activity Details (Found in Prev. $UserActivitySearchWindowHours Hour(s)) ---" -ForegroundColor Cyan
        Write-Host "Investigate these preceding events for potential triggers (esp. 'Update user' details in 'RelModProps')."
        Write-Host "-------------------------------------------------------------------------------------------------"
        foreach ($result in $eventsWithEntraDetails) { Write-Host "`n[+] For Group Change at $($result.Time) | User: $($result.UserModified) | By: $($result.PerformedBy)" -ForegroundColor Yellow; $result.RelatedUserEventsDetails | Select-Object RelTime, RelAction, RelActor, RelResult, RelTarget, RelModProps | Format-Table -AutoSize -Wrap }
        Write-Host "-------------------------------------------------------------------------------------------------" -ForegroundColor Cyan
    } elseif ($SearchRelatedUserActivity) { Write-Host "`n--- Related Entra User Activity Details ---" -ForegroundColor Cyan; Write-Host "No preceding Entra user activity found or search disabled."; Write-Host "-------------------------------------" -ForegroundColor Cyan }

    # Related Unified Audit Log Details
    $eventsWithUALDetails = $results | Where-Object {$_.UnifiedAuditLogDetails -ne $null -and $_.UnifiedAuditLogDetails.Count -gt 0 -and ($_.UnifiedAuditLogDetails[0].Info -eq $null) } # Check Info property isn't just placeholder/error
    if ($SearchUnifiedAuditLog -and $eventsWithUALDetails) {
        Write-Host "`n--- Related Unified Audit Log Activity (Found in Prev. $UnifiedAuditLogSearchWindowHours Hour(s)) ---" -ForegroundColor Cyan
        Write-Host "Shows broader M365 activity for the user. Inspect 'UALAuditData' manually for full details if needed."
        Write-Host "----------------------------------------------------------------------------------------------------"
        foreach ($result in $eventsWithUALDetails) {
            Write-Host "`n[+] For Group Change at $($result.Time) | User: $($result.UserModified) | By: $($result.PerformedBy)" -ForegroundColor Yellow
            # Display UAL summary table - AuditData excluded for brevity, check $result.UnifiedAuditLogDetails.UALAuditData manually
            $result.UnifiedAuditLogDetails | Select-Object UALTime, UALOp, UALService, UALActor, UALTarget, UALClientIP | Format-Table -AutoSize -Wrap
        }
        Write-Host "----------------------------------------------------------------------------------------------------" -ForegroundColor Cyan
    } elseif ($SearchUnifiedAuditLog) { Write-Host "`n--- Related Unified Audit Log Activity ---" -ForegroundColor Cyan; Write-Host "No preceding UAL activity found, search disabled, EXO not connected, or error occurred."; Write-Host "--------------------------------------" -ForegroundColor Cyan }


    # Display Context Message for System Actions
    if ($results.PerformedBy -match "Microsoft Substrate Management|Microsoft Lifecycle Management|Unknown/System|Microsoft Entra Connect Sync") {
         Write-Host "`n--- Context for System Actions ('PerformedBy') ---" -ForegroundColor Cyan; Write-Host " System accounts indicate automated processes."; if ($CheckEntitlementManagement) { Write-Host " EM check result shown in main table." }; if ($SearchRelatedUserActivity){ Write-Host " Preceding Entra user activity details shown above."} else {Write-Host " Entra user activity search disabled."}; if ($SearchUnifiedAuditLog){ Write-Host " Preceding Unified Audit Log activity details shown above."} else {Write-Host " Unified Audit Log search disabled."}; if ($isDynamicGroup) { Write-Host " '$($targetGroup.DisplayName)' is DYNAMIC (Rule: $dynamicGroupRule)."} else { Write-Host " '$($targetGroup.DisplayName)' has STATIC membership." }; Write-Host " Analyze related Entra/UAL activity details (esp. Modified Properties or EM info) for trigger clues."
    }

    # SOC Audit Limitation Note
    Write-Host "`n--- IMPORTANT NOTE FOR AUDIT PURPOSES ---" -ForegroundColor Yellow; Write-Host "This script provides data from Entra ID Directory Audits & optionally related Entra/UAL searches."; Write-Host "It aims to surface preceding activity or context that *might* explain system actions."; Write-Host "However, definitively proving the root cause for SOC audits may require:"; Write-Host " * Manual correlation across different time windows or log sources (Entra, Purview, EM, Apps)."; Write-Host " * Analysis of specific configurations (EM Policies, Lifecycle Workflows, HR sync rules, custom apps)."; Write-Host " * Understanding your tenant's specific operational procedures."; Write-Host "This script is an investigation tool but cannot guarantee finding the absolute trigger in every complex scenario."; Write-Host "-------------------------------------------" -ForegroundColor Yellow

} else { Write-Host "No audit log events found matching criteria." -FG Yellow; if ($TargetUserName) { Write-Host "(Filtered for user '$TargetUserName')" -FG Yellow} }
Write-Host "---------------------------------------------------------------"
Write-Host "Script finished."

# --- Script End ---