﻿**Version 3.10.1 [05/03/2024]**
- Added Turkish language support. Fixes #973.
- Added parameter sets to Configure-EdgeExtensions to ensure mandatory parameters are correctly grouped.
- Added missing elements to XML for Finnish language. Fixes #967.
- Added -ExcludeServiceProfiles to Get-UserProfiles, add passthru of Exclude-ServiceProfiles/ExcludeSystemProfiles/ExcludeDefaultUser/ExcludeNTAccount to Copy-FileToUserProfiles & Remove-FileFromUserProfiles.
- Added back the folder structure, changelog, license, and examples to the release zip file.
- Added VLC and WinSCP as new example deployment scripts.
- Added ServiceUI launch helper script and launch testing batch files to examples.
- Added Contributing Guide, Code of Conduct, Security Policy and Support docs.
- Added GitHub form-based Bug Report and Feature Request templates.
- Added RoboCopyParams to Copy-File, which allows full override of Robocopy Parameters. Added /IT /IM to default parameters.
- Changed Welcome Prompt so that the Deferral Time Remaining is now bolded. App Title size was also slightly increased so as to remain more prominent.
- Changed button font so that text wasn't cut off for some localized languages. Fixes #980.
- Changed RunHidden.vbs to get rid of use of ArrayList as it caused problems on some systems.
- Changed Robocopy folder creation code to match native PowerShell Copy-File.
- Changed Execute-ProcessAsUser to include an example for executing the function using "LeastPrivilege".
- Fixed an issue where Execute-ProcessAsUser was not able to execute with RunLevel = 'LeastPrivilege' in some cases.
- Fixed an issue in Execute-ProcessAsUser where the command line for two of the examples was incorrect.
- Fixed an issue with Configure-EdgeExtensions where a new extension would not be added if no extensions were configured previously. Fixes #931 and #984.
- Fixed an issue where Robocopy mode of Copy-File fails to copy files or folders when path begis with ".\" or "..\". Fixes #938.
- Fixed AppDeployToolkitExtensions.ps1 dot-sourcing to where it's always been dot-sourced from.
- Fixed PowerShell v2 and .NET 3.5 Compatibility. Fixes #948.
- Removed check on SecurityHealthSystray. Fixes #941.
- Removed the PDF documentation, the website will be the primary source for this going forward.
- Updated existing example deployment scripts.
- Updated Pull Request template.
- Updated Spanish translations. Fixes #805.

**Version 3.10.0 [03/27/2024]**
- Added the ability to configure Microsoft Edge Extensions using ExtensionSettings. Function: Configure-EdgeExtensions. This enables Edge Extensions to be installed and managed like applications, enabling extensions to be pushed to specific devices or users alongside existing GPO/Intune extension policies. This should not be used in conjunction with Edge Management Service which leverages the same registry key to configure Edge extensions.
- Added the ability to copy the Toolkit folder and files to a cache folder on the local machine and execute from there.
    Functions: Copy-ContentToCache and Remove-ContentFromCache. The cache path can be configured in the config.xml file, default is $envProgramData\SoftwareCache. When Copy-ContentToCache is used, $dirFiles is updated to point to the local cache path.
- Added the ability to copy files to all user profiles.
    Functions: Copy-FileToUserProfiles and Remove-FileFromUserProfiles
- Changed Copy-File to use Robocopy by default, overcoming the 260 character limitation of native PowerShell Copy-Item and providing more comprehensive file copy functionality and logging. This can be toggled off in the config.xml file by setting Toolkit_UseRobocopy = $false
- Added Log File Rotation
- Added VSCode workspace with default settings for PowerShell code formatting, default version (Windows PowerShell) and PSScriptAnalyzer linting.
- Added ability to position progress dialogs to 'TopLeft', 'Top', 'TopRight', 'TopCenter', 'BottomLeft', 'Bottom', 'BottomRight'
- Added -Wow6432Node switches for 32-bit registry support to all registry functions.
- Improved all user interface components to work correctly in High DPI mode.
- Improved all images and icons for brand consistency and double the resolution for High DPI mode.
- Improved Get-RunningProcesses function, reducing complexity and improving performance
- Improved Russian (RU) translations PR #843
- Improved German (DE) translations PR #854
- Added translations for many missing strings across a number of different languages. Thanks to ChatGPT for being an XML workhorse :)
- Added shortcut keys for UI buttons - Alt-D for Defer, Alt-P for Close Programs, Alt-C for Continue
- Fixed issue with Execute-MSI adding "Transforms" Parameters to uninstall Command Line #828
- Resolved an issue where ConvertTo-NTAccountOrSID would throw an error when attempting to convert orphaned accounts, a warning is now displayed rather than an error. #865
- Resolved an issue where if WindowStyle parameter is set to 'Hidden', then the UseShellExecute parameter to should be set to '$true' #678
- Resolved an issue with how Set-ActiveSetup comma-separated version strings were compared to determine which was greater #743
- Resolved an issue where $IsMultiSessionOS did not return true for Windows 11 Enterprise Multi-Session #807
- Improved detection of OOBE / ESP
- Improved documentation of Set-ActiveSetup function.
- Resolved an issue where Execute-ProcessAsUser ran into issue when called multiple times because it overwrote files from previous invocation #333
- Resolved an issue where Get-LoggedOnUser choose the wrong user session on Windows multi-session workstation operating systems #863
- Improved Execute-Process with a useful example #835
- Improved Execute-MSI so that all repetitive references to .log or .txt file extension in $LogName variable are all removed #759
- Improved Get-HardwarePlatform to add support for detecting Parallels virtual machines #838
- Improved Execute-ProcessAsUser and how it parses and executes command-line arguments. #794 #894 #782 #762 #851
- Improved setup for $ResolveParameters (uses proper filter and piping, supports arrays and dictionaries) #874
- Improved setup for setting $ScriptSource in Write-Log.
- Improve $envOffice variable setup by reducing the number of Get-ItemProperty calls.
- Factor in GPO-configured UpdateChannel property vs. CDNBaseURL property for $envOfficeChannel. #837
- Add "monthly enterprise" UUID to $envOfficeChannel setup.
- Changed when dot-sourcing of AppDeployToolkitExtensions occurs, so that use of custom functions can make use of UI elements, e.g. displaying a progress dialog.
- Improved overall codebase for better compliance with Strict Mode.
- Improved use of arrays throughout in order to improve performance.
- Improved threat safety when using thread on user interface elements should result in fewer issues with progress dialogs.
- Fixed UI message strings being incorrectly spaced on screen.

**Version 3.9.3 [01/05/2023]**
- Improved accuracy of Intune Provisioning/ESP detection #779 #801
- Resolved an issue where Show-InstallationRestartPrompt would not work with -NoSilentRestart $false #785
- Resolved an issue where Execute-ProcessAsUser would not work correctly since 3.9.2 #749 #776 #784
- Resolved an issue where Execute-ProcessAsUser Scheduled Task is not removed after execution since 3.9.0 #791 #793
- Resolved an issue where 3.9.2 would fail on devices running PowerShell versions below 5.1 due to the function Get-ComputerInfo #765
- Resolved an issue where Execute-ProcessAsUser would fail due to a whitespace in the $installName if called prior to the Initialization phase #788
- Resolved an issue where the status text on Show-InstallationProgress was not centered #780
- Fixed French translation for WelcomePrompt_CountdownMessage #796
- Added XML configuration option <Toast_Disable> to turn toast notifications off, default value is TRUE, thus reverting to balloon tip notifications as standard until stability is reached in the toast function.
- Added XML configuration option <Toast_AppName> to define the standard app display name of the toast notification #778

**Version 3.9.2 [06/02/2023]**
- Toast notifications now show the custom logo in the notification header (#745)
- Added variables for Office C2R: $envOfficeVersion, $envOfficeBitness, $envOfficeCDNBaseURL, $envOfficeChannel
- Resolved an issue where toast notifications invoked from Show-BalloonTip would not work when running as a standard user in scenarios where a standard user does not have access to the $envTemp directory or to the profile of another user. (#741)
- Resolved an issue where Execute-ProcessAsUser fails due to an unexpected VBS file association. (#741)
- Resolved an issue where the script would fail on Windows Home editions due to the check for Power Users Group which doesn't exist on Home editions. (#728)
- Removed the (cosmetic) console output related to creation of a temporary directory. (#739)

**Version 3.9.1 [20/01/2023]**
- Resolved an issue introduced in v3.9.0 where toast notifications would not display when run in the system context (#733)
- Added Finnish language translations to the UI components (#681)
- Added variable $IsMultiSessionOS for virtual environments (#697)
- Add missing dash before TypeName parameter used with New-Object in the in Set-ItemPermission function which resulted in failing to set read permissions on the BlockExecution path. (#721)
- Resolved an issue introduced in v3.8.4 where registry entries set by Block-AppExecution would not be cleaned up after an interrupted installation (#714)
- Resolved an issue where a PowerShell prompt would flash on screen when invoking a PowerShell script using Execute-ProcessAsUser (#735)
- Added the appropriate exit code 1638 for MSIs that are already installed when using the Execute-MSI function with -PassThru (#736)
- Added creation of the $dirAppDeployTemp directory by default so the variable can be used for general actions (#708)

**Version 3.9.0 [10/01/2023]**
- AddedRefreshed UI components with updated PSAppDeployToolkit branding
- Added support for Windows Toast Notifications, instead of using a BalloonTip (only for Windows 10 and above)
- Changes to dialogs so that the default system font is used for all labels and buttons, adding a more consistent look and feel.
- Improved documentation - new overview, features and benefits detail to enhance value proposition. Improved Function documentation and overall PDF document layout.
- Updated text and structure for Comment-Based-Help to align with PowerShell Best Practices
- Added detection of defaultuser0 OOBE or ESP process to enable Non-Interactive deployment mode for Intune Autopilot/ESP scenarios.
- Show-InstallationPrompt now has a -TopMost parameter. Fixed the function failing to display a window with -NoWait parameter specified.
- Show-InstallationWelcome will now display the countdown message if there are no processes running when CloseApps timer is enabled.
- Show-InstallationProgress now supports moving the WindowLocation dynamically for existing progress dialogs (#685). When the WindowLocation is set to BottomRight, the position is now offset so as not to overlap with toast notifications (#685)
- Show-InstallationRestartPrompt now has a -TopMost parameter. Resolved an issue where parameters were not handled correctly when running asynchronously. The titlebar now shows the toolkit icon.
- Fixed vulnerability CWE-428 ensuring quotes are added to ActiveSetup paths with spaces (#726)
- Fixed -FilterApplication logic in Remove-Applications (#665)
- Fixed help text for some functions that contained old/incorrect information
- Fixed $LocalPowerUsersGroup throwing errors on machines where it does not exist (e.g. Domain Controllers)
- Resolved an issue with Set-ActiveSetup where the function would not correctly execute the process for the current user (#644)
- Resolved an issue where Set-ActiveSetup would not correctly evaluate whether to execute while running as system (#707)
- Set-ActiveSetup now also checks Version and IsInstalled properties when attempting to run the process for current user. DisableActiveSetup now also disables ExecuteForCurrentUser. Enhanced logging.
- Execute-Process now shows a shorter error description rather than the full error information in the popup window, the full error details are available in the log.
- Changed Block-AppExecution and Unblock-AppExecution so the functions also run during Non-Interactive installs (#699)
- Fixed a bug that caused Update-Desktop/Refresh-Desktop to crash Powershell on 32bit systems
- Improved calculation of dpi scale and pixels in the $GetDisplayScaleFactor variable.
- Resolved an issue where New-Folder would not create the entire path name in some scenarios.
- CHANGE: If the -ContinueOnError parameter on The Remove-Folder function now stops the script on all errors if the -ContinueOnError parameter is set to $false. The -DisableRecursion parameter now removes everything except non-empty folders.
- Resolved an issue where the $LocalSystemNTAccount variable was not populated.
- Resolved an issue where UNC paths were not handled by New-Shortcut, Set-Shortcut and Get-Shortcut. Minor improvement to $iconIndex logic in New-Shortcut.
- Resolved an issues where the banner file object is locked and cannot be deleted in the same PowerShell session (#717)
- Updated Get-InstalledApplication to make removal of control characters less aggressive (#691)
- Updated entire toolkit to align with toolkit styling and formatting standards
- Updated Execute-MSP functions to utilize toolkit MSI database helper functions
- Added "-Force" and "-ErrorAction" parameters to "Get-ChildItem" calls in "Copy-File" function
- Added Greek translations
- Added the appropriate exit code for MSIs that are already installed to the Execute-MSI function (#686)
- Improved Unblock-AppExecution function removal of the BlockAppExecution Schedule Task XML file
- Updated Set-PinnedApplication to use "OrdinalIgnoreCase" instead of "CurrentCultureIgnoreCase" to ignore case of pin action on Windows 10
- Set the ErrorAction of the "Unregister-Event'" command in the Finally block of the Execute-Process function to "SilentlyContinue"
- Reverted some changes in "Show-InstallationWelcome" and "Show-WelcomePrompt" to maintain compatibility with PowerShell 2.0
- Reverted changes that removed "Select-Object -ExpandProperty" calls to maintain compatibility with PowerShell 2.0
- Reverted unnecessary conversions of Where-Object pipeline pattern to less readable ForEach-Object pattern. Where-Object pattern is not more performant than ForEach-Object (ForEach or For would be, but the micro-optimization is unnecessary for our usecase).
- Reverted Resolve-Error function code to that from v3.8.3 to undo all changes

**Version 3.8.4 [26/01/2021]**
- Fixed Boolean parameters not being passed to Execute-Process
- Changed Show-InstallationWelcome:
  - Buttons are now wider
  - Listbox is also wider so it is aligned with buttons
  - Countdown is now bigger and in bold
  - Appname is now in bold to be more visible
  - Function now internally closes processes by name not by ID
  - Fixed the hidden abort button not working on toolkit timeout
  - Performance improvements
- Changed Show-InstallationPrompt:
  - Fixed the hidden abort button not working on toolkit timeout
  - Buttons are now identical in size and position to the ones in Show-InstallationWelcome
  - Increased the gap between the banner and the text when the message is too short
  - The function now doesn't call Close-InstallationProgress when running in async mode
- Changed Show-InstallationRestartPrompt:
  - Reworked the window to look consistent with Show-InstallationWelcome
- Changed Show-InstallationProgress:
  - Reworked the window to look consistent with Show-InstallationWelcome
  - Function now logs when it is being bypassed due to Deploy Mode
- Changed Set-ActiveSetup:
  - Fixed an issue where the function was adding executed active setup registry key when -ExecuteForCurrentUser $false was specified
  - Function now checks the Active Setup registry key to evaluate whether to execute the StubPath based on the IsInstalled property
- Fixed typo in Stop-ServiceAndDependencies saying "start" instead of "stop"
- Fixed the toolkit checking task scheduler services twice
- Fixed the toolkit not creating $app variables if they are not created in Deploy-Application.ps1
- Reworked Close-InstallationProgress so it waits a couple of seconds in case the window is not opened yet
  - This fixes an issue where the Show-InstallationProgress dialog would not get closed on slower machines where Close-InstallationProgress was called right after Show-InstallationProgress
  - Function now gets bypassed in Silent Deploy Mode
- Changed Execute-ProcessAsUser:
  - The function now escapes invalid xml characters prior to using them in a xml file
- Fixed signatures for a couple of functions inside AppDeployToolkitMain.cs based on official documentation - No breaking changes
- Performance improvements across the toolkit
- Fixed a bug in Set-ItemPermission where SID translation did not work correctly
- Changed Show-BalloonTip:
  - Function Show-BalloonTip now logs when it is being bypassed
  - Added parameter -NoWait for asynchronous execution
  - The icon tooltip now contains Title - Text
- Fixed an issue where $envLogonServer would start with backslashes
- Fixed an issue where some global variable did not get cleared on exit
  - This caused an issue if you ran multiple toolkits in the same powershell session. It would use the same log file for all toolkits.
  - This only occurred if you used the .ps1 files directly. Deploy-Application.exe opens a new session each time.
- Added function Set-Shortcut that allows you to change existing shortcuts
- Added function Get-Shortcut to retrieve information from a shortcut
- Changed New-Shortcut:
  - BREAKING CHANGE: IconIndex parameter is now an Integer. It was previously incorrectly specified as a String.
  - Reworked the way this function sets shortcut to run as admin
  - Added additional checks for the input Path in order to provide better log messages and better handling of relative paths
  - Improved function help text
- Changed the way Execute-MSI handles paths for Transforms and Patches:
  - The only accepted separator is now a semi-colon. Commas can be a part of the paths now.
  - Double quotes are removed from the paths before the entire string is wrapped in double quotes.
- Encoding of the files has been changed back to UTF8 with BOM, back from UTF8 without BOM:
  - We chose to go with UTF8 without BOM because it is the encoding for Powershell v6+ and because it is one of the most popular encodings in the world.
    However this caused issues on Windows Powershell for clients that use a different language from English. Due to Microsoft's chaotic coding decisions, Powershell uses the BOM as a verification that the code is written with non-English characters.
    In their own words:"It's not possible to force PowerShell to use a specific input encoding. PowerShell 5.1 and below, running on Windows with the locale set to en-US, defaults to Windows-1252 encoding when there's no BOM.
    Other locale settings may use a different encoding. To ensure interoperability, it's best to save scripts in a Unicode format with a BOM."
  - Just like changing to UTF8 without BOM, this will not affect the size of the files or their functionality. BOM or Byte Order Mark is just one special character, placed at the start of the files.
    If you do not use the toolkit on machines with a different language, then this will have no effect on you or your clients. It will however help everyone else display the text in their language correctly.

**Version 3.8.3 [01/10/2020]**
- Added function Set-ItemPermission that allows you to easily change permissions on files or folders.
- Added setting READ permissions to BUILTIN\Users for toolkit's temporary folder so they can get the popup message.
- Added -AddParameters for Execute-MSP, that lets you add additional parameters.
- Added -ExecuteForCurrentUser for Set-ActiveSetup, which allows you to disable executing the StubExePath for the current user.
- Added -UseShellExecute for Execute-Process:
  - WindowStyle Hidden doesn't usually work without this parameter on. CreateNoWindow will usually not work without this off.
  - This parameter changes the way the application is started. It is similar to entering a path into the Run dialog or into the Windows Explorer. If it is a file not an application, then the Shell will open it in a program assigned for this extension. The Shell does not stand for command shell but for operating system shell. It also changes how Working Directory is used.
  - If this parameter is set to $true there is no output/error stream. Only an exit code. Default value is $false which was also the case before, however you couldn't change it through parameters previously.
  - Expanded description for WindowStyle, CreateNoWindow and WorkingDirectory parameters.
- Added return value for Execute-Process with -PassThru and -NoWait specified: Returns an object with Id, Handle and ProcessName.
- Added config paths to the config file for installations where RequireAdmin is False.
- Added revised documentation which is now included as a PDF.
- Changed Variables to no longer rely on the system variable PATH for system applications.
- Changed default -Source value for Write-Log to a more useful value: it will be set to Parent's function name and if not inside a function then the name of the script. If not inside a function or a script, then the value will be set to 'Unknown'.
- Changed UnBlock-AppExecution and Block-AppExecution so they don't run without admin rights. Instead of throwing an error, they only write and entry to the log.
- Changed Execute-MSI so when the action is Patch, two variables REINSTALL and REINSTALLMODE are appended to parameters as specified here: https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/msiexec#update-options
- Changed timestamp of compressed toolkit logs to 24h format to avoid possible double filenames.
- Changed Get-ScheduledTask to Get-SchedulerTask to resolve the conflict on Windows 10 and added an alias on Powershell versions where this function does not exist.
- Changed logging inside Convert-RegistryPath to be disabled by default, to decrease log clutter.
- Changed registry paths to no longer use registry PSDrives and contain full paths.
- Changed Show-InstallationPrompt so the window scales with the amount of text entered and removed unused space. If there is no icon specified, the text will scale the entire width too.
- Changed Show-InstallationRestartPrompt so it restarts the computer in Silent and VerySilent deploy modes.
  - Added NoSilentRestart parameter for the function to retain previous behaviour, which defaults to True.
  - Renames 'Restart Later' to 'Minimize' to make it more clear to the user.
- Fixed visual bugs in Show-InstallationWelcome.
  - The application name is now always displayed.
  - Fixed uncentered text.
  - Height of the window is now not limited and scales with the text.
  - Removes unused variables.
- Fixed uses of Execute-Process throughout the toolkit with the -PassThru parameter since -PassThru no longer ignores exit codes.
- Fixed issue with $envOSVersionRevision not being populated on Windows 8 because the "UBR" registry value is only being checked if "$envOSVersionMajor = 10".
- Fixed an issue where the correct ProgramFiles and CommonProgramFiles paths were not populated if the Toolkit was launched from a 32-bit process on a 64-bit OS.
- Fixed Powershell 2.0 issues with GetHostEntry and GetFolderPath.
- Fixed the issue with config file being read incorrectly and showing incorrect characters in the UI.
- Fixed Get-UserProfiles so it ignores "defaultuser0" account.
- Fixed a rare issue in Execute-ProcessAsUser causing an error, because of a negative index for SubString.
  - Improved logging in the function.
  - CMD will now be started silently without a popup just like powershell.

**Version 3.8.2 [08/05/2020]**

Security Notice

- Fixed security issue that could allow elevation of privilege related to the toolkit temp file path [Reserved CVE-2020-10962]
  - The script temp file path was set to a location that was writeable by a standard user. This is set in the configuration XML.
  - This security risk only affects deployments that use the Show-InstallationWelcome function with the parameter -BlockExecution or the internal function Block-AppExecution AND are running with elevated permissions AND can be exploited only while the script is running.
  - To further minimize the risk of an attacker using this to gain additional permissions, it has been moved to a write-protected location.
  - Note: This change will break deployment scripts that run with Standard User rights. While this has been technically possible to do, we are not aware of anyone doing this. As such, we feel this is the safest course of action for the overall PSADT userbase.
  - Additionally, a standalone PowerShell script was created, to identify and remediate the issue on existing PSAppDeployToolkit deployment packages. It can be found here:
    - https://github.com/PSAppDeployToolkit/PSAppDeployToolkit/blob/master/Sources/Set-ToolkitTempPath.ps1

Breaking Changes

- Changed behavior of parameters in Execute-Process. This is a breaking change which may require changes to deployment scripts:
  - Changes to behavior are being made to reduce confusion around the wording of parameters when a custom workflow is required.
  - No changes required if your deployment script does not have custom handling of exceptions / exit codes.
  - ContinueOnError was scoped to ignore non-zero exit codes. It now operates at the function level and will allow continuing if an unknown exception occurs.
  - If Passthru was specified, all non-zero exit codes and exceptions were ignored. Now, both event types cause a Script Failure.
	- To ignore specific exit codes, use the new IgnoreExitCodes parameter, using * to ignore all.
	- To stop the script if the process fails, use the ExitOnProcessFailure parameter.
  - Execute-Process would fail if an exception occurred, even when ContinueOnError was specified. It now ignores exceptions as expected.
  - ContinueOnError now ignores exceptions as expected. To ignore specific exit codes, use the IgnoreExitCodes parameter.

Other

- Added new parameters to the Execute-MSI function:
  - NoWait. Allows immediately returning to the script once the process has been started, without waiting for it to complete (Default: False).
  - IgnoreExitCodes. Allows ignoring specific or all exit codes returned by the process (Default: None).
  - PriorityClass. Allows changing the process priority immediately after process starts (Default: Normal).
  - ExitOnProcessFailure. Allows setting the script to immediately stop if the process fails and returns the exit code to the caller (e.g. SCCM) (Default: True).
  - RepairFromSource. Allows rewriting local msi cache and repair installation from source
- Added new parameters to the Execute-Process function:
  - IgnoreExitCodes. Now supports ignoring all exit codes returned by the process (Default: None).
  - PriorityClass. Allows changing the process priority immediately after process starts (Default: Normal).
  - ExitOnProcessFailure. Allows setting the script to immediately stop if the process fails and returns the exit code to the caller (e.g. SCCM) (Default: True).
- Added async reading of standard error stream to Execute-Process
- Added restoring of PowerShell window title on script exit
- Changed function Remove-InvalidFileNameChars to only retrieve Invalid Filename Characters once per script execution, instead of once per each function call
- Improved handling of application install title / name variables
- Fixed issue where exceptions were bypassed in Execute-Process with PassThru parameter specified.
- Fixed issue where multiple processes were displayed in the Show-InstallationWelcome dialog when the same process has multiple descriptions.
- Fixed a number of typos in the documentation.
- Reverted part of the function Get-InstalledApplication that was causing slowdowns
- Removed unused variable $processStarted.
- Added parameter -WorkingDirectory for Execute-ProcessAsUser
- Fixes for Slovak and Czech translations

**Version 3.8.1 [03/28/2020]**
- Added support for Repair as an action type (e.g. Install, Uninstall, Repair)
- Added disabling of the close button for the Show-InstallationProgress window
- Added ability to Execute-ProcessAsUser function to run PowerShell scriptblocks
- Added ability to retrieve Product Version in Get-FileVersion
- Added ability to display help for custom functions in the AppDeployToolkitHelp UI
- Added ability to disable recursion in Remove-Folder
- Added Remove-InvalidFileNameChars function and implemented across the toolkit
- Added Powershell window renaming, in case it's visible to the user
- Added Czech translations
- Changed -In parameter usage to .Contains as -In parameter not supported in PowerShell 2.0
- Changed Banner image to use Cascadia Code as primary font
- Improved documentation
- Improved Dutch translations
- Improved logging in Get-InstalledApplication
- Fixed issue in Set-PinnedApplication where pin action on StartMenu is case sensitive on Windows 10
- Fixed Set-ActiveSetup purging incorrectly testing registry values
- Fixed issue with Deploy-Application.exe where it would prompt to download .NET Framework 3.5 on Windows 8 and above
- Added default -Source parameter value for Write-Log to fix CMTrace not displaying severity

**Version 3.8.0 [09/23/2019]**
- Added ability to change the dialog banner height, with a configurable maximum
- Added ability to change the dialog banner and icon filename through the configuration file
- Added support for animated GIFs in the dialog banner
- Added a dynamic dialog banner height with a configurable maximum to the dialogs
- Added support for flattening folder structure within Copy-File with -Flatten switch
- Added architecture independence for Zero Touch MSIs where filename ends in x64.msi or x86.msi
- Added logging to Remove-Folder function so it is also traceable in the log file when successful
- Changed ProcessObjects parameter in Get-RunningProcesses function to mandatory=$false so Show-InstallationWelcome without CloseApps parameter does not fail
- Changed encoding of AppDeployToolkitConfig.xml to UTF-8 (was incorrectly UTF16)
- Fixed an issue where Execute-Process -MsiExecWaitTime used ticks instead of seconds
- Fixed a number of glitches relating to illegal characters and locale-specific time / date formats
- Fixed Get-RunningProcessesDynamically .NET error in Show-WelcomePrompt
- Fixed issue with Write-Log creating bad formatting for CMTrace in non-English cultures
- Fixed issue with non-standard quote characters in the Execute-MSP function
- Fixed issue where Show-WelcomePrompt hangs when ForceCloseAppsCountdown is passed to Show-InstallationWelcomePrompt and application(s) listed in CloseApps are not running when timeout is reached
- Fixed issue where Set-PinnedApplication did not pin to / unpin from taskbar on Windows 10 because Microsoft removed the taskbar verbs
- Fixed issue where Get-InstalledApplication only supported Basic Latin unicode characters by removing logic which "removes any control characters which might interfere with logging and creating file path names from these variables", as the creation of file name is only used in Execute-MSI function and the logic there already replaces invalid filename characters
- Fixed issue where Get-LoggedOnUser failed to detect if user is member of the Administrators group, when Administrator group has an orphaned account listed
- Fixed issue where active setup execution fails when StubExePath contains ps1 file in Set-ActiveSetup function
- Fixed issue where Show-InstallationPrompt would not show when using Title parameter in combination with NoWait
- Fixed issue where defer times would not decrease from 1 to 0 when Show-InstallationWelcome prompt timed out
- Fixed issue where a .NET Framework error would appear stating the term 'Get-RunningProcessesDynamically' was not recognized
- Fixed an ActiveSetup purging error
- Fixed typo in Remove-Folder function
- Fixed examples in Enable/Disable-TerminalServerInstallMode
- Fixed detection of wired network connections in Test-NetworkConnection function on Windows 10 workstations
- Updated the AppDeployToolkit logo to match the website

**Version 3.7.0 [02/13/2018]**
- Added the ability to dynamically re-enumerate running applications while displaying the Show-InstallationWelcome prompt. If the CloseApps processes were not running when the prompt was displayed and are subsequently detected to be running, the prompt will be updated with the apps to close. If the CloseApps processes were running when the prompt was displayed and are subsequently detected not to be running, then the installation will automatically continue if no deferral is available. If the CloseApps processes change (new processes launched or running processes closed), the list box will dynamically update every 2 seconds to reflect the currently running applications.
- Added ContinueFileCopyOnError parameter to the File-Copy function to continue copying files if an error is encountered.
- Added error handling to catch errors retrieving/deleting scheduled task in Unblock-AppExecution
- Added detection of App-V pending tasks to the Get-PendingReboot function
- Added support for creating a hotkey to the New-Shortcut function
- Added the parameter type column to the logging function
- Added the Win32_BaseService class to the Test-ServiceExists function
- Added environment variables; CurrentUILanguage
- BREAKING CHANGE, The "Regex" filter in Remove-MSIApplications previously escaped RegEx characters, but now parses a RegEx statement as expected. Added "Contains" filter which emulates RegEx filter from previous version.
- Re-named "Refresh-Desktop" to "Update-Desktop" and "Refresh-SessionEnvironmentVariables" to "Update-SessionEnvironmentVariables" to conform to approved Cmdlet verbs, an alias preserves backwards compatibility with the previous function names
- Fixed issue where OS Revision Version is not always retrieved correctly in Windows 10
- Fixed issue in Copy-File where it was not possible to copy an array of file objects
- Fixed issue in Set-RegistryKey where registry keys with forward slashes now contains actual forward slash [char]0x002F instead of [char]0x2215
- Fixed issue in Execute-ProcessAsUser where result from the task scheduler could not be parsed on non-English language systems. Fix applies for Vista and higher
- Fixed issue in Execute-ProcessAsUser which could delete the task before executing it if used without the Wait switch
- Fixed issue in Execute-ProcessAsUser to allow parameters ending with a double quote
- Fixed issue where Test-PowerPoint erroneously returned False on non-English language systems
- Fixed issue where zero-config MSI would run when Show-InstallationRestartPrompt is called
- Fixed issue where BalloonTip doesn't launch asynchronously when initiated from Exit-Script
- Fixed case sensitivity in the shortcut path passed to New-Shortcut
- Fixed issue in Remove-File where a confirmation dialogue would hang the script if a non-empty folder is encountered when the Recurse switch is not specified
- Fixed issue with white spaces in scheduled task creation breaking the -BlockExecution parameter
- Fixed missing information in documentation and fixed encoding issue with AppDeployToolkitConfig.xml
- Fixed issue where TopMost parameter in Show-DialogBox function could not be set to $false
- Removed PS v3.0 minimum check for Get-Hotfix cmdlet used in Test-MSUpdates
- Improved parameter validation on -CheckDiskSpace and -RequiredDiskSpace parameters in Show-InstallationWelcome prompt
- Improved comments and documentation

**Version 3.6.9 [02/12/2017]**
- Added SecureParameters parameter to additional section of Execute-ProcessAsUser
- Added SecureParameters parameter to Execute-ProcessAsUser
- Added -RegEx parameter to Get-InstalledApplication and modified the help text for the -Name parameter
- Added DoNotExpandEnvironmentNames parameter to Get-RegistryKey to enable retrieving of unexpanded REG_EXPAND_SZ values
- Added expandstring functionality to the MSI_InstallParams, MSI_SilentParams, and MSI_UninstallParams XML properties
- Added support for Windows 10 to the function Set-PinnedApplication to enable pinning and unpinning to the Start Menu
- Added IncludeUpdatesAndHotfixes Parameter to Execute-MSI and Remove-MSI functions
- BREAKING CHANGE, Changed variable envCommonPrograms to envCommonStartMenuPrograms
- Changed behavior of Remove-Folder function to continue deleting files recursively when an error is encountered when $ContinueOnError is used
- Changed severity of failure messages created in the Set-PinnedApplication to warning (2) from error (3)
- Changed .ps1 script files so that they are UTF-8 BOM encoded so that you can digitally sign files on non-unicode language systems
- Fixed issue in Exit-Script where the system tray icon was not being removed at the end of the script
- Fixed issue in Remove-MSIApplication which caused the function to fail in the case of multiple filtered or excluded applications
- Fixed issue in Get-PendingReboot where detection of a pending SCCM reboot would cause a fatal error for some clients
- Fixed issue in Test-MSUpdates giving preference to the Get-Hotfix cmdlet for detecting updates
- Fixed issue where variable envOSVersion was inconsistent in Windows 8 and 8.1 workstations
- Fixed issue in Execute-ProcessAsUser with Powershell script execution
- Fixed issue in Test-MSUpdate which returned a false positive when searching for an installed update
- Fixed typo in Get-HardwarePlatform function
- Fixed issue in Set-RegistryKey where registry keys with forward slashes were treated like backslashes and broken up into subkeys
- Fixed issue in Remove-RegistryKey function where it failed to delete the (Default) value when specified due to limitation of Remove-ChildItem cmdlet
- Fixed issue in New-Shortcut to use Rename-Item parameter name that is compatible with PS 2.0
- Fixed issue in Get-RegistryKey function which caused it to ignore the (Default) value
- Improved comments and documentation

**Version 3.6.8 [02/06/2016]**
- Added environment variables with additions for Pictures, Templates, and Common User locations
- Added SecureParameters parameter to Execute-MSI and Execute-Process functions
- Added language translations for Slovak, Hungarian and also updated translations for German
- Added ParentProcessId property to Get-WindowTitle
- Fixed issue in Remove-File where deleting of multiple files would stop silently on first error and added LiteralPath parameter
- Fixed issue in the Uninstall Office 2013 Deploy-Application example where an incorrect variable was referenced to determine if a user session was present for the script execution
- Fixed issue in Test-MSUpdates where a fatal error occurred when no update history is present on the target system
- Fixed issue in Install-MSUpdates where the full path to the update file was not passed to Execute-Process for installation
- Fixed issue where Get-InstalledApplication throws an error when attempting to import invalid registry values from the subkeys of the Uninstall key
- Fixed formatting of window title name when showing the blocked app dialog window or the restart computer window
- Fixed issue in Test-PowerPoint where detection did not work if there were multiple screens or if presentation was on screen without mouse focus
- Fixed issue in Get-RegistryKey where reading an empty value or a DWord value of 0 caused function to return $null
- Fixed issue in Set-DeferHistory where function did now log $deferTimesRemaining and referenced an incorrect variable name instead
- Fixed issue in Get-HardwarePlatform where function did not detect Microsoft Surface hardware correctly
- Fixed issue in Remove-MSIApplications to make sure that an invalid ProductCode is not passed to the Execute-MSI function
- Improved toolkit so that asynchronous executions of the toolkit write to the same log file instead of creating a new one
- Improved Get-InstalledApplication to verify ProductCode is valid product code before storing value, also added UninstallSubkey property
- Improved toolkit to allow setting custom $InstallTitle and $InstallName in Deploy-Application
- Improved temporary logging folder naming convention to include deployment type in the name
- Improved Office example script by updating Offscrub13.vbs to latest version from Microsoft, fixes issue with completion notice
- Improved error handling in the Get-InstalledApplication function
- Improved error handling in the Test-MSUpdates function
- Improved Execute-MSI so that environment variables created by MSIs are always updated for Windows Explorer
- Improved Execute-Process function to capture out of range exit codes and return custom 60013 exit code
- Improved Remove-RegistryKey function to write warning instead of error to log if a property being deleted does not exist
- Improved Get-PendingReboot function to check if "ROOT\CCM\ClientSDK\CCM_ClientUtilities" WMI class exists before executing method
- Improved Show-BalloonTip function to suppress balloon notifications if a PowerPoint presentation is in fullscreen slideshow mode or presentation mode
- Improved comments and documentation

**Version 3.6.7 [09/22/2015]**
- Fixed Write-Log issue introduced in v3.6.6 that created malformed CMTrace type log files
- Improved documentation to add section on how to use zero-config MSI install option

**Version 3.6.6 [09/21/2015]**
- Added error handling for Remove-RegistryKey function when used on key with child keys and -Recurse parameter not used to avoid script hanging
- Added ability in Remove-MSIApplications to filter the list of results returned by Get-InstalledApplication to only those that should be uninstalled
- Fixed issue with Set-ServiceStartMode where setting the service to Automatic will set it to Automatic (Delayed Start)
- Fixed issue introduced in v3.6.5 where blocked application dialog did not display for blocked applications
- Fixed issue with $appRevision being appended to $installName if not defined
- Fixed issue in Show-InstallationPrompt with $installName being passed instead of $Title for -ReferringApplication parameter when -NoWait option specified
- Fixed issue in Test-MSUpdates where information about the discovered KBArticle was not being converted to readable text before being logged
- Fixed issue with detecting UI language by removing faulty detection method for Windows 8
- Fixed issue in Block-AppExecution by removing illegal characters such as "&" from the arguments string of the XML file before importing it
- Fixed issue in Get-InstalledApplication where invalid data in the registry could terminate enumeration of the registry
- Fixed issue with compiling scripts to EXE by removing support for alternative PowerShell hosts from AppDeployToolkitMain.ps1 file
- Fixed typo in config XML file for Swedish language message for 'ClosePrompt_CountdownMessage'
- Fixed issue in Show-InstallationWelcome where $notesPath was not defined before it was used
- Fixed issue in Show-InstallationWelcome where all process names were not enumerated on PS2.0 when trying to close notes processes
- Changed message update priority, to increase it, from 'Normal' to 'Send' in Show-InstallationProgress (should hopefully address reports that messages do not display on screen on time)
- Changed Set-Acl and Get-Acl -LiteralPath parameter to -Path in function New-ZipFile as -LiteralPath is not available in PS 2.0
- Changed $global: variables to $script: variables as these two scopes are the same in the context of the powershell console that executes the script
- Improved registry functions to throw error if registry hive not specified
- Improved comments and documentation

**Version 3.6.5 [08/17/2015]**
- Added function New-MsiTransform to allow creation of transform files for MSI databases. Supporting functions: Set-MsiProperty, Invoke-ObjectMethod, Get-ObjectProperty.
- Added ability in Remove-MSIApplications to exclude software from being uninstalled by matching against specified property/value/match-type pairs
- Added function Get-PendingReboot to get pending reboot status for the local computer (thanks to user 'That-Annoying-Guy' for the original code)
- Added function Test-RegistryValue to check for the existence of a registry value, needed to support Get-PendingReboot function
- Added ability to install MSP files as part of a zero-config MSI install
- Added ability to Get-MsiTableProperty to apply a list of transform files to an MSI database after opening it. Also added ability to read properties from MSP files. Also added -GetSummaryInformation parameter to allow reading of Summary Information from a Windows Installer database file.
- Added support for retrieving $MyInvocationInfo when using alternative PowerShell hosts such as with Sapien PowerShell compiled EXEs
- Added following variables: $currentDateTime, $envLogicalDrives, $envUserCookies, $envUserDesktop, $envUserFavorites, $envUserInternetCache, $envUserInternetHistory, $envUserMyDocuments, $envUserSendTo, $envUserStartMenu, $envUserStartMenuPrograms, $envUserStartUp, $envSystem32Directory
- Added function New-ZipFile to allow creating zip archives, cleaned up the code for zipping the log folder from the Exit-Script function
- Fixed issue with Get-WindowTitle which caused the -PromptToSave functionality in Show-InstallationWelcome to fail on Win 7 PowerShell 2.0 machines
- Fixed issue with Show-WelcomePrompt which caused the -TopMost $false option to not work (also affected Show-InstallationWelcome)
- Fixed issue introduced in v3.6.3 where Norwegian language code in config XML was changed to NO. Code reverted back to NB as this is the proper code for the Bokmål written language supported by Windows.
- Fixed issue introduced in v3.6.4 by adding back a double quote to the .vbs file generated by Execute-ProcessAsUser (initial reported issue was caused by bad code in the example which has been fixed)
- Fixed issue in Refresh-Desktop which caused refreshing the global process environment block to fail
- Fixed issue in Test-MSUpdates where updates that had been uninstalled were detected as installed by the Office updates detection method
- Fixed issue with detecting user's display UI on XP from registry key 'HKCU\Control Panel\International\Locale', and fixed type in one of the other registry paths checked
- Fixed issue in Set-ActiveSetup so that HKCU active setup registry keys are created when stubpath is executed for currently logged in user so that it does not execute a second time when user logs into account
- Fixed issue with New-Folder function displaying console output
- Fixed issue with Get-MsiTableProperty where reading the property table for a second MSI would throw an error unless -Force parameter specified
- Fixed issue with the zip compressed log files only being accessible to elevated users
- Fixed issue with Show-InstallationRestartPrompt where the PSADT restart prompt prevented a manual user initiated Windows Shutdown from being executed
- Fixed issue with Show-InstallationPrompt and Show-InstallationRestartPrompt where the dialog would be displayed in Silent Mode
- Fixed issue in Get-InstalledApplication where -ErrorAction could prematurely stop function if permissions issue while enumerating registry keys
- Fixed issue with Invoke-HKCURegistrySettingsForAllUsers where registry profiles would fail to unload
- Fixed issue where the final (asynchronous) balloon tip would not display
- Fixed issue with Zero-Config MSI installations not being able to handle MST files
- Fixed issue with Get-MsiTableProperty where handle to MSI database file was not released by powershell.exe after reading properties
- Fixed issue with Close-InstallationProgress where $global:ProgressRunspace was not closed
- Fixed issue where the toolkit would terminate in session zero due to absence of user domain variable
- Changed 5000 exit code for "InstallationDefer_ExitCode" in config XML to 60012 to be in line with newly defined exit code ranges for the toolkit
- Changed Deploy-Application.ps1 to allow exiting with custom exit code if failure to parse toolkit
- Changed registry manipulation functions to use -LiteralPath instead of -Path because * and ? are rare but valid characters for registry names
- Changed Show-InstallationWelcome and Show-WelcomePrompt to allow display of custom message from XML by using -CustomMessage parameter. Also added -ForceCountdown to allow us to have a countdown timer with the defer option on the welcome screen.
- Changed variable $envTemp to use recommended method of retrieving temp folder path
- Improved Exit-Script so that it is possible to manually exit the script with a 3010 or 1641 exit code and trigger an SCCM "Soft Reboot"
- Improved comments and documentation

**Version 3.6.4 [05/19/2015]**
- Fixed issue with detecting user's display UI when enforced by Group Policy, fixed typo in one of the registry paths
- Fixed issue with extra double quote at end of VBScript command in function Execute-ProcessAsUser (caused issue in Set-ActiveSetup as well)
- Fixed issue with Enable-TerminalServerInstallMode and Disable-TerminalServerInstallMode expecting a successful execution of change.exe to return 0 when it should really be 1
- Fixed issue with Execute-MSI so that Get-MsiTableProperty is only called when the file being executed is an MSI and not when it is an MSP
- Fixed issue in Invoke-HKCURegistrySettingsForAllUsers where a registry hive would fail to unload because of an active memory reference by manually invoking garbage collection
- Fixed issue where registry key for deferrals was not being set correctly
- Fixed issue in Show-InstallationWelcome where blocking "notes" would stop script execution
- Fixed issue where System.Threading.Mutex class does not have Dispose() method prior to .NET 3.5 so will call Close() method instead. This also fixed issue with blocked app message not showing on Win 7 .NET 2.
- Changed StubPath registry type to 'String' in Set-ActiveSetup
- Improved comments and documentation

**Version 3.6.3 [04/29/2015]**
- Added an informational tooltip to the 'Continue' button on the close apps screen to make it clear that it can only be used after closing the listed applications
- Added -PendingStatusWait as a configurable option to functions Start-ServiceAndDependencies and Stop-ServiceAndDependencies
- Added translations for following languages to config XML: Arabic, Hebrew, Korean, Portuguese (Brazil), Russian, Chinese (Traditional), Chinese (Simplified)
- Fixed issue with Get-ScheduledTask not working properly with non-English languages
- Fixed issue with Write-Log throwing error on Windows 7 when discovering script source under certain conditions
- Fixed issue with Get-RunningProcesses on Windows 7 where the custom object did not return ProcessDescription property unless -PassThru option was used
- Fixed issue with the creation of the blocked apps cleanup scheduled task to avoid 259 character limit for the 'Command' property
- Fixed issue where a file remnant from an old version of the toolkit caused failure in ability to copy toolkit to temporary folder defined in config XML
- Fixed issue with Set-ActiveSetup where StubExePath was a mandatory parameter when purging active setup entry
- Fixed issue in config XML where two letter ISO language code for Norwegian was defined as NB instead of NO
- Fixed issue in Write-Log where hashtable is storing $LogfilePath instead of $LogFileName for LogfileName property when archiving logs
- Changed icon for Deploy-Application.exe
- Improved Get-LoggedOnUser by increasing performance. Added IsActiveUserSession and IsRdpSession properties.
- Improved documentation

**Version 3.6.2 [04/14/2015]**
- Improved capture of error details in function Invoke-HKCURegistrySettingsForAllUsers when a user profile fails to load
- Improved internal function Test-MsiExecMutex: renamed function to Test-IsMutexAvailable, added parameters to allow checking for any named mutex, improved error trapping and messages
- Improved Get-LoggedOnUser by increasing performance (should be much faster now)
- Improved Show-WelcomePrompt by adding horizontal scrollbar for listbox items that are longer then the window
- Changed toolkit so that all custom C# code is now in a separate file called "AppDeployToolkitMain.cs" which gets compiled once after functions are declared
- Added an alias called 'Text' for the -Message parameter in Write-Log for backwards compatibility
- Fixed issue with retrieving the active logged in user's system DPI scale factor because of typo in registry key
- Fixed issue with malformed log entries being inserted into log file when dot sourcing scriptblock to retrieve logged in user session details
- Fixed issue in Remove-MSIApplications where 'Path' key could be added to hash table more than once
- Fixed issue with detecting a logged in user's locale
- Fixed issue in Get-RunningProcesses where a regex match would detect processes that partially matched the name of the process being searched for
- Fixed issue in Get-RunningProcesses and Show-InstallationWelcome where a specified custom process description was ignored
- Fixed issue where Deploy-Application.ps1 script version would be set to toolkit version if it was not specified

**Version 3.6.1 [04/02/2015]**
- Added Zero-Config deployment for MSI files. No need to customize anything. Drop your MSI in the Files folder and run the toolkit. Information is pulled from the MSI to do branding, logging, and closing in-use processes.
- Added -DisableLogging switch to Deploy-Application.ps1 to disable logging to file for the script
- Added -SoftwareUpdatesScanWaitInSeconds and -WaitForPendingUpdatesTimeout parameters to Install-SCCMSoftwareUpdates
- Added -WildCard search option to Get-InstalledApplication and Remove-MSIApplications
- Added -PassThru option to Test-Battery which will return the following properties: IsLaptop, IsUsingACPower, ACPowerLineStatus, BatteryChargeStatus, BatteryLifePercent, BatteryLifeRemaining, BatteryFullLifetime
- Added following parameters to Remove-MSIApplications to match those in Execute-MSI: -Parameters, -AddParameters, -LoggingOptions, -LogName
- Added default active username for -UserName parameter in Execute-ProcessAsUser
- Added -SkipMSIAlreadyInstalledCheck parameter to Execute-MSI function to allow ability to skip the check that determines if an MSI is already installed on the system
- Added SID property to Get-LoggedOnUser function results
- Added -LoadLoggedOnUserEnvironmentVariables parameter to Refresh-SessionEnvironmentVariables function to allow loading user environment variables when running in the SYSTEM context
- Added ability in Set-ActiveSetup to execute active setup entry for currently logged on user when running under the SYSTEM account
- Added ability to detect Primary UI language of active logged in user when running under the SYSTEM account
- Added function Get-WindowTitle to add ability to search for an open window title and return details about the window
- Added -WindowHandle and -GetAllWindowTitles parameters to Send-Keys function
- Added ability to detect display scale factor for currently logged on user when running under the SYSTEM account
- Added -PassThru parameter to Execute-MSI and Remove-MSIApplications to return ExitCode, STDOut, and STDErr output
- Fixed issue introduced in 3.6 whereby the toolkit timeout would not be triggered resulting in the wrong exit code being passed
- Fixed issue in Install-MSUpdates where a folder path with a KB number in the folder name would force the regex to only detect that one KB in the folder
- Fixed issue in Get-ServiceStartMode to avoid error when DelayedAutoStart property does not exist
- Fixed issue introduced in 3.6.0 where $sessionZero was incorrectly set to $false after being set to the correct value
- Fixed issue in Test-PowerPoint related to accurately measuring the size of a non-fullscreen window
- Fixed variable type for -PassThru parameter in functions for manipulating services
- Fixed issue in Execute-ProcessAsUser which improperly caused script exit
- Fixed issue in Get-UserProfiles where 'DefaultUserProfile' registry value was mis-typed as 'DefaultUsersProfile'
- Fixed issue in Show-InstallationWelcome to avoid error if the Description field of a hung process could not be accessed
- Fixed issue where script would hang when removing deferrals (cause: Remove-RegistryKey did not have -Recurse parameter specified)
- Changed Send-Keys function to use new Get-WindowTitle function when searching for window titles
- Changed Deploy-Application.exe to exit with exit codes in the newly defined range
- Improved Install-SCCMSoftwareUpdates to only execute if SCCM 2012 or higher installed because method is not compatible with SCCM 2007 or lower
- Improved Install-SCCMSoftwareUpdates to check if SCCM client service is installed and running before trying to install updates
- Improved Invoke-SCCMTask to check if SCCM client service is installed and running before invoking task
- Improved Invoke-SCCMTask to add a few more SCCM 2012 client compatible schedule ids and to only allow execution of a schedule id compatible with the installed version of the client
- Improved Block-AppExecution by using the import XML method of creating a scheduled task so that we could configure the task that unblocks applications to execute even if running on batteries
- Improved Deploy-Application.ps1 by setting exit code to 60008 if AppDeployToolkitMain.ps1 fails to dot source
- Improved Send-Keys function to be able to detect all open windows (including Explorer windows and all child windows), and also to bring any child window into the foreground
- Improved -PromptToSave function to allow prompting for all child windows of a single instance application (such as Microsoft Word)
- Improved the behavior for dialog shown when user tries to execute a blocked app so that no more than one dialog can be shown at a time
- Improved documentation

**Version 3.6.0 [03/11/2015]**
- Added function Set-ActiveSetup to allow creation/deletion of Active Setup entries (thanks to Denis St-Pierre, Todd MacNaught for code)
- Added function Execute-ProcessAsUser to support running processes as the current logged on user to provide interaction in the system context. Note: if the user is not a local admin the process launched in user context will not inherit admin rights.
- Added -PromptToSave parameter to Show-InstallationWelcome to prompt to save working documents when the users selects to close running applications
- Added -TopMost parameter to Show-InstallationWelcome to allow the ability for the user to move the welcome screen to the background
- Added variable to determine if running in terminal server session
- Added functions to handle Windows services: Test-ServiceExists, Stop-ServiceAndDependencies, Start-ServiceAndDependencies, Get-ServiceStartMode, Set-ServiceStartMode
- Added check to test whether or not the Windows Task Scheduler service is in a healthy state and fix any issues if possible.
- Added ability in Deploy-Application.exe to read if Admin rights required from XML file and then automatically prompt to elevate on Vista or higher
- Added -ExcludeDefaultUser parameter to Get-UserProfiles to allow exclusion of the Default User profile
- Added IsLocalAdmin property to results from Get-LoggedOnUser function to determine if the user account is in the local Administrators security group
- Added -AddParameters parameter to Execute-MSI to allow augmenting of parameters instead of replacing them
- Added -ReturnEmptyKeyIfExists parameter to Get-RegistryKey to allow returning empty registry keys if they exist
- Added -LoggingOptions parameter to Execute-MSI to allow overriding the default logging options specified in the config file
- Added validation for -Path parameter in Execute-MSI function
- Fixed issue where Show-InstallationRestartPrompt and Show-InstallationPrompt with -NoWait parameter didn't work
- Added default action of Install to Execute-MSI
- Added ability to specify multiple transforms and patches in Execute-MSI using a comma separated list
- Added ability in XML configuration to override the detected language with a statically defined language for installation UI elements
- Fixed issue in Show-InstallationProgress where -BottomRight parameter would cause prompt to be placed off screen where DPI is > 100%
- Fixed issue with error being thrown when reading the DPI scale from the registry if the property doesn't exist
- Fixed issue with Show-InstallationWelcome failing if "notes" specified with -CloseApps parameter when Notes application not installed
- Fixed issue in Execute-MSI where LogName parameter did not work correctly
- Fixed issue with Show-InstallationWelcome where deferrals would be ignored if CloseAppsCountdown parameter is specified
- Fixed issue in Copy-File where operation would fail if destination folder was not created before operation when using wildcards in -Path parameter
- Fixed issue in Unblock-AppExecution on some systems where process failed to unblock if only one process was initially blocked
- Fixed issue with Deploy-Application.exe not executing on systems where only .NET 4 or higher installed by adding Deploy-Application.exe.config file
- Fixed issue with Deploy-Application.exe by using -Command parameter instead of -File to avoid bug in PowerShell 2.0 where incorrect exit code might be returned if -File parameter used
- Fixed a number of issues with the Office 2013 example script
- Fixed issue whereby pressing enter would default to a hidden Abort button on Show-installationWelcome and Show-Installation Prompt
- Fixed aesthetic issue whereby use of DeferDays or DeferDeadline without DeferTimes would show a deferral count of 1 remaining
- Fixed issue in Get-InstalledApplication to properly detect applications when -Exact parameter used
- Fixed issue in Get-UniversalDate where function would fail for some valid date formats under some culture settings
- Fixed issue in Write-Log where it was not possible to change logfile name and directory after the first time these paths were set
- Fixed issue with Deploy-Application.ps1 where variable expansion wouldn't work in Show-InstallationPrompt example
- Fixed issue with Install-SCCMSoftwareUpdates not working on all clients
- Fixed issue where modal dialog windows were not properly disposed
- Improved path validation in Deploy-Application.exe, rewrote utility in C#
- Improved defining default values for script variables not defined
- Improved path validation in Execute-Process
- Improved path validation in Execute-MSI
- Improved working directory validation in Execute-MSI
- Improved error message when toolkit launched without admin permissions
- Improved detection of when invoking script is the Help script to avoid false positives
- Improved Deploy-Application.ps1 to use Write-Error instead of Write-Output when toolkit fails to dot source
- Improved Get-ScheduledTask to retrieve all scheduled task details, remove spaces and colons from property names, allow retrieval of details for a specific task name, and remove lines with column headers from results
- Improved compilation of custom C# code by using -IgnoreWarnings switch with Add-Type cmdlet so that toolkit does not fail to run because of a warning
- Improved Write-Log to log following for CMTrace log type: thread id, file name of source script
- Improved environment variable population when running in system context
- Improved Write-Log to log PID and log source for CMTrace log type
- Improved Remove-RegistryKey to check if a key exists before trying to delete it and log a warning if key does not exist
- Changed default Exit Codes for internal Toolkit functions. The range of 60000 is used for internal, and 70000 should be used for custom extensions
- Changed toolkit to remove unused variables which did not exist and were being read from XML file
- Changed Register-DLL/Unregister-DLL function to combine them into one function called Invoke-RegisterOrUnregisterDLL. Created Aliases for new function name to maintain backwards compatibility.
- Changed Notes process termination using NSD to only run if Notes processes are running
- Updated documentation
- Added Polish translations
- Updated German translations
- Updated Italian translations

**Version 3.5.0 [11/24/2014]**
- Added CMTrace compatible log files to Write-Log (now the default). Legacy log format still available through configuration in XML config file
- Added ability to modify HKCU registry settings for all users on a machine by loading registry hives. Added functions to support this: Invoke-HKCURegistrySettingsForAllUsers, Get-UserProfiles, ConvertTo-NTAccountOrSID. Modified functions to support this: Convert-RegistryPath, Get-RegistryKey, Set-RegistryKey, Remove-RegistryKey
- Added ability in Register-DLL / Unregister-DLL to detect bitness of DLL files and register for the correct architecture automatically
- Added ability for Test-PowerPoint to actually detect if user is presenting slideshow in fullscreen mode
- Added Get-LoggedOnUser to get session details for all local and RDP logged on users
- Added Refresh-SessionEnvironmentVariables to allow reloading of newly created environment variables for the current process
- Added new variables for Domain Membership, Process Information, Account Permissions, OS Type and Versions, PowerShell and .NET versions, TimeZone Bias
- Added TRANSFORMSSECURE=1 as default where Transform parameter is specified
- Added ability to detect active console user (user with control of the monitor, keyboard, mouse)
- Added ForceCloseAppsCountdown parameter to Show-InstallationWelcome allowing a countdown to close apps regardless of whether deferral is allowed
- Added Patch parameter to Execute-MSI to allow an MSP file to be specified on the command line with an MSI installation
- Added ability to Get-InstalledApplication to detect whether a 64-bit or 32-bit application
- Added CreateNoWindow parameter to Execute-Process
- Changed Deploy-Application.exe to new executable built in VB.NET
- Changed detection of Admin rights by looking for Admin SID on the current processes access token, previous method checked for membership in the BuiltInAdministrator group
- Changed Restart Prompt default selected button to Restart Later
- Changed $envOSArchitecture variable so that it reports consistent value regardless of OS language
- Improved detection of change.exe failures for TerminalServerInstallMode
- Improved detection of logged in users, including users connected through RDP
- Improved error trapping and logging of messages throughout script. Modified messages for clarity and consistency.
- Improved parameter validation on all functions to simplify function logic and give standard error messages when function parameters not specified properly, also helps make clear which parameters are mandatory and which are not
- Improved Set-PinnedApplication to detect when one of the chosen actions is not available on an operating system (such as pinning items to Start Menu on Windows 8)
- Improved session 0 detection
- Improved Execute-MSI and Execute-Process to sync parameter names for -Path and -Parameters and add alias for backwards compatibility
- Improved method of detecting OS architecture so that detection works on some non-English operating systems
- Improved Block-AppExecution to not flash CMD window right before displaying application block message
- Show-InstallationWelcome, Show-InstallationPrompt: Fixed clipping of text when non-default DPI settings used
- Fixed creation of URL shortcuts in New-Shortcut
- Fixed detection of when script is running from a task sequence
- Fixed issue in Get-InstalledApplication where an "Invalid Cast" error was thrown if registry contains a value with invalid data
- Fixed issue in New-Shortcut where an error would be thrown if no icon was specified
- Fixed issue in Test-Battery where the incorrect information would be returned if the battery was failing
- Fixed issue in Send-Keys where there was an improper script exit upon failure
- Fixed issue with Show-BalloonTip where icon was not disposed before exit
- Fixed issue where Block-Execution would display an error creating the scheduled task if the Application Name was too long (command was longer than 261 characters)
- Fixed issue where Block-Execution was not showing a message window when user tried to open a blocked app
- Fixed issue where Block-Execution was creating a scheduled task to clean up blocked apps which would not execute
- Fixed issue where AppDeployToolkitHelp.ps1 threw an error when executed
- Fixed CloseAppsCountdown and PersistPrompt in Show-InstallationWelcome (bug introduced in v3.2)
- Fixed issue in Show-InstallationPrompt and Show-InstallationWelcome where timeout object was not disposed and would cause timeout of subsequent UI components
- Fixed issue with enumerating hashtable boolean parameters into string format
- Fixed centering of Show-InstallationProgress window where DPI scale is not 100%
- Fixed centering of toolkit banner in UI elements where the full length banner is not used
- Fixed issue where MinimizeAll() and UndoMinimizeAll() method calls were inserting a $null into the pipeline
- Fixed issue in Execute-Process to asynchronously capture process Standard Output
- Updated Deploy-Application template to be consistent with the rest of the toolkit. Scripts from previous versions are fully backwards compatible.
- Updated German translations
- Updated Norwegian translations
- Updated documentation

**Version 3.2.0 [08/22/2014]**
- Added further optimization and best practice enhancements to Execute-Process to improve parameter validation, object and exception handling.
- Added a check for in-progress MSI installations preventing an MSI from installing and waits up to 10 minutes - this reduces instances of MSI error code 1618 (Test-MsiExecMutex)
- Added Get-MsiExitCodeMessage - used by Execute-MSI (also available to Execute-Process) to get MSI exit code description if not handled by default.
- Get-InstalledApplication and Remove-MSIApplication now support exact application name matching
- Added error message to clarify when the toolkit fails to dot source and functions are not available
- Improved exception handling in Write-Log
- The install title format on asynchronous UI elements will now match the synchronous UI elements
- Asynchronous toolkit invocations now write to the same log file as synchronous
- Install-MSUpdates now handles redistributables such as the VC++ runtime redistributables
- Added additional uninstallation sections to Deploy-Application.ps1 template to mirror installation sections
- Added Dutch translations
- (Thanks to mmashwani for his excellent contributions to this release)

**Version 3.1.5 [08/01/2014]**
- Added Send-Keys function to send a sequence of keys to an application window (Thanks to mmashwani)
- Added 3 optimization/stability improvements to Execute-Process following MS best practice (Thanks to mmashwani)
- Fixed issue where Execute-MSI did not use value from XML file for uninstall but instead ran all uninstalls silently by default
- Fixed error on 1641 exit code (should be a success like 3010)
- Fixed issue with error handling in Invoke-SCCMTask
- Fixed issue with deferral dates where the letter Z is in the month (non-English languages)
- Fixed issue where custom process descriptions failed to display in Show-InstallationWelcome if an exact process name was specified
- Updated French translations

**Version 3.1.4 [06/10/2014]**
- Added New-Folder and Remove-Folder functions (Thanks to SueH)
- Added NoWait parameter to Execute-Process
- Added the ability for Deploy-Application.exe to point to a different .ps1 file by specifying it on the command-line
- Added checks to Deploy-Application.exe to verify the AppDeployToolkit folder exists
- Added PSAppDeployToolkit icon to Deploy-Application.exe
- Fixed issue where hang could occur if file version was null when using Get-FileVersion
- Improved exception handling and logging for a number of functions

**Version 3.1.3 [05/22/2014]**
- Added CompressLogs option to the config file. Each Install / Uninstall creates a timestamped zip file with all MSI and PSAppDeployToolkit logs contained within
- Added variable expansion to all paths in the configuration file
- Added documentation for each of the Toolkit internal variables that can be used
- Changed Install-MSUpdates to continue if any errors are encountered when installing updates
- Implement /Force parameter on Update-GroupPolicy (ensure that any logoff message is ignored)
- Fixed issue where the AppClose Countdown timer would reset if the user attempted to close the UI
- Fixed error handling in Set-PinnedApplication if the executable existed but the shortcut wasn't actually pinned

**Version 3.1.2 [04/30/2014]**
- Added Get-IniValue / Set-IniValue functions. Replaces Get-IniContent / Set-IniContent which were a bit unwieldy. The new functions are much easier to use.
- Added -Value parameter to Get-RegistryKey so that a specific registry value can be retrieved instead of the entire Key as an object
- Fixed Test-Battery to work with machines that can have multiple batteries
- Fixed issue where BlockExecution would fail if a Custom Temp Path was specified in the config file
- Updated examples with latest templates and Office 2013
- Updated French translations
- Updated Japanese translations

**Version 3.1.1 [03/27/2014]**
- Added -NoCountdown parameter to Show-InstallationRestartPrompt to provide an option for just displaying the restart now or restart later buttons. The UI will restore/reposition itself persistently based on the interval value specified in the config file.
- Fixed issue whereby the restart prompt could be closed from the task bar
- Added Recurse parameter to Copy-File function
- Fixed exact process match on Show-InstallationWelcome was broken in a recent release
- Fixed MinimizeWindows parameter which was broken in a recent release

**Version 3.1.0 [02/25/2014]**
- Re-coded BlockExecution to be more stable and prevent issues unblocking execution
- Added support for installing applications on Remote Desktop Session Host/Citrix servers
- Added handling of UI assembly load errors affecting PS 2.0 and .NET 4.0 and greater. Errors are ignored only if deploy mode is set to non-interactive
- Fixed ContinueOnError parameter data type which was set as a switch but should have been boolean
- Added default progress message for Uninstallation deployment mode
- Fixed issue with detection of Session 0 on XP which resulted in no UI being displayed
- Fixed issue where Working Directory wouldn't be set correctly if using a subfolder in $dirFiles

**Version 3.0.12 [01/17/2014]**
- Fixed error handling of Pin To Start in Win 8 (not programmatically supported in Win 8.1)
- Fixed issue where user generated thumbs.db file in AppDeployToolkit folder could cause BlockExecution to fail
- Fixed issue where uninstalls would fail if there was a backslash in the MSI DisplayName
- Fixed issue where Execute-Process could fail but return an exit code of 0 indicating success
- Fixed issue where DeferDays or in some scenarios DeferDeadline values would be erroneously converted to a different timezone.
- Fixed issue where Remove-MSIApplications and Get-InstalledApplication wouldn't find a match if the application name contained square brackets

**Version 3.0.11 [01/07/2014]**
- Changed persistent prompt interval to default to 60 seconds
- Fixed issue with false Session 0 detected on some XP machines
- Fixed minimum configuration version checking
- Fixed Set-RegistryValue not accepting arrays

**Version 3.0.10 [12/05/2013]**
- Improved process matching in CloseApps, Remove-MSIApplications & Get-InstalledApplication (for support of apps with special characters, like NotePad++)
- Revert change that prevented AllowDefer and CloseAppsCountdown from working together

**Version 3.0.9 [11/29/2013]**
- Note- We recently attempted to add a killer new feature and release 3.1.0. We thought we had nailed it but ended up hitting problems once we went into production environments.
  - Worse still, we ended up causing a bunch of problems in 3.0.8 trying to reverse out all of these changes.
  - We've prided ourselves on getting to be a really stable toolkit that works well in an enterprise environment.
  - So we're very sorry for the shoddiness of 3.0.8 and have taken a number of steps (a QA process and test scripts) to ensure we don't have these issues going forward.
- Added handling of X86 environment variables so they are never empty
- Added prevention of AllowDefer and CloseAppsCountdown at the same time
- Added x86 support to Register-DLL and Unregister-DLL (if DLL is dropped in SysWOW64)
- Fixed Register-DLL and Unregister-DLL when using paths with spaces
- Fixed sporadic log file corruption that were introduced in v3.0.8
- Fixed CloseAppsCountdown that got broken in v3.0.8
- Fixed DeployMode handling that got broken in v3.0.8

**Version 3.0.8 [11/26/2013]**
- Show-InstallationWelcome now supports exact process matching by specifying .exe after process name.
- Added Swedish translation & improved German translation.
- Changed Write-Log function to use StreamWriter, which is faster
- Modified application name on block app execution prompt (cosmetic).
- Fixed an issue where displaying BlockExecution message with interfere with the main application installation
- Fixed parameter encapsulation in asynchronous prompts and scheduled task creation.
- Fixed extension script not dot-sourcing from UNC path.
- Fixed Toolkit logpath not reading config file value.
- Fixed UI timeout resetting on Show-InstallationWelcome prompt when continue button is clicked
- Fixed UI timeout not decrementing deferrals remaining.

**Version 3.0.7 [10/24/2013]**
- Fixed issue where Session 0 was not detected correctly, resulting in issues when attempting to display a UI when none was allowed
- Fixed Installation Prompt and Installation Restart Prompt appearing when deploy mode was non-interactive or silent
- Fixed issue where defer prompt is displayed after force closing multiple applications
- Fixed issue executing blocked app execution dialog from UNC path (executed instead from local temporary directory)
- Added sanitization of all application details to prevent invalid characters in log file name creation (which would break the script)
- Fixed Test-Battery function returning inverse value (was True when Power was connected)
- Fixed error failing to locate schtasks.exe
- Fixed "Restart Now" button text not using text from config file
- Corrected the window properties of the countdown-no-hide phase of restart prompt to be topmost.

**Version 3.0.6 [10/10/2013]**
- Added PersistPrompt parameter to Show-InstallationWelcome and Show-InstallationPrompt. Prompt window is persistently returned to center screen after interval specified in config file (default 10 seconds). For Show-InstallationWelcome, this only takes effect if deferral is not available to user. The user will have no option but to respond to the prompt - resistance is futile!
- Added example advanced Office 2010 deployment script
- Asynchronous actions now write to the same log file as synchronous actions
- Changed Defer Exit Code default to 5000. In CM07, deployments with deferrals should be set on a recurring schedule (ie, every 4 hours, not just As Soon As Possible). In CM12, the Application Model evaluates once a day by default (can be changed in Client Settings)
- Fixed Get-ScheduledTask not running on non-English system
- Fixed error deleting deferral history in registry on successful installation.
- Fixed non default Defer Exit Code from displaying error - should display a warning
- Updated documentation with information on how to build and deploy Adobe Acrobat using SCCM 2007 / 2012 packages, and SCCM 2012 application model
- Updated documentation with information on how to use advanced Office 2010 deployment script
- Updated documentation with important information on how deferrals work with SCCM 2007 and differences with SCCM 2012

**Version 3.0.5 [09/27/2013]**
- Fixed issue with Show-InstallationPrompt not running asynchronously with the NoWait parameter.
- Fixed issue with Show-InstallationRestartPrompt closing with the parent process.
- Fixed issue matching application names with brackets in Remove-MSIApplications.
- Fixed exception after clicking "Close Programs" button on Show-InstallationWelcomePrompt (only known to affect v3.0.4).
- Fixed issue with one deferral more showing than is remaining (cosmetic only)
- Deferral history is now reset on successful installation
- Added ExitOnTimeout parameter to Show-InstallationPrompt (default is true)
- Added MinimizeWindows parameter to Show-InstallationPrompt (default is false)
- Added MinimizeWindows parameter to Show-InstallationWelcome (default is true)
- Added extra space for the <DeferPrompt_WelcomeMessage> message
- Added additional XML configuration items:
  - Specify separate toolkit and msi log directories
  - Specify whether administrator rights are required
  - Minimum supported version check for the XMl config file
  - Configure deferral exit code
  - Configure temporary path for caching toolkit files
  - Configure registry path for storing deferral information

**Version 3.0.4 [09/13/2013]**
- Added parameter to Show-InstallationWelcome called "AllowDeferCloseApps" which only provides a deferral option if there are running applications that need to be closed.
- Added ability to configure the timeout period for Show-InstallationPrompt
- Added option to position progress window in bottom right of screen (above task bar) and specify whether it is topmost.
- Added Danish translations
- Added Italian translations
- Added Japanese translations
- Changes to French translations
- Fixed issue where Test-MSUpdates function could fail with an exception calling QueryHistory
- Fixed issue where Install-MSUpdates function could fail with an "argument is null or empty" exception
- Removed default ButtonRightText "OK" value on Show-InstallationPrompt to allow prompts with only the middle button.
- The exit code used for UI timeouts or deferrals can now be configured in XML (default is 1618).

**Version 3.0.3 [09/05/2013]**
- Fixed issue where spaces in the file path would cause block execution prompt not to work
- Fixed issue where non-English UIs which were not already localized would fail to fall back on English as a default
- Fixed issue with uninstalling applications with non-ascii characters in the name or version

**Version 3.0.2 [08/29/2013]**

- Added CheckDiskSpace and RequiredDiskSpace parameters to Show-InstallationWelcome
  - If CheckDiskSpace parameter is used, disk space will be evaluated before showing the Welcome screen. If disk space requirements are not met, an error message will be displayed and a Fast Retry (1618) exit code generated. The disk space requirements are calculated automatically based on the size of the source files or by specifying the required space in MB using the RequiredDiskSpace parameter.
- Added Get-FreeDiskSpace function (returns value in MB)
- Show-InstallationPrompt enhancements
  - MessageAlignment - align message text on the prompt
  - Icon - display a system icon on the prompt
  - NoWait - display the prompt asynchronously and continue the script without waiting for user response
  - Cosmetic improvements
- Added parameter validation to Invoke-SCCMTask
- Cosmetic improvements to Show-InstallationWelcome & Show-InstallationProgress
- Fixed issue where user would not be granted their last deferral.

**Version 3.0.1 [08/28/2013]**
- Fixed issue where using NonInteractive DeployMode would cause a bluescreen (!!!)
- Fixed issue where Remove-MSIApplications would only remove the first application if multiple matches were made
- Fixed issue where automatic timeout of Welcome screen was not triggered
- Fixed issue where automatic timeout of installation prompt caused a .NET exception
- Fixed help system to work with Standard User rights
- Fixed text cutoff issue with non-English languages
- Minor fixes to the help information and default template
- Updated French translation

**Version 3.0.0 [08/23/2013]**
- Each UI element now has a customizable banner
- Show-InstallationWelcome function
  - Replaces Stop-RunningApplications (deprecated) and presents a unified welcome screen
  - Deferral system. Ability to defer installation x number of times / days / until deadline date
  - Ability to force closing applications after a countdown period
- Show-InstallationPrompt function
  - Replaces Show-DialogBox (deprecated)
  - Standardized UI with the rest of the toolkit
  - Customizable text and 3 buttons
- Show-InstallationRestartPrompt function
  - Displays a restart prompt with a countdown to a forced restart, independent of SCCM restart mechanism
- Invoke-SCCMTask function
  - Replaces Update-SCCMInventory and Update-SCCMDeployments (deprecated)
  - No longer reliant on COM object which could cause issues on x64 machines
- Get-UniversalDate function
- Write-Log now works with the PowerShell Pipeline and -PassThru
- Set-RegistryKey now accepts arrays
- Improved error handling: ContinueOnError switch allows to to control the error action for functions that fail. ContinueOnErrorGlobalPreference allows you to debug the script to detect errors in functions.
- Fixed issue with BlockExecution failing under PowerShell v2
- Fixed issue with Show-InstallationProgress failing on .NET Framework versions prior to 4.5
- Fixed issue with command-line parameters not being passed through Deploy-Application.exe on x86 operating systems
- Test-MSUpdates KB regex matching is now case insensitive
- Parameters passed to scripts are now logged.

- Backwards Compatibility Considerations:
  - The following functions have been deprecated and are no longer supported:
  - Stop-RunningApplications
  - Update-SCCMInventory
  - Update-SCCMDeployments

Version 2.0.1 [08/15/2013]**
- New Show-DialogBox parameters: TopMost and DefaultButton. Specify whether dialog box is topmost (new default) and specify default selected button.
- Fixed issue with long file names when removing certain types of software using Remove-MSIApplications.
- Added ability to turn automatic balloon notifications on or off in the XML config file.
- Attempted to fix issue with stale balloon notifications.
- Attempted to fix issue with installation progress not updating or hanging.

Version 2.0.0 [08/07/2013]**
- Initial Public Release.
