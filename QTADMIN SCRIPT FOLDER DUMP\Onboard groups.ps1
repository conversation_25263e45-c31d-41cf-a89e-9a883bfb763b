﻿Import-Module AzureAD
Import-Module ExchangeOnlineManagement

Connect-azuread
Connect-ExchangeOnline

do{
    $TemplateUser = Read-Host -Prompt 'Who will be used as the template user? (firstname.lastname)'
    $TemplateUser += '@qhrtech.com'

    $NewUser = Read-Host -Prompt 'What is the name of the new user? (firstname.lastname)'
    $NewUser += '@qhrtech.com'
    $NewUserObjectId = (Get-AzureADUser -ObjectId $NewUser).ObjectId 
    $NewUserDisplayName = (Get-AzureADUser -ObjectId $NewUser).DisplayName

    $templateUserGroups = Get-AzureADUserMembership -ObjectId $TemplateUser 
    foreach($group in $templateUserGroups) { 
        try{
            Add-AzureADGroupMember -ObjectId $group.ObjectId -RefObjectId $NewUserObjectId
            }
        catch{
            if($_ -like "*Cannot Update a mail-enabled security groups and or distribution list*"){
                $distGroupName = $group.DisplayName
                $distGroupMembers = Get-DistributionGroupMember -Identity $distGroupName

                if($distGroupMembers.Name -contains $NewUserDisplayName){continue}
                else{
                    if($group.DisplayName -notlike "*All Staff*"){
                        Write-Host
                        Write-Host "Adding $NewUserDisplayName to the mail group: $distGroupName"
                        Add-distributionGroupMember -Identity $distGroupName -Member $NewUser
                        }
                    }  
            }
            else{continue}
        }
    }
    $NewUserArea = "All Staff - "
    $NewUserArea += Read-Host -Prompt 'Which province/region is the user from? (BC, AB, MB, NS, ON, SK)'
    if( $NewUserArea -contains "All Staff - BC"){
        $NewUserKelownaOrRemote = Read-Host -Prompt "Does the new onboard live in Kelowna? (y/n)"
        if($NewUserKelownaOrRemote -contains "y"){
            Write-Host "Adding $NewUserDisplayName to the mail group: All Staff - BC Kelowna"
            Add-distributionGroupMember -Identity "All Staff - Kelowna BC" -Member $NewUser
            }
        else{
            Write-Host "Adding $NewUserDisplayName to the mail group: All Staff - BC"
            Add-distributionGroupMember -Identity "All Staff - BC" -Member $NewUser
            }
        }
    else{
        Write-Host "Adding $NewUserDisplayName to the mail group:  $NewUserArea"
        Add-distributionGroupMember -Identity $NewUserArea -Member $NewUser
        }
    $strQuit = Read-Host "`n Stop replicating groups for new users? (y/n)"
}
Until ($strQuit -contains "y")
disconnect-exchangeonline