﻿$users = get-aduser -Filter {Enabled -eq $True -and PasswordNeverExpires -eq $False} #Only Checks Enabled users that has no expired password set



$groupCheck = @("* All Staff","* All Staff-Winnipeg MB", "* All Staff-Vancouver BC", "* All Staff-Kelowna LM5-300", "* All Staff-Kelowna LM5-200", "* All Staff-Toronto GTA ON", "* All Staff-Toronto 18 King E", "* All Staff-Ottawa ON", "* All Staff-Kelowna BC", "* All Staff-Calgary AB", "* All Staff- SK", "* All Staff- ON", "* All Staff- MB", "* All Staff- BC", "* All Staff- AB") #Enter One Group Here or multiple groups if users only have to match 1 group.


foreach ($user in $users) {

    $groupMemberships = Get-ADPrincipalGroupMembership -Identity $user.SamAccountName



    $groupNotMatch = "true"
    foreach ($group in $groupMemberships) {

        for ($i = 0; $i -lt $groupCheck.Length; $i++) {
     
            if ($group.name -eq $groupCheck[$i]) {
                
                $groupNotMatch = "false"
                
          
            }

        


   
        } #end for loop

    }

    if ($groupNotMatch -eq "true") {
     $name = $user.SamAccountName
    "Groups Not Found For: $name"
        
        
    

    }
}