﻿#Reference File
#$reffile = Get-Content -Path C:\Scripts\Chris\ISO-data-man\CreatedBy.txt
$count = 0
$EditMode = $true

$ext2archive = Read-Host "Enter the FILE EXTENSION (without dot) to archive (or all)"
$cat2archive = Read-Host "Enter the CATEGORY to archive (or all)"
$des2archive = Read-Host "Enter the DESIGNATION to archive (or all)"

#Load necessary module to connect to SPOService
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client") | Out-Null
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client.Runtime") | Out-Null

$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)

Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred

$WebUrl = "https://qhrtech.sharepoint.com/ca/ISO/"

#Connect to SharePoint Online service
Write-Host "Logging into SharePoint online service." -ForegroundColor Green

$Context = New-Object Microsoft.SharePoint.Client.ClientContext($WebUrl)
$Context.Credentials = New-Object Microsoft.SharePoint.Client.SharePointOnlineCredentials($creduser, $encrypted)


#Get the Necessary List
Write-Host "Getting the required list.`n`n" -ForegroundColor Green
$List = $Context.Web.Lists.GetByTitle("Documents")

$Query = [Microsoft.SharePoint.Client.CamlQuery]::CreateAllItemsQuery(1500); 
$Items = $List.GetItems($Query);

$Context.Load($Items);
$Context.ExecuteQuery();

#### WARNING EDITING DONE BELOW THIS LINE!!! ###
foreach($item in $Items)
{
   $ifquery = $true
    if ($ext2archive -ne "all") {
        $ifquery = $item["File_x0020_Type"] -eq $ext2archive
      } 
    if ($cat2archive -ne "all") {
        $ifquery = $ifquery -and  ($item["Categories0"] -eq $cat2archive)
      } 
    if ($des2archive -ne "all") {
        $ifquery = $ifquery -and ($item["Designation"] -eq $des2archive)
      }  

    $ifquery = $ifquery -and ($item["Archived"] -eq $false)
    
    if($ifquery)
    #if($item["File_x0020_Type"] -eq $ext2archive -and $item["Categories0"] -eq $cat2archive -and $item["Archived"] -eq $false -and $item["Designation"] -eq $des2archive)
    {
        Write-Host "Archving: $($item["FileLeafRef"])" -ForegroundColor Green        
        #sleep 3
        $count++
        # Set EditMode to $true at top of file to make actual changes!!! 
        if ($EditMode -eq $true) {
          $item.file.CheckOut()
          $item["Archived"] = $true
          $item.Update()
          $Context.ExecuteQuery()      

          $item.file.CheckIn("Archived",[Microsoft.SharePoint.Client.CheckinType]::MajorCheckIn)
          $Context.ExecuteQuery()      
        }
       
    }
}

Write-Host "`n`nArchived $count files." -ForegroundColor Green