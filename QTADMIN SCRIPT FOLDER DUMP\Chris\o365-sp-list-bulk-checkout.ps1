﻿
$count = 0
$EditMode = $false

$filenametocheckout = "car"
$filenametoignore = "car0"

#Load necessary module to connect to SPOService
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client") | Out-Null
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client.Runtime") | Out-Null

$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)

Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred

$WebUrl = "https://qhrtech.sharepoint.com/ca/ISO/"

#Connect to SharePoint Online service
Write-Host "Logging into SharePoint online service." -ForegroundColor Green

$Context = New-Object Microsoft.SharePoint.Client.ClientContext($WebUrl)
$Context.Credentials = New-Object Microsoft.SharePoint.Client.SharePointOnlineCredentials($creduser, $encrypted)


#Get the Necessary List
Write-Host "Getting the required list.`n`n" -ForegroundColor Green
$List = $Context.Web.Lists.GetByTitle("Documents")

$Query = [Microsoft.SharePoint.Client.CamlQuery]::CreateAllItemsQuery(1500); 
$Items = $List.GetItems($Query);

$Context.Load($Items);
$Context.ExecuteQuery();

#### WARNING EDITING DONE BELOW THIS LINE!!! ###
foreach($item in $Items)
{
      
   
   if($item["FileLeafRef"] -notmatch $filenametoignore -and $item["FileLeafRef"] -match $filenametocheckout -and $item["Archived"] -eq $false)
    {
        Write-Host "Checking Out: $($item["FileLeafRef"])" -ForegroundColor Green        
        #sleep 3
        $count++
        # Set EditMode to $true at top of file to make actual changes!!! 
        if ($EditMode -eq $true) {
          $item.file.CheckOut()
          $Context.ExecuteQuery()      

    }
       
    }
}

Write-Host "`n`nChecked Out $count files." -ForegroundColor Green