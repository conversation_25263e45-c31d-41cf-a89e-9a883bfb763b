<#
.SYNOPSIS
    Removes the currently logged-on user from the local 'Administrators' group on Windows 11 devices.

.DESCRIPTION
    This script is designed to be deployed via Microsoft Intune as a remediation script.
    It identifies the currently logged-on user and attempts to remove them from the
    local 'Administrators' group. The script includes robust error handling, detailed
    logging directly to a file (no console output), and will exit with a code of 0
    for success or 1 for failure to Intune.

    It prioritizes using the PowerShell module 'Microsoft.PowerShell.LocalAccounts'
    for group management, falling back to 'net localgroup' command if the module
    is not available or cannot be installed.

.NOTES
    Version: 3.1
    Date: 2025-06-18
    Author: <PERSON><PERSON>

    Intended for Azure AD Joined / Hybrid Azure AD Joined Windows 11 devices.
    Runs in SYSTEM context when deployed via Intune.

    Important: For administrative changes to take full effect for an actively
    logged-in user, the user typically needs to sign out and sign back in.

.EXAMPLE
    .\Remove-LocalAdminPermissions.ps1
#>

[CmdletBinding(DefaultParameterSetName = 'Default', SupportsShouldProcess = $false)]
param()

# --- Script Configuration ---
# Define the local group name to manage
$TargetGroupName = "Administrators"
# Define the path for the script's custom log file
# This log file will be created/appended in C:\temp
$LogPath = Join-Path "C:\temp" "RemoveLocalAdminPermissions_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# --- Error Handling & Logging Setup ---
# Set Strict Mode for better error detection
Set-StrictMode -Version Latest

# Initialize a flag to track overall script success (script-scoped)
$script:scriptSuccess = $false

# Function to write messages directly to the log file
function Write-ToLog {
    param (
        [string]$Message,
        [string]$Level = "INFO" # INFO, VERBOSE, or ERROR
    )
    $timestamp = (Get-Date).ToString('yyyy-MM-dd HH:mm:ss')
    $logEntry = "$timestamp - $Level{}: $Message"
    # Use Add-Content to append to the log file. Use SilentlyContinue to prevent logging issues from stopping the script.
    # No console output from this function.
    Add-Content -Path $LogPath -Value $logEntry -ErrorAction SilentlyContinue
}

# Function for verbose messages (logs to file only)
function Write-ScriptVerbose {
    param (
        [string]$Message
    )
    Write-ToLog -Message $Message -Level "VERBOSE"
}

# Function for error messages (logs to file only, sets failure flag)
function Write-ScriptError {
    param (
        [string]$Message
    )
    Write-ToLog -Message $Message -Level "ERROR"
    $script:scriptSuccess = $false # Ensure failure if an error occurs
}

# --- Main Script Logic ---
# Capture existing preference variables to restore them later (good practice, though less critical with direct logging)
$originalVerbosePreference = $VerbosePreference
$originalErrorActionPreference = $ErrorActionPreference

# Set preferences for this script's execution
# Set VerbosePreference to SilentlyContinue to prevent *external cmdlet* verbose output to console
$VerbosePreference = 'SilentlyContinue'
# Set ErrorActionPreference to SilentlyContinue for all cmdlets by default,
# and we will explicitly check $? or $Error for success/failure.
$ErrorActionPreference = 'SilentlyContinue'

try {
    Write-ScriptVerbose "Starting script: Remove-LocalAdminPermissions.ps1 (Version 3.1)"
    Write-ScriptVerbose "Log file path: $LogPath"

    # Ensure C:\temp directory exists for logs
    # Using Out-Null to suppress potential console output from New-Item only if it's not being assigned
    New-Item -Path "C:\temp" -ItemType Directory -ErrorAction SilentlyContinue | Out-Null
    if (-not (Test-Path "C:\temp" -PathType Container -ErrorAction SilentlyContinue)) {
        # If directory creation failed or it doesn't exist, log and fall back to user's temp
        Write-ScriptError "Failed to ensure C:\temp directory exists. Error: $($Error[0].Exception.Message). Log will not be saved there."
        $LogPath = Join-Path $env:TEMP "RemoveLocalAdminPermissions_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
        Write-ScriptVerbose "Using user's TEMP directory for log path: $LogPath"
    }

    # 1. Determine current logged-on user
    Write-ScriptVerbose "Attempting to identify the currently logged-on user..."
    $loggedInUser = $null
    $loggedInUserSid = $null # To store the SID of the logged-in user
    
    $userSuccessfullyIdentified = $false # Flag to track if user identification was successful
    
    # Corrected: Get-WmiObject output is assigned to $computerSystem without | Out-Null
    $computerSystem = Get-WmiObject -Class Win32_ComputerSystem -ErrorAction SilentlyContinue
    if ($? -and $null -ne $computerSystem -and $null -ne $computerSystem.UserName) {
        $loggedInUser = $computerSystem.UserName
        Write-ScriptVerbose "Identified logged-on user: '$loggedInUser'"
        $userSuccessfullyIdentified = $true

        # Attempt to get the SID of the logged-on user for more robust comparison
        try {
            $ntAccount = New-Object System.Security.Principal.NTAccount($loggedInUser)
            $loggedInUserSid = $ntAccount.Translate([System.Security.Principal.SecurityIdentifier]).Value
            Write-ScriptVerbose "Identified logged-on user SID: '$loggedInUserSid'"
        }
        catch {
            Write-ScriptVerbose "Could not resolve SID for logged-on user '$loggedInUser'. Error: $($_.Exception.Message). Will rely on username matching."
        }
    }
    else {
        Write-ScriptError "No interactive user is currently logged on or could not be identified."
        $script:scriptSuccess = $false # Set failure flag
    }

    # Only proceed with group management if a user was successfully identified
    if ($userSuccessfullyIdentified -and $null -ne $loggedInUser) {
        # 2. Validate the logged-on user name for expected format (firstname.lastname)
        $userToCheck = $loggedInUser.Split('\') | Select-Object -Last 1
        Write-ScriptVerbose "User name extracted for check: '$userToCheck'"

        if ($userToCheck -notmatch '^[a-zA-Z]+\.[a-zA-Z]+$') {
            Write-ScriptVerbose "Logged-on user '$userToCheck' does not appear to follow the 'firstname.lastname' pattern. Proceeding regardless, but be aware."
            Write-ScriptVerbose "This script will attempt to remove '$userToCheck' if they are an admin."
        }

        # 3. Check for existence of 'Microsoft.PowerShell.LocalAccounts' module and install if needed
        $useLocalAccountsModule = $false
        Write-ScriptVerbose "Checking for 'Microsoft.PowerShell.LocalAccounts' module..."

        # Corrected: Get-Module output is checked directly, not nulled if needed for variable
        $moduleCheck = Get-Module -ListAvailable -Name "Microsoft.PowerShell.LocalAccounts" -ErrorAction SilentlyContinue
        if (-not $? -or $null -eq $moduleCheck) {
            # If Get-Module itself failed, or module not found
            Write-ScriptVerbose "Module 'Microsoft.PowerShell.LocalAccounts' not found. Attempting to install it."
            # Use Out-Null to suppress potential console output from Install-Module
            Install-Module -Name "Microsoft.PowerShell.LocalAccounts" -Scope AllUsers -Force -ErrorAction SilentlyContinue | Out-Null
            if ($?) {
                # Check if Install-Module was successful
                Write-ScriptVerbose "Module 'Microsoft.PowerShell.LocalAccounts' installed successfully."
            }
            else {
                Write-ScriptError "Failed to install 'Microsoft.PowerShell.LocalAccounts' module. Error: $($Error[0].Exception.Message). This might be due to network issues, repository access, or insufficient permissions. Falling back to 'net localgroup'."
                $useLocalAccountsModule = $false # Ensure module is not used
            }
        }

        # Now attempt to import the module (whether it was already present or just installed)
        # Corrected: Check if the module is actually present before attempting import.
        # Import-Module output is nulled.
        if (Get-Module -ListAvailable -Name "Microsoft.PowerShell.LocalAccounts" -ErrorAction SilentlyContinue) {
            Import-Module -Name "Microsoft.PowerShell.LocalAccounts" -ErrorAction SilentlyContinue | Out-Null
            if ($?) {
                # Check if Import-Module was successful
                Write-ScriptVerbose "Module 'Microsoft.PowerShell.LocalAccounts' imported successfully. Will use it for group management."
                $useLocalAccountsModule = $true
            }
            else {
                Write-ScriptError "Failed to import 'Microsoft.PowerShell.LocalAccounts' module even after availability check. Error: $($Error[0].Exception.Message). Falling back to 'net localgroup'."
                $useLocalAccountsModule = $false # Ensure module is not used
            }
        }
        else {
            Write-ScriptVerbose "Module 'Microsoft.PowerShell.LocalAccounts' is not available after install attempt or was never available. Will fall back to 'net localgroup' command."
            $useLocalAccountsModule = $false # Ensure module is not used
        }

        # 4. Check if the user is a member of the Administrators group and remove them
        $isMember = $false

        # Try using the module if it's available and successfully imported
        if ($useLocalAccountsModule) {
            Write-ScriptVerbose "Checking membership using Get-LocalGroupMember..."
            # Corrected: $members is assigned without | Out-Null
            $members = Get-LocalGroupMember -Group $TargetGroupName -ErrorAction SilentlyContinue | Select-Object Name, SID
            
            if ($?) {
                # Check if Get-LocalGroupMember itself succeeded
                if ($null -ne $members) {
                    # Check by name or by SID (if SID was successfully resolved)
                    $foundByName = $members | Where-Object { $_.Name -eq $loggedInUser -or $_.Name -eq $userToCheck }
                    $foundBySid = $false
                    if ($null -ne $loggedInUserSid) {
                        $foundBySid = $members | Where-Object { $_.SID -and ($_.SID.Value -eq $loggedInUserSid) }
                    }

                    if ($foundByName -or $foundBySid) {
                        $isMember = $true
                        Write-ScriptVerbose "User '$loggedInUser' is currently a member of the '$TargetGroupName' group (via Get-LocalGroupMember)."
                    }
                    else {
                        Write-ScriptVerbose "User '$loggedInUser' is NOT a member of the '$TargetGroupName' group (via Get-LocalGroupMember). No action needed."
                        $script:scriptSuccess = $true # User is not an admin, so script goal is met
                    }
                }
                else {
                    # Get-LocalGroupMember returned no members. This might mean the group is empty or an issue occurred.
                    Write-ScriptVerbose "Get-LocalGroupMember returned no members. Falling back to 'net localgroup' for re-check."
                    $useLocalAccountsModule = $false # Force fallback
                }
            }
            else {
                # Get-LocalGroupMember failed (e.g., access denied, internal error)
                Write-ScriptError "Failed to check group membership with Get-LocalGroupMember. Error: $($Error[0].Exception.Message). Falling back to 'net localgroup'."
                $useLocalAccountsModule = $false # Disable module usage for the rest of the script
            }
        }

        # Fallback/Primary logic if module is not used or failed, or if user was found to be a member by the module.
        if (-not $useLocalAccountsModule -or ($isMember -and -not $script:scriptSuccess)) {
            if (-not $script:scriptSuccess) {
                Write-ScriptVerbose "Checking membership and attempting removal using 'net localgroup' as primary or fallback method."
                
                # Corrected: Capture output to variable, then use Out-Null to suppress console if needed.
                $groupOutput = net localgroup $TargetGroupName 2>&1 | Out-String 
                if ($LASTEXITCODE -eq 0) {
                    # Command itself ran successfully
                    # Check if the user's full name, username part, or SID exists in the group output.
                    if (($groupOutput -match [regex]::Escape($loggedInUser)) -or
                        ($groupOutput -match [regex]::Escape($userToCheck)) -or
                        ($groupOutput -match [regex]::Escape($loggedInUser.Split('\')[-1])) -or
                        ($null -ne $loggedInUserSid -and ($groupOutput -match [regex]::Escape($loggedInUserSid)))) {
                        
                        $isMember = $true # User is confirmed a member by net localgroup
                        Write-ScriptVerbose "User '$loggedInUser' identified as a member of the '$TargetGroupName' group via 'net localgroup' (by name or SID)."
                    }
                    else {
                        Write-ScriptVerbose "User '$loggedInUser' is NOT a member of the '$TargetGroupName' group via 'net localgroup'. No action needed."
                        $script:scriptSuccess = $true # User is not an admin, so script goal is met
                    }
                }
                else {
                    Write-ScriptError "Failed to check group membership using 'net localgroup'. Exit code: $LASTEXITCODE. Output: $groupOutput."
                    $script:scriptSuccess = $false # Set failure flag
                }
            }
        }

        # If user is confirmed to be a member by either method (and $script:scriptSuccess is not already true), proceed with removal
        if ($isMember -and -not $script:scriptSuccess) {
            Write-ScriptVerbose "Attempting to remove user '$loggedInUser' from the '$TargetGroupName' group."
            if ($useLocalAccountsModule) {
                Write-ScriptVerbose "Attempting removal using Remove-LocalGroupMember."
                $priorErrorCount = $Error.Count # Capture current error count

                # Corrected: Remove-LocalGroupMember output is nulled *after* potential assignment/check.
                Remove-LocalGroupMember -Group $TargetGroupName -Member $loggedInUser -ErrorAction SilentlyContinue 
                
                # Check if a new error occurred
                if ($Error.Count -eq $priorErrorCount) {
                    # No new error, assume success
                    Write-ScriptVerbose "Successfully removed '$loggedInUser' from '$TargetGroupName' using Remove-LocalGroupMember."
                    $script:scriptSuccess = $true
                }
                else {
                    Write-ScriptError "Failed to remove '$loggedInUser' from '$TargetGroupName' using Remove-LocalGroupMember. Error: $($Error[0].Exception.Message). Attempting with 'net localgroup' as fallback."
                    # Fallback to net localgroup if module removal failed
                    $netRemoveResult = (net localgroup $TargetGroupName "$loggedInUser" /delete 2>&1 | Out-String) # Capture output and errors, suppress console
                    if ($LASTEXITCODE -eq 0 -or $netRemoveResult -match 'The command completed successfully.') {
                        Write-ScriptVerbose "Successfully removed '$loggedInUser' from '$TargetGroupName' using 'net localgroup'."
                        $script:scriptSuccess = $true
                    }
                    else {
                        Write-ScriptError "Failed to remove '$loggedInUser' from '$TargetGroupName' using 'net localgroup'. Error: $netRemoveResult. Last exit code: $LASTEXITCODE."
                        $script:scriptSuccess = $false
                    }
                }
            }
            else {
                # If module was not available from the start or failed
                Write-ScriptVerbose "Attempting removal using 'net localgroup'."
                $netRemoveResult = (net localgroup $TargetGroupName "$loggedInUser" /delete 2>&1 | Out-String) # Capture output and errors, suppress console
                if ($LASTEXITCODE -eq 0 -or $netRemoveResult -match 'The command completed successfully.') {
                    Write-ScriptVerbose "Successfully removed '$loggedInUser' from '$TargetGroupName' using 'net localgroup'."
                    $script:scriptSuccess = $true
                }
                else {
                    Write-ScriptError "Failed to remove '$loggedInUser' from '$TargetGroupName' using 'net localgroup'. Error: $netRemoveResult. Last exit code: $LASTEXITCODE."
                    $script:scriptSuccess = $false
                }
            }
        }
    }
    else {
        Write-ScriptVerbose "Skipping group membership check and removal as no interactive user was identified or an error occurred during identification."
        # If user identification failed, $script:scriptSuccess would already be false.
    }

}
catch {
    # Catch any unhandled errors in the main script logic that were not caught by specific blocks
    Write-ScriptError "An unhandled critical error occurred during script execution: $($_.Exception.Message)"
    $script:scriptSuccess = $false
}
finally {
    # Restore original preference variables
    $VerbosePreference = $originalVerbosePreference
    $ErrorActionPreference = $originalErrorActionPreference

    # Set final exit code for Intune
    # Absolutely no Write-Output or Write-Error here, only the exit code.
    if ($script:scriptSuccess) {
        exit 0
    }
    else {
        exit 1
    }
}
