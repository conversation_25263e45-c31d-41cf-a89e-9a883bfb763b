﻿$userlist = (Get-Content C:\Scripts\username-change-list.txt).ToLower()

$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)
#$LiveCred = Get-Credential

Connect-MsolService -Credential $LiveCred -ErrorAction Stop


foreach ($newusername in $userlist) {
$oldsam = $newusername.Split('.')
$oldusername = $oldsam[0].Substring(0,1) + $oldsam[1]

try {
$userfull = get-aduser $oldusername -Properties *
}
catch {
$error.Clear()
$oldusername = $newusername
$userfull = get-aduser $oldusername -Properties *
}

$oldsip = $userfull.proxyAddresses | ? {$_ -match "SIP"}
$oldupn = $userfull.userprincipalname
$newupn = $newusername + '@QHRtech.com'


Write "`nChanging username $oldusername to $newusername"
Write "Changing UPN from $oldupn to $newupn"
Write "Changing SIP address from $oldsip to sip:$newupn"


$lastchance = Read-Host "`n*** PROCEEDING CANNOT BE REVERSED ***`nContinue??? (type: yes)"
if ($lastchance -ne "yes" -or $error) {
    Write "`n`nAborted!!! (or error occurred) Checking next user..."
} else {

# *** CAUTION *** START CHANGING THINGS!!!! *** CAUTION ***

# Update AD Username and UPN
Set-ADUser $oldusername -SamAccountName $newusername -UserPrincipalName $newupn

# Clear Home Folder
Set-ADUser $newusername -Clear homedirectory,msexchshadowproxyaddresses,msRTCSIP-DeploymentLocator,msRTCSIP-FederationEnabled,msRTCSIP-InternetAccessEnabled,msRTCSIP-OptionFlags,msRTCSIP-PrimaryHomeServer,msRTCSIP-PrimaryUserAddress,msRTCSIP-UserEnabled 

# Update Office365 Username
Set-MsolUserPrincipalName -newuserprincipalname $newupn -userprincipalname $oldupn

# Remove OLD SIP address and add new one
#Set-ADUser $newusername -Replace @{"msRTCSIP-PrimaryUserAddress"="sip:$($newupn)"}
Set-ADUser $newusername -Remove @{"Proxyaddresses"=$($oldsip)}
Set-ADUser $newusername -Add @{"ProxyAddresses"="sip:$($newupn)"}

     }

}
Write "`n`n`nChanges Complete!  Force ADFS Sync and update SharePoint username(s) on qtspwfe1!`n`n`n"
Pause