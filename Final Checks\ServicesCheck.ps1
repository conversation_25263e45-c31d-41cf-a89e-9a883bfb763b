# Prerequisites
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Error "This script requires PowerShell version 5 or higher."
    return
}
# Check if WMI is available
if (-not (Get-WmiObject -List | Where-Object {$_.Name -eq 'Win32_OperatingSystem'})) {
    Write-Error "WMI service is not available. Cannot retrieve computer description."
    return 
}
# Check if Get-Service cmdlet is available
if (-not (Get-Command Get-Service -ErrorAction SilentlyContinue)) {
    Write-Error "Get-Service cmdlet is not available. Cannot check service status."
    return 
}

# Function to check service status
function Check-ServiceStatus($ServiceName) {
    $Service = Get-Service $ServiceName -ErrorAction SilentlyContinue
    if ($Service) {
        return [PSCustomObject]@{
            Service = $ServiceName
            Status  = $Service.Status
            Found   = $true
        }
    } else {
        return [PSCustomObject]@{
            Service = $ServiceName
            Status  = "Not Found"
            Found   = $false
        }
    }
}

# Get and display computer description
Write-Output "Computer Description:"
try {
    wmic os get description | Select-Object -Skip 1 
} catch {
    Write-Warning "An error occurred while retrieving the computer description: $_"
}

# Default services to check
$ServicesToCheck = @("ir_agent", "AMPWatchDog", "konea", "ZSAService", "ZSATrayManager", "ZSATunnel", "ZSAUpdater", "ZSAUpm", "CSFalconService")

# Prompt for additional services
$AdditionalServices = Read-Host "Enter any additional services to check (comma-separated, or press Enter to skip)"
if ($AdditionalServices -ne "") {
    $ServicesToCheck += $AdditionalServices -split ','
}

# Check service statuses and store results in an array
$ServiceResults = foreach ($ServiceName in $ServicesToCheck) {
    Check-ServiceStatus $ServiceName
}

# Display results in a table
Write-Output "`nService Statuses:"
$ServiceResults | Format-Table -AutoSize | Out-String -Width 200

# Highlight missing services in red
foreach ($Result in $ServiceResults) {
    if (-not $Result.Found) {
        Write-Host "$($Result.Service) not found." -ForegroundColor Red
    }
}
