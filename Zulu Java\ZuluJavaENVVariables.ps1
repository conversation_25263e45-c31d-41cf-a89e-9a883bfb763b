# Define the paths
$javaHomePath = "C:\Program Files\Zulu\Zulu-8"
$jdkHomePath = "C:\Program Files\Zulu\Zulu-8\bin"

# Set System Environment Variable JAVA_HOME
[System.Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, [System.EnvironmentVariableTarget]::Machine)
Write-Output "System environment variable JAVA_HOME set to $javaHomePath"

# Set User Environment Variable JAVA_HOME
[System.Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, [System.EnvironmentVariableTarget]::User)
Write-Output "User environment variable JAVA_HOME set to $javaHomePath"

# Set User Environment Variable JDK_HOME
[System.Environment]::SetEnvironmentVariable("JDK_HOME", $jdkHomePath, [System.EnvironmentVariableTarget]::User)
Write-Output "User environment variable JDK_HOME set to $jdkHomePath"

# Optionally, update PATH environment variable for the user to include JDK_HOME
$currentUserPath = [System.Environment]::GetEnvironmentVariable("Path", [System.EnvironmentVariableTarget]::User)
if ($currentUserPath -notlike "*$jdkHomePath*") {
    $newUserPath = "$currentUserPath;$jdkHomePath"
    [System.Environment]::SetEnvironmentVariable("Path", $newUserPath, [System.EnvironmentVariableTarget]::User)
    Write-Output "User PATH updated to include $jdkHomePath."
} else {
    Write-Output "User PATH already includes $jdkHomePath."
}

# Optionally, update PATH environment variable for the system to include JAVA_HOME
$currentSystemPath = [System.Environment]::GetEnvironmentVariable("Path", [System.EnvironmentVariableTarget]::Machine)
if ($currentSystemPath -notlike "*$javaHomePath*") {
    $newSystemPath = "$currentSystemPath;$javaHomePath"
    [System.Environment]::SetEnvironmentVariable("Path", $newSystemPath, [System.EnvironmentVariableTarget]::Machine)
    Write-Output "System PATH updated to include $javaHomePath."
} else {
    Write-Output "System PATH already includes $javaHomePath."
}
