# Function to copy a user in Local AD

function Copy-ADUser {
    param (
        [Parameter(Mandatory=$true)]
        [string]$TemplateUser,
        [Parameter(Mandatory=$true)]
        [string]$NewUser
    )

    # Define the base OU
    $baseOU = "OU=QHR Technologies Inc,DC=QuadrantHR,DC=com"

    Write-Verbose "Copying user $TemplateUser to create new user $NewUser in Local AD..."
    try {
        # Search for the template user in all OUs under the base OU
        $templateUserObject = Get-ADUser -Filter { UserPrincipalName -eq "$<EMAIL>" } -SearchBase $baseOU -SearchScope Subtree -Properties DistinguishedName

        if ($null -eq $templateUserObject) {
            Write-Error "Template user $TemplateUser not found in Active Directory."
            return $false
        }

        # Extract the OU from the template user's DistinguishedName
        $ou = ($templateUserObject.DistinguishedName -split ",",2)[1]

        # Create the new user in the same OU as the template user
        $newUser = New-ADUser -SamAccountName $NewUser -UserPrincipalName "$<EMAIL>" -GivenName $templateUserObject.GivenName -Surname $templateUserObject.Surname -Description $templateUserObject.Description -Path $ou -PassThru

        Write-Verbose "User $NewUser created successfully."
    }
    catch {
        Write-Error "Failed to copy user: $_. Exception: $($_.Exception.Message)"
        return $false
    }

    # Check if the new user exists
    try {
        $existingUser = Get-ADUser -Identity $NewUser -SearchBase $baseOU -SearchScope Subtree
        if ($existingUser -ne $null) {
            Write-Verbose "User $NewUser exists in Local AD."
            return $true
        }
        else {
            Write-Error "User $NewUser does not exist in Local AD."
            return $false
        }
    }
    catch {
        Write-Error "Failed to check if user exists: $_. Exception: $($_.Exception.Message)"
        return $false
    }
}


# Check if Active Directory module is available, if not, install it
$adModule1 = Get-Module -Name ActiveDirectory -ListAvailable
$adModule2 = Get-Module -Name AzureAD -ListAvailable
$adModule3 = Get-Module -Name ExchangeOnlineManagement -ListAvailable

if ($adModule1 -eq $null) {
    Write-Verbose "Installing Active Directory module..."
    Install-WindowsFeature RSAT-AD-PowerShell
    Import-Module ActiveDirectory
}

if ($adModule2 -eq $null) {
    Write-Verbose "Installing AzureAD module..."
    Install-Module AzureAD
    Import-Module AzureAD
}

if ($adModule3 -eq $null) {
    Write-Verbose "Installing ExchangeOnlineManagement module..."
    Install-Module ExchangeOnlineManagement
    Import-Module ExchangeOnlineManagement
}


# Import required modules
Write-Verbose "Importing AzureAD and ExchangeOnlineManagement modules..."
Import-Module AzureAD
Import-Module ExchangeOnlineManagement
Import-Module ActiveDirectory

# Connect to Azure AD and Exchange Online
Write-Verbose "Connecting to Azure AD and Exchange Online..."
Connect-AzureAD
Connect-ExchangeOnline

# Loop for user replication
do {
    # Prompt for the template user's email address
    $TemplateUser = Read-Host -Prompt 'Who will be used as the template user? (firstname.lastname)'
    $TemplateUser += '@qhrtech.com'

    # Prompt for the new user's email address
    $NewUser = Read-Host -Prompt 'What is the name of the new user? (firstname.lastname)'
    $NewUser += '@qhrtech.com'

    # Check if the new user already exists in Local AD
    try {
        $existingUser = Get-ADUser -Identity $NewUser -ErrorAction Stop
        Write-Error "The user $NewUser already exists in Local AD. Please provide a new user."
    }
    catch {
        if ($_.Exception -is [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException]) {
            # Copy the template user to create a new user
            $userCreated = Copy-ADUser -TemplateUser $TemplateUser -NewUser $NewUser
            if ($userCreated) {
                # Get new user's Object ID and Display Name
                try {
                    Write-Verbose "Getting new user's Object ID and Display Name..."
                    $NewUserObject = Get-AzureADUser -ObjectId $NewUser
                    $NewUserObjectId = $NewUserObject.ObjectId
                    $NewUserDisplayName = $NewUserObject.DisplayName
                }
                catch {
                    Write-Error "Failed to get new user's Object ID and Display Name: $_"
                    continue
                }
            }
        }
        else {
            Write-Error "Failed to check if user exists: $_"
        }
    }

     # Get template user's group memberships
    try {
        Write-Verbose "Getting template user's group memberships..."
        $templateUserGroups = Get-AzureADUserMembership -ObjectId $TemplateUser 
    }
    catch {
        Write-Error "Failed to get template user's group memberships: $_"
        continue
    }

    foreach ($group in $templateUserGroups) { 
        try {
            Write-Verbose "Adding new user to group: $($group.ObjectId)..."
            Add-AzureADGroupMember -ObjectId $group.ObjectId -RefObjectId $NewUserObjectId
        }
        catch {
            if ($_ -like "*Cannot Update a mail-enabled security groups and or distribution list*") {
                $distGroupName = $group.DisplayName
                $distGroupMembers = Get-DistributionGroupMember -Identity $distGroupName

                if ($distGroupMembers.Name -contains $NewUserDisplayName) {
                    continue
                }
                else {
                    if ($group.DisplayName -notlike "*All Staff*") {
                        Write-Host
                        Write-Verbose "Adding $NewUserDisplayName to the mail group: $distGroupName"
                        Add-DistributionGroupMember -Identity $distGroupName -Member $NewUser
                    }
                }
            }
            else {
                Write-Error "Failed to add new user to group: $_"
                continue
            }
        }
    }

    # Prompt for the new user's province/region
    $NewUserArea = "All Staff - "
    $NewUserProvince = Read-Host -Prompt 'Which province/region is the user from? (BC, AB, MB, NS, ON, SK)'

    # Add a question, is the user internal or external
    $NewUserInternalOrExternal = Read-Host -Prompt 'Is the user internal or external? (internal/external)'
    if ($NewUserInternalOrExternal -contains "external") {
        $NewUserArea += "Externals"
    } else {
        $NewUserArea += $NewUserProvince

        # Ask location-specific questions based on the province
        switch ($NewUserProvince) {
            "BC" {
                # Add a question, is the user located in Vancouver, Kelowna, or other
                $NewUserLocation = Read-Host -Prompt 'Is the user located in Vancouver, Kelowna, or other? (Vancouver/Kelowna/other)'
                if ($NewUserLocation -contains "Vancouver") {
                    $NewUserArea = "All Staff - Vancouver BC"
                } elseif ($NewUserLocation -contains "Kelowna") {
                    # Add a question, is the new user working in the office? Is the user working on floor 2 or floor 4?
                    $NewUserWorkingInOffice = Read-Host -Prompt 'Is the new user working in the office? (y/n)'
                    if ($NewUserWorkingInOffice -contains "y") {
                        $NewUserFloor = Read-Host -Prompt 'Is the user working on floor 2 or floor 4? (2/4)'
                        if ($NewUserFloor -contains "2") {
                            $NewUserArea = "All Staff - Kelowna LM5-200"
                        } elseif ($NewUserFloor -contains "4") {
                            $NewUserArea = "All Staff - Kelowna LM5-400"
                        }
                    } else {
                        $NewUserArea = "All Staff - Kelowna BC"
                    }
                } else {
                    $NewUserArea = "All Staff - BC"
                }
            }
            "AB" {
                $NewUserArea = "All Staff - AB"
                $NewUserLocation = Read-Host -Prompt 'Is the user located in Calgary? (y/n)'
                if ($NewUserLocation -contains "y") {
                    $NewUserArea = "All Staff - Calgary AB"
                }
            }
            "MB" {
                $NewUserArea = "All Staff - MB"
            }
            "NS" {
                $NewUserArea = "All Staff - NS"
                $NewUserLocation = Read-Host -Prompt 'Is the user located in Halifax? (y/n)'
                if ($NewUserLocation -contains "y") {
                    $NewUserArea = "All Staff - Halifax"
                }
            }
            "ON" {
                # Add a question, is the user from the Greater Toronto Area or Ottawa?
                $NewUserFromGTAorOttawa = Read-Host -Prompt 'Is the user from the Greater Toronto Area or Ottawa? (GTA/Ottawa/other)'
                if ($NewUserFromGTAorOttawa -contains "GTA") {
                    $NewUserArea = "All Staff - Toronto GTA ON"
                } elseif ($NewUserFromGTAorOttawa -contains "Ottawa") {
                    $NewUserArea = "All Staff - Ottawa ON"
                } else {
                    $NewUserArea = "All Staff - ON"
                }
            }
            "SK" {
                $NewUserArea = "All Staff - SK"
            }
        }
    }

    # Add the user to the appropriate mail group
    Write-Verbose "Adding $NewUserDisplayName to the mail group:  $NewUserArea"
    Add-DistributionGroupMember -Identity $NewUserArea -Member $NewUser

    # Prompt to continue replicating or stop
    $strQuit = Read-Host "`n Stop replicating groups for new users? (y/n)"
}
Until ($strQuit -contains "y")

# Disconnect from Exchange Online
Write-Verbose "Disconnecting from Exchange Online..."
Disconnect-AzureAD
Disconnect-ExchangeOnline
