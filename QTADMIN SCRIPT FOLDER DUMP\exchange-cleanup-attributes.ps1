﻿$sharedmailboxes = get-mailbox | ? recipienttypedetails -eq sharedmailbox | select userprincipalname

foreach ($mbx in $sharedmailboxes) {
    $samaccount = $mbx.userprincipalname.tolower().replace('@qhrtech.com','')
    try {
        $checkacct = get-aduser $samaccount -Properties * | select msExchRemoteRecipientType, msExchRecipientTypeDetails 
    }
    catch {
    }
    if ($checkacct.msExchRecipientTypeDetails -eq ********** -and $checkacct.msExchRemoteRecipientType -eq 4 -and $samaccount -notmatch '@') {
    Write-Host "Fixing: $samaccount"
    
    set-aduser $samaccount -Replace @{msExchRemoteRecipientType = 100; msExchRecipientTypeDetails = ***********}
    }
}