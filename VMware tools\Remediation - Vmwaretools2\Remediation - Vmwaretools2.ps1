﻿## This program will search for vmware tools 11.5 and 12.1, then run the uninstaller for each.

function Get-Uninstall
{
    # paths: x86 and x64 registry keys are different
    if ([IntPtr]::Size -eq 4) {
        $path = 'HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*'
    }
    else {
        $path = @(
            'HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*'
            'HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*'
        )
    }

    # get all data
    Get-ItemProperty $path |
    # use only with name and unistall information
    .{process{ if ($_.DisplayName -and $_.UninstallString) { $_ } }} |
    # select more or less common subset of properties
    Select-Object DisplayName, Publisher, InstallDate, DisplayVersion, HelpLink, UninstallString |
    # and finally sort by name
    Sort-Object DisplayName
}

$Programs = Get-Uninstall

foreach($program in $Programs)
{
    if ($program.UninstallString -contains 'MsiExec.exe /I{25932044-BBC8-444F-ACF4-7E508054FA12}')
    {
         start-process MsiExec.exe "/X {25932044-BBC8-444F-ACF4-7E508054FA12} /qn" -Wait
         Write-Output "vmware tools detected and uninstall started"
         exit 0
    }elseif($program.UninstallString -contains 'MsiExec.exe /I{A3631D35-CFA5-45F6-A65E-DAFA81C4CBE6}')
    {
         start-process MsiExec.exe "/X {A3631D35-CFA5-45F6-A65E-DAFA81C4CBE6} /qn" -Wait
         Write-Output "vmware tools detected and uninstall started"
         exit 0
    }
}