<#
.SYNOPSIS
This script connects to Microsoft Graph API to retrieve a list of Intune-managed devices and their installed applications.
It filters devices to only include those with names starting with "C-". The retrieved application information for each 
device is exported to an Excel file with a formatted table, and app installation summary is saved in another Excel file.

.DESCRIPTION
The script performs the following steps:
1. Connects to Microsoft Graph API using the `Connect-MgGraph` cmdlet.
2. Retrieves all Intune-managed devices using the Graph API.
3. Filters the devices to only include those whose names start with "C-".
4. For each filtered device, retrieves the installed applications using the `detectedApps` property.
5. Exports the device and application information to an Excel file with a formatted table.
6. Exports a summary of the installed applications (grouped by app name and version) to another Excel file.
7. Uses a progress bar to show the script's progress and adds verbosity for detailed output.

.PARAMETER None
There are no parameters required for this script. Ensure that the user running the script has the necessary permissions.

.OUTPUTS
An Excel file named "ManagedDevices_InstalledApps.xlsx" containing device and application details.

.NOTES
- The script requires the `Microsoft.Graph` and `ImportExcel` PowerShell modules to be installed.
- The user must have permissions: `DeviceManagementApps.Read.All`, `DeviceManagementManagedDevices.Read.All`.
- The script uses `getallpagination` function to handle paginated responses from Microsoft Graph API.

.AUTHOR
Slader Sheppard
#>

# Set error action preference
$ErrorActionPreference = "Stop"

# Define the log file path
$logFilePath = Join-Path -Path $env:TEMP -ChildPath "intune-$(Get-Date -Format yyyyMMddTHHmmssffff).log"
Start-Transcript -Path $logFilePath

# Install and import Microsoft Graph modules if required (current user scope)
Write-Host "Installing and Importing Microsoft Graph modules if required..." -Verbose

if (-not (Get-Module -ListAvailable -Name Microsoft.Graph.Authentication)) {
    Install-Module -Name Microsoft.Graph -Scope CurrentUser -Force
}

if (-not (Get-Module -ListAvailable -Name Microsoft.Graph.DeviceManagement)) {
    Install-Module -Name Microsoft.Graph -Scope CurrentUser -Force
}

if (-not (Get-Module -ListAvailable -Name ImportExcel)) {
    Install-Module -Name ImportExcel -Scope CurrentUser -Force
}

# Import necessary modules
Import-Module Microsoft.Graph.Authentication
Import-Module Microsoft.Graph.DeviceManagement
Import-Module ImportExcel

# Function to connect to Microsoft Graph
Function Connect-ToGraph {
    param (
        [string]$Scopes = "DeviceManagementApps.Read.All, DeviceManagementManagedDevices.Read.All"
    )
    try {
        Connect-MgGraph -Scopes $Scopes
        Write-Host "Successfully connected to Microsoft Graph" -Verbose
    }
    catch {
        Write-Error "Failed to connect to Microsoft Graph. Error: $_"
        Stop-Transcript
        exit
    }
}

# Connect to Microsoft Graph
Connect-ToGraph

# Function to handle paginated responses from Graph API
function getallpagination {
    param ($url)
    $allItems = @()
    try {
        $response = Invoke-MgGraphRequest -Uri $url -Method Get -OutputType PSObject
        $allItems += $response.value
        $nextLink = $response."@odata.nextLink"
        
        while ($nextLink) {
            $response = Invoke-MgGraphRequest -Uri $nextLink -Method Get -OutputType PSObject
            $allItems += $response.value
            $nextLink = $response."@odata.nextLink"
        }
    }
    catch {
        Write-Error "Error while retrieving paginated data. Error: $_"
    }
    return $allItems
}

# Define the base URI for managed devices
$devicesUri = "https://graph.microsoft.com/beta/deviceManagement/managedDevices"

# Retrieve all managed devices using pagination
Write-Host "Retrieving all managed devices..." -Verbose
$allDevices = getallpagination -url $devicesUri

# Filter devices starting with "C-"
$filteredDevices = $allDevices | Where-Object { $_.deviceName -like "C-*" }
$deviceCount = $filteredDevices.Count

Write-Host "Total devices starting with 'C-': $deviceCount" -Verbose

# Create an array to store app inventory results
$results = [System.Collections.Concurrent.ConcurrentBag[PSObject]]::new()

# Process each device sequentially
$progressCount = 0 # Initialize progress count outside the loop
foreach ($device in $filteredDevices) {
    $retryCount = 0
    $maxRetries = 3

    while ($retryCount -lt $maxRetries) {
        try {
            $deviceAppUri = "https://graph.microsoft.com/beta/deviceManagement/managedDevices('$($device.id)')?`$expand=detectedApps"
            $apps = (Invoke-MgGraphRequest -Uri $deviceAppUri -Method Get -OutputType PSObject).detectedApps

            foreach ($app in $apps) {
                $resultEntry = [PSCustomObject]@{
                    DeviceName   = $device.deviceName
                    DeviceId     = $device.id
                    OSVersion    = $device.osVersion
                    Manufacturer = $device.manufacturer
                    Model        = $device.model
                    AppName      = $app.displayName
                    AppVersion   = $app.version
                    Publisher    = $app.publisher
                }
                $results.Add($resultEntry)
            }

            Write-Output "Processed device: $($device.deviceName)"
            break
        }
        catch {
            Write-Warning "Attempt $($retryCount + 1) failed to retrieve apps for device: $($device.deviceName). Error: $_"
            $retryCount++
            if ($retryCount -ge $maxRetries) {
                Write-Error "Max retries reached for device: $($device.deviceName). Skipping."
            } else {
                Start-Sleep -Seconds 5
            }
        }
    }

    # Update progress after each device is processed
    $progressCount++
    $progressPercent = [math]::Round(($progressCount / $deviceCount) * 100)
    Write-Progress -Activity "Processing Devices" -Status "Completed $progressCount of $deviceCount" -PercentComplete $progressPercent
}

# Check if any results were collected
if ($results.Count -eq 0) {
    Write-Error "No application data retrieved. Exiting script."
    Stop-Transcript
    exit
}

# Convert results to an array for Excel export
$resultsArray = $results.ToArray()

# Export the results to an Excel file with a formatted table
$excelFilePath = "ManagedDevices_InstalledApps.xlsx"
try {
    $resultsArray | Export-Excel -Path $excelFilePath -WorksheetName "DeviceApps" -TableName "DeviceAppTable" -AutoSize -BoldTopRow
    Write-Host "Export complete. Check '$excelFilePath' in your current directory." -Verbose
}
catch {
    Write-Error "Failed to export the data to Excel. Error: $_"
}


# End transcript
Stop-Transcript