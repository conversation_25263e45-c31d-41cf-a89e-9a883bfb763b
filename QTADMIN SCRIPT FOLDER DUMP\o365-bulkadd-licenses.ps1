﻿$LiveCred = Get-Credential "<EMAIL>"
#$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
#Import-PSSession $Session
Connect-MsolService -Credential $livecred

#Set-MsolUserPrincipalName -newuserprincipalname <EMAIL> -userprincipalname <EMAIL>

#make a text file with ONE USER per line.
$userlist = Get-Content 'T:\Scratch\Chris Kitella\lyncmigusers.txt'


foreach ($user in $userlist) {
$checklic = (get-msoluser -UserPrincipalName $user).islicensed
if (!$checklic) {

 Write "About to assign E3 License to $user...  Press CTRL-C to abort"
 sleep 2
 Set-MsolUser -UserPrincipalName $user -UsageLocation CA
 Set-MsolUserLicense -AddLicenses qhrtech:ENTERPRISEPACK  -UserPrincipalName $user
 }
 }