<#
.SYNOPSIS
    This script connects to Microsoft Graph to retrieve all users and provides a count of users per department.

.DESCRIPTION
    The script performs the following actions:
    1. Checks for the required Microsoft.Graph.Users module and installs it if missing.
    2. Connects to Microsoft Graph with the necessary 'User.Read.All' permissions.
    3. Fetches all user objects from the tenant, handling pagination automatically.
    4. Groups the users by the 'department' attribute in their profile.
    5. Outputs a formatted table showing each department and the total number of users in it.

.AUTHOR
    Slader Sheppard

.NOTES
    - You must have administrative privileges to install PowerShell modules.
    - You will need an account with permissions to grant consent for the 'User.Read.All' scope if it's the first time running this script.
#>

# --- Step 1: Check and Install the Required MS Graph Module ---
Write-Host "Checking if the 'Microsoft.Graph.Users' module is installed..." -ForegroundColor Cyan

# Get the module, if it's not found, install it
If (-not (Get-Module -ListAvailable -Name Microsoft.Graph.Users)) {
    Write-Host "'Microsoft.Graph.Users' module not found. Attempting to install..." -ForegroundColor Yellow
    try {
        Install-Module Microsoft.Graph.Users -Scope CurrentUser -Repository PSGallery -Force -AllowClobber
        Write-Host "Module installed successfully." -ForegroundColor Green
    }
    catch {
        Write-Host "Error: Failed to install the module. Please try running PowerShell as an Administrator and execute 'Install-Module Microsoft.Graph.Users'." -ForegroundColor Red
        return # Exit the script if the module can't be installed
    }
}
else {
    Write-Host "Module is already installed." -ForegroundColor Green
}

# --- Step 2: Connect to Microsoft Graph ---
Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Cyan
try {
    # Define the required permission scope
    $requiredScopes = @("User.Read.All")
    
    # Connect to Microsoft Graph with the specified scopes
    Connect-MgGraph -Scopes $requiredScopes
    
    Write-Host "Successfully connected to Microsoft Graph." -ForegroundColor Green
    Write-Host "Authenticated as: $((Get-MgContext).Account)" -ForegroundColor Gray
}
catch {
    Write-Host "Error: Failed to connect to Microsoft Graph. Please check your connection and credentials." -ForegroundColor Red
    return # Exit if connection fails
}


# --- Step 3: Fetch All Users from the Tenant ---
Write-Host "Fetching all users from the tenant. This may take a moment..." -ForegroundColor Cyan
try {
    # We select only the properties we need (id, displayName, department) to make the query faster.
    $allUsers = Get-MgUser -All -Property "id,displayName,department"
    $userCount = $allUsers.Count
    Write-Host "Successfully retrieved $userCount user profiles." -ForegroundColor Green
}
catch {
    Write-Host "Error: Failed to retrieve users. Ensure you have the 'User.Read.All' permission." -ForegroundColor Red
    return # Exit if user retrieval fails
}


# --- Step 4: Process and Group Users by Department ---
Write-Host "Processing user data and counting users per department..." -ForegroundColor Cyan

# Filter users who have a department specified, group them by the department property,
# and then create a custom object for the report.
$departmentReport = $allUsers | 
Where-Object { $_.Department -ne $null -and $_.Department -ne "" } | 
Group-Object -Property Department | 
Select-Object @{Name = "DepartmentName"; Expression = { $_.Name } }, @{Name = "UserCount"; Expression = { $_.Count } } |
Sort-Object -Property DepartmentName

# --- Step 5: Display the Final Report ---
Write-Host "--- Department Headcount Report ---" -ForegroundColor Magenta

if ($departmentReport) {
    # Display the report in a formatted table
    $departmentReport | Format-Table -AutoSize
}
else {
    Write-Host "No users with department information were found." -ForegroundColor Yellow
}

# --- Disconnect from Microsoft Graph ---
Write-Host "Disconnecting from Microsoft Graph session." -ForegroundColor Cyan
Disconnect-MgGraph