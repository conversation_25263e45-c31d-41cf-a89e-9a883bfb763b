<#
.SYNOPSIS
(SIMULATION MODE) Connects via Delegated permissions to simulate renaming Intune devices per corporate convention (C-DP-FLast), excluding specific prefixes/keywords. Logs proposed changes without execution. Corrected suffix logic (starts at 2).

.DESCRIPTION
*** THIS SCRIPT IS CURRENTLY IN READ-ONLY / SIMULATION MODE ***
*** IT WILL NOT MAKE ANY CHANGES TO DEVICE NAMES IN INTUNE ***

Simulates renaming corporate Windows devices, excluding names starting with CPC, EMR, QHRCIAB, QHR or containing TEST/TESTING.
Corrected Suffix Logic: If ideal name exists, starts checking duplicates from suffix 2.
1. Defines logging function.
2. Sets up Department Map.
3. Connects to Graph (interactive login).
4. Gets ALL device names for duplicate checks.
5. Gets target devices matching filter.
6. For each target device:
    a. Checks exclusion rules; skips if matched.
    b. Checks Intune Primary User / Entra Registered User; skips if none found.
    c. Gets user details.
    d. Determines Department Code (user/manager fallback); skips if invalid.
    e. Constructs ideal name ('C-{DPT}-{FirstInitial}{LastName}'), truncated to 15 chars.
    f. Skips if current name matches ideal.
    g. Checks for duplicates (ideal name first, then suffix starting from 2); appends suffixes with dynamic truncation if needed.
    h. LOGS the proposed rename action.
    i. Updates internal lookup (for simulation accuracy).
    j. Logs all actions, successes, warnings, errors.

Requires necessary Graph SDK modules & user permissions/consent.

.EXAMPLE
.\intuneNameComplianceREADONLY.ps1
Runs simulation. Check console/log file for proposed actions & exclusions.

.NOTES
Author: Slader Sheppard
Set to SIMULATION MODE
Version: 4.1-Simulation (Corrected Suffix Logic)
Requires PowerShell 5.1+. User needs Intune/Entra permissions.
Naming: 'C-{DPT}-{FirstInitial}{LastNameTruncated}[Suffix]'. Department Map keys must match Entra ID.
To enable actual renaming: Modify Step 4h/4i in the live script version.
#>

#region Configuration and Setup
# ===================================================================================
# User's preferred log path
$LogFilePath = "C:\Temp\DeviceRenameLogs\DeviceRenameLog_$(Get-Date -Format 'yyyyMMdd_HHmmss')_SIMULATION.log"
Function Write-Log { Param([Parameter(Mandatory)][string]$Message, [Parameter(Mandatory)][ValidateSet("INFO","WARN","ERROR","ACTION","SUCCESS","DEBUG")][string]$Level) ; $ts = Get-Date -Format "yyyy-MM-dd HH:mm:ss" ; $le = "$ts [$($Level.PadRight(7))] $Message" ; $c = switch ($Level) {"INFO"{"White"} "WARN"{"Yellow"} "ERROR"{"Red"} "ACTION"{"Cyan"} "SUCCESS"{"Green"} "DEBUG"{"Gray"} default{"White"}} ; Write-Host $le -ForegroundColor $c ; try { Add-Content -Path $LogFilePath -Value $le -EA Stop } catch { Write-Warning "Failed log write: $($_.Exception.Message)" } }
# User's updated Department Map
$DepartmentMap = @{ "Revenue Mangement"="AC";"Project Management"="PM";"Corp IT"="TE";"Executive"="EX";"CS/Support"="CS";"CS / Support"="CS";"Operations"="PO";"Human Resources"="HR";"Product Development"="QA";"Platform Integrations"="IM";"Development"="DE";"Quality Assurance"="QA";"IT"="TE";"Administration"="AD"; "Revenue Management"="AC"; "Implementations"="IM"; "Training"="TR"; "Data and Software Architecture"="IM"; "Data Insights and Integrations"="IM"; "Client Services"="CS"; "Finance"="FI"; "HR"="HR"; "Technology"="TE"; "Marketing"="MA"; "Development (Product development)"="DE"; "Sales National"="CH"; "Product Management"="PM"; "Product Development (PD Quality Assurance)"="QA"; "Product Operations"="PO"; "Platform Integration"="DE"; "CHN Central"="CH"; "CHN East"="CH"; "CHN West"="CH"; "GRC"="TE" } # ADJUST KEYS!
$DevicePrefix = "C"; $Separator = "-"; $MaxNameLength = 15
$DeviceFilter = "operatingSystem eq 'Windows' and managedDeviceOwnerType eq 'company'"
$RequiredScopes = @("User.Read.All", "DeviceManagementManagedDevices.ReadWrite.All", "Device.Read.All")
$IgnorePrefixes = @('CPC', 'EMR', 'QHRCIAB', 'QHR') # Uppercase
$IgnoreKeywords = @('TEST', 'TESTING') # Uppercase
#endregion

#region Main Script Logic
# ===================================================================================
Write-Log -Level "ACTION" -Message "Starting Intune Device Rename Script (v4.1 - Corrected Suffix - SIMULATION MODE)... Log: $LogFilePath"
Write-Log -Level "WARN" -Message "*** SIMULATION MODE ACTIVE - NO CHANGES WILL BE MADE ***"

# --- Step 1: Authenticate ---
Write-Log -Level "ACTION" -Message "Connecting to Graph..."
try { $ctx = Get-MgContext -EA SilentlyContinue; $miss = $RequiredScopes | Where-Object { $null -eq $ctx -or $ctx.Scopes -notcontains $_ }; if ($null -eq $ctx -or $miss) { if ($null -ne $ctx) { Write-Log -Level "INFO" -Message "Reconnecting for scopes..."; Disconnect-MgGraph -EA SilentlyContinue }; Write-Log -Level "WARN" -Message "Connect prompt may appear."; Connect-MgGraph -Scopes $RequiredScopes -EA Stop -NoWelcome; $ctx = Get-MgContext -EA SilentlyContinue ; if ($null -eq $ctx) { Throw "Connect failed." }; $miss = $RequiredScopes | Where-Object { $ctx.Scopes -notcontains $_ }; if ($miss) { Throw "Missing scopes: $($miss -join ', ')" } } else { Write-Log -Level "INFO" -Message "Already connected." }; Write-Log -Level "SUCCESS" -Message "Graph connected (User: $($ctx.Account), Tenant: $($ctx.TenantId)).";} catch { Write-Log -Level "ERROR" -Message "Fatal connect/scope error: $($_.Exception.Message)"; exit 1 }

# --- Step 2: Get ALL Device Names ---
Write-Log -Level "ACTION" -Message "Getting all device names..." ; $AllDeviceNamesLookup = @{}; try { $AllDevs = Get-MgDeviceManagementManagedDevice -Property "deviceName" -All -EA Stop; foreach ($d in $AllDevs) { if (-not [string]::IsNullOrEmpty($d.DeviceName)) { $AllDeviceNamesLookup[$d.DeviceName.ToUpper()] = $true } }; Write-Log -Level "SUCCESS" -Message "Got $($AllDeviceNamesLookup.Count) unique names for duplicate check." } catch { Write-Log -Level "ERROR" -Message "Fatal error getting all names: $($_.Exception.Message)"; Write-Log -Level "INFO" -Message "Disconnecting..."; Disconnect-MgGraph; exit 1 }

# --- Step 3: Get Target Devices ---
Write-Log -Level "ACTION" -Message "Getting target devices (filter: '$($DeviceFilter)')..."; try { $TargetDevices = Get-MgDeviceManagementManagedDevice -Filter $DeviceFilter -Property "id,deviceName,userPrincipalName,azureADDeviceId" -All -EA Stop; Write-Log -Level "SUCCESS" -Message "Found $($TargetDevices.Count) target devices." } catch { Write-Log -Level "ERROR" -Message "Error getting targets: $($_.Exception.Message)"; Write-Log -Level "INFO" -Message "Disconnecting..."; Disconnect-MgGraph; exit 1 }
if ($null -eq $TargetDevices -or $TargetDevices.Count -eq 0) { Write-Log -Level "WARN" -Message "No targets found. Exiting."; Write-Log -Level "INFO" -Message "Disconnecting..."; Disconnect-MgGraph; exit 0 }

# --- Step 4: Process Target Devices ---
Write-Log -Level "ACTION" -Message "Processing $($TargetDevices.Count) target devices (Simulation)..." ; $simulatedRenameCounter = 0
foreach ($Device in $TargetDevices) {
    $DeviceLogPrefix = "--> Device '$($Device.DeviceName)' (IntuneID: $($Device.id)):" ; Write-Log -Level "INFO" -Message "`n$DeviceLogPrefix Processing started."
    $UserPrincipalName = $null ; $UserDataSource = "None"

    # --- 4a: EXCLUSION CHECK ---
    $CurrentDeviceNameUpper = $Device.DeviceName.ToUpper()
    $SkipDevice = $false ; $SkipReason = ""
    foreach ($prefix in $IgnorePrefixes) { if ($CurrentDeviceNameUpper.StartsWith($prefix)) { $SkipDevice = $true; $SkipReason = "Name starts with ignored prefix '$prefix'"; break } }
    if (-not $SkipDevice) { foreach ($keyword in $IgnoreKeywords) { if ($CurrentDeviceNameUpper.Contains($keyword)) { $SkipDevice = $true; $SkipReason = "Name contains ignored keyword '$keyword'"; break } } }
    if ($SkipDevice) { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Skipping - $SkipReason."; continue }

    # --- 4b: Determine User Principal Name ---
    # Using user's fix for debug log {} placeholder
    if (-not [string]::IsNullOrEmpty($Device.UserPrincipalName)) { $UserPrincipalName = $Device.UserPrincipalName; $UserDataSource = "Intune Primary User" ; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Found $UserDataSource{}: '$($UserPrincipalName)'."
    } else { Write-Log -Level "INFO" -Message "$DeviceLogPrefix Intune Primary User missing. Checking Entra Registered User..."; if (-not [string]::IsNullOrEmpty($Device.azureADDeviceId)) { $EntraDeviceId = $Device.azureADDeviceId; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Found Entra ID: '$EntraDeviceId'. Querying..."; try { $EntraDevice = Get-MgDevice -DeviceId $EntraDeviceId -ExpandProperty "registeredUsers(`$select=id,userPrincipalName)" -EA Stop ; if ($null -ne $EntraDevice.RegisteredUsers -and $EntraDevice.RegisteredUsers.Count -gt 0) { $RegisteredUserUPN = $EntraDevice.RegisteredUsers[0].UserPrincipalName; if (-not [string]::IsNullOrEmpty($RegisteredUserUPN)) { $UserPrincipalName = $RegisteredUserUPN; $UserDataSource = "Entra Registered User"; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Found $UserDataSource{}: '$($UserPrincipalName)'." } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Entra Registered User object has empty UPN." } } else { Write-Log -Level "INFO" -Message "$DeviceLogPrefix No registered user found for Entra device." } } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Failed get Entra device/user for ID '$EntraDeviceId': $($_.Exception.Message)" } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Entra ID missing. Cannot check registered user." } }
    if ([string]::IsNullOrEmpty($UserPrincipalName)) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Could not determine user. Skipping."; continue }

    # --- 4c: Get User Details ---
    $User = $null ; try { Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Getting details for '$UserPrincipalName' ($UserDataSource)..." ; $User = Get-MgUser -UserId $UserPrincipalName -Property "id,displayName,department,manager" -EA Stop ; if ($null -eq $User) { Throw "User null." } } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Failed get user details for '$UserPrincipalName'. Skipping. Error: $($_.Exception.Message)"; continue }

    # --- 4d: Determine Department Code ---
    $DepartmentCode = $null ; $UserDepartment = $User.Department
    if (-not [string]::IsNullOrEmpty($UserDepartment)) { Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix User Dept: '$UserDepartment'. Checking map..."; if ($DepartmentMap.ContainsKey($UserDepartment)) { $DepartmentCode = $DepartmentMap[$UserDepartment] ; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Using Dept Code '$DepartmentCode' from User ('$UserDepartment')." } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix User dept '$UserDepartment' not in map." } } else { Write-Log -Level "INFO" -Message "$DeviceLogPrefix User dept empty. Check manager..." }
    if ($null -eq $DepartmentCode) { if ($null -ne $User.Manager -and $User.Manager -is [Microsoft.Open.MSGraph.Model.DirectoryObject] -and -not [string]::IsNullOrEmpty($User.Manager.Id)) { $ManagerId = $User.Manager.Id; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Mgr ID: $ManagerId. Getting details..." ; $Manager = $null ; try { $Manager = Get-MgUser -UserId $ManagerId -Property "id,department,displayName" -EA Stop ; if ($null -ne $Manager) { $ManagerDepartment = $Manager.Department; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Mgr '$($Manager.DisplayName)' Dept: '$ManagerDepartment'. Checking map..." ; if (-not [string]::IsNullOrEmpty($ManagerDepartment)) { if ($DepartmentMap.ContainsKey($ManagerDepartment)) { $DepartmentCode = $DepartmentMap[$ManagerDepartment]; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Using Dept Code '$DepartmentCode' from Mgr ('$ManagerDepartment')." } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Mgr dept '$ManagerDepartment' not in map." } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Mgr '$($Manager.DisplayName)' has empty dept." } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Mgr object (ID: $ManagerId) null." } } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Failed get mgr (ID: $ManagerId): $($_.Exception.Message)" } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix User has no manager." } }
    if ($null -eq $DepartmentCode) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix No valid Dept Code found. Skipping."; continue }

    # --- 4e: Construct UsernamePart ---
    $DisplayName = $User.DisplayName; if ([string]::IsNullOrEmpty($DisplayName)) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix User '$UserPrincipalName' empty DisplayName. Skipping."; continue } ; $NameParts = $DisplayName -split '\s+'; if ($NameParts.Count -lt 2) { Write-Log -Level "WARN" -Message "$DeviceLogPrefix DisplayName '$DisplayName' may not be 'First Last'."; $FirstInitial = $NameParts[0].Substring(0, 1); $LastName = $NameParts[0] } else { $FirstInitial = $NameParts[0].Substring(0, 1); $LastName = $NameParts[-1] } ; $UsernamePartRaw = "$($FirstInitial)$($LastName)".Replace(" ",""); Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Raw UsernamePart: '$UsernamePartRaw'."

    # --- 4f: Construct Ideal Name & Initial Truncation ---
    $FixedPrefix = "$($DevicePrefix)$($Separator)$($DepartmentCode)$($Separator)"; $MaxUserPartLength = $MaxNameLength - $FixedPrefix.Length; if ($MaxUserPartLength -lt 1) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Prefix '$FixedPrefix' too long. Skipping."; continue } ; $UsernamePart = if ($UsernamePartRaw.Length -gt $MaxUserPartLength) { $UsernamePartRaw.Substring(0, $MaxUserPartLength) } else { $UsernamePartRaw } ; $IdealName = "$($FixedPrefix)$($UsernamePart)"; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Ideal name (initial): '$IdealName'."

    # --- 4g: Check if Current Name Matches Ideal ---
    # Compare case-insensitively
    if ($Device.DeviceName -eq $IdealName) { Write-Log -Level "INFO" -Message "$DeviceLogPrefix Current name matches ideal. No action needed."; continue }

    # --- 4h: Duplicate Check & Suffixing (Corrected Logic) ---
    $FinalName = $null
    $IdealNameUpper = $IdealName.ToUpper()

    # Check if the ideal name itself is available
    if (-not $AllDeviceNamesLookup.ContainsKey($IdealNameUpper)) {
        $FinalName = $IdealName # Ideal name is unique
        Write-Log -Level "INFO" -Message "$DeviceLogPrefix Ideal name '$FinalName' is unique."
    } else {
        # Ideal name exists, start checking suffixes from 2
        Write-Log -Level "WARN" -Message "$DeviceLogPrefix Ideal name '$IdealName' already exists. Checking suffixes starting from 2..."
        $Suffix = 2 # Start checking from 2
        while ($true) { # Loop until break
            $SuffixStr = $Suffix.ToString()
            $MaxLengthBeforeSuffix = $MaxNameLength - $SuffixStr.Length
            $MaxUserPartLengthWithSuffix = $MaxLengthBeforeSuffix - $FixedPrefix.Length

            if ($MaxUserPartLengthWithSuffix -lt 1) {
                Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Cannot make unique name for ID $($Device.Id). Suffix '$SuffixStr' leaves no room within $MaxNameLength chars. Skipping."
                $FinalName = $null # Explicitly nullify
                break # Exit while loop
            }

            # Re-truncate the ORIGINAL raw username part based on the space available with the current suffix
            $UsernamePartForSuffix = if ($UsernamePartRaw.Length -gt $MaxUserPartLengthWithSuffix) {
                $UsernamePartRaw.Substring(0, $MaxUserPartLengthWithSuffix)
            } else {
                $UsernamePartRaw
            }
            # Construct the candidate name with this suffix
            $CandidateName = "$($FixedPrefix)$($UsernamePartForSuffix)$($SuffixStr)"
            Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Trying suffix candidate: '$CandidateName'."

            # Check if this suffixed name exists
            if (-not $AllDeviceNamesLookup.ContainsKey($CandidateName.ToUpper())) {
                $FinalName = $CandidateName # Found a unique name
                Write-Log -Level "INFO" -Message "$DeviceLogPrefix Found unique name with suffix: '$FinalName'."
                break # Exit while loop
            }

            # Name with current suffix exists, increment and loop again
            $Suffix++
            if ($Suffix -ge 1000) { # Safety break
                Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Max suffix attempts (1000) reached for ID $($Device.Id). Skipping."
                $FinalName = $null
                break # Exit while loop
            }
        } # End While loop
    } # End Else block (ideal name existed)

    # --- 4i: Simulate Rename if Necessary ---
    if ($null -ne $FinalName -and $Device.DeviceName -ne $FinalName) {
        # ---!!! SIMULATION MODE !!!---
        Write-Log -Level "WARN" -Message "$DeviceLogPrefix --- SIMULATION MODE --- Would attempt rename from '$($Device.DeviceName)' to '$FinalName'."
        $simulatedRenameCounter++
        try {
            # $Body = @{ deviceName = $FinalName } | ConvertTo-Json -Depth 4; $RenameUri = "https://graph.microsoft.com/v1.0/deviceManagement/managedDevices/$($Device.Id)/setDeviceName"
            # Invoke-MgGraphRequest -Method POST -Uri $RenameUri -Body $Body -ContentType "application/json" -ErrorAction Stop # <<< RENAME DISABLED
            # Simulate updating lookup table
            if ($AllDeviceNamesLookup.ContainsKey($Device.DeviceName.ToUpper())) { $AllDeviceNamesLookup.Remove($Device.DeviceName.ToUpper()) } ; $AllDeviceNamesLookup[$FinalName.ToUpper()] = $true; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix SIMULATION: Updated internal name lookup for '$FinalName'."
        } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix SIMULATION: Error would have occurred: $($_.Exception.Message)" ; if ($_.Exception.Response) { $ER = $_.Exception.Response.GetResponseStream(); $SR = New-Object System.IO.StreamReader($ER); $EB = $SR.ReadToEnd() | ConvertFrom-Json -EA SilentlyContinue; Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Error Response: $($EB | Out-String)" } }
        # --- End Simulation Block ---
    } elseif ($null -ne $FinalName -and $Device.DeviceName -eq $FinalName) { Write-Log -Level "INFO" -Message "$DeviceLogPrefix Device already has final name '$FinalName'." # This condition might be less common now
    } elseif ($null -eq $FinalName) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Rename skipped: Could not determine unique final name." }

} # End Foreach Device Loop

# --- Step 5: Disconnect ---
Write-Log -Level "ACTION" -Message "`nScript processing complete (Simulation Mode). Proposed $simulatedRenameCounter rename actions."
Write-Log -Level "INFO" -Message "Disconnecting from Microsoft Graph..." ; Disconnect-MgGraph ; Write-Log -Level "INFO" -Message "Disconnected."
#endregion