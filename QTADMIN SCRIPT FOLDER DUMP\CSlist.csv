﻿"name","extensionAttribute1"
"MSX Services",
"Server Farm",
"Domain Admin - <PERSON>",
"Exchange Online-ApplicationAccount",
"SystemMailbox{bb558c35-97f1-4cb9-8ff7-d53741dc928c}",
"Disabled Guest",
"QHRNetAdmin",
"SMS#_GUARDIAN",
"IUSR_QHR-002",
"TsInternetUser",
"CLINICARE$",
"krbtgt",
"MDT",
"O Perator",
"Domain Admin - Martin Weiss",
"Domain Admin - Yeison Rios",
"DevTrainer",
"Domain Admin - Allen McCarty",
"SqlSrv-QsQRsDeplyCC-SqlAgent",
"Domain Admin - Alex <PERSON>hl",
"FinUser",
"Nan Adams",
"SPSetup",
"root",
"BPWebServices",
"Domain Admin - Devin Nate",
"QHR Infrastructure Operator",
"Service Account - Asigra",
"Domain Admin - Mark McLean",
"res_migrator",
"<PERSON>HTest",
"Domain Admin - <PERSON>",
"IWAM_QHRMOO01",
"<PERSON>",
"Optimed Sales",
"<PERSON>",
"Lisa St. Laurent","0"
"Opt_Development",
"<PERSON>ldebrandt","5"
"FAX Archive",
"SystemMailbox{1f05a927-72a5-4a07-9dbc-6b1cd178ad2e}",
"Marion Sherback",
"MacUser",
"QHR Technologies","1"
"Greg Harshenin",
"Blake <PERSON>ie",
"Al Hildebrandt",
"Cheryl Cain",
"IS DHCP",
"Information Systems",
"Accuro",
"Investor Relations",
"Bob Gemmell",
"Clinicare",
"Quarantine",
"Ricoh",
"Staff Meeting",
"Alex Mehl",
"SQL-OptDevKel01",
"SQL-OptDevKel02",
"SQL-OptDevKel03",
"SQL-QHRSQL03",
"SQL-qhrsql03SQL2K5",
"e learning",
"SRV-OptOnline",
"QHR Hosted",
"voicemail",
"Stefanie Giddens","5"
"Toshiba",
"Alfred Loh",
"SQL-OCS",
"QT StaffMeetings",
"OS StaffMeetings",
"Optimed Admin",
"Allen McCarty","3"
"Amanda Korecki",
"Sara Konkin",
"Sakura Newcomb","0"
"Ron Hughes",
"Shaun O'Grady",
"Tony Elumir","0"
"Roxanne Geiger","0"
"Matti Kalijärvi",
"Louise Richardson",
"Luba O'Brien","0"
"Jennifer Makar",
"Lee-Ann Tiede",
"Jen Danchuk","0"
"Claire Blaker","0"
"Brenda Undiri","0"
"Elton Mahabir",
"Wayne Knorr",
"IT Admin",
"Dayna McInnis",
"Lauren Romano",
"QHR Tech  Admin",
"FepSQLAdmin",
"Tim Fox",
"Mark Vanrietschoten",
"Marcel Hebert",
"Jeff VanDenHeuvel",
"Brad Paffe","0"
"Christie Thomsen",
"Devin Nate",
"Mark McLean",
"Neil Hylton","0"
"Angie Jarabe","0"
"Adele Williams","0"
"Lesley Beamond","0"
"Resource Calgary Boardroom",
"Paul Wait","0"
"Shelley Hughes",
"QHR Tech Privacy Officer",
"Chris Hollman","0"
"Optimed Sales Engineers",
"Daryl Laverdure",
"Jennifer Davidoff",
"Brad Reibin",
"Terry-Lyn Kardel","0"
"Janet Hatfield","0"
"SQL-qsdevbi2",
"SQL-QHRSQL04QSSUP2K8R2",
"SQL qsbi",
"VMware Converter",
"AccTest",
"RTCGuestAccessUser",
"Lucy Montagnese",
"Robert Thornton",
"Susan Poisson",
"Karyn Dallimore",
"Stevan Christiansen","0"
"Dana Alderson",
"Adam Sinai",
"Shawn Manary",
"Michael Hall",
"Colin Greenway",
"Shelly Arsenault",
"Temi Beckley",
"Craig Hounsham","0"
"Ravi Anandarajah","0"
"Russell King",
"HS After Hours Support",
"Chenoa McMullen-Hunt","0"
"CSR","1"
"Jennifer Hodge","0"
"Julie Tieh",
"Rebecca Ferrie","0"
"Ken Gordon","0"
"QHR Accounting",
"Optimed Development After Hours",
"Nicola Austin","0"
"Amanda Keating",
"careers",
"Viktor Velkovski",
"EMR Client Services Leadership Team","1"
"Cody Cudmore","0"
"Christopher Cadieux",
"Kevin Kendall",
"Dylan Wood","0"
"Scripts (Service Account)",
"Jerry Diener",
"Shelby Laidlaw","0"
"OptimedImpW",
"Robert Armstrong",
"Justin Hebert",
"Products EMR",
"qtmitelvmail",
"qtmiteloaisys",
"Aron Ashmead","0"
"Jonathan Chalaturnyk",
"Yeison Rios",
"EMR Implementations",
"Tops",
"Resource Toronto - Board Room",
"Resource Toronto - Meeting Room",
"MichaelCopy",
"Jessica Severiano","0"
"qhr_devweb",
"SQL-QHRSQL03sql2k5v1dm",
"Carole D'Arcy",
"SecureStore",
"SQL-OSWebSQL01",
"Erik Adamson","0"
"QHR Supplies",
"Amanda Tubello",
"qtialerts",
"Nicol Solomonides",
"QHR Technologies Inc. Events",
"noreply support",
"OptImpCordEast1",
"OptImpCordEast2",
"Human Resources",
"Mobile Alex Mehl",
"Lisa Gunnlaugson",
"Optimed Data Analyst",
"EMR Development",
"QHR Tech Marketing",
"Guest",
"Aaron Hartnell",
"OptEntImp EMR Enterprise Imp",
"Zsolt Kiss","0"
"SQL-QSRMSQL02",
"SQL-QSRMSQL01",
"Opt Training",
"Matt Cahill",
"VMware Orchestrator",
"VMware Converter Manager",
"VMware Account",
"UNIX",
"Zuora IT",
"Martin Weiss","4"
"Resource Kelowna 3rd Floor - Board Room 1",
"Resource Kelowna 3rd Floor - Board Room 2",
"Resource Kelowna 3rd Floor - Meeting Room 1",
"Tim Melmoth","0"
"Raj Diocee",
"SMSServer_Q01",
"SMSClient_Q01",
"SMSServer_QHR",
"SMSClient_QHR",
"apc powerchute account",
"Student2",
"Student",
"srv-virtualBackup",
"VSAdmin",
"IIS-DevQHRnet2",
"IIS-DevQHRnet",
"ISAEntLogging",
"SSOSrv",
"Fax Medical Solutions",
"srv-VSBackup",
"ISAEntCSS-SA",
"WDS",
"ISAEntReports",
"RTCService",
"RTCComponentService",
"CWAService",
"QTSPTestSQLService",
"QTSPTestSPSetup",
"QTSPTestServices",
"DevPPService",
"SPUserProfile",
"pstdrop",
"Restricted Access",
"ForecasterAdmin",
"ADRmsAdmin",
"LM5BRPC",
"IT User",
"Zabbix Service Account",
"wsus",
"sonicwall",
"Benjamin Schellenberger",
"Miriam Gaspar",
"EMRleads",
"Optimed Careers",
"Sandra Baker",
"Alan Zantingh","0"
"Nyel English","0"
"Julie Duncan","0"
"Judy Zeeben","0"
"UnattendedService",
"QTSQL01-Reporting",
"Partner Sales",
"Kyle Newton",
"Cory Ingram",
"ADRmsSQL",
"sql-qtsqlman",
"temp",
"archiver",
"Dynamics Advisor (Contractor)",
"BPAdminAppPool",
"BPAppPool",
"BPCrawl",
"BPSearch",
"BPSharePointDBA",
"BPSSPAdmin",
"oss_user",
"QHR Software Privacy Officer",
"Optimed Privacy Officer",
"HS Emr Mirth Alert",
"Bryan Bergen",
"Andrew Bondarenko",
"Kevan Poeschek","0"
"Healthscreen Support",
"sql-emssupqrs2k12agent",
"sql-emssupqrs2k12",
"sql-emsdevsql2k14ea",
"sql-emsdevsql2k14e",
"sql-emsdbscrub2k8r2",
"sql-emsdbscrub2k8",
"sql-emsdbscrub2k5",
"sql-emsdbscrub2k12",
"SQL-DPM",
"SQL- doniasql2000",
"SQL-qsqasql01sql2012",
"SQL-qsqasql01agent",
"SQL-QsQaQrs2k12agent",
"SQL-QsQaQrs2k12",
"SQL-QSPMSQL2K5",
"SQL-QSDEVSQLP1agent",
"SQL-QSDEVSQLP",
"sql-qsdevsql2k8",
"sql-qsdevmeditech",
"SQL-qsdevbi3agent",
"SQL-qsdevbi3",
"SQL-QSBI2",
"SQL-QSBAKRoyea",
"SQL-ISSQLTest01",
"QS VersionOne",
"SQL-QSQASQL2K8",
"SQL-QHRTFS",
"SQL-QHRSQL06",
"SQL-QHRSQL05QSIMP2K8R2",
"SQL-QHRSQL05QA2K8BR2",
"SQL-QHRSQL04QSSUP2K8",
