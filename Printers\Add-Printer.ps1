[CmdletBinding()]
param (
    [Parameter(Mandatory = $false)]
    [string]$PrinterIP,

    [Parameter(Mandatory = $false)]
    [string]$PrinterName,

    [Parameter(Mandatory = $false)]
    [string]$PrinterDriverINFPath = "C:\Windows\System32\DriverStore\FileRepository\ntprint.inf_amd64_xxxxxxxx\ntprint.inf" # Replace 'xxxxxxxx' with the actual folder name on your system
)

# --- Functions ---

function Get-PrinterIP {
    while ($true) {
        $PrinterIP = Read-Host "Enter the IP address of the printer (or type 'help' or 'exit'): "

        if ($PrinterIP -eq 'help') {
            Write-Host "Please refer to this page for help finding your printer's IP address:"
            Write-Host "https://qhr.atlassian.net/wiki/spaces/DCO/pages/18089483/Printer+Konica-+Copier+Config"
        } elseif ($PrinterIP -eq 'exit') {
            return $null
        } elseif (Test-Connection -ComputerName $PrinterIP -Count 1 -Quiet) {
            # Ensure the IP is reachable before accepting it
            break
        } else {
            Write-Warning "Invalid or unreachable IP address. Please try again."
        }
    }
    return $PrinterIP
}

function Get-PrinterName {
    while ($true) {
        $PrinterName = Read-Host "Enter a name for the printer (or type 'exit'): "
        if ($PrinterName -eq 'exit') {
            return $null
        } elseif ($PrinterName -match '^[a-zA-Z0-9\s\-_]+$') {  # Allow basic characters
            break
        } else {
            Write-Warning "Invalid printer name. Use letters, numbers, spaces, hyphens, or underscores."
        }
    }
    return $PrinterName
}

# --- Main Script ---
# ... (Help message and parameter handling remain the same)

# Detect OS version to determine driver location
$OSVersion = (Get-WmiObject -Class Win32_OperatingSystem).Version
$IsWindows11 = [version]$OSVersion -ge [version]'10.0.22000.0'  # Windows 11 version threshold

# Set driver store path based on OS version
$DriverStorePath = if ($IsWindows11) {
    "C:\Windows\System32\spool\drivers\x64\3"
} else {
    "C:\Windows\System32\DriverStore\FileRepository\ntprint.inf_amd64*"
}

do {
    Write-Host "Welcome to the Printer Setup Wizard!"
    $choice = 0

    if (-not $PrinterIP) { 
        $PrinterIP = Get-PrinterIP
        if ($PrinterIP -eq $null) { break } 
    }

    if (-not $PrinterName) {
        $PrinterName = Get-PrinterName
        if ($PrinterName -eq $null) { break }
    }

    # --- Clean Up Existing Printer and Port ---
    if (Get-Printer -Name $PrinterName) {
        Write-Verbose "Removing existing printer with the same name."
        Remove-Printer -Name $PrinterName -ErrorAction SilentlyContinue
    }
   
    $PortName = "\\$PrinterIP\print"  # Use the correct format for a TCP/IP port name

    # --- Add the Printer with Manual Driver Selection ---
    try {
        Write-Verbose "Adding printer with name: $PrinterName and port: $PortName"
        # If the driver path was provided, install the driver first:
        if ($PrinterDriverINFPath) {
            pnputil.exe /add-driver "$PrinterDriverINFPath" /install
        }
        Add-Printer -Name $PrinterName -DriverName "Auto Detect" -PortName $PortName -ErrorAction Stop
        # --- Manual Driver Installation Prompt (If necessary) ---
        if (-not (Get-PrinterDriver -Name $PrinterName)) {
            Write-Warning "Could not automatically find a driver for this printer."
            if ((Read-Host -Prompt "Would you like to manually select a driver? (y/n)") -like 'y*') {
                rundll32 printui.dll,PrintUIEntry /e /n "$PrinterName"
            } else {
                Write-Warning "You will need to manually install the driver for this printer to function properly."
            }
        }
    }
    catch {
        Write-Error "Failed to add the printer. Ensure the IP address and port are correct, no other printers with that name exist, and try again."
        $choice = 0 # Force the loop to continue to the menu
    }
    
    # --- Optional Test Print ---
    if ($choice -ne 0) {
        if ((Read-Host -Prompt "Send a test print? (y/n)") -like 'y*') {
            try {
                Write-Host "Sending test print..."
                Start-Process -FilePath "notepad.exe" -ArgumentList "-print $PortName" -Verb Print -Wait
                Write-Host "Test print successful!"
            }
            catch {
                Write-Error "Test print failed. Please check the printer's status."
            }
        }
    }
   

    # --- Menu for Next Steps ---

    Write-Host "What do you want to do now?"
    Write-Host "1) Retry with current printer"
    Write-Host "2) Retry with a new printer IP"
    Write-Host "3) Close script"

    do {
        $choice = Read-Host "Enter your choice (1-3): "
    } until ($choice -match '^[1-3]$') # Ensure valid input
    
    switch ($choice) {
        1 { $choice = 0; continue } # Reset choice and retry with the same printer
        2 { $PrinterIP = $null; continue } # Prompt for new IP
        3 { break }  # Exit the script
    }
    
} while ($true) 

Write-Host "Printer setup complete!"
