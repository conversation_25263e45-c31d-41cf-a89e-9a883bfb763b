<#
.SYNOP<PERSON>S
    Detection script for Intune to check if browser bookmarks have been backed up
    by the companion remediation script within the last 6 days.

.DESCRIPTION
    This script checks for the 'BrowserBookmarkBackups' folder. If found, it checks 
    if any file within it has been modified less than 6 days ago.
    Remediation is triggered if backups are 6 days old or older.

    - Exits 0 (Compliant): If a backup file modified < 6 days ago is found.
    - Exits 1 (Non-Compliant): If backup folder doesn't exist, is empty, 
                               or all backup files are 6 days old or older.
    
    This script produces no console output.

.NOTES
    Author: Enhanced and formatted by <PERSON><PERSON>
    Date: June 3, 2025
#>

#------------------------------------------------------------------------------
# Function Definition (copied from remediation script for consistency)
#------------------------------------------------------------------------------
Function Get-BrowserBackupBaseDirectory {
    <#
    .SYNOPSIS
        Determines the root backup directory for all browser bookmarks.
    .RETURNS
        [string] The path to the base backup directory, or $null if not found.
    #>
    Param()
    $backupRootFolderName = "BrowserBookmarkBackups"
    $potentialDocPaths = [System.Collections.Generic.List[string]]::new()
    
    if ($env:OneDrive) {
        $oneDriveDocs = Join-Path -Path $env:OneDrive -ChildPath "Documents"
        if (Test-Path $oneDriveDocs) { $potentialDocPaths.Add($oneDriveDocs) }
    }
    if ($env:OneDriveCommercial) {
        $oneDriveCommDocs = Join-Path -Path $env:OneDriveCommercial -ChildPath "Documents"
        if (Test-Path $oneDriveCommDocs) {
            if (-not ($potentialDocPaths.Contains($oneDriveCommDocs))) { $potentialDocPaths.Add($oneDriveCommDocs) }
        }
    }
    $localUserDocs = Join-Path -Path $env:USERPROFILE -ChildPath "Documents"
    if (Test-Path $localUserDocs) {
        if (-not ($potentialDocPaths.Contains($localUserDocs))) { $potentialDocPaths.Add($localUserDocs) }
    }
    
    foreach ($docPath in ($potentialDocPaths | Select-Object -Unique)) {
        $targetBaseDir = Join-Path -Path $docPath -ChildPath $backupRootFolderName
        if (Test-Path $targetBaseDir) {
            return $targetBaseDir 
        }
    }
    return $null 
}
    
#------------------------------------------------------------------------------
# Main Detection Logic
#------------------------------------------------------------------------------
try {
    $baseBackupDirectoryPath = Get-BrowserBackupBaseDirectory
    
    if ([string]::IsNullOrEmpty($baseBackupDirectoryPath) -or (-not (Test-Path -Path $baseBackupDirectoryPath -PathType Container))) {
        # Backup base directory does not exist or is not a folder, remediation needed.
        #Write-Host "Backup base directory does not exist or is not a folder. Remediation needed."
        exit 1
    }
    
    # Get the newest file in the backup directory, recursively.
    $newestFile = Get-ChildItem -Path $baseBackupDirectoryPath -Recurse -File -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if ($null -eq $newestFile) {
        # No files found in the backup directory. Remediation needed.
        #Write-Host "No files found in the backup directory. Remediation needed."
        exit 1
    }
    
    # Check if the newest file is less than 6 days old.
    # If LastWriteTime is GREATER THAN (Now - 6 days), it means it's 0-5 days old.
    $comparisonDate = (Get-Date).AddDays(-6)
    if ($newestFile.LastWriteTime -gt $comparisonDate) {
        # Backup is recent enough (less than 6 days old). Compliant.
        #Write-Host "Backup is recent enough (less than 6 days old). Compliant."
        exit 0
    }
    else {
        # Backup is 6 days old or older. Remediation needed.
        #Write-Host "Backup is 6 days old or older. Remediation needed."
        exit 1
    }
}
catch {
    # Any unexpected error during detection means we can't confirm compliance.
    #Write-Host "An error occurred during detection: $($_.Exception.Message)"
    exit 1
    }