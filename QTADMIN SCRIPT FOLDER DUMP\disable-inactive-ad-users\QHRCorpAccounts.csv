"SamAccountName","Name"
"18king","QHR Toronto Office"
"aaron.blair","<PERSON>"
"accvm","Accounting Voicemail"
"ADMTUser1","ADMT User1"
"ADRmsSQL","ADRmsSQL"
"allen.jonoke","<PERSON>"
"amanda.keating","<PERSON>"
"amanda.korecki","<PERSON>"
"amehlx","amehlx blah"
"angelo.mariano","<PERSON>"
"anothony.filipovich","Anothony Filipovich"
"axis","AXIS"
"BDCService","BDC Service"
"ben.merlin","<PERSON> Merlin"
"carole.darcy","Carole D'Arcy"
"CLINICARE$","CLINICARE$"
"ContentAccess","ContentAccess"
"crgroup","CR Group"
"curtis.tobin","Curtis Tobin"
"daniel.berry","<PERSON>"
"david.krish","<PERSON>"
"deanna.gourley","<PERSON><PERSON>"
"DevPPService","DevPPService"
"devtrainer","DevTrainer"
"elizabeth.morgan","<PERSON>"
"elton.mahabir","<PERSON> Mahabir"
"emmanuel.izuorgu","<PERSON> Izuorgu"
"EMRleads","EMRleads"
"FastSearch","FastSearch"
"FepSQLAdmin","FepSQLAdmin"
"FEPSQLRPT","FEPSQLRPT"
"george.papadogolus","George Papadogolus"
"GPeconnect","GPeconnect"
"graham.pomfret","Graham Pomfret"
"greg.harpell","Greg Harpell"
"hssupport","HS After Hours Support"
"IIS-DevQHRnet","IIS-DevQHRnet"
"IIS-DevQHRnet2","IIS-DevQHRnet2"
"IT","QHR Technologies IT"
"ITUser","IT User"
"jerry.diener","Jerry Diener"
"jonokeservice","Jonoke Service"
"josephine.kwong","Josephine Kwong"
"LM5BRPC","LM5BRPC"
"lmkiosk","LM Kiosk"
"lydia.chan","Lydia Chan"
"ManagedMetadata","ManagedMetadata"
"marlene.sullivan","Marlene Sullivan"
"MichaelCopy","MichaelCopy"
"nadeen.aljamal","Nadeen Aljamal"
"nancy.chapeskie","Nancy Chapeskie"
"natalia.ros","Natalia Ros"
"nazia.tarikh","Nazia Tarikh"
"NEWeconnect","NEWeconnect"
"NPUMAdmin","NPUMAdmin"
"oleg.serebryany","Oleg Serebryany"
"ops.jira","ops jira"
"optdev","EMR Development"
"optimedprivacyoffice","Optimed Privacy Officer"
"OSCareers","Optimed Careers"
"osdevah","Optimed Development After Hours"
"patient.prep","Patient Prep"
"PowerPivot","PowerPivot"
"qhrbackupsmtp","QHR backup smtp"
"qhrsoftwareprivacyof","QHR Software Privacy Officer"
"qtialerts","qtialerts"
"qtmitelccm","qtmitelccm"
"qtmiteloaisys","qtmiteloaisys"
"qtmitelvmail","qtmitelvmail"
"QTSPSQL1","QTSPSQL1"
"QTSPTestSPSetup","QTSPTestSPSetup"
"QTSPTestSQLService","QTSPTestSQLService"
"QTSQL01-Reporting","QTSQL01-Reporting"
"raccess","Restricted Access"
"rafay.siddiqui","Rafay Siddiqui"
"ron.hughes","Ron Hughes"
"saad.hussain","Saad Hussain"
"SecureStore","SecureStore"
"sep","SEP"
"ServerFarm","Server Farm"
"shawn.manary","Shawn Manary"
"shelly.quick","Shelly Quick"
"SM_0f8a0f47cb2e4a22a","HealthMailbox6a4d16e7eb864382990662dc6f168e07"
"SM_257bdd6fe0d6447d9","HealthMailboxfc9b0fd3d8fe4ea185981c4cb793decd"
"SM_261419858daf4ba58","HealthMailbox8776ebe746b44341a301c0fc67f7ce71"
"SM_28be8f1d238941bba","HealthMailbox5443095f615d4f93b4be46d342d2a49c"
"SM_43779851bc4149518","HealthMailbox8857b66bf5eb4a7abe1a17f90224017f"
"SM_699e27c8c8424a92b","HealthMailbox82ac275a78844637b61bb3488cdfc19e"
"SM_82b5a0702710414f8","HealthMailbox415fb300067042e5850f52c06bb2192d"
"SM_9206f638bd4148188","HealthMailbox27dc2182340a4e6f88073afa6fd5b6b0"
"SM_ade6589e198e43559","HealthMailboxa8f4ea260413413f996af1a0efbd241b"
"SM_b65d50d0151a49ebb","HealthMailboxcf912289b3fa4a87aff09807b36c3cc0"
"SM_bd33416f26274fe08","HealthMailboxbdcc22f2556449419d46e868fe8ab66f"
"SM_c720eb4fba804a0c8","HealthMailboxe022c10c51a2441c8070f2de7dd2a67b"
"SM_c906edddab944224b","HealthMailboxc7aca89a7b8f4114be2b5b8b3dd90f67"
"SM_cf943557801c4b8e8","HealthMailboxaf57a026aeb84990ae4de35637d6d2ff"
"SM_d340e90239284575a","HealthMailbox0272b40aebbf4324a041630b1ade971b"
"SM_db00432ac47644a0b","HealthMailbox0533d4b12e1440ebb34e88cb23dba6fa"
"sonicwall","sonicwall"
"SpiceWorksSA","SpiceWorksSA"
"SPSearch","SpSearch"
"SPSetup","SPSetup"
"SPUserProfile","SPUserProfile"
"SQL-OptDevKel03","SQL-OptDevKel03"
"SQL-QHRSQL03","SQL-QHRSQL03"
"SQL-qhrsql03SQL2K5","SQL-qhrsql03SQL2K5"
"SQL-QHRSQL03sql2k5v1","SQL-QHRSQL03sql2k5v1dm"
"SQL-QTSQL01","SQL-QTSQL01"
"SRV-OptOnline","SRV-OptOnline"
"srv-virtualBackup","srv-virtualBackup"
"srv-VSBackup","srv-VSBackup"
"stella.cruz","Stella Cruz"
"SuperReader","SuperReader"
"SuperUser","SuperUser"
"terry.wagner","Terry Wagner"
"test.jira","Test Jira"
"test.onprem","Test OnPrem"
"testuser2","test user2"
"testvpn","TestVPN"
"tfsservice","TFSSERVICE"
"TsInternetUser","TsInternetUser"
"viktor.velkovski","Viktor Velkovski"
"VSAdmin","VSAdmin"
"wds","WDS"
