{
  "policies": {
    "AllowedDomainsForApps": "managedfirefox.com,example.com",
    "AppAutoUpdate": true | false,
    "AppUpdatePin": "106.",
    "AppUpdateURL": "https://yoursite.com",
    "Authentication": {
      "SPNEGO": ["mydomain.com", "https://myotherdomain.com"],
      "Delegated": ["mydomain.com", "https://myotherdomain.com"],
      "NTLM": ["mydomain.com", "https://myotherdomain.com"],
      "AllowNonFQDN": {
        "SPNEGO": true | false,
        "NTLM": true | false
      },
      "AllowProxies": {
        "SPNEGO": true | false,
        "NTLM": true | false
      },
      "Locked": true | false,
      "PrivateBrowsing": true | false
    },
    "AutoLaunchProtocolsFromOrigins": [{
      "protocol": "zoommtg",
      "allowed_origins": [
        "https://somesite.zoom.us"
      ]
    }],
    "BackgroundAppUpdate": true | false,
    "BlockAboutAddons": true | false,
    "BlockAboutConfig": true | false,
    "BlockAboutProfiles": true | false,
    "BlockAboutSupport": true | false,
    "Bookmarks": [
      {
        "Title": "Example",
        "URL": "https://example.com",
        "Favicon": "https://example.com/favicon.ico",
        "Placement": "toolbar" | "menu",
        "Folder": "FolderName"
      }
    ],
    "CaptivePortal": true | false,
    "Certificates": {
      "Install": ["cert1.der", "/home/<USER>/cert2.pem"],
    },
    "Containers": {
      "Default": [
        {
          "name": "My container",
          "icon": "pet",
          "color": "turquoise"
        }
      ]
    },
    "Cookies": {
      "Allow": ["http://example.org/"],
      "AllowSession": ["http://example.edu/"],
      "Block": ["http://example.edu/"],
      "Locked": true | false,
      "Behavior": "accept" | "reject-foreign" | "reject" | "limit-foreign" | "reject-tracker" | "reject-tracker-and-partition-foreign",
      "BehaviorPrivateBrowsing": "accept" | "reject-foreign" | "reject" | "limit-foreign" | "reject-tracker" | "reject-tracker-and-partition-foreign",
    },
    "DefaultDownloadDirectory": "${home}/Downloads",
    "DisableBuiltinPDFViewer": true | false,
    "DisabledCiphers": {
      "CIPHER_NAME": true | false,
    },
    "DisableDeveloperTools": true | false,
    "DisableEncryptedClientHello": true | false,
    "DisableFeedbackCommands": true | false,
    "DisableFirefoxAccounts": true | false,
    "DisableFirefoxScreenshots": true | false,
    "DisableFirefoxStudies": true | false,
    "DisableForgetButton": true | false,
    "DisableFormHistory": true | false,
    "DisableMasterPasswordCreation": true | false,
    "DisablePasswordReveal": true | false,
    "DisablePrivateBrowsing": true | false,
    "DisableProfileImport": true | false,
    "DisableProfileRefresh": true | false,
    "DisableSafeMode": true | false,
    "DisableSecurityBypass": {
      "InvalidCertificate": true | false,
      "SafeBrowsing": true | false
    },
    "DisableSetDesktopBackground": true | false,
    "DisableSystemAddonUpdate": true | false,
    "DisableTelemetry": true | false,
    "DisplayBookmarksToolbar": "always" | "never" | "newtab",
    "DisplayMenuBar": "always", "never", "default-on", "default-off",
    "DNSOverHTTPS": {
      "Enabled":  true | false,
      "ProviderURL": "URL_TO_ALTERNATE_PROVIDER",
      "Locked": true | false,
      "ExcludedDomains": ["example.com"]
    },
    "DontCheckDefaultBrowser": true | false,
    "DownloadDirectory": "${home}/Downloads",
    "EnableTrackingProtection": {
      "Value": true | false,
      "Locked": true | false,
      "Cryptomining": true | false,
      "Fingerprinting": true | false,
      "EmailTracking": true | false,
      "Exceptions": ["https://example.com"]
    },
    "EncryptedMediaExtensions": {
      "Enabled": true | false,
      "Locked": true | false
    },
    "ExemptDomainFileTypePairsFromFileTypeDownloadWarnings": [{
      "file_extension": "jnlp",
      "domains": ["example.com"]
    }],
    "Extensions": {
      "Install": ["https://addons.mozilla.org/firefox/downloads/somefile.xpi", "//path/to/xpi"],
      "Uninstall": ["<EMAIL>"],
      "Locked":  ["<EMAIL>"]
    },
    "ExtensionSettings": {
      "*": {
        "blocked_install_message": "Custom error message.",
        "install_sources": ["https://yourwebsite.com/*"],
        "installation_mode": "blocked",
        "allowed_types": ["extension"]
      },
      "<EMAIL>": {
        "installation_mode": "force_installed",
        "install_url": "https://addons.mozilla.org/firefox/downloads/latest/ublock-origin/latest.xpi"
      },
      "<EMAIL>": {
        "installation_mode": "allowed"
      }
    },
    "ExtensionUpdate": true | false,
    "FirefoxHome": {
      "Search": true | false,
      "TopSites": true | false,
      "SponsoredTopSites": true | false,
      "Highlights": true | false,
      "Pocket": true | false,
      "SponsoredPocket": true | false,
      "Snippets": true | false,
      "Locked": true | false
    },
    "FirefoxSuggest": {
      "WebSuggestions": true | false,
      "SponsoredSuggestions": true | false,
      "ImproveSuggest": true | false,
      "Locked": true | false
    },
    "GoToIntranetSiteForSingleWordEntryInAddressBar": true | false,
    "Handlers": {
      "mimeTypes": {
        "application/msword": {
          "action": "useSystemDefault",
          "ask": false
        }
      },
      "schemes": {
        "mailto": {
          "action": "useHelperApp",
          "ask": true | false,
          "handlers": [{
            "name": "Gmail",
            "uriTemplate": "https://mail.google.com/mail/?extsrc=mailto&url=%s"
          }]
        }
      },
      "extensions": {
        "pdf": {
          "action": "useHelperApp",
          "ask": true | false,
          "handlers": [{
            "name": "Adobe Acrobat",
            "path": "/usr/bin/acroread"
          }]
        }
      }
    },
    "HardwareAcceleration": true | false,
    "Homepage": {
      "URL": "http://example.com/",
      "Locked": true | false,
      "Additional": ["http://example.org/",
                    "http://example.edu/"],
      "StartPage": "none" | "homepage" | "previous-session" | "homepage-locked"
    },
    "HttpAllowlist": ["http://example.org",
                       "http://example.edu"],
    "HttpsOnlyMode": "allowed" | "disallowed" | "enabled" | "force_enabled",
    "InstallAddonsPermission": {
      "Allow": ["http://example.org/",
                "http://example.edu/"],
      "Default": true | false
    },
    "LegacySameSiteCookieBehaviorEnabled": true | false,
    "LegacySameSiteCookieBehaviorEnabledForDomainList": ["example.org", "example.edu"],
    "LocalFileLinks": ["http://example.org/", "http://example.edu/"],
    "ManagedBookmarks": [
      {
        "toplevel_name": "My managed bookmarks folder"
      },
      {
        "url": "example.com",
        "name": "Example"
      },
      {
        "name": "Mozilla links",
        "children": [
          {
            "url": "https://mozilla.org",
            "name": "Mozilla.org"
          },
          {
            "url": "https://support.mozilla.org/",
            "name": "SUMO"
          }
        ]
      }
    ],
    "NetworkPrediction": true | false,
    "NewTabPage": true | false,
    "NoDefaultBookmarks": true | false,
    "OfferToSaveLogins": true | false,
    "OfferToSaveLoginsDefault": true | false,
    "OverrideFirstRunPage": "http://example.org",
    "OverridePostUpdatePage": "http://example.org",
    "PasswordManagerEnabled": true | false,
    "PasswordManagerExceptions": ["https://example.org", "https://example.edu"],
    "PDFjs": {
      "Enabled": true | false,
      "EnablePermissions": true | false
    },
    "Permissions": {
      "Camera": {
        "Allow": ["https://example.org","https://example.org:1234"],
        "Block": ["https://example.edu"],
        "BlockNewRequests": true | false,
        "Locked": true | false
      },
      "Microphone": {
        "Allow": ["https://example.org"],
        "Block": ["https://example.edu"],
        "BlockNewRequests": true | false,
        "Locked": true | false
      },
      "Location": {
        "Allow": ["https://example.org"],
        "Block": ["https://example.edu"],
        "BlockNewRequests": true | false,
        "Locked": true | false
      },
      "Notifications": {
        "Allow": ["https://example.org"],
        "Block": ["https://example.edu"],
        "BlockNewRequests": true | false,
        "Locked": true | false
      },
      "Autoplay": {
        "Allow": ["https://example.org"],
        "Block": ["https://example.edu"],
        "Default": "allow-audio-video" | "block-audio" | "block-audio-video",
        "Locked": true | false
      }
    },
    "PictureInPicture": {
      "Enabled": true | false,
      "Locked": true | false
    },
    "PopupBlocking": {
      "Allow": ["http://example.org/",
                "http://example.edu/"],
      "Default": true | false,
      "Locked": true | false
    },
    "PostQuantumKeyAgreementEnabled": true | false,
    "Preferences": {
      "accessibility.force_disabled": {
        "Value": 1,
        "Status": "default"
      },
      "browser.cache.disk.parent_directory": {
        "Value": "SOME_NATIVE_PATH",
        "Status": "user"
      },
      "browser.tabs.warnOnClose": {
        "Value": false,
        "Status": "locked"
      }
    },
    "PrimaryPassword": true | false,
    "PrintingEnabled": true | false,
    "PrivateBrowsingModeAvailability": 0 | 1 | 2,
    "PromptForDownloadLocation": true | false,
    "Proxy": {
      "Mode": "none" | "system" | "manual" | "autoDetect" | "autoConfig",
      "Locked": true | false,
      "HTTPProxy": "hostname",
      "UseHTTPProxyForAllProtocols": true | false,
      "SSLProxy": "hostname",
      "FTPProxy": "hostname",
      "SOCKSProxy": "hostname",
      "SOCKSVersion": 4 | 5,
      "Passthrough": "<local>",
      "AutoConfigURL": "URL_TO_AUTOCONFIG",
      "AutoLogin": true | false,
      "UseProxyForDNS": true | false
    },
    "RequestedLocales": "de,en-US",
    "SanitizeOnShutdown": {
      "Cache": true | false,
      "Cookies": true | false,
      "History": true | false,
      "Sessions": true | false,
      "SiteSettings": true | false,
      "Locked": true | false
    },
    "SearchEngines": {
      "Add": [
        {
          "Name": "Example1",
          "URLTemplate": "https://www.example.org/q={searchTerms}",
          "Method": "GET" | "POST",
          "IconURL": "https://www.example.org/favicon.ico",
          "Alias": "example",
          "Description": "Description",
          "PostData": "name=value&q={searchTerms}",
          "SuggestURLTemplate": "https://www.example.org/suggestions/q={searchTerms}"
        }
      ],
      "Default": "NAME_OF_SEARCH_ENGINE",
      "PreventInstalls": true | false,
      "Remove": ["NAME_OF_SEARCH_ENGINE"]
    },
    "SearchSuggestEnabled": true | false,
    "SecurityDevices": {
      "NAME_OF_DEVICE": "PATH_TO_LIBRARY_FOR_DEVICE"
    },
    "ShowHomeButton": true | false,
    "SkipTermsOfUse": true | false,
    "SSLVersionMax": "tls1" | "tls1.1" | "tls1.2" | "tls1.3",
    "SSLVersionMin": "tls1" | "tls1.1" | "tls1.2" | "tls1.3",
    "SupportMenu": {
      "Title": "Support Menu",
      "URL": "http://example.com/support",
      "AccessKey": "S"
    },
    "StartDownloadsInTempDirectory": true | false,
    "TranslateEnabled": true | false,
    "UserMessaging": {
      "ExtensionRecommendations": true | false,
      "FeatureRecommendations": true | false,
      "UrlbarInterventions": true | false,
      "SkipOnboarding": true | false,
      "MoreFromMozilla": true | false,
      "FirefoxLabs": true | false,
      "Locked": true | false
    },
    "UseSystemPrintDialog": true | false.,
    "WebsiteFilter": {
      "Block": ["<all_urls>"],
      "Exceptions": ["http://example.org/*"]
    }
  }
}