﻿#Created May 28, 2021
#Last Modified June 1, 2021 by <PERSON> Pinske
#Pulls members of a specified local ad group and then use those members to create a new group in Azure cloud


get-adgroup -filter * | sort name | select Name | Out-host

#Selects the local AD group whose members will be used to populate a new Azure AD group
$localADGroupName = read-host "`n`nEnter the local AD group name whose users will be used to populate the new Azure group`n"
Get-ADGroupMember -identity $localADGroupName | get-aduser -Property mail | select mail | Export-csv -path "C:\Scripts\Userlists\group-userlist.csv" -NoTypeInformation
Write-Output "`nPlease authenticate to Azure-AD to export the users into the new Azure group" | out-host

connect-azuread



#Creates the new Azure AD group
$groupName = read-host "Enter the name for the new Azure group"
$groupDesc = read-host "Enter the description for the new Azure group"
$groupMailEnabled = read-host "Will this group use email? (Enter 'n' for the group to be a Security group instead) (y/n)"



#Specifies if the new group is to be a Microsoft 365(email-attached) group or a Security group
if($groupMailEnabled -eq "y"){
$groupMailName = $groupName + "@qhrtech.com"
new-azureadgroup -Description $groupDesc -DisplayName $groupName -MailEnabled $true -SecurityEnabled $false -MailNickName $groupMailName 
}
else{new-azureadgroup -Description $groupDesc -DisplayName $groupName -MailEnabled $false -SecurityEnabled $true -MailNickName "none"
}



#Specifies the owner the newly created Azure AD group
$groupObjectID = $(Get-AzureADGroup -searchstring $groupName).ObjectId
$groupOwner = read-host "Please specify the Owner of the group (firstname.lastname)"
$groupOwner += "@qhrtech.com"
Add-AzureADGroupOwner -ObjectId $groupObjectID -RefObjectId $(get-azureaduser -objectid $groupOwner).ObjectId

#Asks to add other owners
$anotherGroupOwner = read-host "Set another user as an Owner of the group? (y/n)"
while( $anotherGroupOwner -eq "y"){
$groupOwner = read-host "Please specify the Owner of the group (firstname.lastname)"
$groupOwner += "@qhrtech.com"
Add-AzureADGroupOwner -ObjectId $groupObjectID -RefObjectId $(get-azureaduser -objectid $groupOwner).ObjectId
$anotherGroupOwner = read-host "Set another user as an Owner of the group? (y/n)"
}



#Adds users taken from the specifed local AD group into the newly created Azure AD group
$userList = import-csv -delimiter "," -path 'C:\Scripts\Userlists\group-userlist.csv'
foreach($user in $userList){ 
Add-AzureADGroupMember -ObjectId $groupObjectID -RefObjectId $(get-azureaduser -objectid $user.mail).ObjectId
}