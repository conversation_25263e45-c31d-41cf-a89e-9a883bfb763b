﻿$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)

Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred

# Get group members
# Get-SPOSiteGroup -Site https://qhrtech.sharepoint.com -Group "QTSS - Admins" |select -ExpandProperty Users

# Get user memberships
## $grouplist = Get-SPOUser -Site https://qhrtech.sharepoint.com  -LoginName <EMAIL>| select -ExpandProperty groups

# Find user
# Get-SPOUser -Site https://qhrtech.sharepoint.com -limit all | ? DisplayName -match "Kerry"