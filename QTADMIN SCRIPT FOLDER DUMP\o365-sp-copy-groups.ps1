﻿clear

$SPsite = "https://qhrtech.sharepoint.com"
$source = $null
$target = $null


Try {
  $connected = Get-SPOSite
  Write-Host "Already Connected to SharePoint Online!`n`n" -ForegroundColor Green
}
Catch {
  Write-Host "Connecting to SharePoint Online...`n`n" -ForegroundColor Green
  $creduser = "<EMAIL>"
  $LiveCred = Get-Credential $creduser
  #$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
  #$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)
  Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred
 }

do {
$Error.Clear()
 $source = Read-Host "Enter email address of user to copy groups FROM"
 $checksource = get-spouser -Site $SPsite -LoginName $source -ErrorAction SilentlyContinue
} until ($Error.Count -eq 0)

do {
$Error.Clear()
 $target = Read-Host "Enter email address of user to copy groups TO"
 $checksource = get-spouser -Site $SPsite -LoginName $target -ErrorAction SilentlyContinue
} until ($Error.Count -eq 0)


# Get group members
# Get-SPOSiteGroup -Site $SPsite -Group "QTSS - Admins" |select -ExpandProperty Users

# Get user memberships
$grouplist = Get-SPOUser -Site $SPsite  -LoginName $source| select -ExpandProperty groups

Write-Host "`n----------"
$grouplist

$lastchance = Read-Host "----------`n`nAdd $target to the above groups??? (y/n)"

if ($lastchance -eq "y") {
  foreach ($group in $grouplist) {
   Add-SPOUser -Site $SPsite -LoginName $target -Group $group
  }
}
Write-Host "`n`nWork Complete!!!" -ForegroundColor Green