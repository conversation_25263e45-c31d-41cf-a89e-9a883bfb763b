# Uninstall Microsoft.Graph modules except Microsoft.Graph.Authentication

# Get all available Microsoft.Graph modules, filter out 'Microsoft.Graph.Authentication', and get unique module names
$Modules = Get-Module Microsoft.Graph* -ListAvailable | 
    Where-Object { $_.Name -ne "Microsoft.Graph.Authentication" } | 
    Select-Object -ExpandProperty Name -Unique

# For each unique module name, get all its available versions
foreach ($Module in $Modules) {
    $Versions = Get-Module $Module -ListAvailable

    # For each version, uninstall it
    foreach ($Version in $Versions) {
        Write-Host "Uninstalling Module: $Module $($Version.Version)"
        Uninstall-Module $Module -RequiredVersion $Version.Version -ErrorAction SilentlyContinue
    }
}

# Uninstall modules that couldn't be removed in the first part
$InstalledModules = Get-InstalledModule Microsoft.Graph* | 
    Where-Object { $_.Name -ne "Microsoft.Graph.Authentication" } | 
    Select-Object -ExpandProperty Name -Unique

foreach ($InstalledModule in $InstalledModules) {
    $InstalledVersions = Get-Module $InstalledModule -ListAvailable

    foreach ($InstalledVersion in $InstalledVersions) {
        Write-Host "Uninstalling Module: $InstalledModule $($InstalledVersion.Version)"
        Uninstall-Module $InstalledModule -RequiredVersion $InstalledVersion.Version -ErrorAction SilentlyContinue
    }
}

# Uninstall Microsoft.Graph.Authentication
$ModuleName = "Microsoft.Graph.Authentication"
$Versions = Get-Module $ModuleName -ListAvailable

foreach ($Version in $Versions) {
    Write-Host "Uninstalling Module: $ModuleName $($Version.Version)"
    Uninstall-Module $ModuleName -RequiredVersion $Version.Version
}
