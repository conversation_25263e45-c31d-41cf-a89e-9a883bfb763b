# Prompt for the Distribution Group email address
$GroupEmailID = Read-Host -Prompt "Please enter the email address for the Distribution Group"

# Prompt for the CSV file path
$CSVFile = Read-Host -Prompt "Please enter the full path to the CSV file (no extra double-quotes)"

# Connect to Exchange Online (requires ExchangeOnlineManagement module)
Connect-ExchangeOnline -ShowBanner:$False

# Test if the group is a valid Distribution Group
Write-Host "Attempting to find distribution group: $GroupEmailID"
$CheckGroup = Get-DistributionGroup -Identity $GroupEmailID -ErrorAction SilentlyContinue
if (-not $CheckGroup) {
    Write-Host -ForegroundColor Red "Group '$GroupEmailID' not found or not a valid distribution group."
    return
}

# Retrieve existing members
$DLMembers = Get-DistributionGroupMember -Identity $GroupEmailID -ResultSize Unlimited |
             Select-Object -ExpandProperty PrimarySmtpAddress

# Import Distribution List Members from CSV
try {
    Import-CSV $CSVFile -Header "UPN" | ForEach-Object {
        if ($DLMembers -contains $_.UPN) {
            Write-Host -ForegroundColor Yellow "User is already a member of the Distribution List:" $_.UPN
        }
        else {
            Add-DistributionGroupMember -Identity $GroupEmailID -Member $_.UPN
            Write-Host -ForegroundColor Green "Added User to Distribution List:" $_.UPN
        }
    }
}
catch {
    Write-Host -ForegroundColor Red "Error importing or processing CSV file: $CSVFile. $($_.Exception.Message)"
}
