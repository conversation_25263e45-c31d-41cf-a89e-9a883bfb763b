<#
.SYNOPSIS
    Copies group memberships from a source user, or assigns groups by department, to one or more target users and ensures licensing is applied.

.DESCRIPTION
    This script provides a menu-driven interface for two common user setup tasks, now with support for processing multiple users in a single run.
    It also assigns common and department-specific licenses, and forces a license reconciliation for each user's account.

    1) Copy User Groups: Clones all Entra ID group memberships from a single source user to one or more target users.
    2) Assign by Department: Assigns a predefined list of groups to one or more target users based on their department code (e.g., CS, TE).

    It requires the Microsoft.Graph PowerShell module and appropriate permissions.

.NOTES
    Author: Slader Sheppard
    Version: 3.3
    Domain: @qhrtech.com
    Change Log:
    v3.2 - Fixed the `Invoke-MgGraphRequest` call by adding `-Depth 5` to the `ConvertTo-Json` command.
    v3.3 - Added a results array and a final summary table to display the outcome for each processed user.
         - Refined the output to reduce clutter from successful Invoke-MgGraphRequest calls.

.EXAMPLE
    .\Copy-UserGroups.ps1
    (The script will then present a menu and prompt for the required information)
#>

# --- PREPARATION ---

# 1. Install and Import the Microsoft Graph Module
if (-not (Get-Module -ListAvailable -Name Microsoft.Graph)) {
    Write-Host "Microsoft.Graph module not found. Installing..." -ForegroundColor Yellow
    Install-Module Microsoft.Graph -Scope CurrentUser -Force -AllowClobber
}

# 2. Connect to Microsoft Graph
try {
    Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Cyan
    # Required permissions for all script actions.
    $requiredScopes = @("User.Read.All", "Group.Read.All", "GroupMember.Read.All", "GroupMember.ReadWrite.All", "User.ReadWrite.All")
    Connect-MgGraph -Scopes $requiredScopes
    Write-Host "Successfully connected." -ForegroundColor Green
}
catch {
    Write-Error "Failed to connect to Microsoft Graph. Please ensure you have the necessary permissions."
    return
}


# --- GROUP AND LICENSE DEFINITIONS ---

# A list of groups to be assigned to ALL users, regardless of department.
$commonGroups = @(
    "License Assignment - Microsoft 365 Comprehensive"
)

# A dictionary mapping department codes to a list of group DISPLAY NAMES.
$departmentGroups = @{
    "CS" = @(
        "AIP-Client-Services",
        "azure-adp",
        "BeyondTrust - Client Services - SSO",
        "CloudSync Admin Tool",
        # Using the full, exact name to ensure a precise match.
        "Conditional Access: Risky sign-in multifactor authentication (a4ea6c0f-b8fb-4d29-91f1-9f8cf0601e98)",
        "CXONE - Screen Agent Pilot",
        "CXONE Screen Recording",
        "MDM Enroll",
        "MDM Users",
        "Okta p - Accuro Cloud Users",
        "Okta p - Central Admin Users",
        "Okta p - Help Desk Administrators",
        "Okta p - Medeo Admin Tools",
        "Salesforce-Client Services",
        "Sendgrid - SSO",
        "tm-CS inbound",
        "Zscaler-Client-Services",
        "ZSCALER-USER"
    );
    "TE" = @(
        "azure-adp",
        "MDM Users"
    )
}


# --- SCRIPT HELPER FUNCTION ---

function Add-UserToGroup {
    param(
        [Parameter(Mandatory = $true)]
        [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphUser]
        $TargetUser,
        [Parameter(Mandatory = $true)]
        [Microsoft.Graph.PowerShell.Models.IMicrosoftGraphGroup]
        $Group
    )

    try {
        $groupMembers = Get-MgGroupMember -GroupId $Group.Id -All -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Id
        if ($TargetUser.Id -in $groupMembers) {
            Write-Host "User '$($TargetUser.DisplayName)' is ALREADY a member of group '$($Group.DisplayName)'." -ForegroundColor Yellow
        }
        else {
            Write-Host "Adding '$($TargetUser.DisplayName)' to group '$($Group.DisplayName)'..."
            New-MgGroupMember -GroupId $Group.Id -DirectoryObjectId $TargetUser.Id
            Write-Host " -> Success." -ForegroundColor Green
        }
        return $true
    }
    catch {
        Write-Warning " -> FAILED to add user to group '$($Group.DisplayName)' ($($Group.Id))."
        Write-Warning "   Error: $($_.Exception.Message)"
        return $false
    }
}


# --- MAIN SCRIPT LOGIC ---

$results = @()

try {
    # Display Menu and Get User's Choice
    Write-Host "`nPlease choose an action:" -ForegroundColor Cyan
    Write-Host "1) Copy group memberships from one user to another."
    Write-Host "2) Assign default groups to a user based on their department."
    $choice = Read-Host -Prompt "Enter your choice (1 or 2)"

    # Fetch all tenant groups and licenses once for efficiency and accurate matching.
    Write-Host "Fetching all tenant groups for local filtering... (this may take a moment)"
    $allGroups = Get-MgGroup -All
    Write-Host "Found $($allGroups.Count) total groups." -ForegroundColor Green
    
    Write-Host "Fetching all available licenses in the tenant..."
    $allSkus = Get-MgSubscribedSku -All
    Write-Host "Found $($allSkus.Count) available license types." -ForegroundColor Green
    
    # --- Collect Target Users ---
    $targetUserUpns = New-Object System.Collections.Generic.List[string]
    do {
        $email = Read-Host -Prompt "Enter a target user's full email (or press Enter to finish)"
        if (-not [string]::IsNullOrWhiteSpace($email)) {
            $targetUserUpns.Add($email)
        }
    } while (-not [string]::IsNullOrWhiteSpace($email))

    if ($targetUserUpns.Count -eq 0) {
        throw "No target users were entered. Aborting."
    }

    Write-Host "`nReady to process $($targetUserUpns.Count) target user(s)." -ForegroundColor Cyan
    
    # --- Get Configuration based on choice ---
    $sourceUserGroups = $null
    $deptCode = $null
    $departmentGroupNames = $null

    switch ($choice) {
        "1" {
            Write-Host "`n--- Mode: Copy Groups from User ---" -ForegroundColor Cyan
            $sourceUserUpn = Read-Host -Prompt "Enter the full email of the user to COPY groups from (e.g., <EMAIL>)"
            if ([string]::IsNullOrWhiteSpace($sourceUserUpn)) { throw "Source user email is required." }
            $sourceUser = Get-MgUser -UserId $sourceUserUpn -ErrorAction Stop
            Write-Host "Successfully found Source User: $($sourceUser.DisplayName)" -ForegroundColor Green
            $sourceUserGroups = Get-MgUserMemberOf -UserId $sourceUser.Id -All | Where-Object { $_.OdataType -eq '#microsoft.graph.group' }
            if (-not $sourceUserGroups) {
                Write-Warning "Source user '$($sourceUser.DisplayName)' is not a member of any groups."
            }
        }
        "2" {
            Write-Host "`n--- Mode: Assign Groups by Department ---" -ForegroundColor Cyan
            $deptCode = Read-Host -Prompt "Enter the department code (e.g., CS, TE)"
            if (-not $departmentGroups.ContainsKey($deptCode.ToUpper())) {
                throw "Invalid department code. Available codes are: $($departmentGroups.Keys -join ', ')"
            }
            $departmentGroupNames = $departmentGroups[$deptCode.ToUpper()]
        }
        default {
            throw "Invalid selection."
        }
    }

    # --- Process Each Target User ---
    foreach ($userUpn in $targetUserUpns) {
        $userResult = [PSCustomObject]@{
            User    = $userUpn
            Status  = "Success"
            Details = New-Object System.Collections.Generic.List[string]
        }

        try {
            Write-Host "`n=========================================================="
            Write-Host "Processing Target User: $userUpn" -ForegroundColor Yellow
            $targetUser = Get-MgUser -UserId $userUpn -ErrorAction Stop
            Write-Host "Successfully found user: $($targetUser.DisplayName)"

            # Perform the selected action
            if ($choice -eq "1") {
                # Copy
                if ($sourceUserGroups) {
                    Write-Host "`nStarting group copy process..."
                    foreach ($group in $sourceUserGroups) {
                        $fullGroupObject = $allGroups | Where-Object { $_.Id -eq $group.Id }
                        if ($fullGroupObject) { Add-UserToGroup -TargetUser $targetUser -Group $fullGroupObject }
                    }
                }
            }
            elseif ($choice -eq "2") {
                # Assign by Dept
                Write-Host "`nStarting department group assignment process for '$($deptCode.ToUpper())'..."
                foreach ($groupName in $departmentGroupNames) {
                    $group = $allGroups | Where-Object { $_.DisplayName -eq $groupName }
                    if ($group) { Add-UserToGroup -TargetUser $targetUser -Group $group }
                    else { Write-Warning "Could not find a group with the name '$($groupName)' in Entra ID. Skipping." }
                }
            }

            # --- COMMON AND DEPARTMENT-SPECIFIC TASKS ---
            # 1. Assign Common Groups
            Write-Host "`n--- Assigning Common Groups ---" -ForegroundColor Cyan
            foreach ($groupName in $commonGroups) {
                $group = $allGroups | Where-Object { $_.DisplayName -eq $groupName }
                if ($group) { Add-UserToGroup -TargetUser $targetUser -Group $group }
                else { Write-Warning "Could not find common group with the name '$($groupName)'. Skipping." }
            }

            # 2. Assign CS-Specific Direct License
            if ($deptCode -and $deptCode.ToUpper() -eq 'CS') {
                Write-Host "`n--- Assigning CS Department Specific License ---" -ForegroundColor Cyan
                try {
                    $callingPlanSkuPartNumber = "MCOPSTN_5"
                    $callingPlanSku = $allSkus | Where-Object { $_.SkuPartNumber -eq $callingPlanSkuPartNumber }

                    if ($callingPlanSku) {
                        Write-Host "Found license SKU for Calling Plan: $($callingPlanSku.SkuPartNumber)"
                        $body = @{
                            addLicenses    = @( @{ disabledPlans = @(); skuId = $callingPlanSku.SkuId } )
                            removeLicenses = @()
                        } | ConvertTo-Json -Depth 5
                        
                        $uri = "https://graph.microsoft.com/v1.0/users/$($targetUser.Id)/assignLicense"
                        Invoke-MgGraphRequest -Method POST -Uri $uri -Body $body -ContentType "application/json" | Out-Null
                        Write-Host "Successfully assigned direct license '$($callingPlanSku.SkuPartNumber)' to '$($targetUser.DisplayName)'." -ForegroundColor Green
                        $userResult.Details.Add("Assigned Calling Plan license.")
                    }
                    else {
                        Write-Warning "Could not find the SKU for '$($callingPlanSkuPartNumber)'. Skipping."
                        $userResult.Details.Add("Calling Plan license SKU not found.")
                    }
                }
                catch {
                    $userResult.Status = "Partial Failure"
                    $userResult.Details.Add("FAILED to assign direct license: $($_.Exception.Message)")
                    Write-Warning "-> FAILED to assign direct license for CS department."
                    Write-Warning "   Error: $($_.Exception.Message)"
                }
            }

            # 3. Force License Reprocessing
            Write-Host "`n--- Forcing License Reconciliation ---" -ForegroundColor Cyan
            try {
                Invoke-MgLicenseUser -UserId $targetUser.Id | Out-Null
                Write-Host "Successfully sent license reconciliation request for '$($targetUser.DisplayName)'." -ForegroundColor Green
                $userResult.Details.Add("License reconciliation requested.")
            }
            catch {
                $userResult.Status = "Partial Failure"
                $userResult.Details.Add("FAILED license reconciliation: $($_.Exception.Message)")
                Write-Warning "-> FAILED to send license reconciliation request."
                Write-Warning "   Error: $($_.Exception.Message)"
            }

        }
        catch {
            $userResult.Status = "Failed"
            $userResult.Details.Add("Could not process user: $($_.Exception.Message)")
            Write-Error "An error occurred while processing user '$userUpn': $($_.Exception.Message)"
        }
        finally {
            # Convert details list to a single string for the table
            $userResult.Details = $userResult.Details -join " "
            $results += $userResult
        }
    }
}
catch {
    Write-Error "A critical error occurred: $($_.Exception.Message)"
}
finally {
    # Display Summary Table
    if ($results.Count -gt 0) {
        Write-Host "`n========================= SCRIPT SUMMARY =========================" -ForegroundColor Green
        $results | Format-Table -AutoSize
    }

    Write-Host "`nScript finished. Disconnecting from Microsoft Graph." -ForegroundColor Blue
    #Disconnect-MgGraph
}
