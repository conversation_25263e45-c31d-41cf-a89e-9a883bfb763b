﻿$SQLServer = "qtsql01" #use Server\Instance for named SQL instances! 
$SQLDBName = Read-Host "Enter Database Name (ccare, opt, medeo, iplex, opene, sserv, qhrt)"

$username = Read-Host "Enter GP username to fix"
$pwd = Read-Host -AsSecureString "Enter SA password"

$creds = New-Object System.Management.Automation.PSCredential -ArgumentList $username,$pwd

$pwd = $creds.GetNetworkCredential().Password


$SqlQuery = "select bachnumb from SY00500 where bachnumb = '$username'" 
$DelQuery = "delete from SY00500 where bachnumb = '$username'" 

$SqlConnection = New-Object System.Data.SqlClient.SqlConnection
$SqlConnection.ConnectionString = "Server = $SQLServer; Database = $SQLDBName; User Id = 'sa'; Password = $pwd;"

$SqlCmd = New-Object System.Data.SqlClient.SqlCommand
$SqlCmd.CommandText = $SqlQuery
$SqlCmd.Connection = $SqlConnection

$SqlAdapter = New-Object System.Data.SqlClient.SqlDataAdapter
$SqlAdapter.SelectCommand = $SqlCmd

$DataSet = New-Object System.Data.DataSet

if ($SqlAdapter.Fill($DataSet) -gt 0) {

$lastchance = Read-Host "`n`n$($SqlAdapter.Fill($DataSet)) entries found. DELETE??? (y/n)"
if ($lastchance -eq "y") {

    $SqlCmd.CommandText = $DelQuery 
    $SqlAdapter.SelectCommand = $SqlCmd
    $DataSet = New-Object System.Data.DataSet
    $SqlAdapter.Fill($DataSet) > $null
   
    Write-Host "`nEntries deleted.  Please try to access GP now."
}

}

else {
   Write-Host "`n`nNo entries found!!! Please try to access GP now."
}

$SqlConnection.Close()
pause