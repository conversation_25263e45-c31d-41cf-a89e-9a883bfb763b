<#
.SYNOPSIS
    Retrieves managed devices from Microsoft Intune and Jamf Pro (qhrtech.jamfcloud.com),
    filters them by Jamf 'Last Enrollment' date or Intune enrollment date (2024),
    and exports the list to an Excel (.xlsx) file with a dynamic filename in a predefined path.
    Uses per-device detail calls for Jamf enrollment date.

.DESCRIPTION
    This script connects to Microsoft Graph (v1.0 endpoint) to fetch Intune-managed devices and connects
    to the Jamf Pro API (qhrtech.jamfcloud.com) to fetch Jamf-managed devices.
    It first gets basic Jamf inventory IDs using pagination, then makes a SEPARATE API CALL PER JAMF DEVICE
    to the '/v1/computers-inventory-detail/{id}' endpoint to get the 'general.lastEnrolledDate'.
    THIS WILL BE SIGNIFICANTLY SLOWER FOR JAMF DATA RETRIEVAL.
    It requires the 'Microsoft.Graph' and 'ImportExcel' PowerShell modules.
    It extracts Device Name, Primary User/Assigned User, Enrollment Date (or Last Enrollment for Jamf), and Management Source.
    The script filters the combined list to include only devices where the date falls between Jan 1, 2024, and Dec 31, 2024.
    The user running the script needs appropriate permissions in Microsoft Graph (e.g., DeviceManagementManagedDevices.Read.All)
    and valid Jamf Pro API credentials (Client Secret required at runtime) with 'Read Computers' permission.
    The output is saved to an Excel file in 'C:\Temp\Helpdesk\IntuneDeviceReports\AuditReports\'
    with a filename like 'EnrollmentReport_2024_{timestamp}.xlsx'.

.PARAMETER JamfApiClientSecret
    The Client Secret for your Jamf Pro API credentials (corresponding to the hardcoded Client ID).

.NOTES
    Author: Slader Sheppard
    Version: 6.0 (Using Jamf general.lastEnrolledDate from detail endpoint - SLOWER)
    Date: 2025-04-29
    ScriptName: AuditDeviceReport.ps1
    Requires: PowerShell 5.1 or later, Microsoft.Graph module, ImportExcel module.
    Uses v1.0 Graph endpoint for Intune.
    Jamf URL: Hardcoded to https://qhrtech.jamfcloud.com
    Jamf Client ID: Hardcoded to d1c15c56-cf91-42a9-b18f-d47973fd0e65
    Ensure Jamf API credentials have 'Read Computers' permission.
    Jamf Enrollment Date: Uses 'general.lastEnrolledDate' from '/v1/computers-inventory-detail/{id}'. This reflects the *last* enrollment, not necessarily the initial one.

.EXAMPLE
    .\AuditDeviceReport.ps1 -JamfApiClientSecret "YOUR_CLIENT_SECRET"
    (The script will prompt for the Jamf Client Secret if the parameter is omitted)

#>
param(
    [Parameter(Mandatory=$false)] # Made non-mandatory to allow secure prompt
    [System.Security.SecureString]$JamfApiClientSecret
)

#region Configuration
# Hardcoded Jamf Details
$JamfProUrl = "https://qhrtech.jamfcloud.com"
$JamfApiClientId = "d1c15c56-cf91-42a9-b18f-d47973fd0e65"

# Report Path and Filename Configuration
$ReportBasePath = "C:\Temp\Helpdesk\IntuneDeviceReports\AuditReports"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$ReportFileName = "EnrollmentReport_2024_$($Timestamp).xlsx"
$FilePath = Join-Path -Path $ReportBasePath -ChildPath $ReportFileName

# Date Range Filter Configuration
$StartDate = [datetime]"2024-01-01T00:00:00Z" # Start of 2024 UTC
$EndDate = [datetime]"2025-01-01T00:00:00Z"   # Start of 2025 UTC (exclusive)

# Jamf API Configuration
$jamfPageSize = 200 # Number of devices to retrieve per Jamf API inventory call (for IDs)
#endregion

#region Module Installation Check

Write-Host "Checking for required PowerShell modules..." -ForegroundColor Yellow

# Check for Microsoft.Graph module
if (-not (Get-Module -Name Microsoft.Graph -ListAvailable)) {
    Write-Host "Microsoft.Graph module not found." -ForegroundColor Yellow
    $installGraph = Read-Host "Do you want to install the Microsoft.Graph module now? (y/n)"
    if ($installGraph -eq 'y') {
        Write-Host "Installing Microsoft.Graph module..." -ForegroundColor Green
        try {
            Install-Module Microsoft.Graph -Scope CurrentUser -Repository PSGallery -Force -AllowClobber -ErrorAction Stop
            Write-Host "Microsoft.Graph module installed successfully." -ForegroundColor Green
        } catch { Write-Error "Failed to install Microsoft.Graph module. Error: $($_.Exception.Message)"; exit 1 }
    } else { Write-Error "Microsoft.Graph module is required."; exit 1 }
} else { Write-Host "Microsoft.Graph module found." -ForegroundColor Green }

# Check for ImportExcel module
if (-not (Get-Module -Name ImportExcel -ListAvailable)) {
    Write-Host "ImportExcel module not found." -ForegroundColor Yellow
    $installExcel = Read-Host "Do you want to install the ImportExcel module now? (y/n)"
    if ($installExcel -eq 'y') {
        Write-Host "Installing ImportExcel module..." -ForegroundColor Green
        try {
            Install-Module ImportExcel -Scope CurrentUser -Repository PSGallery -Force -ErrorAction Stop
            Write-Host "ImportExcel module installed successfully." -ForegroundColor Green
        } catch { Write-Error "Failed to install ImportExcel module. Error: $($_.Exception.Message)"; exit 1 }
    } else { Write-Error "ImportExcel module is required."; exit 1 }
} else { Write-Host "ImportExcel module found." -ForegroundColor Green }

# Import the modules
Write-Host "Importing modules..." -ForegroundColor Cyan
try {
    Import-Module Microsoft.Graph -Force -ErrorAction Stop
    Import-Module ImportExcel -Force -ErrorAction Stop
    Write-Host "Modules imported successfully." -ForegroundColor Green
} catch {
    Write-Error "Failed during module import. Error: $($_.Exception.Message)"
    exit 1
}

#endregion

#region Jamf API Credentials Handling
Write-Host "Handling Jamf API Credentials..." -ForegroundColor Cyan
# Prompt for secret if not provided as a parameter
if (-not $PSBoundParameters.ContainsKey('JamfApiClientSecret')) {
    Write-Host "Please enter the Jamf API Client Secret (for Client ID $JamfApiClientId):" -ForegroundColor Yellow
    $JamfApiClientSecret = Read-Host -AsSecureString
}
# Convert SecureString to plain text for API call (handle with care)
$PlainTextJamfSecret = $null
if ($null -ne $JamfApiClientSecret) {
    $PlainTextJamfSecret = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto(
        [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($JamfApiClientSecret)
    )
} else {
     Write-Error "Jamf API Client Secret was not provided."
     exit 1
}
#endregion

#region Get Jamf Data

Write-Host "Attempting to retrieve data from Jamf Pro: $JamfProUrl" -ForegroundColor Yellow
Write-Host "NOTE: This version uses per-device detail calls for 'Last Enrollment' dates and will be slower." -ForegroundColor Yellow
$allJamfDevicesProcessed = @() # Renamed to avoid confusion
$jamfToken = $null
$jamfAuthHeader = $null

try {
    # 1. Get Jamf API Token
    Write-Host "Getting Jamf API token..." -ForegroundColor Cyan
    $tokenUrl = "$JamfProUrl/api/oauth/token"
    $tokenBody = @{
        client_id     = $JamfApiClientId # Using hardcoded variable
        client_secret = $PlainTextJamfSecret
        grant_type    = 'client_credentials'
    }
    $tokenResponse = Invoke-RestMethod -Uri $tokenUrl -Method Post -Body $tokenBody -ContentType 'application/x-www-form-urlencoded' -ErrorAction Stop
    $jamfToken = $tokenResponse.access_token
    $jamfAuthHeader = @{ Authorization = "Bearer $jamfToken" }
    Write-Host "Successfully obtained Jamf API token." -ForegroundColor Green

    # 2. Get Computer IDs using Basic Inventory with Pagination
    Write-Host "Starting initial Jamf inventory retrieval (for IDs only)..." -ForegroundColor Cyan
    $jamfDeviceIDs = [System.Collections.Generic.List[string]]::new() # Store only IDs
    $currentPage = 0
    $totalCount = 0
    do {
        Write-Host "Retrieving Jamf inventory ID page $($currentPage + 1)..." -ForegroundColor Cyan
        # Construct URL for the current page - only need the ID section
        $computerInventoryUrl = "$JamfProUrl/api/v1/computers-inventory?section=GENERAL&page=$currentPage&page-size=$jamfPageSize&sort=id:asc" # Only need ID from general section
        $jamfInventoryResponse = Invoke-RestMethod -Uri $computerInventoryUrl -Method Get -Headers $jamfAuthHeader -ContentType 'application/json' -ErrorAction Stop

        $devicesOnPage = $jamfInventoryResponse.results
        # Get total count only from the first page response
        if($currentPage -eq 0) {
            $totalCount = $jamfInventoryResponse.totalCount
            if($totalCount -eq 0) {
                Write-Host "No devices found in Jamf Pro inventory." -ForegroundColor Yellow
                break # Exit loop if no devices exist
            }
             Write-Host "Total Jamf devices reported: $totalCount" -ForegroundColor Green
        }

        if ($devicesOnPage) {
            Write-Host "Storing IDs for $($devicesOnPage.Count) devices from page $($currentPage + 1)." -ForegroundColor Green
            $devicesOnPage | ForEach-Object { $jamfDeviceIDs.Add($_.id) } # Add only the ID
            $currentPage++
        } else {
             if($currentPage -gt 0) { break } # No more devices on subsequent pages
        }
    } while (($currentPage * $jamfPageSize) -lt $totalCount)

    Write-Host "Finished initial inventory retrieval. Found $($jamfDeviceIDs.Count) device IDs." -ForegroundColor Green

    # 3. Loop through each device ID to get details and accurate enrollment date
    Write-Host "Retrieving details and 'Last Enrollment' date per device (this will be slow)..." -ForegroundColor Yellow
    $deviceCounter = 0
    foreach ($deviceId in $jamfDeviceIDs) {
        $deviceCounter++
        Write-Progress -Activity "Fetching Jamf Device Details" -Status "Processing device $deviceCounter of $($jamfDeviceIDs.Count) (ID: $deviceId)" -PercentComplete (($deviceCounter / $jamfDeviceIDs.Count) * 100)

        $enrollmentDate = $null # Use this variable name for consistency, but it holds 'Last Enrollment' date
        $deviceName = "N/A" # Default values
        $assignedUser = "N/A"

        try {
            # Make the second call to get inventory details for this specific device ID
            $detailUrl = "$JamfProUrl/api/v1/computers-inventory-detail/$deviceId"
            $detailResponse = Invoke-RestMethod -Uri $detailUrl -Method Get -Headers $jamfAuthHeader -ContentType 'application/json' -ErrorAction Stop

            # Extract needed info from the detail response
            $deviceName = $detailResponse.general.name
            $assignedUser = if ($detailResponse.userAndLocation) { $detailResponse.userAndLocation.username } else { "N/A" }

            # --- MODIFIED: Extract the 'Last Enrollment Date' ---
            # Check if the 'lastEnrolledDate' field exists and is not empty
            if ($detailResponse.general.lastEnrolledDate -and -not([string]::IsNullOrWhiteSpace($detailResponse.general.lastEnrolledDate))) {
                 $lastEnrolledDateString = $detailResponse.general.lastEnrolledDate
                 try {
                    # Attempt to parse the date string. Adjust format/culture if needed.
                    # Example format: "02/28/2025 12:02 AM" -> "MM/dd/yyyy hh:mm tt"
                    # Using InvariantCulture is generally safer for APIs, but might need 'en-US' if format is specific.
                    $enrollmentDate = [datetimeoffset]::Parse($lastEnrolledDateString, [System.Globalization.CultureInfo]::InvariantCulture).UtcDateTime
                    # If Parse fails, try ParseExact with expected format
                    # $enrollmentDate = [datetimeoffset]::ParseExact($lastEnrolledDateString, "MM/dd/yyyy hh:mm tt", [System.Globalization.CultureInfo]::InvariantCulture).UtcDateTime
                 } catch {
                     Write-Warning "Jamf device '$deviceName' (ID: $deviceId): Could not parse Last Enrollment Date string '$lastEnrolledDateString'. Error: $($_.Exception.Message)"
                 }
            } else {
                Write-Warning "Jamf device '$deviceName' (ID: $deviceId): 'general.lastEnrolledDate' not found or empty via /v1/computers-inventory-detail endpoint."
            }
            # --- END MODIFICATION ---

        } catch {
             Write-Warning "Jamf device ID '$deviceId': Failed to retrieve details via /v1/computers-inventory-detail. Error: $($_.Exception.Message)"
             # Cannot get name or user if detail call fails
        }

        # Add the processed device to the final list
        $allJamfDevicesProcessed += [PSCustomObject]@{
            DeviceName       = $deviceName
            PrimaryUserUPN   = if ([string]::IsNullOrWhiteSpace($assignedUser)) { "N/A" } else { $assignedUser }
            EnrollmentDate   = $enrollmentDate # Storing 'Last Enrollment Date' here
            ManagementSource = "Jamf"
        }
    }
     Write-Progress -Activity "Fetching Jamf Device Details" -Completed

    Write-Host "Successfully processed $($allJamfDevicesProcessed.Count) total devices from Jamf using 'Last Enrollment Date'." -ForegroundColor Green

} catch {
    Write-Error "An error occurred while retrieving Jamf data: $($_.Exception.Message)"
    # Optionally, continue without Jamf data or exit
    # exit 1
} finally {
    # Clear plain text secret variable
    if ($PlainTextJamfSecret) {
        Write-Host "Clearing plain text Jamf secret from memory..." -ForegroundColor Cyan
        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR(
            [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($JamfApiClientSecret)
        )
        Clear-Variable PlainTextJamfSecret -ErrorAction SilentlyContinue
        Write-Host "Plain text Jamf secret cleared." -ForegroundColor Cyan
    }
    # Invalidate Jamf token (optional, good practice)
    if ($jamfToken) {
        try {
            Write-Host "Invalidating Jamf API token..." -ForegroundColor Cyan
            $invalidateUrl = "$JamfProUrl/api/v1/auth/invalidate-token"
            Invoke-RestMethod -Uri $invalidateUrl -Method Post -Headers $jamfAuthHeader -ErrorAction SilentlyContinue | Out-Null
            Write-Host "Jamf API token invalidated." -ForegroundColor Cyan
        } catch {
            Write-Warning "Could not invalidate Jamf API token: $($_.Exception.Message)"
        }
    }
}

#endregion

#region Get Intune Data

Write-Host "Connecting to Microsoft Graph for Intune data..." -ForegroundColor Yellow
$allIntuneDevices = @()

try {
    # Connect without the welcome message (check connection first)
    Write-Host "Checking Graph connection..." -ForegroundColor Cyan
    if (-not (Get-MgContext -ErrorAction SilentlyContinue)) {
         Write-Host "Not connected to Graph, connecting now..." -ForegroundColor Cyan
         Connect-MgGraph -Scopes @("DeviceManagementManagedDevices.Read.All", "User.Read.All") -NoWelcome -ErrorAction Stop
         Write-Host "Successfully connected to Microsoft Graph." -ForegroundColor Green
    } else {
        Write-Host "Already connected to Microsoft Graph." -ForegroundColor Green
    }

    Write-Host "Retrieving Intune managed devices..." -ForegroundColor Yellow
    # Get devices with necessary properties including enrollment date
    $intuneDevicesRaw = Get-MgDeviceManagementManagedDevice -All -ErrorAction Stop `
        -Property "Id, DeviceName, UserPrincipalName, EnrolledDateTime"

    Write-Host "Successfully retrieved $($intuneDevicesRaw.Count) devices from Intune." -ForegroundColor Green

    # Process Intune Devices
    Write-Host "Processing Intune devices..." -ForegroundColor Cyan
    foreach ($device in $intuneDevicesRaw) {
        $upn = if ([string]::IsNullOrWhiteSpace($device.UserPrincipalName)) { "N/A" } else { $device.UserPrincipalName }
        # Ensure enrollment date is treated as DateTime object in UTC
        $enrollmentDate = $null
        if ($device.EnrolledDateTime) {
            try {
                # EnrolledDateTime from Graph is already a DateTime object, ensure it's UTC
                 if ($device.EnrolledDateTime.Kind -eq [System.DateTimeKind]::Unspecified) {
                    # Assume UTC if unspecified, Graph usually returns UTC
                    $enrollmentDate = [System.DateTime]::SpecifyKind($device.EnrolledDateTime, [System.DateTimeKind]::Utc)
                 } elseif ($device.EnrolledDateTime.Kind -eq [System.DateTimeKind]::Local) {
                    $enrollmentDate = $device.EnrolledDateTime.ToUniversalTime()
                 }
                 else { # Already UTC
                    $enrollmentDate = $device.EnrolledDateTime
                 }
            } catch {
                 Write-Warning "Could not parse enrollment date '$($device.EnrolledDateTime)' for Intune device $($device.DeviceName)."
            }
        }

        $allIntuneDevices += [PSCustomObject]@{
            DeviceName       = $device.DeviceName
            PrimaryUserUPN   = $upn
            EnrollmentDate   = $enrollmentDate # Intune's actual enrollment date
            ManagementSource = "Intune"
        }
    }
     Write-Host "Finished processing Intune devices." -ForegroundColor Green

} catch {
    Write-Error "An error occurred while retrieving Intune data: $($_.Exception.Message)"
    # Optionally, continue without Intune data or exit
    # exit 1
}

Write-Host "Processed $($allIntuneDevices.Count) Intune devices." -ForegroundColor Green

#endregion

#region Combine and Filter Data

Write-Host "Combining and filtering device data for enrollment year 2024..." -ForegroundColor Yellow

$combinedDevices = $allIntuneDevices + $allJamfDevicesProcessed # Use the accurately processed Jamf list
Write-Host "Total devices combined: $($combinedDevices.Count)"

# Filter by enrollment date (ensure EnrollmentDate is not null)
$filteredDevices = $combinedDevices | Where-Object {
    $_.EnrollmentDate -is [datetime] -and # Check it's a valid date after parsing
    $_.EnrollmentDate -ge $StartDate -and
    $_.EnrollmentDate -lt $EndDate
}

Write-Host "Devices enrolled in 2024 (using Intune Enrollment/Jamf Last Enrollment): $($filteredDevices.Count)" -ForegroundColor Green

#endregion

#region Export to Excel

Write-Host "Entering Export to Excel section..." -ForegroundColor Cyan
if ($filteredDevices.Count -gt 0) {
    Write-Host "Exporting filtered data to Excel file: $FilePath" -ForegroundColor Yellow
    try {
        # Create directory if it doesn't exist
        if (-not (Test-Path -Path $ReportBasePath)) {
             Write-Host "Creating report directory: $ReportBasePath" -ForegroundColor Cyan
            New-Item -ItemType Directory -Path $ReportBasePath -Force | Out-Null
        }

        # Select properties for export and format date nicely
        $exportData = $filteredDevices | Select-Object DeviceName, PrimaryUserUPN, @{Name='EnrollmentDate';Expression={if($_.EnrollmentDate){$_.EnrollmentDate.ToString('yyyy-MM-dd HH:mm:ss')} else {'N/A'}}}, ManagementSource

        # Export using ImportExcel module
        Write-Host "Calling Export-Excel..." -ForegroundColor Cyan
        $exportData | Export-Excel -Path $FilePath -AutoSize -WorksheetName "2024 Enrollments" -FreezeTopRow -ErrorAction Stop
        Write-Host "Export-Excel finished." -ForegroundColor Green

        Write-Host "Export completed successfully." -ForegroundColor Green
    } catch {
        Write-Error "Failed to export data to Excel. Error: $($_.Exception.Message)"
        Write-Error "Ensure the path '$FilePath' is valid and you have write permissions."
    }
} else {
    Write-Warning "No devices found enrolled in 2024 to export based on available dates."
}

#endregion

#region Disconnect Graph
Write-Host "Entering Disconnect Graph section..." -ForegroundColor Cyan
Write-Host "Disconnecting from Microsoft Graph." -ForegroundColor Yellow
if (Get-MgContext -ErrorAction SilentlyContinue) {
    Disconnect-MgGraph | Out-Null
}
#endregion

Write-Host "Script finished." -ForegroundColor Cyan
