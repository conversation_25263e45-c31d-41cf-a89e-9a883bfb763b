#!/bin/zsh

#================================================================================================================
#
#   DESCRIPTION
#
#       This script will remove administrator rights from the currently logged-in user.
#
#   USAGE
#
#       Deploy via a Jamf Pro policy. It is recommended to run this at login or recurring check-in
#       to ensure user privileges remain as intended.
#
#   AUTHOR
#
#       Slader Sheppard
#
#================================E================================================================================

# --- Script Variables ---

# Get the currently logged in user
loggedInUser=$(/usr/bin/stat -f%Su /dev/console)

# Define a list of users to exclude from demotion (e.g., your management account or other critical admins)
# Add any usernames to this array, separated by a space.
excludeUsers=("root" "jamfadmin" "localadmin") # Customize this list as needed

# Log file for auditing
logFile="/var/log/user_demotion.log"

# --- Functions ---

# Function to write log messages
log_message() {
    local message="$1"
    echo "$(date +"%Y-%m-%d %H:%M:%S") - ${message}" >> "${logFile}"
}

# --- Main Script ---

log_message "--- Starting User Demotion Script ---"
log_message "Current logged-in user: ${loggedInUser}"

# Check if the loggedInUser variable is empty or 'root'
if [[ -z "${loggedInUser}" || "${loggedInUser}" == "root" || "${loggedInUser}" == "loginwindow" ]]; then
    log_message "No user is currently logged in, or the user is 'root' or 'loginwindow'. Exiting."
    exit 0
fi

# Check if the logged-in user is in the exclusion list
for excluded in "${excludeUsers[@]}"; do
    if [[ "${loggedInUser}" == "${excluded}" ]]; then
        log_message "User '${loggedInUser}' is in the exclusion list. No action will be taken."
        exit 0
    fi
done

# Check if the user is currently an administrator
isAdmin=$(/usr/bin/dscl . -read /Groups/admin GroupMembership | /usr/bin/grep -o "${loggedInUser}")

if [[ -n "${isAdmin}" ]]; then
    log_message "User '${loggedInUser}' is an administrator. Proceeding with demotion."

    # Attempt to remove the user from the admin group
    /usr/sbin/dseditgroup -o edit -d "${loggedInUser}" -t user admin
    
    # Verify the demotion
    if [[ $? -eq 0 ]]; then
        log_message "Successfully demoted '${loggedInUser}' to a standard user."
        # Optional: Display a notification to the user
        /usr/bin/osascript -e 'display dialog "Your administrator permissions have been updated to standard user privileges for security compliance. Please reboot your device for these changes to take effect." with title "Permissions Update" buttons {"OK"} default button "OK" with icon note'
    else
        log_message "ERROR: Failed to demote '${loggedInUser}'. dseditgroup command failed."
        exit 1
    fi
else
    log_message "User '${loggedInUser}' is already a standard user. No action needed."
fi

log_message "--- User Demotion Script Finished ---"
echo "" >> "${logFile}" # Add a blank line for readability

exit 0