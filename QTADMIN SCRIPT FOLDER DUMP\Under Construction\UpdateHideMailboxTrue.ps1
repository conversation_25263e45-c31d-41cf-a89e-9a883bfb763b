﻿# Prompt for the User Logon Name
$userLogonName = Read-Host "Enter the user logon name (e.g. first.last)"

# Import the Active Directory module
Import-Module ActiveDirectory

# Get the user object from Active Directory
$user = Get-ADUser -Identity $userLogonName -Properties msExchHideFromAddressLists

# Check if the attribute is present and display its current value
if ($user -ne $null) {
    $hideFromAddressLists = $user.msExchHideFromAddressLists
    if ($hideFromAddressLists -eq $null) {
        Write-Host "The msExchHideFromAddressLists attribute is not set."
    } else {
        Write-Host "Current value of msExchHideFromAddressLists: $hideFromAddressLists"
    }
} else {
    Write-Host "User not found in Active Directory."
}

# Set the msExchHideFromAddressLists attribute to True
Set-ADUser -Identity $userLogonName -Replace @{msExchHideFromAddressLists=$true}
Write-Host "The msExchHideFromAddressLists attribute has been set to True."