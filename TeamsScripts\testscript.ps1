# Connect to Microsoft Graph
Connect-MgGraph -Scopes "DeviceManagementManagedDevices.Read.All"

# Retrieve all managed devices
$devices = Get-MgDeviceManagementManagedDevice -Top 999

# Initialize an array to store device information
$deviceInfo = @()

# Retrieve installed applications for each device
foreach ($device in $devices) {
    $deviceId = "{" + $device.id + "}"  # Place curly braces around device ID

    # Construct the endpoint URL
    $endpoint = "/deviceManagement/managedDevices/$($deviceId)"

    # Query installed applications for the device
    $installedApps = Invoke-MgGraphRequest -Uri $endpoint -Method GET
    if ($installedApps) {
        foreach ($app in $installedApps.value) {
            $deviceInfo += [PSCustomObject]@{
                DeviceName = $device.deviceName
                DeviceId = $device.id
                ApplicationName = $app.displayName
                ApplicationVersion = $app.appVersion
            }
        }
    }
}

# Output the report
$deviceInfo | Export-Csv -Path "Installed_Applications_Report.csv" -NoTypeInformation
Write-Host "Installed applications report has been saved to Installed_Applications_Report.csv"
