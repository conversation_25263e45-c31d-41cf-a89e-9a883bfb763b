﻿#Reference File
#$reffile = Get-Content -Path C:\Scripts\Chris\ISO-data-man\CreatedBy.txt

#Load necessary module to connect to SPOService
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client") | Out-Null
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client.Runtime") | Out-Null

$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)

Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred

$WebUrl = "https://qhrtech.sharepoint.com/ca/ISO/"

#Connect to SharePoint Online service
Write-Host "Logging into SharePoint online service." -ForegroundColor Green

$Context = New-Object Microsoft.SharePoint.Client.ClientContext($WebUrl)
$Context.Credentials = New-Object Microsoft.SharePoint.Client.SharePointOnlineCredentials($creduser, $encrypted)


#Get the Necessary List
Write-Host "Getting the required list." -ForegroundColor Green
$List = $Context.Web.Lists.GetByTitle("Documents")

$Query = [Microsoft.SharePoint.Client.CamlQuery]::CreateAllItemsQuery(1500); 
$Items = $List.GetItems($Query);

$Context.Load($Items);
$Context.ExecuteQuery();

#Edit existing list items
foreach($item in $Items)
{
    #if($item["Author"].LookupValue -eq "Admin @ QHR Tech")
    #if ($reffile[$item.id] -ne "###")
    #{
    #    $newauthuser = $reffile[$item.id].ToString()
        Write-Host "Changing Author for Record #$($item.Id) to $newauthuser" -ForegroundColor Green
   #     $newauthor = $Context.Web.EnsureUser($newauthuser)
    #    $item["Author"] = $newauthor
    #    $item.Update()
    #    $Context.ExecuteQuery();
        sleep 1
   # }
}

#Write-Host "Your changes have now been made." -ForegroundColor Green