﻿"userPrincipal<PERSON>ame","Logon Name","displayName","Title","id","Account Type","Department","accountEnabled","Description","Created","Last Log-on Date"
"<EMAIL>","$DUPLICATE-1f4","Administrator","Service Account","49734d69-5caa-4c2f-a9d3-9d0698324dde","*********",,"True","PLEASE INFORM HELPDESK IF YOU ACTIVATE THIS ACCOUNTService Account: Corp IT - Built-in account for administering the computer/domain/enterprise","9/10/2001 3:38:27 PM","10/31/2019 4:30:14 PM"
"<EMAIL>","da-amcfadden","Domain Admin - Andrew <PERSON>",,"e5bdf13e-e581-4ebf-93ce-8a01f9501a59","*********",,"True",,"3/8/2017 11:41:26 AM","12/30/2022 1:21:19 PM"
"<EMAIL>","da-kkendall","Domain Admin - <PERSON>",,"f60bb455-bb6b-48fe-990c-3390b23f83fd","*********",,"True",,"5/30/2018 2:23:28 PM","1/9/2023 2:54:54 PM"
"<EMAIL>","da-smay","Domain Admin - Scott May",,"911b675b-1ad1-48dd-a8e3-b09b21302bf8","*********",,"True",,"2/11/2019 2:40:19 PM","1/13/2023 2:48:12 PM"
"<EMAIL>","da-croseberry","Domain Admin - Chris Roseberry",,"bbc0565e-31aa-42a5-a488-d98afd8238fb","*********",,"True",,"4/26/2019 11:20:55 AM","1/10/2023 2:10:39 PM"
"<EMAIL>","da-nated","Domain Admin - Devin Nate",,"2a74206f-2228-4022-b780-c830fc5ae509","*********",,"True",,"6/27/2013 3:07:02 PM","1/21/2020 3:05:40 PM"
"<EMAIL>","da-mmclean","Domain Admin - Mark McLean","Sr. Manager Cloud Technology","d25d079c-ce65-4f45-ac55-51a811dfca65","*********",,"True",,"6/27/2013 3:08:33 PM","4/16/2021 8:46:41 AM"
"<EMAIL>","Da-Plaudenklos","Domain Admin - Peter Laudenklos",,"6ff505cf-5380-4d35-b915-09062228ddca","*********",,"True",,"5/16/2019 2:51:57 PM","1/11/2023 9:22:46 AM"
"<EMAIL>","da-mhernandez","Domain Admin - Miguel Hernandez",,"8cd8ded2-0c2b-461c-80ae-98126d2e2b27","*********",,"True",,"12/19/2018 2:57:51 PM","11/25/2022 11:35:51 AM"
"<EMAIL>","da-rarmstrong","Domain Admin - Robert Armstrong",,"9065e0d4-843d-4f95-8669-146e82bbd7b7","*********",,"True",,"6/27/2012 1:46:05 PM","1/16/2023 7:53:50 AM"
"<EMAIL>","da-sbradford","Domain Admin - Sam Bradford",,"66a3e0db-d3ea-4c96-ab2b-9630858f2f83","*********",,"True",,"8/2/2016 10:33:47 AM","1/11/2023 11:32:56 AM"
"<EMAIL>","da-mhoppe","Domain Admin - Michal Hoppe",,"f1ea0dfa-8c0b-4209-8c18-38f7272d62ff","*********",,"True",,"8/25/2016 2:13:41 PM","12/21/2021 1:48:57 PM"
"<EMAIL>","da-tdrescher","Domain Admin - Taylor Drescher",,"7e162a5d-af03-4dca-ae54-30d1e47dec64","*********",,"True",,"11/13/2019 10:52:32 AM","8/23/2022 11:35:14 AM"
"<EMAIL>","da-chounsham","Domain Admin - Craig Hounsham",,"931c0d3e-86eb-4613-9831-62e57d57899e","*********",,"True",,"12/6/2019 8:22:00 AM","3/22/2022 12:18:18 PM"
"<EMAIL>","da-mkandy","Domain Admin - Mohammad Kandy",,"2a525081-20d9-4e82-befa-d009253f0e9b","*********",,"True",,"3/11/2020 5:40:42 AM","9/13/2021 12:09:50 PM"
"<EMAIL>","da-balbrecht","Domain Admin - Butch Albrecht",,"88dbc942-a31b-427a-ba70-c9dbda3989ba","*********",,"True",,"6/2/2020 8:08:38 AM","12/16/2020 11:48:48 AM"
"<EMAIL>","da-pfarry","Domain Admin - Paul Farry",,"58304693-a90b-4275-bba6-c4419250d47f","*********",,"True",,"12/10/2021 8:07:13 AM","1/16/2023 9:37:05 AM"
"<EMAIL>","da-smool","Domain Admin - Sudeep Mool",,"6462d106-3674-4800-b153-36fbec1ca175","*********",,"True",,"4/13/2022 3:30:37 PM","11/17/2022 2:51:20 PM"
"<EMAIL>","da-jgermain","Domain Admin - Justin Germain",,"33f9e65e-2136-4c95-bc77-ee710e4e0cbb","*********",,"True",,"5/6/2022 11:13:13 AM","12/31/1600 4:00:00 PM"
"<EMAIL>","da-gharshenin","Domain Admin - Greg Harshenin",,"a15c120b-747f-400c-b51f-7dd2fe4cc2bb","*********",,"True",,"2/19/2007 10:56:21 AM","1/13/2023 3:38:54 PM"
"<EMAIL>","operator","O Perator","Service Account","99d6f5a7-97bf-4d1c-9b17-ba711475ceab","*********",,"False","Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","9/18/2001 8:56:28 AM","1/18/2022 8:00:00 AM"
"<EMAIL>","da-mkennedy","Domain Admin- Malcolm Kennedy",,"ceb5de5c-8985-423a-a679-31ee2dd9d09e","*********",,"True",,"7/18/2019 3:33:50 PM","9/28/2020 11:52:47 AM"
"<EMAIL>","da-nenglish","Domain Admin - Nyel English",,"67ec3daa-9872-4b45-a885-190af5a8cafc","*********",,"True",,"12/10/2018 7:50:15 AM","1/16/2023 10:37:42 AM"
"<EMAIL>","da-pkainth","Domain Admin - Preet Kainth",,"a0395fac-fa35-4dee-9168-eab29f4ffb1b","*********",,"True",,"1/14/2019 11:33:33 AM","1/16/2023 10:03:18 AM"
"<EMAIL>","da-njanzen","Domain Admin Nick Janzen",,"13116fcf-b56b-4124-81c6-f0ffb65ef65d","*********",,"True",,"12/11/2017 10:37:39 AM","12/1/2022 1:01:35 PM"
"<EMAIL>","da-krosal","Domain Admin - Kevin Rosal",,"2df6fbd3-0ce5-4e39-9ece-24fa26ff6c6b","*********",,"True",,"2/27/2017 8:37:17 AM","9/19/2022 8:52:13 AM"
"<EMAIL>","da-amehl","Domain Admin - Alex Mehl",,"b1c7a9fc-461e-4f91-b74f-68350e774c5f","*********",,"True",,"2/9/2010 6:16:34 PM","3/2/2021 2:53:17 PM"
"<EMAIL>","da-jbell","Domain Admin - Jeffrey Bell",,"7de81fa2-404a-4d24-8615-7906e255e410","*********",,"True",,"12/18/2019 11:13:45 AM","1/12/2023 3:17:53 AM"
"<EMAIL>","da-jpond","Domain Admin - Jarrid Pond",,"5971c21a-e476-4949-b2a8-99d4be4bb24f","*********",,"True",,"4/19/2021 10:46:03 AM","1/16/2023 10:31:59 AM"
"<EMAIL>","da-dtorres","Domain Admin - Dilcia Torres",,"06aebc94-6341-4d33-b81b-7735352e43e8","*********",,"True",,"6/14/2021 12:38:47 PM","1/4/2023 2:40:55 PM"
"<EMAIL>","da-bkeefe","Domain Admin - Brad Keefe",,"6fa9cb55-1753-45bc-9a18-faf0edec7035","*********",,"True",,"1/4/2023 2:54:24 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","cpotharaju","Domain Admin - Chandra Potharaju",,"55628da9-4a5a-4d05-8ffe-ddb38af35bb8","*********",,"True",,"1/4/2023 3:16:28 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","$DUPLICATE-1f4","Administrator","Service Account","49734d69-5caa-4c2f-a9d3-9d0698324dde","*********",,"True","PLEASE INFORM HELPDESK IF YOU ACTIVATE THIS ACCOUNTService Account: Corp IT - Built-in account for administering the computer/domain/enterprise","9/10/2001 3:38:27 PM","10/31/2019 4:30:14 PM"
"<EMAIL>","$DUPLICATE-1f4","Administrator","Service Account","49734d69-5caa-4c2f-a9d3-9d0698324dde","*********",,"True","PLEASE INFORM HELPDESK IF YOU ACTIVATE THIS ACCOUNTService Account: Corp IT - Built-in account for administering the computer/domain/enterprise","9/10/2001 3:38:27 PM","10/31/2019 4:30:14 PM"
"<EMAIL>","MDT","MDT","Service Account","88f612da-eaa6-40bf-a2f5-bed41aeda4d4","*********",,"True","Service Account: Windows deployment account","1/28/2010 1:57:19 PM","1/13/2023 12:14:16 PM"
"<EMAIL>","jirasync","JIRA LDAP SYNC","Service Account","b6b4da2e-cb80-453d-8fa6-f07a3bf552de","*********",,"True","Service Account: Corp IT - Jira and Confluence Service Account","2/8/2017 9:09:06 AM","11/8/2021 12:29:09 PM"
"<EMAIL>","qtadmin1service","QTADMIN1 service","Service Account","53b1cae9-a030-411e-aae5-a081a0e8d2d6","*********",,"True","Service Account: Corp IT - Account used to run scheduled scripts on qtadmin1; Admin monitoring scripts","2/22/2018 3:25:46 PM","1/16/2023 8:00:01 AM"
"<EMAIL>","OPT_Sales","EMR Sales (fwd)","Service Account","a7fe9a24-d805-454a-bd36-b0de9733789f","*********","Sales","True","Service Account:Email Account for Optimed Software Sales","6/28/2004 7:59:31 AM",
"<EMAIL>","opt_development","Opt_Development","Service Account","1ceec199-b99f-4b49-b10c-92f9fdc0a1d6","*********",,"True","Service Account: Development - SQL Server Account","6/3/2004 3:29:25 PM","9/21/2021 9:10:49 AM"
"<EMAIL>","OPT_Support","EMR Support","Service Account","c179cc77-f880-4cd3-86c7-ddb1f10878d2","*********","Client Services","True","Service Account: Corp IT - Email / GTA no screen saver / After hours BB Support","6/21/2004 4:05:50 PM","1/8/2023 3:51:01 AM"
"<EMAIL>","elearning","e learning","Service Account","016caf68-e5ac-42d3-9a3b-1dc4fa8e3c99","*********",,"True","Service Account:For learning and Development","7/23/2002 8:50:39 AM","12/31/1600 4:00:00 PM"
"<EMAIL>","itadmin","IT Admin","Service Account","21f7b76e-3413-459b-a759-d8dacf4ffadd","*********",,"True","Service Account:  Corp IT - Shared email inbox","2/21/2011 2:42:25 PM",
"<EMAIL>","Products","EMR Products","Service Account","7a8e7c0f-5402-48b0-b372-365a45aa0d54","*********",,"True","shared email address monitored by the Product Operations Support Team (POST), for notifications from government bodies regarding lists that need to be regularly updated in Accuro.","6/13/2012 10:12:00 AM",
"<EMAIL>","Optcslt","EMR - CS Leadership Team","Service Account","6370dc1d-4acd-4f09-baae-7bba333a4d8e","*********","Client Services","True","Client Services Leadership Team email box, for communication between CSLT with CS staff.","4/11/2012 9:18:32 AM",
"<EMAIL>","OptimedImpW","EMR Implementations Calendar West","Service Account","1f6fa67e-cb52-48dd-8ef1-d92e82a123aa","*********","Implementations","True","Linked to an outlook calendar for implementer booking","6/6/2012 2:41:08 PM",
"<EMAIL>","osdevah","EMR Development After Hours","Service Account","2af78e8b-ac73-4b2e-b9e1-3bc01e17569b","*********","Development","True","Service Account: This Account is for after hours support so if CS needs to get a hold of DEV that's what they use it for.","1/27/2012 9:52:00 AM",
"<EMAIL>","accounting","QHR Accounting","Service Account","a09da89d-8202-4628-b80c-11d991ae64d5","*********",,"True","Service Account: Finance - 	User account with an active email address that was used on qtfin2 to send invoices to clients","1/25/2012 3:20:40 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","OSAdmin","EMR Admin","Service Account","8a8098a9-3566-4116-aa60-55fea2e3ac27","*********",,"True","Master inbox that is used for receiving sales e-fax and general sales related emails.","12/21/2010 9:38:43 PM",
"<EMAIL>","Implementations","EMR Implementations","Service Account","d41b52da-ab4b-4db9-8316-721fddd88b99","*********","Implementations","True","Service Account: PM's check that inbox routinely for faxes and or emails from client","6/28/2004 1:57:32 PM",
"<EMAIL>","optdev","Development Github","Service Account","8343256c-a6fe-4211-9c3a-77b499f7c54e","*********","Development","True","Service Account: central developer account for app stores and alerts","3/10/2005 1:48:40 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","qtmitelccm","qtmitelccm","Service Account","320bd22c-75cb-4a3e-8a94-e95302976fd0","*********",,"True","Service Account: Corp IT - Service account on qtmitelcsm, also has scripts running with those credentials; Mitel Services","6/18/2012 11:06:32 AM","12/31/1600 4:00:00 PM"
,"MSOL_0e6855de790d",,"Service Account","610e3153-645d-4e2d-a39a-f0e5342bd378","*********",,"True","Service Account: Corp IT - Account created by the Windows Azure Active Directory Sync tool with installation identifier '0e6855de790d47ebaefa653faf59f481' running on computer 'QTADFS2' configured to synchronize to tenant 'qhrtech.onmicrosoft.com'. This account must have directory replication permissions in the local Active Directory and write permission on certain attributes to enable Hybrid Deployment.","1/22/2015 10:23:05 AM","1/16/2023 2:46:02 AM"
"<EMAIL>","bomgarIC","bomgarIC","Service Account","8dfaff50-90ff-44d2-83ee-d6cfc8d904f4","*********",,"True","Service Account: Corp IT - User for Bomgar Integration Client Scheduler service running on QTAdmin1","1/27/2016 1:55:58 PM","9/30/2022 2:51:52 AM"
"<EMAIL>","qhrbackupsmtp","QHR backup smtp","Service Account","e7ce8616-3d17-4309-86f1-c80a5fe0fda8","*********",,"True","Service Account: Tech Infrastructure - Backup notifications to C. Hounsham (SF Case ********)","11/8/2016 1:29:36 PM",
"<EMAIL>","IT","IT & DC Helpdesk","Service Account","f6127037-0e24-4f02-8e9c-6a6b4d42bcf9","*********",,"True","Service Account: Corp IT - Contact for IT assistance","5/30/2008 11:04:24 AM",
"<EMAIL>","OXIDIZEDSA","OXIDIZEDSA","Service Account","df1c9b54-40d5-4032-b4fe-9d9ffb35e305","*********",,"True","Service Account: Networking backups and scripts","2/5/2019 4:22:57 PM","1/14/2022 3:10:15 PM"
"<EMAIL>","trevor.trainee","Trevor Trainee","Service Account","10ab30f0-48a7-430e-82fb-593dca6db96b","*********",,"True","Service Account:  HR - Used for Training Video/Document","9/18/2019 8:37:29 AM",
"<EMAIL>","sql-osqasql1","SQL-osqasql1","Service Account","43e96c3d-5e46-40bb-80ea-3763c119f2be","*********",,"True","Service Account: QA - SQL Service Account","7/12/2011 2:42:24 PM","3/23/2021 5:01:16 AM"
"<EMAIL>","vmwarereport","VMware Reporting Service Account","Service Account","ea1392e4-f2a7-4637-a316-403d5675b02a","*********",,"True","Service Account: Tech Infrastructure - Read only Service account for VMWare","6/21/2017 9:18:21 AM","1/16/2023 10:44:13 AM"
"<EMAIL>","econnect","econnect","Service Account","8186a307-de82-4ce6-b6e4-c28784391889","*********",,"True","Service Account: Finance - for Dynamics running on qtfin3","12/8/2015 2:53:09 PM","1/15/2023 2:38:03 AM"
"<EMAIL>","actiontec","ActionTec","Service Account","9ce49c34-65c0-4c06-ae7f-75f18888aef5","*********",,"True","Service Account: User required for wireless connection of ActionTec devices to ""Quadrant Network""","11/15/2018 1:09:06 PM",
"<EMAIL>","jenkins","jenkins","Service Account","2ed7d2c0-5c9f-4399-bfeb-e2296c5f68d3","*********",,"True","Service Account: Development/QA - QA Test Account","7/25/2014 2:46:48 PM","3/21/2021 4:59:57 PM"
"<EMAIL>","SQL-OSDEVSQL2012","SQL-OSDEVSQL2012","Service Account","45984b1b-c506-47a3-aab3-d1ab6b7ac58a","*********",,"True","Service Account: Development - SQL Service Account; used to run OSDEVSQL2012\OSDEVSQL2012
OSDEVSQL2012\MSSQL2014
and OSDEVSQL2012\MSSQL2016
MSSQL instances.","2/22/2017 9:11:02 AM","8/10/2022 8:26:44 AM"
"<EMAIL>","mimecastsync","Mimecast LDAPS Sync","Service Account","2e05d2aa-acce-41b3-8cf8-82fca9291a10","*********",,"True","Service Account: Security - Secure LDAP account for Mimecast","3/27/2014 2:09:06 PM",
"<EMAIL>","splunk.alerts","Splunk Alerts","Service Account","bcf76b34-59bf-46b4-8b58-78055621b729","*********",,"True","Service Account: Pprovide Alerts generated from Splunk service.","3/20/2019 4:16:50 PM",
"<EMAIL>","GPeconnect","GPeconnect","Service Account","c42b7d5d-29d0-4633-93b8-456bb3134fc3","*********",,"True","Service Account: Finance - Dynamics service account used on qtfin3","7/11/2017 11:21:49 AM",
"<EMAIL>","sql-qtversionone","SQL-QTVersionOne","Service Account","b602eefc-b704-4f05-8771-bf311b81a2cc","*********",,"True","Service Account: Development - SQL Server account for VersionOne. Legacy system - replaced by JIRA - still referred to.","5/14/2013 5:41:45 PM","3/4/2021 3:03:42 AM"
"<EMAIL>","sftp","sftp service","Service Account","a9effbec-977b-4b13-9409-c3c3398b3a04","*********",,"True","Service Account: Corp IT  - Used to mount shares on qtsftp1. Also used by newer Cerberus SFTP service (on QTSFTP1982 that never made it to production) ","6/13/2014 12:15:40 PM","4/1/2021 3:01:41 AM"
"<EMAIL>","konicaminolta","KonicaMinolta","Service Account","da3af169-2c70-4768-9839-8756e3fd46fd","*********",,"True","Service Account: LDAP & Email (for scans)","4/5/2019 3:33:02 PM","6/30/2020 9:42:15 AM"
"<EMAIL>","rhel-virt-who","rhel-virt-who","Service Account","890536a9-cf11-4177-bbc8-3e58eedcb752","*********",,"True","Service Account: RHEL Services Account","6/19/2019 12:53:18 PM","1/16/2023 10:01:30 AM"
"<EMAIL>","cvvcuser","Commvault vCenter User - cvvcuser","Service Account","288b7531-b3d5-4ac4-b6fc-1172dcc35b1b","*********",,"True","Service Account: Commvault vCenter User","3/13/2019 12:40:22 PM","1/16/2023 10:37:16 AM"
"<EMAIL>","JiraConfshare","JiraConfshare","Service Account","72de2d7a-2f25-4ad3-9886-896245429fd6","*********",,"True","Service Account: Corp IT - Sync account between JIRA servers","6/11/2019 8:46:25 AM","1/15/2023 3:46:54 AM"
"<EMAIL>","KACE_User","Kace User","Service Account","********-2fa2-422b-94f6-407a2cf3db84","*********",,"True","Service Account: Corp IT - Read only user for LDAP imports (do NOT delete)","3/11/2011 8:17:14 AM","1/13/2021 3:22:21 PM"
"<EMAIL>","SQL-EMRDATASQL01","SQL-EMRDATASQL01","Service Account","774090e7-809c-46ef-b8c1-cf07b25a891f","*********",,"True","Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2008R2
EMRDATASQL01\SQL2012
and EMRDATASQL01\SQL2017 MSSQL instances.","2/22/2017 9:12:55 AM","1/16/2023 1:15:40 AM"
"<EMAIL>","mrsvc","Dynamics MR Service","Service Account","20d3d976-7c2f-43ad-a79c-1a1583fcbdc4","*********",,"True","Service Account: add-on to GP that provides/creates financial reports","5/8/2015 12:33:10 PM","10/27/2022 3:31:23 PM"
"<EMAIL>","mitelrecording","Mitel Recording","Service Account","7d503485-999c-4383-b003-c07d811f53ae","*********",,"True","Service Account: Corp IT - User account used on qtmitelscreen and qtmitelcall for accessing shares on those servers","6/7/2018 11:38:31 AM","8/24/2022 12:02:04 PM"
"<EMAIL>","vcenter-skyline","vCenter Read Only Skyline User","Service Account","f7c98220-a69c-4941-94ab-72221e0c462d","*********",,"True","Service Account: Tech Infrastructure - Service account for VMWare","11/21/2019 12:43:54 PM","1/16/2023 9:33:43 AM"
"<EMAIL>","vcenter-ro","vCenter Read Only User","Service Account","f314bee2-c411-4b64-ac29-e0aba3c64fed","*********",,"True","Service Account: Tech Infrastructure - Read only Service account for VMWare","11/22/2019 12:25:58 PM","1/16/2023 10:40:15 AM"
"<EMAIL>","solarwindsdbservice","SolarWinds DB Service Account","Service Account","24169f47-3feb-4e7d-b5ea-6fdb0499c966","*********",,"True","Service Account: AD account that it connects to its database with on infrasql where specific permissions are configured","3/17/2020 10:14:29 AM","1/16/2023 10:17:58 AM"
"<EMAIL>","kantechemail","KantechEmail Weekly Report","Service Account","b5670b1c-9c30-4af6-b9d9-9df147a0ff3b","*********",,"True",,"4/1/2020 1:43:49 PM",
"<EMAIL>","cs.training","CS Training","Service Account","732bd6da-c759-4213-bf50-1fae526647f9","*********","CS","True","Client Services, used for booking training for employees.","4/22/2020 9:32:33 AM",
"<EMAIL>","IntuneNDES","Intune NDES","Service Account","326cae9e-4791-4da1-90b6-c8450a456a46","*********",,"True","Service Account : Corp IT-NDES to enroll certificates from Certificate Authority to AADJ Devices","4/29/2020 11:15:32 AM","10/17/2021 7:24:20 AM"
"<EMAIL>","ca_secscan","ca_secscan","Service Account","fe06a57d-ca17-4383-893a-c410adf03dd0","*********",,"True","Service Account: Security - This AD Account was created in response to salesforce ticket ******** to access QHR servers and report back to the CIS platform dashboard. This account ensure servers are configured Securely.","7/7/2020 2:21:33 PM",
"<EMAIL>","omeuser","OME User","Service Account","e29d4214-86c7-447c-8825-68072683f4e0","*********",,"True","Service Account: Corp IT - Windows account to access SMB Share on Dell-DRM","8/13/2020 3:02:05 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","SQL-EMRDATASQL02","SQL-EMRDATASQL02","Service Account","5efc39e9-2161-4f26-a2f2-2a9a68d8373c","*********",,"True","Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2019 MSSQL instance.","9/9/2020 12:22:48 PM","8/10/2022 9:04:45 AM"
"<EMAIL>","OXIDIZEDSABASH","OXIDIZEDSABASH","Service Account","bded6cdc-bfb3-4497-8f85-04f40410c5e5","*********",,"True",,"1/5/2021 11:39:11 AM",
"<EMAIL>","servicenow.blob","Service-Now-Blob-Reader","Service Account","c31aec6e-fa1c-4558-9303-d9824c805860","*********",,"True","Service Account: Corp IT - Used for Azure blob storage and Service-Now Prod impersonation connection","2/5/2021 12:49:32 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","DGPSQLServiceUser","DGPSQLServiceUser","Service Account","673f4482-68da-4051-ab5e-f742855ec4e7","*********",,"True","Ran on QTFINSQL for sql service","10/4/2022 9:21:00 AM","1/16/2023 4:09:16 AM"
"<EMAIL>","DGPeConnectService","DGPeConnectService","Service Account","1c1e7910-a502-4fb1-a93b-db3fdc581978","*********",,"True","Econnect service on QTFINSQL and QTFINRDS","10/4/2022 9:25:15 AM","1/1/2023 2:55:25 AM"
"<EMAIL>","jira-serviceaccount","jira-serviceaccount","Service Account","d2e439e3-2009-45ba-a82f-28779dd6e76f","*********",,"True",,"1/10/2023 11:29:44 AM","1/11/2023 2:21:35 PM"
"<EMAIL>","da-gharshenin","Domain Admin - Greg Harshenin",,"a15c120b-747f-400c-b51f-7dd2fe4cc2bb","*********",,"True",,"2/19/2007 10:56:21 AM","1/13/2023 3:38:54 PM"
"<EMAIL>","da-mkennedy","Domain Admin- Malcolm Kennedy",,"ceb5de5c-8985-423a-a679-31ee2dd9d09e","*********",,"True",,"7/18/2019 3:33:50 PM","9/28/2020 11:52:47 AM"
"<EMAIL>","da-nenglish","Domain Admin - Nyel English",,"67ec3daa-9872-4b45-a885-190af5a8cafc","*********",,"True",,"12/10/2018 7:50:15 AM","1/16/2023 10:37:42 AM"
"<EMAIL>","da-pkainth","Domain Admin - Preet Kainth",,"a0395fac-fa35-4dee-9168-eab29f4ffb1b","*********",,"True",,"1/14/2019 11:33:33 AM","1/16/2023 10:03:18 AM"
"<EMAIL>","da-njanzen","Domain Admin Nick Janzen",,"13116fcf-b56b-4124-81c6-f0ffb65ef65d","*********",,"True",,"12/11/2017 10:37:39 AM","12/1/2022 1:01:35 PM"
"<EMAIL>","da-krosal","Domain Admin - Kevin Rosal",,"2df6fbd3-0ce5-4e39-9ece-24fa26ff6c6b","*********",,"True",,"2/27/2017 8:37:17 AM","9/19/2022 8:52:13 AM"
"<EMAIL>","da-amehl","Domain Admin - Alex Mehl",,"b1c7a9fc-461e-4f91-b74f-68350e774c5f","*********",,"True",,"2/9/2010 6:16:34 PM","3/2/2021 2:53:17 PM"
"<EMAIL>","da-amcfadden","Domain Admin - Andrew McFadden",,"e5bdf13e-e581-4ebf-93ce-8a01f9501a59","*********",,"True",,"3/8/2017 11:41:26 AM","12/30/2022 1:21:19 PM"
"<EMAIL>","da-kkendall","Domain Admin - Kevin Kendall",,"f60bb455-bb6b-48fe-990c-3390b23f83fd","*********",,"True",,"5/30/2018 2:23:28 PM","1/9/2023 2:54:54 PM"
"<EMAIL>","da-smay","Domain Admin - Scott May",,"911b675b-1ad1-48dd-a8e3-b09b21302bf8","*********",,"True",,"2/11/2019 2:40:19 PM","1/13/2023 2:48:12 PM"
"<EMAIL>","da-croseberry","Domain Admin - Chris Roseberry",,"bbc0565e-31aa-42a5-a488-d98afd8238fb","*********",,"True",,"4/26/2019 11:20:55 AM","1/10/2023 2:10:39 PM"
"<EMAIL>","da-nated","Domain Admin - Devin Nate",,"2a74206f-2228-4022-b780-c830fc5ae509","*********",,"True",,"6/27/2013 3:07:02 PM","1/21/2020 3:05:40 PM"
"<EMAIL>","da-mmclean","Domain Admin - Mark McLean","Sr. Manager Cloud Technology","d25d079c-ce65-4f45-ac55-51a811dfca65","*********",,"True",,"6/27/2013 3:08:33 PM","4/16/2021 8:46:41 AM"
"<EMAIL>","Da-Plaudenklos","Domain Admin - Peter Laudenklos",,"6ff505cf-5380-4d35-b915-09062228ddca","*********",,"True",,"5/16/2019 2:51:57 PM","1/11/2023 9:22:46 AM"
"<EMAIL>","da-mhernandez","Domain Admin - Miguel Hernandez",,"8cd8ded2-0c2b-461c-80ae-98126d2e2b27","*********",,"True",,"12/19/2018 2:57:51 PM","11/25/2022 11:35:51 AM"
"<EMAIL>","da-rarmstrong","Domain Admin - Robert Armstrong",,"9065e0d4-843d-4f95-8669-146e82bbd7b7","*********",,"True",,"6/27/2012 1:46:05 PM","1/16/2023 7:53:50 AM"
"<EMAIL>","da-sbradford","Domain Admin - Sam Bradford",,"66a3e0db-d3ea-4c96-ab2b-9630858f2f83","*********",,"True",,"8/2/2016 10:33:47 AM","1/11/2023 11:32:56 AM"
"<EMAIL>","da-mhoppe","Domain Admin - Michal Hoppe",,"f1ea0dfa-8c0b-4209-8c18-38f7272d62ff","*********",,"True",,"8/25/2016 2:13:41 PM","12/21/2021 1:48:57 PM"
"<EMAIL>","da-tdrescher","Domain Admin - Taylor Drescher",,"7e162a5d-af03-4dca-ae54-30d1e47dec64","*********",,"True",,"11/13/2019 10:52:32 AM","8/23/2022 11:35:14 AM"
"<EMAIL>","da-chounsham","Domain Admin - Craig Hounsham",,"931c0d3e-86eb-4613-9831-62e57d57899e","*********",,"True",,"12/6/2019 8:22:00 AM","3/22/2022 12:18:18 PM"
"<EMAIL>","da-jbell","Domain Admin - Jeffrey Bell",,"7de81fa2-404a-4d24-8615-7906e255e410","*********",,"True",,"12/18/2019 11:13:45 AM","1/12/2023 3:17:53 AM"
"<EMAIL>","da-mkandy","Domain Admin - Mohammad Kandy",,"2a525081-20d9-4e82-befa-d009253f0e9b","*********",,"True",,"3/11/2020 5:40:42 AM","9/13/2021 12:09:50 PM"
"<EMAIL>","da-balbrecht","Domain Admin - Butch Albrecht",,"88dbc942-a31b-427a-ba70-c9dbda3989ba","*********",,"True",,"6/2/2020 8:08:38 AM","12/16/2020 11:48:48 AM"
"<EMAIL>","da-jpond","Domain Admin - Jarrid Pond",,"5971c21a-e476-4949-b2a8-99d4be4bb24f","*********",,"True",,"4/19/2021 10:46:03 AM","1/16/2023 10:31:59 AM"
"<EMAIL>","da-jpinske","Domain Admin - Jordan Pinske",,"d39fb43c-69ea-4668-9cdd-f7a55c1e06b0","*********",,"True",,"4/29/2021 7:32:07 AM","1/5/2023 2:03:57 PM"
"<EMAIL>","DA-TAruleba","Domain Admin - Tayo Aruleba",,"b43bb947-fa86-46cb-aab6-fe7e1741286d","*********",,"True",,"5/10/2021 12:19:55 PM","1/16/2023 8:27:20 AM"
"<EMAIL>","da-jfleming","Domain Admin - Jeff Fleming",,"9159459e-b607-43a9-8e4d-d14330d87a6e","*********",,"True",,"6/14/2021 5:41:05 AM","1/13/2023 2:13:41 PM"
"<EMAIL>","da-dtorres","Domain Admin - Dilcia Torres",,"06aebc94-6341-4d33-b81b-7735352e43e8","*********",,"True",,"6/14/2021 12:38:47 PM","1/4/2023 2:40:55 PM"
"<EMAIL>","da-fxiao","Domain Admin - Fred Xiao",,"67b6a401-112e-4610-8ca0-12b97d330770","*********",,"True",,"9/28/2021 11:32:34 AM","12/31/1600 4:00:00 PM"
"<EMAIL>","da-pfarry","Domain Admin - Paul Farry",,"58304693-a90b-4275-bba6-c4419250d47f","*********",,"True",,"12/10/2021 8:07:13 AM","1/16/2023 9:37:05 AM"
"<EMAIL>","da-cbrown-humphrey","Domain Admin - Chrisaine Brown-Humphrey",,"2e6c0f10-f843-4469-8076-9452916076bf","*********",,"True",,"1/28/2022 9:29:21 AM","12/31/1600 4:00:00 PM"
"<EMAIL>","da-smool","Domain Admin - Sudeep Mool",,"6462d106-3674-4800-b153-36fbec1ca175","*********",,"True",,"4/13/2022 3:30:37 PM","11/17/2022 2:51:20 PM"
"<EMAIL>","da-jgermain","Domain Admin - Justin Germain",,"33f9e65e-2136-4c95-bc77-ee710e4e0cbb","*********",,"True",,"5/6/2022 11:13:13 AM","12/31/1600 4:00:00 PM"
"<EMAIL>","da-ntaylor","Domain Admin - Nathan Taylor",,"28ca089a-eb88-406e-bf18-f5b08c66d844","*********",,"True",,"8/23/2022 6:46:59 AM","12/12/2022 6:47:39 AM"
"<EMAIL>","da-rpanchal","Domain Admin - Rushik Panchal",,"77c415e6-b941-4fd4-8b19-2a0aa41627f3","*********",,"True",,"12/8/2022 1:56:52 PM",
"<EMAIL>","da-bkeefe","Domain Admin - Brad Keefe",,"6fa9cb55-1753-45bc-9a18-faf0edec7035","*********",,"True",,"1/4/2023 2:54:24 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","cpotharaju","Domain Admin - Chandra Potharaju",,"55628da9-4a5a-4d05-8ffe-ddb38af35bb8","*********",,"True",,"1/4/2023 3:16:28 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","Testpw","Test Passwriteback",,"8e4e8245-8612-4e20-8609-7f813085e37c","*********",,"True","For Password Writeback and MDM testing Azure","11/22/2019 1:07:16 PM","9/15/2020 8:38:16 PM"
"<EMAIL>","test.phoneforward","Test Phoneforward",,"45c1d244-5d18-449b-bf8a-450d620a34c9","*********",,"True","Service Account: Corp IT - Testing Teams Phone Forwarding","7/29/2021 9:16:15 AM",
"<EMAIL>","test.user","Test User","Client Services Analyst","7ff3bee7-505c-4be8-860b-bf5ba4a2d2eb","*********","Client Services","True",,"11/22/2022 4:02:57 PM","12/31/1600 4:00:00 PM"
"<EMAIL>","mrsvc","Dynamics MR Service","Service Account","20d3d976-7c2f-43ad-a79c-1a1583fcbdc4","*********",,"True","Service Account: add-on to GP that provides/creates financial reports","5/8/2015 12:33:10 PM","10/27/2022 3:31:23 PM"
"<EMAIL>","svc-flexapp","SVC Flexapp",,"2729ab42-5cfe-488f-952e-79b559e37a59","*********",,"True","Service Account:Do not change - service account for Flexapp","8/17/2020 10:33:18 AM",
"<EMAIL>","svc-jenkins","SVC Jenkins",,"eb5f1145-b0f2-40a3-bfe0-3fed0ee9664f","*********",,"True","DO not change - Jenkins slave account for Flexapp","8/17/2020 1:07:10 PM","1/16/2023 3:48:17 AM"
"<EMAIL>","svc-commvault","CommVault Backup Admin",,"e124dd29-3c21-454e-afc2-918f325de4e4","*********",,"True",,"12/18/2020 7:47:26 AM","1/10/2023 2:33:34 AM"
"<EMAIL>","svc-mdisensor","MDI sensor",,"51d60f24-c6eb-4ae2-8169-2632a26ee21b","*********",,"True","Service Account: Security - Microsoft Defender for Identity Sensor for QTDC01/02 and QTADFS2","4/14/2021 11:46:11 AM",
"<EMAIL>","svc_splunksync","Splunk Sync",,"5e06f5bd-9277-4108-af3a-f4beaa20c2fc","*********",,"True","Service Account: Security - Used for Splunk Syncing","10/27/2021 10:29:07 AM",
"<EMAIL>","svc-sqlbackups","svc-sqlbackups",,"0e3cf0b6-223b-48c3-9a30-f8d99e7545cf","*********",,"True",,"2/17/2022 10:19:43 AM","1/16/2023 10:31:23 AM"
"<EMAIL>","svc-vmbackups","svc-vmbackups",,"1720d225-ee6a-4050-a837-d8cf24551317","*********",,"True",,"3/22/2022 8:25:10 AM","12/31/1600 4:00:00 PM"
"<EMAIL>","svc-mstrueup","svc-mstrueup",,"f89c4111-3d4e-45cb-833a-e978e770c43b","*********",,"True",,"3/29/2022 10:18:27 AM","12/31/1600 4:00:00 PM"
