﻿Try {
  $connected = Get-mailbox <EMAIL>
  Write-Host "Already Connected to Exchange Online!`n`n" -ForegroundColor Green
}
Catch {
  $LiveCred = Get-Credential <EMAIL>

    Clear
    Write-Host "Connecting to Exchange Online..."

    $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
    Import-PSSession $Session -AllowClobber
    Connect-MsolService -Credential $LiveCred
}


#$onPremGroupName = read-host "Enter Exact Display Name of onprem Group"
$onPremGroupName = '* All Staff'



$group = Get-DistributionGroup -Identity $onPremGroupName

write-host "Checking if group is onprem...`n" -ForegroundColor Green

if ($group.IsDirSynced) { #Check is group is onPrem
    $group.Name
    write-host "Are you sure you want to move '$group' to office365? y/n" -ForegroundColor Yellow
    $answer = read-host

    if ($answer -eq "y") {
  
        write-host "Old Group Name: $group" -ForegroundColor Yellow
        $newO365groupname = read-host "`nEnter New Group Name. It can't be the same as the old email and without *" 
        $newAlias = read-host "Enter new Alias, this will also be used to make the primary SMTP address"
        $newSMTP = "$newAlias" + "@qhrtech.com" 
        New-DistributionGroup -Name $newO365groupname  -DisplayName $newO365groupname -Alias $newAlias -PrimarySmtpAddress $newSMTP
        write-host "`n Created New Group" -ForegroundColor Green

        
        
        $groupmembers = Get-DistributionGroupMember -Identity $onPremGroupName

        $usergroupmembers = ""
        foreach ($user in $groupmembers) {
    
            $userType = $user.ObjectClass


            if ($userType -eq "user") { # Checks to make sure Members of the onprem group are users to filter out groups.
                $username = $user.PrimarySmtpAddress
                
                Add-DistributionGroupMember -Identity $newO365groupname -Member $username
                
                $usergroupmembers += "$user "

                
            }


        } #end foreach

        write-host "`n Added Users To Group: $userGroupMembers" -ForegroundColor Green
    }
}

else {
    write-host "Group is already a office365 distribution group" -ForegroundColor Yellow
    break
}
