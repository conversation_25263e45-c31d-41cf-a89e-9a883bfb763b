﻿#PLEASE NOTE: This Script Runs Daily as a Task Sequence on QTADMIN1

#Connect to Office365
#only works if running as the qtadmin1service account
$LiveCred = Import-CliXml -Path "C:\Scripts\Credentials\office365admin-qtadmin1service.cred"

#Use below instead if running manually but remember to comment it back after 
#$LiveCred = Get-Credential <EMAIL>

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
Connect-MsolService -Credential $LiveCred


$Header = @"
<style>
TABLE {border-width: 1px; padding: 10px; border-style: solid; border-color: black; border-collapse: collapse;}
TD {border-width: 1px; padding: 10px; border-style: solid; border-color: black;}
</style>
"@


#Parameters that you can edit
$recipients = @("<EMAIL>", "<EMAIL>", "<EMAIL>")

#Set day of week to run weekly report:
$dayOfWeek = "Monday"



$allLicenses = Get-MsolAccountSku | Format-Table

#$e3Units = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:ENTERPRISEPACK"}


$e3Units = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:ENTERPRISEPACK"}
$e3Units | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Office 365 Enterprise E3"
#$e3Units = $e3Units | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$audioUnits = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:MCOMEETADV"}
$audioUnits | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Audio Conferencing"
#$audioUnits = $audioUnits | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$VisioPlan2Units = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:VISIOCLIENT"}
$VisioPlan2Units | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Visio Online Plan 2"
#$VisioPlan2Units = $VisioPlan2Units | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$e1Units = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:STANDARDPACK"}
$e1Units | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Office 365 Enterprise E1"
#$e1Units = $e1Units | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$ProjectEssUnits = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:PROJECTESSENTIALS"}
$ProjectEssUnits | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Project Online Essentials"
#$ProjectEssUnits = $ProjectEssUnits | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$ProjectProUnits = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:PROJECTPROFESSIONAL"}
$ProjectProUnits | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Project Online Professional"
#$ProjectProUnits = $ProjectProUnits | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$ExcPlan1Units = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:EXCHANGESTANDARD"}
$ExcPlan1Units | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Exchange Online (Plan1)"
#$ExcPlan1Units = $ExcPlan1Units | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$KioskUnits = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:EXCHANGEDESKLESS"}
$KioskUnits | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Exchange Online Kiosk"
#$KioskUnits = $KioskUnits | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$MobilityUnits = Get-MsolAccountSku | where {$_.AccountSkuId -eq "qhrtech:EMS"}
$MobilityUnits | Add-Member -MemberType NoteProperty -Name "FriendlyName" -Value "Entperise Mobility + Security E3"
#$MobilityUnit = $MobilityUnit | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits

$ArrayLicense = @($e3Units,$audioUnits,$VisioPlan2Units,$e1Units,$ProjectEssUnits,$ProjectProUnits,$ExcPlan1Units,$KioskUnits,$MobilityUnits)

$ArrayLicense = $ArrayLicense | ConvertTo-Html -Property FriendlyName, ActiveUnits, ConsumedUnits -Head $Header

$e3Active = $e3Units.ActiveUnits
$e3Consumed = $e3Units.ConsumedUnits
$e3Unitsleft = $e3Active - $e3Consumed




$message = "<h3>E3 Licenses Report</h3> <h5> E3 Active Units: <b>$e3Active</b> | E3 Consumed Units: <b>$e3Consumed</b> </h5>"
$warningmessage = "<h4>WARNING: Less than 10 Office 365 E3 licenses remain <br><br> E3 Units Left: $e3Unitsleft <br><br> E3 Active Units: $e3Active | Consumed Units: $e3Consumed </h4>"
#$allLicenses = Get-MsolAccountSku | ConvertTo-Html -Property AccountSkuId, ActiveUnits, ConsumedUnits

$dayToday = (get-date).DayOfWeek

if ($dayToday -match $dayofWeek -and $e3UnitsLeft -gt 10)
{
    Send-MailMessage -To $recipients -From “<EMAIL>” -SmtpServer mail.qhrtech.com -Subject “Office365 License Weekly Report” `
    -body "$message <br><br><br> <h5> All License info: </h5> $ArrayLicense` 
    <br><br><br> NOTE: This script runs on QTADMIN1 daily at 8am (Only Mondays for Weekly Report) as a scheduled task (o365-license-report.ps1)." -BodyAsHtml
}


if ($e3Unitsleft -lt 10 -and $dayToday -notmatch "Saturday" -and $dayToday -notmatch "Sunday" ) {
    Send-MailMessage -To $recipients -From “<EMAIL>” -SmtpServer mail.qhrtech.com -Subject “Alert - Less Than 10 Office 365 E3 Licenses Left” `
    -body "$warningmessage <h5> All License info: </h5> $ArrayLicense ` 
    <br><br><br> NOTE: This script runs on QTADMIN1 daily mon-fri at 8am as a scheduled task (o365-license-report.ps1)." -BodyAsHtml
}