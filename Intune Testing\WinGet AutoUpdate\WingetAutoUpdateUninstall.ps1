# Define variables
$LogFile = "C:\temp\WingetAutoUpdateUninstall.log"
$ProgramName = "Winget-AutoUpdate"
$ProgramDataPath = "C:\ProgramData"

# Initialize log file
Write-Output "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] Starting Winget-AutoUpdate uninstallation process..." | Out-File -FilePath $LogFile -Append

# Function to log messages
function Log-Message {
    param (
        [string]$Message
    )
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Output "[$timestamp] $Message" | Out-File -FilePath $LogFile -Append
}

# Remove matching items from ProgramData
Log-Message "Checking for items named '$ProgramName*' in $ProgramDataPath..."

try {
    $items = Get-ChildItem -Path $ProgramDataPath -ErrorAction Stop
    foreach ($item in $items) {
        if ($item.Name -like "$ProgramName*") {
            Log-Message "Found matching item: $($item.FullName). Attempting to remove..."
            Remove-Item -Path $item.FullName -Recurse -Force -ErrorAction Stop
            Log-Message "Successfully removed: $($item.FullName)"
        }
    }
    Log-Message "Completed checking for items in $ProgramDataPath."
} catch {
    Log-Message "Error removing items from ${ProgramDataPath}: $($_.Exception.Message)"
    Exit 1
}

# Uninstall Winget-AutoUpdate using registry uninstall strings
Log-Message "Searching for uninstall strings for programs named '$ProgramName'..."

try {
    $registryPaths = @(
        "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall",
        "HKLM:\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall",
        "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall"
    )

    $found = $false

    foreach ($path in $registryPaths) {
        $apps = Get-ItemProperty -Path "$path\*" -ErrorAction SilentlyContinue | Where-Object { $_.DisplayName -like "*$ProgramName*" }
        foreach ($app in $apps) {
            $found = $true
            $uninstallString = $app.UninstallString
            Log-Message "Found program: $($app.DisplayName), Version: $($app.DisplayVersion). Uninstall string: $uninstallString"
            
            if ($uninstallString) {
                # If the uninstall string contains "MsiExec", add silent flags
                if ($uninstallString -match "MsiExec") {
                    $uninstallString += " /quiet /norestart"
                }
                
                Log-Message "Attempting to uninstall using: $uninstallString"
                Start-Process -FilePath "cmd.exe" -ArgumentList "/c $uninstallString" -Wait -NoNewWindow
                
                Log-Message "Uninstallation command executed for: $($app.DisplayName)"
            } else {
                Log-Message "No uninstall string found for: $($app.DisplayName)"
            }
        }
    }

    if (-not $found) {
        Log-Message "No programs found with the name '$ProgramName' in the registry."
    }
} catch {
    Log-Message "Error during uninstallation process: $($_.Exception.Message)"
    Exit 1
}

# Final log entry and exit
Log-Message "Winget-AutoUpdate uninstallation process completed."
Exit 0
