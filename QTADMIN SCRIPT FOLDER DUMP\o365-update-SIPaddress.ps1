﻿$userlist = Get-Content C:\Scripts\sip-change-list.txt

foreach ($user in $userlist) {
$userfull = get-aduser $user -Properties *
$oldsip = $userfull.proxyAddresses | ? {$_ -match "SIP"}

Write "Setting $user SIP address to $($userfull.userprincipalname)"
sleep 3

# Remove OLD SIP address and add new one
Set-Aduser $user -Replace @{"msRTCSIP-PrimaryUserAddress"="sip:$($userfull.userprincipalname)"} 
Set-ADUser $user -Remove @{"Proxyaddresses"=$($oldsip)} -ErrorAction SilentlyContinue
Set-ADUser $user -Add @{"ProxyAddresses"="sip:$($userfull.userprincipalname)"}
Set-ADUser $user -Remove @{"MsExchShadowProxyAddresses"=$($oldsip)} -ErrorAction SilentlyContinue
Set-ADUser $user -Add @{"MsExchShadowProxyAddresses"="sip:$($userfull.userprincipalname)"}

}

Write "Complete!!!! Force ADFS Sync!!!"


