﻿
#DO NOT RUN WITH WITHOUT TALKING TO MARTIN YOUR HERO

clear
Get-Module AD
$SPsite = "https://qhrtech.sharepoint.com"
$source = $null
$target = $null
$info = $null


Try {
  $connected = Get-SPOSite
  Write-Host "Already Connected to SharePoint Online!`n`n" -ForegroundColor Green
}
Catch {
  Write-Host "Connecting to SharePoint Online...`n`n" -ForegroundColor Green
  $creduser = "<EMAIL>"
  $LiveCred = Get-Credential $creduser
  #$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
  #$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)
  Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred
 }



$disabledAccounts = Search-ADAccount -AccountDisabled
$disabledUsernames = $disabledAccounts.UserPrincipalName

foreach ($source in $disabledUsernames)
    {
    if ($source -match "@qhrtech")
        {
                
                $Error.Clear()             
                $checksource = get-spouser -Site $SPsite -LoginName $source -ErrorAction SilentlyContinue
                

                $grouplist = Get-SPOUser -Site $SPsite  -LoginName $source| select -ExpandProperty groups -ErrorAction SilentlyContinue

                Write-Host "`n----------"
                foreach ($group in $grouplist) 
                {
                    write-host "Removing $source from $group" -ForegroundColor Blue
                    $info += "`r`n Removing $source from $group"
                    Remove-SPOUser -Site $SPsite -LoginName $source -Group $group -ErrorAction SilentlyContinue
                }

                Write-Host "$source finshed" -ForegroundColor Green
                $info += "`r`n $source finshed"
                $info | Out-File c:\scripts\O365_Disabled_Users_Removed_From_Sharepoint_Groups.txt

        }


    }


#$info | Out-File c:\scripts\O365_Disabled_Users_Removed_From_Sharepoint_Groups.txt