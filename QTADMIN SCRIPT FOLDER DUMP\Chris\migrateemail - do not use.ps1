﻿$LiveCred = Get-Credential "<EMAIL>"
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session


$remcred = get-credential "quadranthr\da-ckitella"
New-MoveRequest -identity "<PERSON>" -remote -remotehostname mail.qhrtech.com -RemoteCredential $remcred -TargetDeliveryDomain qhrtech.mail.onmicrosoft.com

#get status
#Get-MoveRequest | ? status -eq inprogress | Get-MoveRequestStatistics
#Get-MoveRequest | Get-MoveRequestStatistics

$i = 0; while ($i -le 10) { Get-MoveRequest |? status -ne completed | Get-MoveRequestStatistics | select Displayname,SourceServer,StatusDetail,TotalMailboxSize,TotalArchiveSize,PercentComplete | ft; sleep 10; $i++ }