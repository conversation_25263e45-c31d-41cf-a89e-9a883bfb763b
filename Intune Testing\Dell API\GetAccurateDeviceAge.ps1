<#
.SYNOPSIS
Retrieves device information from Intune (Dell) and Jamf Pro (Apple).
Queries Dell TechDirect API for Dell warranty. Uses Jamf Pro data for Apple warranty/age.
Calculates device age, warranty status, and age summaries. Exports a multi-sheet report.

.DESCRIPTION
This script performs the following actions:
1. Checks and installs required PowerShell modules.
2. Connects to Microsoft Graph (for Intune devices).
3. Retrieves Dell devices from Intune.
4. Prompts for Dell TechDirect API Client Secret & obtains Dell API token.
5. Queries Dell TechDirect Warranty API for Dell devices, with retries.
6. Prompts for Jamf Pro API Client ID & Secret & obtains Jamf API token using OAuth client_credentials flow. (SecureString conversion updated for compatibility)
7. Retrieves Apple computer inventory from Jamf Pro API, handling pagination.
8. Processes Apple device data, prioritizing warranty/purchase/enrollment dates from Jamf for age calculation.
9. Merges Dell and Apple device data.
10. Calculates overall average age, median age, age buckets, and per-model average age.
11. Exports compiled information and summaries to a multi-sheet Excel (.xlsx) file.

.NOTES
ScriptName: GetAccurateDeviceAge.ps1
Author: <PERSON><PERSON>
Date: 2025-05-07
Version: 1.68

Prerequisites:
- PowerShell 5.0 or later (for ConvertFrom-SecureString -AsPlainText behavior, though this script now uses a more compatible method).
- PowerShell 7+ is still recommended for best experience with modern APIs.
- Internet connectivity.
- Dell: Active Dell TechDirect account with API access (Client ID & Secret).
- Jamf: Jamf Pro instance with API access.
  - An API Role in Jamf Pro with 'Read Computer Inventory' permission.
  - An API Client (for 'Client Credentials' grant type) associated with the API Role. Client ID and Secret required.
- Permissions to read device information from Microsoft Intune.
- The 'ImportExcel' PowerShell module.

JAMF PRO API CREDENTIAL SETUP: (Ensure these are precisely followed)
1. In Jamf Pro, navigate to Settings > System > API roles.
2. Click '+ New' to create an API Role (e.g., "Device Age Report Role").
3. Under Privileges, grant 'Read Computer Inventory'. Save the role.
4. Navigate to API clients (under the same System settings area).
5. Click '+ New' to create an API Client.
6. Display Name: e.g., "Device Age Report Client"
7. API Role: Select the role created in step 3.
8. Allowed Grant Types: Check 'Client Credentials'.
9. Access Token Lifetime: Default or as per your policy.
10. Enable API Client: Ensure it's checked.
11. Save the API Client. Securely copy the generated Client ID and Client Secret.
    The Client ID will be used for '$jamfApiUser' in the script configuration (or prompted).
    The Client Secret will be prompted for by the script at runtime.

Disclaimer:
- Verify API URLs and parsing logic against respective documentation.
- Apple warranty information is based SOLELY on data available in Jamf Pro.
- Ensure the Convert-ModelIdentifierToName function is updated.

.EXAMPLE
.\GetAccurateDeviceAge.ps1
#>

#region Script Configuration
# --- USER CONFIGURATION REQUIRED ---
$dellClientId = "l7082fdd679ec1459fb40359fe03b4d57a" # Replace with your Dell TechDirect Client ID

# --- JAMF PRO CONFIGURATION ---
$jamfProUrl = "https://qhrtech.jamfcloud.com" 
$jamfApiUser = "5dda26cc-020a-4352-921b-82fb863ca4fe" # Pre-filled per user log

# --- Dell API URLs ---
$dellTokenUrl = "https://apigtwb2c.us.dell.com/auth/oauth/v2/token"
$dellWarrantyUrlBase = "https://apigtwb2c.us.dell.com/PROD/sbil/eapi/v5/asset-entitlements"
$dellSingleTagUrlBase = "https://apigtwb2c.us.dell.com/PROD/sbil/eapi/v5/asset-entitlement-components"

# --- Optional Configuration ---
$dellApiBatchSize = 100
$outputDirectory = "C:\Temp\Device Age Report"
$singleTagRetryDelaySeconds = 1
$jamfInventoryPageSize = 100 
#endregion Script Configuration

#region Prerequisites Check & Installation
# (Condensed for brevity)
Write-Host "Checking for required PowerShell modules..." -ForegroundColor Yellow; $requiredModules = @("Microsoft.Graph.Authentication", "Microsoft.Graph.Devices.CorporateManagement", "ImportExcel"); $modulesToInstall = @(); foreach ($moduleName in $requiredModules) { if (-not (Get-Module -ListAvailable -Name $moduleName)) { Write-Host "Module '$moduleName' not found."; $modulesToInstall += $moduleName } else { Write-Host "Module '$moduleName' found." } }; if ($modulesToInstall.Count -gt 0) { Write-Warning "Missing modules: $($modulesToInstall -join ', ')"; $installChoice = Read-Host "Install now? (Y/N)"; if ($installChoice -match '^y') { Write-Host "Installing..."; [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; foreach ($moduleName in $modulesToInstall) { Write-Host "Installing $moduleName..."; try { Install-Module -Name $moduleName -Scope CurrentUser -Repository PSGallery -Force -AllowClobber -ErrorAction Stop; Write-Host "Installed $moduleName." -ForegroundColor Green } catch { Write-Error "Failed install: $moduleName. Error: $($_.Exception.Message)"; Exit 1 } }; Write-Host "Installation complete." -ForegroundColor Yellow } else { Write-Error "Required modules missing."; Exit 1 } } else { Write-Host "All required core modules present." -ForegroundColor Green }; try { Import-Module ImportExcel -ErrorAction Stop; Write-Host "ImportExcel module imported successfully." } catch { Write-Error "Failed to import ImportExcel module. Error: $($_.Exception.Message)"; Exit 1 }
#endregion Prerequisites Check & Installation

#region Helper Functions
Function Convert-ModelIdentifierToName { param([string]$ModelIdentifier); $modelNameMapping = @{ "MacBookPro11,1" = "MacBook Pro (Retina, 13-inch, Late 2013)"; "MacBookPro11,2" = "MacBook Pro (Retina, 15-inch, Late 2013)"; "MacBookPro11,3" = "MacBook Pro (Retina, 15-inch, Late 2013)"; "MacBookPro11,4" = "MacBook Pro (Retina, 15-inch, Mid 2014)"; "MacBookPro11,5" = "MacBook Pro (Retina, 15-inch, Mid 2015)"; "MacBookPro12,1" = "MacBook Pro (Retina, 13-inch, Early 2015)"; "MacBookPro13,1" = "MacBook Pro (13-inch, 2016, Two Thunderbolt 3 ports)"; "MacBookPro13,2" = "MacBook Pro (13-inch, 2016, Four Thunderbolt 3 ports)"; "MacBookPro13,3" = "MacBook Pro (15-inch, 2016)"; "MacBookPro14,1" = "MacBook Pro (13-inch, 2017, Two Thunderbolt 3 ports)"; "MacBookPro14,2" = "MacBook Pro (13-inch, 2017, Four Thunderbolt 3 ports)"; "MacBookPro14,3" = "MacBook Pro (15-inch, 2017)"; "MacBookPro15,1" = "MacBook Pro (15-inch, 2018/2019)"; "MacBookPro15,2" = "MacBook Pro (13-inch, 2018/2019, Four Thunderbolt 3 ports)"; "MacBookPro15,3" = "MacBook Pro (15-inch, 2019)"; "MacBookPro15,4" = "MacBook Pro (13-inch, 2019, Two Thunderbolt 3 ports)"; "MacBookPro16,1" = "MacBook Pro (16-inch, 2019)"; "MacBookPro16,2" = "MacBook Pro (13-inch, 2020, Four Thunderbolt 3 ports)"; "MacBookPro16,3" = "MacBook Pro (13-inch, 2020, Two Thunderbolt 3 ports)"; "MacBookPro17,1" = "MacBook Pro (13-inch, M1, 2020)"; "MacBookPro18,1" = "MacBook Pro (16-inch, M1 Pro/Max, 2021)"; "MacBookPro18,2" = "MacBook Pro (16-inch, M1 Pro/Max, 2021)"; "MacBookPro18,3" = "MacBook Pro (14-inch, M1 Pro/Max, 2021)"; "MacBookPro18,4" = "MacBook Pro (14-inch, M1 Pro/Max, 2021)"; "Mac14,2" = "MacBook Air (13-inch, M2, 2022)"; "Mac14,5" = "MacBook Pro (14-inch, M2 Pro/Max, 2023)"; "Mac14,6" = "MacBook Pro (16-inch, M2 Pro/Max, 2023)"; "Mac14,7" = "MacBook Pro (13-inch, M2, 2022)"; "Mac14,9" = "MacBook Pro (14-inch, M2 Pro/Max, 2023)"; "Mac14,10" = "MacBook Pro (16-inch, M2 Pro/Max, 2023)"; "Mac14,15" = "MacBook Air (15-inch, M2, 2023)"; "Mac15,3" = "MacBook Pro (14-inch, M3, Nov 2023)"; "Mac15,6" = "MacBook Pro (14-inch, M3 Pro/Max, Nov 2023)"; "Mac15,9" = "MacBook Pro (14-inch, M3 Pro/Max, Nov 2023)"; "Mac15,11" = "MacBook Pro (14-inch, M3 Pro/Max, Nov 2023)"; "Mac15,7" = "MacBook Pro (16-inch, M3 Pro/Max, Nov 2023)"; "Mac15,8" = "MacBook Pro (16-inch, M3 Pro/Max, Nov 2023)"; "Mac15,10" = "MacBook Pro (16-inch, M3 Pro/Max, Nov 2023)"; "Mac15,12" = "MacBook Air (13-inch, M3, 2024)"; "Mac15,13" = "MacBook Air (15-inch, M3, 2024)"; "Mac16,8" = "Apple Silicon Mac (ID: Mac16,8)"; "MacBookAir6,1" = "MacBook Air (11-inch, Mid 2013/Early 2014)"; "MacBookAir6,2" = "MacBook Air (13-inch, Mid 2013/Early 2014)"; "MacBookAir7,1" = "MacBook Air (11-inch, Early 2015)"; "MacBookAir7,2" = "MacBook Air (13-inch, Early 2015/2017)"; "MacBookAir8,1" = "MacBook Air (Retina, 13-inch, 2018)"; "MacBookAir8,2" = "MacBook Air (Retina, 13-inch, 2019)"; "MacBookAir9,1" = "MacBook Air (Retina, 13-inch, 2020)"; "MacBookAir10,1" = "MacBook Air (M1, 2020)"; "iMac14,1" = "iMac (21.5-inch, Late 2013)"; "iMac14,2" = "iMac (27-inch, Late 2013)"; "iMac14,4" = "iMac (21.5-inch, Mid 2014)"; "iMac15,1" = "iMac (Retina 5K, 27-inch, Late 2014/Mid 2015)"; "iMac16,1" = "iMac (21.5-inch, Late 2015)"; "iMac16,2" = "iMac (Retina 4K, 21.5-inch, Late 2015)"; "iMac17,1" = "iMac (Retina 5K, 27-inch, Late 2015)"; "iMac18,1" = "iMac (21.5-inch, 2017)"; "iMac18,2" = "iMac (Retina 4K, 21.5-inch, 2017)"; "iMac18,3" = "iMac (Retina 5K, 27-inch, 2017)"; "iMac19,1" = "iMac (Retina 5K, 27-inch, 2019)"; "iMac19,2" = "iMac (Retina 4K, 21.5-inch, 2019)"; "iMac20,1" = "iMac (Retina 5K, 27-inch, 2020)"; "iMac20,2" = "iMac (Retina 5K, 27-inch, 2020)"; "iMac21,1" = "iMac (24-inch, M1, 2021)"; "iMac21,2" = "iMac (24-inch, M1, 2021)"; "Mac15,4" = "iMac (24-inch, M3, 2023)"; "Mac15,5" = "iMac (24-inch, M3, 2023)"; "Macmini7,1" = "Mac mini (Late 2014)"; "Macmini8,1" = "Mac mini (2018)"; "Macmini9,1" = "Mac mini (M1, 2020)"; "Mac14,3" = "Mac mini (M2, 2023)"; "Mac14,12" = "Mac mini (M2 Pro, 2023)"; "MacPro6,1" = "Mac Pro (Late 2013)"; "MacPro7,1" = "Mac Pro (2019)"; "Mac14,8" = "Mac Pro (M2 Ultra, 2023)"; "Mac13,1" = "Mac Studio (M1 Max, 2022)"; "Mac13,2" = "Mac Studio (M1 Ultra, 2022)"; "Mac14,13" = "Mac Studio (M2 Max, 2023)"; "Mac14,14" = "Mac Studio (M2 Ultra, 2023)" }; if ($modelNameMapping.ContainsKey($ModelIdentifier)) { return $modelNameMapping[$ModelIdentifier] } else { Write-Warning "Model Identifier '$ModelIdentifier' not found in mapping table. Returning original identifier."; return $ModelIdentifier } }
#endregion Helper Functions

#region Core Functions
# (Dell related functions are unchanged - condensed for brevity)
Function Connect-GraphAPI { param([Parameter(Mandatory = $true)][string[]]$Scopes); Import-Module Microsoft.Graph.Authentication -ErrorAction Stop; Import-Module Microsoft.Graph.Devices.CorporateManagement -ErrorAction Stop; Write-Host "Connecting to Microsoft Graph..."; try { Disconnect-MgGraph -ErrorAction SilentlyContinue; Connect-MgGraph -Scopes $scopes -ErrorAction Stop; $graphContext = Get-MgContext; Write-Host "Connected to Graph as '$($graphContext.Account)'." -ForegroundColor Green; return $true } catch { Write-Error "Microsoft Graph connection failed: $($_.Exception.Message)"; return $false } }
Function Get-IntuneDellDevices { Write-Host "Retrieving Dell Windows devices from Intune..."; try { $selectProps = @("Id", "DeviceName", "SerialNumber", "Manufacturer", "Model", "enrolledDateTime", "OperatingSystem"); Write-Host "Querying Microsoft Graph API for managed devices..."; $intuneDevices = Get-MgDeviceManagementManagedDevice -Filter "Manufacturer eq 'Dell Inc.' and OperatingSystem eq 'Windows'" -All -Property $selectProps -ErrorAction Stop; $dellDevices = $intuneDevices | Where-Object { -not [string]::IsNullOrWhiteSpace($_.SerialNumber) }; Write-Host "Found $($dellDevices.Count) Dell devices in Intune with serial numbers." -ForegroundColor Green; return $dellDevices } catch { Write-Error "Failed to retrieve Dell devices from Intune: $($_.Exception.Message)"; return $null } }
Function Get-DellApiAccessToken { param([Parameter(Mandatory = $true)][string]$ClientId, [Parameter(Mandatory = $true)][System.Security.SecureString]$ClientSecretSecure, [Parameter(Mandatory = $true)][string]$TokenUrl); Write-Host "Requesting Dell API Access Token..."; $credential = [System.Management.Automation.PSCredential]::new($ClientId, $ClientSecretSecure); $clientSecretPlain = $credential.GetNetworkCredential().Password; $body = @{ grant_type = 'client_credentials'; client_id = $ClientId; client_secret = $clientSecretPlain }; try { $tokenResponse = Invoke-RestMethod -Method Post -Uri $TokenUrl -Body $body -ContentType 'application/x-www-form-urlencoded' -UserAgent "PowerShell-DeviceAgeReportScript/1.68" -ErrorAction Stop; Write-Host "Dell API Token obtained." -ForegroundColor Green; return $tokenResponse.access_token } catch { $errorMessageText = "Dell API Token request failed."; if ($_.Exception.Response) { $errorMessageText += " Status: $($_.Exception.Response.StatusCode). Desc: $($_.Exception.Response.StatusDescription)."; try { $errorDetail = ($_.Exception.Response.Content | ConvertFrom-Json).error_description; if ($errorDetail) { $errorMessageText += " Detail: $errorDetail" } } catch {} } else { $errorMessageText += " Error: $($_.Exception.Message)" }; Write-Error $errorMessageText; return $null } finally { Clear-Variable clientSecretPlain -ErrorAction SilentlyContinue } }
Function Get-DellWarrantyInfoBatch { param([Parameter(Mandatory = $true)][string[]]$SerialNumbers, [Parameter(Mandatory = $true)][string]$AccessToken, [Parameter(Mandatory = $true)][string]$WarrantyUrlBase, [Parameter()][int]$BatchSize = 100); Write-Host "Querying Dell Warranty API (Batch: /asset-entitlements) in batches of $BatchSize for Dell devices..."; $allResults = [System.Collections.Generic.List[object]]::new(); $totalSerials = $SerialNumbers.Count; if ($totalSerials -eq 0) { Write-Host "No Dell serial numbers provided for batch query."; return $allResults }; for ($i = 0; $i -lt $totalSerials; $i += $BatchSize) { $batch = $SerialNumbers[$i..([math]::Min($i + $BatchSize - 1, $totalSerials - 1))]; $serialBatchString = ($batch -join ','); $warrantyUrl = $WarrantyUrlBase.TrimEnd('/') + "?servicetags=$([System.Web.HttpUtility]::UrlEncode($serialBatchString))"; $currentBatchNum = ($i / $BatchSize) + 1; $totalBatches = [math]::Ceiling($totalSerials / $BatchSize); Write-Host "Processing Dell Batch $currentBatchNum of $totalBatches ($($batch.Count) serials)..."; $headers = @{ "Authorization" = "Bearer $AccessToken"; "Accept" = "application/json" }; try { $batchResponse = Invoke-RestMethod -Method Get -Uri $warrantyUrl -Headers $headers -UserAgent "PowerShell-DeviceAgeReportScript/1.68" -ErrorAction Stop; if ($null -ne $batchResponse) { if ($batchResponse -isnot [array]) { $batchResponse = @($batchResponse) }; $allResults.AddRange($batchResponse); Write-Host "Dell Batch $currentBatchNum OK. Received $($batchResponse.Count)." } else { Write-Warning "Dell Batch $currentBatchNum null response."; foreach ($sn in $batch) { $allResults.Add([PSCustomObject]@{ serviceTag = $sn; ReportErrorMessage = "Dell API returned no data." }) } } } catch { $fullErrorMessage = "Dell Batch $currentBatchNum Failed (Start: '$($batch[0])')."; $simpleErrorMessage = "Dell API Call Failed"; if ($_.Exception.Response) { $statusCode = if ($_.Exception.Response.StatusCode) { $_.Exception.Response.StatusCode.value__ } else { 'N/A' }; $statusDescription = if ($_.Exception.Response.StatusDescription) { $_.Exception.Response.StatusDescription } else { 'N/A' }; $fullErrorMessage += " Status: $statusCode $statusDescription."; $simpleErrorMessage = "Dell API Call Failed - Status $statusCode"; } else { $fullErrorMessage += " Error: $($_.Exception.Message)"; $simpleErrorMessage = "Dell API Call Failed (Network/Other)" }; Write-Warning $fullErrorMessage; foreach ($sn in $batch) { $allResults.Add([PSCustomObject]@{ serviceTag = $sn; ReportErrorMessage = $simpleErrorMessage }) } } } ; Write-Host "Dell API batch queries complete." -ForegroundColor Green; return $allResults }
Function Get-DellWarrantyInfoSingleTag { param([Parameter(Mandatory = $true)][string]$SerialNumber, [Parameter(Mandatory = $true)][string]$AccessToken, [Parameter(Mandatory = $true)][string]$SingleTagUrlBase); $singleResult = $null; $warrantyUrl = $SingleTagUrlBase.TrimEnd('/') + "?servicetag=$([System.Web.HttpUtility]::UrlEncode($SerialNumber))"; $headers = @{ "Authorization" = "Bearer $AccessToken"; "Accept" = "application/json" }; try { Write-Host "   Querying Dell single tag: $SerialNumber..." -NoNewline; $singleResponse = Invoke-RestMethod -Method Get -Uri $warrantyUrl -Headers $headers -UserAgent "PowerShell-DeviceAgeReportScript/1.68" -ErrorAction Stop; if ($null -ne $singleResponse) { $singleResult = $singleResponse; if ($singleResult -is [PSObject] -and -not ($singleResult.PSObject.Properties.Name -contains 'serviceTag')) { $singleResult | Add-Member -MemberType NoteProperty -Name "serviceTag" -Value $SerialNumber -Force; Write-Host " OK (Added serviceTag)." -ForegroundColor Green } elseif ($singleResult -is [PSObject] -and $singleResult.serviceTag -ne $SerialNumber) { Write-Warning "   Mismatch between queried Dell serial '$SerialNumber' and response serial '$($singleResult.serviceTag)'"; Write-Host " OK (Tag Mismatch)." -ForegroundColor Yellow } else { Write-Host " OK." -ForegroundColor Green } } else { Write-Host " Null response for Dell single tag." -ForegroundColor Yellow; $singleResult = [PSCustomObject]@{ serviceTag = $SerialNumber; ReportErrorMessage = "Dell API returned no data (Single)" } } } catch { $fullErrorMessage = "[Dell SingleTagRetry $SerialNumber] Failed."; $simpleErrorMessage = "Dell API Call Failed (Single)"; if ($_.Exception.Response) { $statusCode = if ($_.Exception.Response.StatusCode) { $_.Exception.Response.StatusCode.value__ } else { 'N/A' }; $statusDescription = if ($_.Exception.Response.StatusDescription) { $_.Exception.Response.StatusDescription } else { 'N/A' }; $fullErrorMessage += " Status: $statusCode $statusDescription."; $simpleErrorMessage = "Dell API Call Failed - Status $statusCode (Single)" } else { $fullErrorMessage += " Error: $($_.Exception.Message)"; $simpleErrorMessage = "Dell API Call Failed (Network/Other Single)" }; Write-Warning $fullErrorMessage; $singleResult = [PSCustomObject]@{ serviceTag = $SerialNumber; ReportErrorMessage = $simpleErrorMessage }; Write-Host " FAILED." -ForegroundColor Red }; return $singleResult }

Function Connect-JamfAPI { 
    param( 
        [Parameter(Mandatory = $true)][string]$JamfBaseUrl, 
        [Parameter(Mandatory = $true)][string]$ApiClientId, 
        [Parameter(Mandatory = $true)][System.Security.SecureString]$ApiClientSecretSecure 
    ) 
    Write-Host "Requesting Jamf Pro API Access Token from $JamfBaseUrl using OAuth Client Credentials..."
    $tokenUrl = "$($JamfBaseUrl.TrimEnd('/'))/api/oauth/token" 
    
    $apiClientSecretPlain = $null
    $Ptr = [System.IntPtr]::Zero
    try {
        $Ptr = [System.Runtime.InteropServices.Marshal]::SecureStringToCoTaskMemUnicode($ApiClientSecretSecure)
        $apiClientSecretPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringUni($Ptr)
    }
    catch {
        Write-Error "Failed to convert Jamf API Client Secret from SecureString: $($_.Exception.Message)"
        return $null
    }
    finally {
        if ($Ptr -ne [System.IntPtr]::Zero) {
            [System.Runtime.InteropServices.Marshal]::ZeroFreeCoTaskMem($Ptr)
        }
    }

    if ([string]::IsNullOrWhiteSpace($apiClientSecretPlain)) {
        Write-Error "Jamf API Client Secret became empty after conversion. Cannot proceed."
        return $null
    }
    
    $body = @{
        client_id     = $ApiClientId
        client_secret = $apiClientSecretPlain
        grant_type    = "client_credentials"
    }
    
    $headers = @{
        "Content-Type" = "application/x-www-form-urlencoded"
        "Accept"       = "application/json" 
    }

    try { 
        $tokenResponse = Invoke-RestMethod -Method Post -Uri $tokenUrl -Body $body -Headers $headers -UserAgent "PowerShell-DeviceAgeReportScript/1.68" -ErrorAction Stop
        
        if ($tokenResponse.access_token -and $tokenResponse.expires_in) { 
            $expiryTime = (Get-Date).AddSeconds($tokenResponse.expires_in).ToString("yyyy-MM-dd HH:mm:ss UTC")
            Write-Host "Jamf API Token obtained. Expires in: $($tokenResponse.expires_in) seconds (approx. $expiryTime)." -ForegroundColor Green
            $script:jamfAccessToken = $tokenResponse.access_token 
            return $tokenResponse.access_token 
        }
        else { 
            Write-Error "Jamf API Token response did not contain expected 'access_token' or 'expires_in'."
            Write-Warning "Raw Token Response: $($tokenResponse | ConvertTo-Json -Depth 3)"
            return $null 
        } 
    }
    catch { 
        $errorResponseText = "Could not determine error response body."
        $statusCode = "N/A"
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode.value__ 
            try {
                if ($_.Exception.Response.Content) { 
                    $errorResponseText = $_.Exception.Response.Content.ReadAsStringAsync().GetAwaiter().GetResult()
                }
                elseif ($_.Exception.Response.GetResponseStream) { 
                    $stream = $_.Exception.Response.GetResponseStream()
                    if ($stream.CanRead) {
                        $reader = New-Object System.IO.StreamReader($stream)
                        $errorResponseText = $reader.ReadToEnd(); $reader.Close()
                    }; $stream.Close()
                }
            }
            catch { $errorResponseText = "Failed to read detailed error stream content: $($_.Exception.Message)" }
        }
        $errorMessage = "Jamf API Token request failed. Status: $statusCode, Response: $errorResponseText. PowerShell Exception: $($_.Exception.Message)"
        Write-Error $errorMessage
        return $null 
    }
    finally { 
        # $apiClientSecretPlain is a local variable and will go out of scope.
        # If it were a script or global variable, we'd clear it here.
    } 
}

Function Get-JamfMacDevices {
    param(
        [Parameter(Mandatory = $true)][string]$JamfBaseUrl,
        [Parameter(Mandatory = $true)][string]$AccessToken,
        [Parameter(Mandatory = $true)][int]$PageSize 
    )
    Write-Host "Retrieving macOS devices from Jamf Pro..."
    $allMacDevicesList = [System.Collections.Generic.List[PSCustomObject]]::new()
    $currentPage = 0; $morePagesAvailable = $true
    $sectionsToInclude = "GENERAL,HARDWARE,OPERATING_SYSTEM,PURCHASING,USER_AND_LOCATION"

    while ($morePagesAvailable) {
        $apiUrl = "$($JamfBaseUrl.TrimEnd('/'))/api/v1/computers-inventory?section=$($sectionsToInclude -join ',')&page=$currentPage&page-size=$PageSize&sort=id:asc"
        $headers = @{ "Authorization" = "Bearer $AccessToken"; "Accept" = "application/json" }
        Write-Host "Fetching Jamf inventory page $currentPage (Size: $PageSize)..."
        try {
            $response = Invoke-RestMethod -Method Get -Uri $apiUrl -Headers $headers -UserAgent "PowerShell-DeviceAgeReportScript/1.68" -ErrorAction Stop
            if ($response.results -and $response.results.Count -gt 0) {
                foreach ($resultItem in $response.results) {
                    $allMacDevicesList.Add($resultItem) 
                }
                Write-Host "  Retrieved $($response.results.Count) devices on page $currentPage. Total so far: $($allMacDevicesList.Count)."
                if ($response.results.Count -lt $PageSize -or ($response.totalCount -le $allMacDevicesList.Count)) { 
                    $morePagesAvailable = $false 
                }
                else { $currentPage++ }
            }
            else { Write-Host "  No more devices found on page $currentPage or empty results."; $morePagesAvailable = $false }
        }
        catch {
            $errorResponseText = "Could not determine error response body."; $statusCode = "N/A"
            if ($_.Exception.Response) {
                $statusCode = $_.Exception.Response.StatusCode.value__ 
                try {
                    if ($_.Exception.Response.Content) { 
                        $errorResponseText = $_.Exception.Response.Content.ReadAsStringAsync().GetAwaiter().GetResult()
                    }
                    elseif ($_.Exception.Response.GetResponseStream) { 
                        $stream = $_.Exception.Response.GetResponseStream()
                        if ($stream.CanRead) {
                            $reader = New-Object System.IO.StreamReader($stream)
                            $errorResponseText = $reader.ReadToEnd(); $reader.Close()
                        }; $stream.Close()
                    }
                }
                catch { $errorResponseText = "Failed to read detailed error stream content: $($_.Exception.Message)" }
            }
            Write-Error "Failed to retrieve Jamf inventory page $currentPage. Status: $statusCode, Response: $errorResponseText. PowerShell Exception: $($_.Exception.Message)"; $morePagesAvailable = $false
        }
        if ($morePagesAvailable) { Start-Sleep -Milliseconds 300 } 
    }
    Write-Host "Retrieved a total of $($allMacDevicesList.Count) Mac devices from Jamf Pro." -ForegroundColor Green
    return $allMacDevicesList
}
#endregion Core Functions

#region Data Processing Functions
Function ConvertTo-DellWarrantyReportData { 
    param( 
        [Parameter(Mandatory = $true)][System.Collections.Generic.List[object]]$DellApiResults, 
        [Parameter(Mandatory = $true)][array]$IntuneDellDevices, 
        [Parameter(Mandatory = $false)][switch]$IsRetryPass = $false 
    )
    if (-not $IsRetryPass) { Write-Host "Converting Dell API results and generating report data..." }
    $reportDataList = [System.Collections.Generic.List[PSCustomObject]]::new()
    $serialsToRetryList = [System.Collections.Generic.List[string]]::new()
    $today = Get-Date    
    $intuneDeviceLookup = $IntuneDellDevices | Group-Object -Property SerialNumber -AsHashTable -AsString

    foreach ($item in $DellApiResults) {
        $serial = $null; $errorMessage = $null; $shipDate = $null;
        $warrantyStartDate = $null; $warrantyEndDate = $null; $calculatedAgeDays = $null; 
        $calculatedAgeYears = $null; 
        $productLineDesc = $null; $latestWarrantyDesc = $null; $deviceName = "N/A";
        $isExpired = $null; $entraJoinDate = $null
        $needsRetry = $false

        $dbgRawShipDate = "N/A"; $dbgParsedShipDate = "N/A"; $dbgShipDateParseError = "N/A"
        $dbgRawEntitlementStartDate = "N/A"; $dbgParsedEntitlementStartDate = "N/A"; $dbgEntitlementStartDateParseError = "N/A"
        $dbgRawEntitlementEndDate = "N/A"; $dbgParsedEntitlementEndDate = "N/A"; $dbgEntitlementEndDateParseError = "N/A"
        $dbgAgeCalcBaseDate = "N/A"; $dbgAgeInDaysRaw = "N/A"

        try {
            if ($item -is [PSCustomObject] -and $item.PSObject.Properties.Name -contains 'ReportErrorMessage') { 
                $serial = $item.serviceTag; $errorMessage = $item.ReportErrorMessage; $isExpired = "Error" 
            }
            elseif ($item -is [PSObject] -and $item.PSObject.Properties.Name -contains 'serviceTag') {
                $serial = $item.serviceTag
                if (-not $IsRetryPass -and $serial) { Write-Host "DEBUG Processing Dell SN: $serial" -ForegroundColor DarkGray }

                if ($item.PSObject.Properties.Name -contains 'invalid' -and $item.invalid -eq $true) { 
                    if (-not $errorMessage) { $errorMessage = "Invalid Service Tag (per Dell API)" } else { $errorMessage += "; Invalid Service Tag (per Dell API)" }; $isExpired = "N/A (Invalid Tag)" 
                }
                else {
                    if ($item.PSObject.Properties.Name -contains 'productLineDescription') { $productLineDesc = $item.productLineDescription }
                    $shipDateStr = $null
                    if ($item.PSObject.Properties.Name -contains 'shipDate') { $shipDateStr = $item.shipDate }; $dbgRawShipDate = $shipDateStr
                    $shipDateParsedSuccessfully = $false
                    if (-not [string]::IsNullOrWhiteSpace($shipDateStr)) { 
                        try { 
                            $shipDate = [System.DateTimeOffset]::Parse($shipDateStr, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime
                            $dbgParsedShipDate = $shipDate.ToString("u")
                            $age = $today - $shipDate; $dbgAgeInDaysRaw = $age.TotalDays
                            $calculatedAgeDays = [math]::Round($age.TotalDays); $calculatedAgeYears = if ($calculatedAgeDays -ge 0) { [math]::Round($calculatedAgeDays / 365.25, 2) } else { $null }
                            $shipDateParsedSuccessfully = $true; $dbgAgeCalcBaseDate = "ShipDate: $($shipDate.ToString('u'))" 
                        }
                        catch { $dbgShipDateParseError = $_.Exception.Message; if (-not $IsRetryPass) { Write-Warning "[Dell Serial: $serial] Could not parse ShipDate '$shipDateStr'. Error: $dbgShipDateParseError" }; $shipDate = $null } 
                    }
                    else { $shipDate = $null }

                    $entitlements = $null
                    if ($item.PSObject.Properties.Name -contains 'entitlements') { $entitlements = $item.entitlements }
                    if ($entitlements -is [array] -and $entitlements.Count -gt 0) {
                        $validEntitlements = @()
                        foreach ($entitlement in $entitlements) {
                            $tempStartDate = $null; $tempEndDate = $null; $_dbgTempStartDateError = "N/A"; $_dbgTempEndDateError = "N/A"
                            $dbgRawEntitlementStartDate = $entitlement.startDate; $dbgRawEntitlementEndDate = $entitlement.endDate
                            if ($entitlement.PSObject.Properties.Name -contains 'startDate' -and -not [string]::IsNullOrWhiteSpace($entitlement.startDate)) { try { $tempStartDate = [System.DateTimeOffset]::Parse($entitlement.startDate, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime; $dbgParsedEntitlementStartDate = $tempStartDate.ToString("u") } catch { $_dbgTempStartDateError = $_.Exception.Message; if (-not $IsRetryPass) { Write-Warning "[Dell Serial: $serial] Entitlement StartDate parse error ('$($entitlement.startDate)'): $_dbgTempStartDateError" } } }
                            if ($entitlement.PSObject.Properties.Name -contains 'endDate' -and -not [string]::IsNullOrWhiteSpace($entitlement.endDate)) { try { $tempEndDate = [System.DateTimeOffset]::Parse($entitlement.endDate, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime; $dbgParsedEntitlementEndDate = $tempEndDate.ToString("u") } catch { $_dbgTempEndDateError = $_.Exception.Message; if (-not $IsRetryPass) { Write-Warning "[Dell Serial: $serial] Entitlement EndDate parse error ('$($entitlement.endDate)'): $_dbgTempEndDateError" } } }
                            if ($tempStartDate -and $tempEndDate) { $validEntitlements += [PSCustomObject]@{ Entitlement = $entitlement; ParsedStartDate = $tempStartDate; ParsedEndDate = $tempEndDate } }
                        }
                        if ($validEntitlements.Count -gt 0) {
                            $earliestStartDateObj = $validEntitlements | Sort-Object ParsedStartDate | Select-Object -First 1
                            $latestEndDateObj = $validEntitlements | Sort-Object ParsedEndDate -Descending | Select-Object -First 1
                            if ($earliestStartDateObj) { $warrantyStartDate = $earliestStartDateObj.ParsedStartDate }
                            if ($latestEndDateObj) { $warrantyEndDate = $latestEndDateObj.ParsedEndDate }
                            if ($latestEndDateObj) { if ($latestEndDateObj.Entitlement.PSObject.Properties.Name -contains 'serviceLevelDescription') { $latestWarrantyDesc = $latestEndDateObj.Entitlement.serviceLevelDescription } }
                            if (-not $shipDateParsedSuccessfully -and $null -ne $warrantyStartDate) {
                                $age = $today - $warrantyStartDate; $dbgAgeInDaysRaw = $age.TotalDays
                                $calculatedAgeDays = [math]::Round($age.TotalDays); $calculatedAgeYears = if ($calculatedAgeDays -ge 0) { [math]::Round($calculatedAgeDays / 365.25, 2) } else { $null }
                                $dbgAgeCalcBaseDate = "WarrantyStartDate: $($warrantyStartDate.ToString('u'))"
                            }
                        }
                        else { if (-not $IsRetryPass) { Write-Warning "[Dell Serial: $serial] Entitlements found but none had valid parsable dates." } }
                    }
                    else { if (-not $IsRetryPass) { Write-Warning "[Dell Serial: $serial] Entitlements property missing/empty." } }

                    if ($null -ne $warrantyEndDate) { $isExpired = if ($warrantyEndDate.Date -lt $today.Date) { "YES" } else { "NO" } } else { $isExpired = "Unknown"; if (-not $IsRetryPass) { $needsRetry = $true; Write-Warning "[Dell Serial: $serial] Cannot determine expiry; WarrantyEndDate unknown or unparsable." } }
                }
            }
            else { $errorMessage = "Unexpected Dell API result format" }
        }
        catch { $processError = $_.Exception.Message; Write-Warning "Error processing Dell data for serial '$serial': $processError"; $errorMessage = "Data Parsing Error (Dell)"; if (-not $serial -and $item -is [PSObject] -and $item.PSObject.Properties.Name -contains 'serviceTag') { $serial = $item.serviceTag } }
        
        $intuneDevice = $null
        if ($serial -and $intuneDeviceLookup.ContainsKey($serial)) { $intuneDevice = $intuneDeviceLookup[$serial][0]; $deviceName = if ($intuneDevice) { $intuneDevice.DeviceName } else { "N/A (Intune Lookup Err)" }; if ([string]::IsNullOrWhiteSpace($productLineDesc) -and $intuneDevice -and -not [string]::IsNullOrWhiteSpace($intuneDevice.Model)) { $productLineDesc = $intuneDevice.Model }; if ($intuneDevice -and $intuneDevice.enrolledDateTime) { $entraJoinDate = ($intuneDevice.enrolledDateTime).ToString("yyyy-MM-dd") } } else { $deviceName = "N/A (Serial '$serial' not in provided Intune Dell list)" }
        if ([string]::IsNullOrWhiteSpace($productLineDesc)) { $productLineDesc = if ($serial) { "Dell Device (SN: $serial)" } else { "Unknown Dell Product" } }
        if ($needsRetry -and $null -eq $errorMessage -and $isExpired -ne "N/A (Invalid Tag)" -and (-not $IsRetryPass)) { $serialsToRetryList.Add($serial) }
        
        if (-not $IsRetryPass -and $serial) { Write-Host ("DEBUG [SN: {0}] RawShip: '{1}', ParsedShip: '{2}', ShipErr: '{3}'. RawEntStart: '{4}', ParsedEntStart: '{5}', EntStartErr: '{6}'. RawEntEnd: '{7}', ParsedEntEnd: '{8}', EntEndErr: '{9}'. AgeBase: '{10}', AgeRawDays: '{11}', CalcAgeDays: '{12}', CalcAgeYears: '{14}', PLD: '{13}'" -f $serial, $dbgRawShipDate, $dbgParsedShipDate, $dbgShipDateParseError, $dbgRawEntitlementStartDate, $dbgParsedEntitlementStartDate, $dbgEntitlementStartDateParseError, $dbgRawEntitlementEndDate, $dbgParsedEntitlementEndDate, $dbgEntitlementEndDateParseError, $dbgAgeCalcBaseDate, $dbgAgeInDaysRaw, $calculatedAgeDays, $productLineDesc, $calculatedAgeYears) -ForegroundColor DarkCyan }
        
        $outputObject = [PSCustomObject]@{ Manufacturer = "Dell"; DeviceName = $deviceName; SerialNumber = $serial; ProductLineDescription = $productLineDesc; ShipDate = if ($shipDate) { $shipDate.ToString("yyyy-MM-dd") } else { $null }; WarrantyStartDate = if ($warrantyStartDate) { $warrantyStartDate.ToString("yyyy-MM-dd") } else { $null }; WarrantyEndDate = if ($warrantyEndDate) { $warrantyEndDate.ToString("yyyy-MM-dd") } else { $null }; IsExpired = $isExpired; LatestWarrantyDescription = $latestWarrantyDesc; CalculatedAge_Days = $calculatedAgeDays; CalculatedAge_Years = $calculatedAgeYears; EntraJoinDate = $entraJoinDate; ErrorMessage = $errorMessage }
        $reportDataList.Add($outputObject)
    }
    if (-not $IsRetryPass) { Write-Host "Initial Dell conversion complete. Processed $($reportDataList.Count) records." -ForegroundColor Green }
    if (-not $IsRetryPass) { $uniqueSerialsToRetry = $serialsToRetryList | Sort-Object -Unique; return @{ ReportList = $reportDataList; SerialsToRetry = [System.Collections.Generic.List[string]]$uniqueSerialsToRetry } } else { return $reportDataList }
}
Function Update-DellReportWithBatchRetryData { param( [Parameter(Mandatory = $true)][System.Collections.Generic.List[PSCustomObject]]$OriginalReportList, [Parameter(Mandatory = $true)][System.Collections.Generic.List[PSCustomObject]]$RetryReportItems ); Write-Host "Processing Dell batch retry results..."; $updatedCount = 0; $stillUnknownCount = 0; $originalReportHashTable = $OriginalReportList | Where-Object { $_.Manufacturer -eq "Dell" } | Group-Object -Property SerialNumber -AsHashTable -AsString; foreach ($retryItem in $RetryReportItems) { $serial = $retryItem.SerialNumber; if ($serial -and $originalReportHashTable.ContainsKey($serial)) { $originalRecord = $originalReportHashTable[$serial][0]; if ($originalRecord.IsExpired -eq 'Unknown' -and $retryItem.IsExpired -ne 'Unknown' -and $retryItem.IsExpired -ne 'Error' -and $retryItem.IsExpired -ne 'N/A (Invalid Tag)') { $originalRecord.ProductLineDescription = $retryItem.ProductLineDescription; $originalRecord.WarrantyStartDate = $retryItem.WarrantyStartDate; $originalRecord.WarrantyEndDate = $retryItem.WarrantyEndDate; $originalRecord.LatestWarrantyDescription = $retryItem.LatestWarrantyDescription; $originalRecord.IsExpired = $retryItem.IsExpired; $originalRecord.ErrorMessage = $retryItem.ErrorMessage; if ($null -eq $originalRecord.ShipDate -and $null -ne $originalRecord.WarrantyStartDate -and $null -ne $retryItem.CalculatedAge_Days) { $originalRecord.CalculatedAge_Days = $retryItem.CalculatedAge_Days; $originalRecord.CalculatedAge_Years = $retryItem.CalculatedAge_Years; Write-Host "   [Dell SN: $serial] Age updated on retry." -ForegroundColor DarkGray }; $updatedCount++ } elseif ($originalRecord.IsExpired -eq 'Unknown') { $originalRecord.ErrorMessage = $retryItem.ErrorMessage; $stillUnknownCount++ } } else { Write-Warning "Dell SN '$serial' from batch retry not in original Dell list." } }; Write-Host "Dell batch retry merge: $updatedCount updated, $stillUnknownCount still unknown." -ForegroundColor Green }
Function Update-DellReportWithSingleTagData { param( [Parameter(Mandatory = $true)][System.Collections.Generic.List[PSCustomObject]]$OriginalReportList, [Parameter(Mandatory = $true)][System.Collections.Generic.List[PSCustomObject]]$SingleTagReportItems ); Write-Host "Processing Dell single-tag retry results..."; $updatedCount = 0; $stillUnknownFinalCount = 0; $originalReportHashTable = $OriginalReportList | Where-Object { $_.Manufacturer -eq "Dell" } | Group-Object -Property SerialNumber -AsHashTable -AsString; foreach ($retryItem in $SingleTagReportItems) { $serial = $retryItem.SerialNumber; if ($serial -and $originalReportHashTable.ContainsKey($serial)) { $originalRecord = $originalReportHashTable[$serial][0]; if ($originalRecord.IsExpired -eq 'Unknown' -and $retryItem.IsExpired -ne 'Unknown' -and $retryItem.IsExpired -ne 'Error' -and $retryItem.IsExpired -ne 'N/A (Invalid Tag)') { $originalRecord.ProductLineDescription = $retryItem.ProductLineDescription; $originalRecord.WarrantyStartDate = $retryItem.WarrantyStartDate; $originalRecord.WarrantyEndDate = $retryItem.WarrantyEndDate; $originalRecord.LatestWarrantyDescription = $retryItem.LatestWarrantyDescription; $originalRecord.IsExpired = $retryItem.IsExpired; $originalRecord.ErrorMessage = $retryItem.ErrorMessage; if ($null -eq $originalRecord.ShipDate -and $null -ne $originalRecord.WarrantyStartDate -and $null -ne $retryItem.CalculatedAge_Days) { $originalRecord.CalculatedAge_Days = $retryItem.CalculatedAge_Days; $originalRecord.CalculatedAge_Years = $retryItem.CalculatedAge_Years; Write-Host "   [Dell SN: $serial] Age updated on single-tag retry." -ForegroundColor DarkGray }; $updatedCount++ } elseif ($originalRecord.IsExpired -eq 'Unknown') { $originalRecord.ErrorMessage = $retryItem.ErrorMessage; $stillUnknownFinalCount++ } } else { Write-Warning "Dell SN '$serial' from single-tag retry not in original Dell list." } }; Write-Host "Dell single-tag retry merge: $updatedCount updated, $stillUnknownFinalCount still unknown." -ForegroundColor Green }
Function ConvertTo-AppleDeviceReportData { param( [Parameter(Mandatory = $true)] [System.Collections.Generic.List[object]]$RawJamfDevices ); Write-Host "Converting Jamf API results for Apple devices..."; $reportDataList = [System.Collections.Generic.List[PSCustomObject]]::new(); $today = Get-Date; if ($RawJamfDevices -and $RawJamfDevices.Count -gt 0 -and $RawJamfDevices[0]) { Write-Host "DEBUG: Structure of the first raw Jamf device object from Get-JamfMacDevices:" -ForegroundColor Yellow; Write-Host "Purchasing Data (first device):"; $RawJamfDevices[0].purchasing | ConvertTo-Json -Depth 3 -Compress | Write-Host -ForegroundColor Yellow; Write-Host "General Data (first device):"; $RawJamfDevices[0].general | ConvertTo-Json -Depth 3 -Compress | Write-Host -ForegroundColor Yellow }; foreach ($jamfDevice in $RawJamfDevices) { $serialNumber = $null; $deviceName = $null; $productLineDescription = $null; $purchaseDate = $null; $warrantyEndDate = $null; $lastEnrolledDate = $null; $initialEntryDate = $null; $ageCalculationStartDate = $null; $calculatedAgeDays = $null; $calculatedAgeYears = $null; $isExpired = "Unknown"; $errorMessage = $null; $dbgRawPoDate = "N/A"; $dbgParsedPoDate = "N/A"; $dbgPoDateErr = "N/A"; $dbgRawWarrantyExp = "N/A"; $dbgParsedWarrantyExp = "N/A"; $dbgWarrantyExpErr = "N/A"; $dbgRawEnrollDate = "N/A"; $dbgParsedEnrollDate = "N/A"; $dbgEnrollDateErr = "N/A"; $dbgRawInitialEntryDate = "N/A"; $dbgParsedInitialEntryDate = "N/A"; $dbgInitialEntryDateErr = "N/A"; $dbgAgeCalcBaseDate = "N/A"; $dbgAgeInDaysRaw = "N/A"; try { if ($jamfDevice.hardware -and $jamfDevice.hardware.PSObject.Properties.Name -contains 'serialNumber' -and -not [string]::IsNullOrWhiteSpace($jamfDevice.hardware.serialNumber)) { $serialNumber = $jamfDevice.hardware.serialNumber } else { Write-Warning "Skipping Jamf device due to missing serial number. Device ID: $($jamfDevice.id)"; continue }; Write-Host "DEBUG Processing Apple SN: $serialNumber" -ForegroundColor DarkMagenta; $deviceName = $jamfDevice.general.name; $rawModelIdentifier = $jamfDevice.hardware.modelIdentifier; $productLineDescription = Convert-ModelIdentifierToName -ModelIdentifier $rawModelIdentifier; if ($productLineDescription -eq $rawModelIdentifier -and -not [string]::IsNullOrWhiteSpace($rawModelIdentifier)) { Write-Host "DEBUG [Apple SN: $serialNumber] Model ID '$rawModelIdentifier' was not in mapping table." -ForegroundColor DarkYellow }; if ([string]::IsNullOrWhiteSpace($productLineDescription)) { $productLineDescription = "Apple Device (SN: $serialNumber)" }; if ($jamfDevice.general -and $jamfDevice.general.PSObject.Properties.Name -contains 'initialEntryDate' -and -not [string]::IsNullOrWhiteSpace($jamfDevice.general.initialEntryDate)) { $dbgRawInitialEntryDate = $jamfDevice.general.initialEntryDate; try { $initialEntryDate = [datetime]::ParseExact($dbgRawInitialEntryDate, "yyyy-MM-dd", [System.Globalization.CultureInfo]::InvariantCulture); $dbgParsedInitialEntryDate = $initialEntryDate.ToString("u") } catch { $dbgInitialEntryDateErr = $_.Exception.Message; Write-Warning "[Apple SN: $serialNumber] Could not parse InitialEntryDate '$dbgRawInitialEntryDate'. Error: $dbgInitialEntryDateErr" } }; if ($jamfDevice.purchasing -and $jamfDevice.purchasing.PSObject.Properties.Name -contains 'poDate' -and -not [string]::IsNullOrWhiteSpace($jamfDevice.purchasing.poDate)) { $dbgRawPoDate = $jamfDevice.purchasing.poDate; try { $purchaseDate = [System.DateTimeOffset]::Parse($dbgRawPoDate, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime; $dbgParsedPoDate = $purchaseDate.ToString("u") } catch { $dbgPoDateErr = $_.Exception.Message; Write-Warning "[Apple SN: $serialNumber] Could not parse PurchaseDate '$dbgRawPoDate'. Error: $dbgPoDateErr" } }; if ($jamfDevice.purchasing -and $jamfDevice.purchasing.PSObject.Properties.Name -contains 'warrantyDate' -and -not [string]::IsNullOrWhiteSpace($jamfDevice.purchasing.warrantyDate)) { $dbgRawWarrantyExp = $jamfDevice.purchasing.warrantyDate; try { $warrantyEndDate = [System.DateTimeOffset]::Parse($dbgRawWarrantyExp, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime; $dbgParsedWarrantyExp = $warrantyEndDate.ToString("u") } catch { $dbgWarrantyExpErr = $_.Exception.Message; Write-Warning "[Apple SN: $serialNumber] Could not parse Warranty Expiry (from warrantyDate) '$dbgRawWarrantyExp'. Error: $dbgWarrantyExpErr" } } elseif ($jamfDevice.purchasing -and $jamfDevice.purchasing.PSObject.Properties.Name -contains 'warrantyExpiresUtc' -and -not [string]::IsNullOrWhiteSpace($jamfDevice.purchasing.warrantyExpiresUtc)) { $dbgRawWarrantyExp = $jamfDevice.purchasing.warrantyExpiresUtc; try { $warrantyEndDate = [System.DateTimeOffset]::Parse($dbgRawWarrantyExp, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime; $dbgParsedWarrantyExp = $warrantyEndDate.ToString("u") } catch { $dbgWarrantyExpErr = $_.Exception.Message; Write-Warning "[Apple SN: $serialNumber] Could not parse Warranty Expiry (from warrantyExpiresUtc) '$dbgRawWarrantyExp'. Error: $dbgWarrantyExpErr" } }; if ($jamfDevice.general -and $jamfDevice.general.PSObject.Properties.Name -contains 'lastEnrolledDate' -and -not [string]::IsNullOrWhiteSpace($jamfDevice.general.lastEnrolledDate)) { $dbgRawEnrollDate = $jamfDevice.general.lastEnrolledDate; try { $lastEnrolledDate = [System.DateTimeOffset]::Parse($dbgRawEnrollDate, [System.Globalization.CultureInfo]::InvariantCulture, [System.Globalization.DateTimeStyles]::AssumeUniversal).UtcDateTime; $dbgParsedEnrollDate = $lastEnrolledDate.ToString("u") } catch { $dbgEnrollDateErr = $_.Exception.Message; Write-Warning "[Apple SN: $serialNumber] Could not parse Last Enrolled Date '$dbgRawEnrollDate'. Error: $dbgEnrollDateErr" } }; if ($initialEntryDate) { $ageCalculationStartDate = $initialEntryDate; $dbgAgeCalcBaseDate = "InitialEntryDate: $($initialEntryDate.ToString('u'))" } elseif ($purchaseDate) { $ageCalculationStartDate = $purchaseDate; $dbgAgeCalcBaseDate = "PurchaseDate: $($purchaseDate.ToString('u'))" } elseif ($lastEnrolledDate) { $ageCalculationStartDate = $lastEnrolledDate; $dbgAgeCalcBaseDate = "LastEnrolledDate: $($lastEnrolledDate.ToString('u'))" }; if ($ageCalculationStartDate) { $age = $today - $ageCalculationStartDate; $dbgAgeInDaysRaw = $age.TotalDays; $calculatedAgeDays = [math]::Round($age.TotalDays); $calculatedAgeYears = if ($calculatedAgeDays -ge 0) { [math]::Round($calculatedAgeDays / 365.25, 2) } else { $null } }; if ($warrantyEndDate) { $isExpired = if ($warrantyEndDate.Date -lt $today.Date) { "YES" } else { "NO" } }; Write-Host ("DEBUG [AppleSN: {0}] RawInitial: '{1}', ParsedInitial: '{2}', InitErr: '{3}'. RawPO: '{4}', ParsedPO: '{5}', POErr: '{6}'. RawWarrExp: '{7}', ParsedWarrExp: '{8}', WarrExpErr: '{9}'. RawEnroll: '{10}', ParsedEnroll: '{11}', EnrollErr: '{12}'. AgeBase: '{13}', AgeRaw: '{14}', CalcAgeDays: '{15}', CalcAgeYrs: '{16}', PLD: '{17}'" -f $serialNumber, $dbgRawInitialEntryDate, $dbgParsedInitialEntryDate, $dbgInitialEntryDateErr, $dbgRawPoDate, $dbgParsedPoDate, $dbgPoDateErr, $dbgRawWarrantyExp, $dbgParsedWarrantyExp, $dbgWarrantyExpErr, $dbgRawEnrollDate, $dbgParsedEnrollDate, $dbgEnrollDateErr, $dbgAgeCalcBaseDate, $dbgAgeInDaysRaw, $calculatedAgeDays, $calculatedAgeYears, $productLineDescription ) -ForegroundColor DarkMagenta; if (-not $ageCalculationStartDate) { $errorMessage = "Missing InitialEntry/Purchase/Enroll date for age calc" }; if ($dbgInitialEntryDateErr -ne "N/A" -or $dbgPoDateErr -ne "N/A" -or $dbgWarrantyExpErr -ne "N/A" -or $dbgEnrollDateErr -ne "N/A") { $errorMessage = ($errorMessage | Where-Object { $_ } | ForEach-Object { "$_;" }) + "Date parsing errors occurred (see console warnings)." } } catch { $errorMessage = "Critical error processing Jamf device SN '$serialNumber': $($_.Exception.Message)"; Write-Error $errorMessage }; $outputObject = [PSCustomObject]@{ Manufacturer = "Apple"; DeviceName = $deviceName; SerialNumber = $serialNumber; ProductLineDescription = $productLineDescription; ShipDate = $null; WarrantyStartDate = if ($ageCalculationStartDate) { $ageCalculationStartDate.ToString("yyyy-MM-dd") } else { $null }; WarrantyEndDate = if ($warrantyEndDate) { $warrantyEndDate.ToString("yyyy-MM-dd") } else { $null }; IsExpired = $isExpired; LatestWarrantyDescription = if ($warrantyEndDate) { "Apple Coverage (from Jamf)" } else { "N/A (from Jamf)" }; CalculatedAge_Days = $calculatedAgeDays; CalculatedAge_Years = $calculatedAgeYears; EntraJoinDate = $null; ErrorMessage = $errorMessage }; $reportDataList.Add($outputObject) }; Write-Host "Converted $($reportDataList.Count) Apple devices from Jamf data." -ForegroundColor Green; return $reportDataList }
#endregion Data Processing Functions

# --- Main Script Execution ---
$allProcessedDevices = [System.Collections.Generic.List[PSCustomObject]]::new(); $script:jamfAccessToken = $null; Write-Host "`n--- Processing Dell Devices ---" -ForegroundColor Cyan; $graphScopes = @("DeviceManagementManagedDevices.Read.All"); if (-not (Connect-GraphAPI -Scopes $graphScopes)) { Write-Error "Cannot proceed without Graph connection."; Exit 1 }; $intuneDellDevices = Get-IntuneDellDevices; if ($intuneDellDevices -and $intuneDellDevices.Count -gt 0) { $dellSerialNumbersToQuery = $intuneDellDevices.SerialNumber; Write-Host "`nPlease provide the Dell TechDirect API Client Secret." -ForegroundColor Yellow; $dellClientSecretSecure = Read-Host -AsSecureString; $dellAccessToken = Get-DellApiAccessToken -ClientId $dellClientId -ClientSecretSecure $dellClientSecretSecure -TokenUrl $dellTokenUrl; if ($dellAccessToken) { $dellWarrantyApiResults = Get-DellWarrantyInfoBatch -SerialNumbers $dellSerialNumbersToQuery -AccessToken $dellAccessToken -WarrantyUrlBase $dellWarrantyUrlBase -BatchSize $dellApiBatchSize; if ($dellWarrantyApiResults) { $initialDellProcessingResult = ConvertTo-DellWarrantyReportData -DellApiResults $dellWarrantyApiResults -IntuneDellDevices $intuneDellDevices -IsRetryPass:$false; $processedDellData = $initialDellProcessingResult.ReportList; $dellSerialsToRetryBatch = $initialDellProcessingResult.SerialsToRetry; if ($dellSerialsToRetryBatch -and $dellSerialsToRetryBatch.Count -gt 0) { Write-Host "`nAttempting Dell BATCH retry for $($dellSerialsToRetryBatch.Count) devices..." -ForegroundColor Yellow; $dellRetryApiResults = Get-DellWarrantyInfoBatch -SerialNumbers $dellSerialsToRetryBatch -AccessToken $dellAccessToken -WarrantyUrlBase $dellWarrantyUrlBase -BatchSize $dellApiBatchSize; if ($dellRetryApiResults) { $dellRetryReportItems = ConvertTo-DellWarrantyReportData -DellApiResults $dellRetryApiResults -IntuneDellDevices $intuneDellDevices -IsRetryPass:$true; Update-DellReportWithBatchRetryData -OriginalReportList $processedDellData -RetryReportItems $dellRetryReportItems } else { Write-Warning "No results from Dell BATCH RETRY." } } else { Write-Host "No Dell devices require batch retry." -ForegroundColor Green }; $dellSerialsForSingleTagRetry = $processedDellData | Where-Object { $_.IsExpired -eq 'Unknown' -and ([string]::IsNullOrWhiteSpace($_.ErrorMessage) -or $_.ErrorMessage -notmatch 'Invalid Service Tag') } | Select-Object -ExpandProperty SerialNumber; if ($dellSerialsForSingleTagRetry -and $dellSerialsForSingleTagRetry.Count -gt 0) { Write-Host "`nAttempting Dell SINGLE-TAG fallback for $($dellSerialsForSingleTagRetry.Count) devices..." -ForegroundColor Yellow; $dellSingleTagResultsList = [System.Collections.Generic.List[object]]::new(); foreach ($singleSerial in $dellSerialsForSingleTagRetry) { $singleResult = Get-DellWarrantyInfoSingleTag -SerialNumber $singleSerial -AccessToken $dellAccessToken -SingleTagUrlBase $dellSingleTagUrlBase; if ($null -ne $singleResult) { $dellSingleTagResultsList.Add($singleResult) }; Start-Sleep -Seconds $singleTagRetryDelaySeconds }; if ($dellSingleTagResultsList.Count -gt 0) { $dellSingleTagReportItems = ConvertTo-DellWarrantyReportData -DellApiResults $dellSingleTagResultsList -IntuneDellDevices $intuneDellDevices -IsRetryPass:$true; Update-DellReportWithSingleTagData -OriginalReportList $processedDellData -SingleTagReportItems $dellSingleTagReportItems } else { Write-Warning "No results from Dell SINGLE-TAG RETRY." } } else { Write-Host "No Dell devices require single-tag fallback." -ForegroundColor Green }; if ($processedDellData) { foreach ($dellDevice in $processedDellData) { $allProcessedDevices.Add($dellDevice) } } } else { Write-Warning "No results from initial Dell Warranty API call." } } else { Write-Warning "Cannot query Dell API without an access token. Skipping Dell devices." } } else { Write-Warning "No Dell devices found in Intune or error retrieving them." }
Write-Host "`n--- Processing Apple Devices (from Jamf Pro) ---" -ForegroundColor Cyan; if ([string]::IsNullOrWhiteSpace($jamfApiUser)) { $jamfApiUser = Read-Host "Enter Jamf Pro API Client ID" }; Write-Host "Please provide the Jamf Pro API Client Secret for Client ID '$jamfApiUser'." -ForegroundColor Yellow; $jamfApiPasswordSecure = Read-Host -AsSecureString; $jamfToken = Connect-JamfAPI -JamfBaseUrl $jamfProUrl -ApiClientId $jamfApiUser -ApiClientSecretSecure $jamfApiPasswordSecure; if ($jamfToken) { $rawMacDevices = Get-JamfMacDevices -JamfBaseUrl $jamfProUrl -AccessToken $jamfToken -PageSize $jamfInventoryPageSize; if ($rawMacDevices -and $rawMacDevices.Count -gt 0) { $processedAppleData = ConvertTo-AppleDeviceReportData -RawJamfDevices $rawMacDevices; if ($processedAppleData -and $processedAppleData.Count -gt 0) { foreach ($appleDevice in $processedAppleData) { $allProcessedDevices.Add($appleDevice) }; Write-Host "Successfully added $($processedAppleData.Count) Apple devices to the main report list." -ForegroundColor Green } else { Write-Warning "Data conversion for Apple devices resulted in no processable records to add to main list." } } else { Write-Warning "No Mac devices retrieved from Jamf or no data to process." } } else { Write-Warning "Could not connect to Jamf API or token not obtained. Skipping Apple devices." }; Write-Host "--- End Jamf Pro / Apple Device Processing ---" -ForegroundColor Cyan
# (Summary and Excel Export sections - condensed for brevity)
$overallAverageAgeDays = $null; $overallAverageAgeYears = $null; $medianAgeDays = $null; $medianAgeYears = $null; $ageBuckets = @{ "Under 1 Year" = 0; "1-2 Years" = 0; "2-3 Years" = 0; "3-4 Years" = 0; "4-5 Years" = 0; "Over 5 Years" = 0 }; $modelAverageAgeList = [System.Collections.Generic.List[PSCustomObject]]::new(); if ($allProcessedDevices -and $allProcessedDevices.Count -gt 0) { Write-Host "`nCalculating age summaries for all $($allProcessedDevices.Count) processed devices..." -ForegroundColor Yellow; $devicesWithAge = $allProcessedDevices | Where-Object { $null -ne $_.CalculatedAge_Days -and ($_.CalculatedAge_Days -is [double] -or $_.CalculatedAge_Days -is [int]) -and $_.CalculatedAge_Days -ge 0 }; if ($devicesWithAge.Count -gt 0) { Write-Host "Found $($devicesWithAge.Count) devices with valid age data for summary." -ForegroundColor Green; $overallAverageAgeDaysObj = $devicesWithAge | Measure-Object -Property CalculatedAge_Days -Average; if ($overallAverageAgeDaysObj.Count -gt 0 -and $null -ne $overallAverageAgeDaysObj.Average) { $overallAverageAgeDays = [math]::Round($overallAverageAgeDaysObj.Average); $overallAverageAgeYears = [math]::Round($overallAverageAgeDays / 365.25, 2); Write-Host "Overall Average Age: $overallAverageAgeDays days ($overallAverageAgeYears years)" -ForegroundColor Green } else { Write-Warning "Could not calculate overall average age." }; $sortedAges = $devicesWithAge.CalculatedAge_Days | Sort-Object; if ($sortedAges.Count -gt 0) { $midPoint = [math]::Floor($sortedAges.Count / 2); if ($sortedAges.Count % 2 -eq 1) { $medianAgeDays = $sortedAges[$midPoint] } else { $medianAgeDays = [math]::Round(($sortedAges[$midPoint - 1] + $sortedAges[$midPoint]) / 2) }; $medianAgeYears = [math]::Round($medianAgeDays / 365.25, 2); Write-Host "Median Device Age: $medianAgeDays days ($medianAgeYears years)" -ForegroundColor Green } else { Write-Warning "Could not calculate median age." }; foreach ($device in $devicesWithAge) { $ageInYears = $device.CalculatedAge_Years; if ($null -ne $ageInYears) { if ($ageInYears -lt 1) { $ageBuckets["Under 1 Year"]++ } elseif ($ageInYears -lt 2) { $ageBuckets["1-2 Years"]++ } elseif ($ageInYears -lt 3) { $ageBuckets["2-3 Years"]++ } elseif ($ageInYears -lt 4) { $ageBuckets["3-4 Years"]++ } elseif ($ageInYears -lt 5) { $ageBuckets["4-5 Years"]++ } else { $ageBuckets["Over 5 Years"]++ } } }; Write-Host "Device Age Buckets:" -ForegroundColor Green; $ageBuckets.GetEnumerator() | Sort-Object Name | ForEach-Object { Write-Host " - $($_.Name): $($_.Value) devices" }; Write-Host "Calculating average age per ProductLineDescription..."; $modelsGrouped = $devicesWithAge | Where-Object { -not [string]::IsNullOrWhiteSpace($_.ProductLineDescription) } | Group-Object -Property ProductLineDescription; foreach ($group in $modelsGrouped) { $modelName = $group.Name; $modelAverageDaysObj = $group.Group | Measure-Object -Property CalculatedAge_Days -Average; if ($modelAverageDaysObj.Count -gt 0 -and $null -ne $modelAverageDaysObj.Average) { $modelAverageDaysRounded = [math]::Round($modelAverageDaysObj.Average); $modelAverageYearsRounded = [math]::Round($modelAverageDaysRounded / 365.25, 2); $modelCount = $group.Count; Write-Host " - Product: '$modelName' ($modelCount devices) | Avg Age: $modelAverageDaysRounded days ($modelAverageYearsRounded years)"; $modelAverageAgeList.Add([PSCustomObject]@{ ProductLineDescription = $modelName; DeviceCount = $modelCount; AverageAge_Days = $modelAverageDaysRounded; AverageAge_Years = $modelAverageYearsRounded }) } else { Write-Warning "Could not calculate average age for PLD '$modelName'." } }; $modelAverageAgeList = $modelAverageAgeList | Sort-Object -Property ProductLineDescription } else { Write-Warning "No devices found with valid calculated age data to perform summary calculations. (Checked for non-null, numeric, non-negative CalculatedAge_Days)" } }
if ($allProcessedDevices -and $allProcessedDevices.Count -gt 0) { $exportColumns = @( "Manufacturer", "DeviceName", "SerialNumber", "ProductLineDescription", "ShipDate", "WarrantyStartDate", "WarrantyEndDate", "IsExpired", "LatestWarrantyDescription", "CalculatedAge_Days", "CalculatedAge_Years", "EntraJoinDate", "ErrorMessage" ); try { if (-not (Test-Path -Path $outputDirectory -PathType Container)) { Write-Host "Creating output directory: $outputDirectory"; New-Item -Path $outputDirectory -ItemType Directory -Force | Out-Null }; $reportDate = Get-Date -Format "yyyy-MM-dd"; $reportFileName = "DeviceAgeReport_All_Manufacturers_$($reportDate).xlsx"; $reportPath = Join-Path -Path $outputDirectory -ChildPath $reportFileName; Write-Host "`nExporting main device detail report to $reportPath (Sheet: Device Report)..." -ForegroundColor Yellow; $allProcessedDevices | Select-Object -Property $exportColumns | Export-Excel -Path $reportPath -AutoSize -AutoFilter -WorksheetName "Device Report" -FreezeTopRow -BoldTopRow -ErrorAction Stop; $summarySheetData = @(); if ($null -ne $overallAverageAgeDays) { $summarySheetData += [PSCustomObject]@{ Statistic = "Overall Average Age (All Devices)"; Value_Days = $overallAverageAgeDays; Value_Years = $overallAverageAgeYears; CategoryValue = ""; Count = "" } }; if ($null -ne $medianAgeDays) { $summarySheetData += [PSCustomObject]@{ Statistic = "Median Age (All Devices)"; Value_Days = $medianAgeDays; Value_Years = $medianAgeYears; CategoryValue = ""; Count = "" } }; $summarySheetData += [PSCustomObject]@{ Statistic = "--- Age Buckets (All Devices) ---"; Value_Days = ""; Value_Years = ""; CategoryValue = "Age Range"; Count = "Device Count" }; foreach ($bucket in $ageBuckets.GetEnumerator() | Sort-Object Name) { $summarySheetData += [PSCustomObject]@{ Statistic = ""; Value_Days = ""; Value_Years = ""; CategoryValue = $bucket.Name; Count = $bucket.Value } }; $summarySheetData += [PSCustomObject]@{ Statistic = "--- Average Age Per ProductLineDescription ---"; Value_Days = ""; Value_Years = ""; CategoryValue = "Product Description"; Count = "Device Count" }; foreach ($modelAvg in $modelAverageAgeList) { $summarySheetData += [PSCustomObject]@{ Statistic = ""; Value_Days = $modelAvg.AverageAge_Days; Value_Years = $modelAvg.AverageAge_Years; CategoryValue = $modelAvg.ProductLineDescription; Count = $modelAvg.DeviceCount } }; if ($summarySheetData.Count -gt 0) { Write-Host "Exporting age summary to $reportPath (Sheet: Age Summary)..." -ForegroundColor Yellow; $summaryExportParams = @{ Path = $reportPath; WorksheetName = "Age Summary"; AutoSize = $true; Append = $true; ErrorAction = 'Stop'; BoldTopRow = $true }; $summarySheetData | Select-Object Statistic, CategoryValue, Count, Value_Days, Value_Years | Export-Excel @summaryExportParams; Write-Host "Report successfully generated with both sheets: $reportPath" -ForegroundColor Green } else { Write-Warning "No summary data calculated, summary sheet not added (or may be empty)."; Write-Host "Main report (Sheet 1) possibly generated: $reportPath" -ForegroundColor Green } } catch { Write-Error "Failed to export to Excel: $($_.Exception.Message)"; Write-Warning "Ensure ImportExcel module works & path '$reportPath' accessible." } } else { Write-Warning "No data processed or generated for the report. No Excel file created." }

Write-Host "`nScript finished."
# --- End of Script ---