Email Expiry Notification - Scheduled Task on QTDC1
Run as QUADRANTHR\operator
Run whether user is logged on or not

Triggers: Weekly 8AM M-F

Actions: start a program
Program: powershell.exe 
Arg: -ExecutionPolicy Bypass  \\quadranthr.com\netlogon\password-expiry-notify.ps1

Settings:
Allow task to be run on demand
If the running task does not end when requested, force it to stop
If task already running, Stop the existing instance

