<#
.SYNOPSIS
Adds a user to a specific Entra ID group and triggers the 'Provision on Demand' synchronization
for the Salesforce application in Microsoft Entra ID using Microsoft Graph.

.DESCRIPTION
This script automates adding a user to a predefined Salesforce license assignment group and then
initiates the 'Provision on Demand' process for that user against the configured Salesforce
Enterprise Application (Salesforce prod(JIT)-2023).

Steps:
1. Checks for the required Microsoft.Graph module and key cmdlets.
2. Connects to Microsoft Graph with necessary permissions.
3. Prompts for the User Principal Name (UPN) of the target user.
4. Finds the user object in Entra ID.
5. Finds the target Entra ID group ('Salesforce - license assignment').
6. Adds the user to the group if they are not already a member (includes corrected error handling).
7. Finds the synchronization job associated with the Salesforce service principal.
8. Triggers the 'Provision on Demand' action using Invoke-MgGraphRequest (v1.0) with manually constructed JSON body.
9. Outputs the results of the actions.

Hardcoded Values:
- Target Group Object ID: adf19d42-1534-4f16-9ff2-ae4cf43e4202 (Salesforce - license assignment)
- Salesforce Service Principal Object ID: 0892ba4e-3f57-40a8-9600-ba45b4bc58cf (Salesforce prod(JIT)-2023)

You must have the Microsoft.Graph PowerShell module installed. If not present, the script will stop
and instruct you to install it using: Install-Module Microsoft.Graph -Scope CurrentUser

Required Microsoft Graph Permissions:
- User.Read.All         (to find the user)
- Group.Read.All        (to find the group)
- GroupMember.ReadWrite.All (to add user to the group)
- Application.Read.All    (to find the service principal, needed for sync job)
- Synchronization.ReadWrite.All (to find the sync job and trigger provision on demand)

.PARAMETER UserPrincipalName
The User Principal Name (UPN) of the user for whom to provision a license.
Example: '<EMAIL>'

.EXAMPLE
.\Provision-Salesforce-OnDemand.ps1 -UserPrincipalName '<EMAIL>'

.NOTES
Author: Gemini
Date: 2025-04-17
Version: 1.11 (Using Invoke-MgGraphRequest v1.0 with manually constructed JSON body)
Requires: Microsoft.Graph PowerShell module. Confirmed issues finding specific cmdlets (like Get-MgAccessToken) and types in user's environment (v2.26.1 shown), strongly suggesting module install/load issues.
Recommend environment troubleshooting (reinstall module, test Graph Explorer) if this fails.
Ensure the account running the script has the necessary Graph API permissions consented in the tenant.
#>
param(
    [Parameter(Mandatory=$true)]
    [string]$UserPrincipalName
)

# --- Hardcoded Configuration ---
$targetGroupId = "adf19d42-1534-4f16-9ff2-ae4cf43e4202" # Object ID for 'Salesforce - license assignment' group
$salesforceSpObjectId = "0892ba4e-3f57-40a8-9600-ba45b4bc58cf" # Object ID for 'Salesforce prod(JIT)-2023' Service Principal
$requiredScopes = @("User.Read.All", "Group.Read.All", "GroupMember.ReadWrite.All", "Application.Read.All", "Synchronization.ReadWrite.All")

# --- Prerequisite Check ---
Write-Host "Checking for Microsoft.Graph module..." -ForegroundColor Cyan
$moduleIssue = $false
$graphModule = Get-Module -Name Microsoft.Graph -ListAvailable
if (-not $graphModule) {
    Write-Error "Microsoft.Graph PowerShell module not found. Please install it first."
    Write-Host "Run: Install-Module Microsoft.Graph -Scope CurrentUser -Repository PSGallery -Force"
    return
} else {
    Write-Host "Microsoft.Graph module found." -ForegroundColor Green
    # Attempt to import needed sub-modules silently
    Import-Module Microsoft.Graph.Applications -ErrorAction SilentlyContinue
    Import-Module Microsoft.Graph.Authentication -ErrorAction SilentlyContinue
    # Check for key cmdlets that have caused issues - warn if missing
    if (-not (Get-Command Get-MgServicePrincipalSynchronizationJob -ErrorAction SilentlyContinue)) {
        Write-Warning "Cmdlet 'Get-MgServicePrincipalSynchronizationJob' (from Microsoft.Graph.Applications) not found."
        $moduleIssue = $true
    }
    # Acknowledge Get-MgAccessToken is confirmed missing in user's v2.26.1 output
    Write-Warning "NOTE: Cmdlet 'Get-MgAccessToken' is confirmed missing in tested environment (v2.26.1)."

    if ($moduleIssue) {
         Write-Warning "One or more expected Microsoft Graph cmdlets were not found. This might indicate an incomplete or outdated module installation."
         Write-Warning "Consider running: Update-Module Microsoft.Graph -Force"
         Write-Warning "Or reinstalling: Install-Module Microsoft.Graph -Force -AllowClobber"
    }
}

# --- Connect to Microsoft Graph ---
Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Cyan
# (Connection logic remains the same)
$currentContext = Get-MgContext -ErrorAction SilentlyContinue
$sufficientScopes = $true
if ($currentContext) {
    foreach ($scope in $requiredScopes) {
        if ($currentContext.Scopes -notcontains $scope) {
            $sufficientScopes = $false
            Write-Warning "Current Graph connection missing scope: $scope"
            break
        }
    }
}

if (-not $currentContext -or -not $sufficientScopes) {
    Write-Host "Not connected or missing required permissions. Attempting connection..."
    try {
        if ($currentContext) {
            Write-Host "Disconnecting existing session to re-connect with required scopes..."
            Disconnect-MgGraph -ErrorAction SilentlyContinue
        }
        Connect-MgGraph -Scopes $requiredScopes -ErrorAction Stop
        $currentContext = Get-MgContext
        Write-Host "Successfully connected to tenant $($currentContext.TenantId) as $($currentContext.Account)" -ForegroundColor Green
    } catch {
        Write-Error "Failed to connect to Microsoft Graph. Please ensure you have the necessary permissions and can authenticate."
        Write-Error $_.Exception.Message
        return
    }
} else {
    Write-Host "Already connected to tenant $($currentContext.TenantId) as $($currentContext.Account) with sufficient scopes." -ForegroundColor Green
}


# --- Main Script Logic ---
Write-Host "Starting Salesforce provisioning process for user: $UserPrincipalName" -ForegroundColor Cyan
try {
    # 1. Get the User Object
    Write-Host "Fetching user object for $UserPrincipalName..."
    $user = Get-MgUser -Filter "userPrincipalName eq '$UserPrincipalName'" -ErrorAction Stop
    Write-Host "Found User: $($user.DisplayName) (ID: $($user.Id))" -ForegroundColor Green

    # 2. Get the Target Group Object
    Write-Host "Fetching target group object (ID: $targetGroupId)..."
    $group = Get-MgGroup -GroupId $targetGroupId -ErrorAction Stop
    Write-Host "Found Group: $($group.DisplayName) (ID: $($group.Id))" -ForegroundColor Green

    # 3. Check and Add User to Group
    # (Logic remains the same - handles 404 on check gracefully)
    Write-Host "Checking if user '$($user.DisplayName)' is already a member of group '$($group.DisplayName)'..."
    $isMember = $false
    $checkError = $null
    try {
        $membership = Get-MgGroupMember -GroupId $targetGroupId -Filter "id eq '$($user.Id)'" -Top 1 -Property 'id' -ErrorAction Stop
        if ($membership) {
            $isMember = $true
        }
    } catch {
        $checkError = $_
        if ($checkError.Exception.Message -match "Request_ResourceNotFound" -or ($checkError.Exception.Response -and $checkError.Exception.Response.StatusCode -eq [System.Net.HttpStatusCode]::NotFound)) {
             Write-Warning "Could not definitively check group membership for user '$($user.DisplayName)' in group '$($group.DisplayName)'. Error: $($checkError.Exception.Message)"
             Write-Warning "Encountered known 404 error during membership check. Assuming user is not a member and attempting to add."
        } else {
             Write-Error "Unexpected error during group membership check: $($checkError.Exception.Message)"
             throw $checkError
        }
    }

    if ($isMember) {
        Write-Warning "User '$($user.DisplayName)' is already a member of group '$($group.DisplayName)'."
    } else {
        Write-Host "User is not confirmed as member. Adding '$($user.DisplayName)' to group '$($group.DisplayName)'..."
        try {
            $newUserMembership = @{ "@odata.id" = "https://graph.microsoft.com/v1.0/directoryObjects/$($user.Id)" }
            Invoke-MgGraphRequest -Method POST -Uri "https://graph.microsoft.com/v1.0/groups/$targetGroupId/members/`$ref" -Body ($newUserMembership | ConvertTo-Json) -ErrorAction Stop
            Write-Host "Successfully added user '$($user.DisplayName)' to group '$($group.DisplayName)'." -ForegroundColor Green
        } catch {
             Write-Error "Failed to add user '$($user.DisplayName)' to group '$($group.DisplayName)'. Error: $($_.Exception.Message)"
             if ($_.Exception.Response -match "one or more added object references already exist") {
                 Write-Warning "User appears to already be a member (add operation failed). Continuing..."
             } else {
                 throw $_
             }
        }
    }

    # 4. Get the Salesforce Service Principal
     Write-Host "Fetching Salesforce Service Principal (ID: $salesforceSpObjectId) to verify..."
     $servicePrincipal = Get-MgServicePrincipal -ServicePrincipalId $salesforceSpObjectId -ErrorAction Stop
     Write-Host "Found Service Principal: $($servicePrincipal.DisplayName) (ID: $($servicePrincipal.Id))" -ForegroundColor Green

    # 5. Find the Synchronization Job for the Service Principal
    Write-Host "Finding synchronization job for Service Principal '$($servicePrincipal.DisplayName)'..."
    if (-not (Get-Command Get-MgServicePrincipalSynchronizationJob -ErrorAction SilentlyContinue)) {
        Write-Error "Command 'Get-MgServicePrincipalSynchronizationJob' not found. Cannot proceed. Please update/reinstall the Microsoft.Graph module."
        return
    }
    $syncJob = Get-MgServicePrincipalSynchronizationJob -ServicePrincipalId $salesforceSpObjectId -ErrorAction Stop
    if ($syncJob -is [array]) {
        Write-Warning "Multiple synchronization jobs found. Using the first one: ID $($syncJob[0].Id)"
        $syncJob = $syncJob[0]
    }
    Write-Host "Found Synchronization Job ID: $($syncJob.Id)" -ForegroundColor Green

    # 5.1 We'll try without a rule ID since we're having issues finding the correct one
    Write-Host "Skipping rule ID lookup and using a simpler request structure..." -ForegroundColor Yellow
    $ruleId = $null

    # 6. Try a different approach - instead of using provisionOnDemand, let's try to start a synchronization job
    Write-Host "Instead of using provisionOnDemand, trying to start a synchronization job for user '$($user.DisplayName)' (ID: $($user.Id))..."

    # Define standard headers
    $headers = @{
        'Content-Type' = 'application/json'
        'Accept' = '*/*'
    }

    # First, check if there's an existing job running
    try {
        Write-Host "Checking current job status..."
        $jobStatus = Invoke-MgGraphRequest -Method GET -Uri "https://graph.microsoft.com/v1.0/servicePrincipals/$salesforceSpObjectId/synchronization/jobs/$($syncJob.Id)" -ErrorAction Stop

        # Extract and display the job status
        $status = "Unknown"
        if ($jobStatus.PSObject.Properties.Name -contains 'status') {
            $status = $jobStatus.status
        }
        Write-Host "Current job status: $status" -ForegroundColor Yellow

        # If the job is not running, start it
        if ($status -ne "inProgress") {
            Write-Host "Starting synchronization job..." -ForegroundColor Yellow
            $startJobBody = @{ }
            Invoke-MgGraphRequest -Method POST -Uri "https://graph.microsoft.com/v1.0/servicePrincipals/$salesforceSpObjectId/synchronization/jobs/$($syncJob.Id)/start" -Body ($startJobBody | ConvertTo-Json) -Headers $headers -ErrorAction Stop
            Write-Host "Successfully started synchronization job." -ForegroundColor Green
        } else {
            Write-Host "Synchronization job is already running." -ForegroundColor Yellow
        }

        Write-Host "Synchronization job has been triggered. This will process all users in the group, including '$($user.DisplayName)'." -ForegroundColor Green
        Write-Host "Note: This is a full synchronization rather than a targeted provision-on-demand for just this user." -ForegroundColor Yellow
    } catch {
        Write-Error "Failed to start synchronization job: $($_.Exception.Message)"
    }

} catch {
    Write-Error "An unexpected error occurred during the process: $($_.Exception.Message)"
    Write-Error $_.ScriptStackTrace
} finally {
    Write-Host "Script execution finished." -ForegroundColor Cyan
}
