# ![PowerShell App Deployment Toolkit Logo](AppDeployToolkitLogo.png)

## Enterprise App Deployment, Simplified.

PSAppDeployToolkit is a versatile solution that streamlines and standardizes the process of software deployment, making it easier than ever to manage your IT environment. It has a comprehensive set of features, such as dynamic logging, user interaction capabilities, and customizable functionality.

### Standardize and enhance every software deployment

PSAppDeployToolkit allows you to encapsulate a typical Windows Installer MSI or Setup executable to provide it with enhanced capabilities.

- Validate prerequisites such as dependencies on minimum software versions
- Ensure that in-use applications are closed and prevent reopening during the deployment
- Check with the user if now is a good time to start an install and allow them to defer
- Uninstall existing applications and perform clean up operations
- Capture any important settings that may be required for an upgrade or migration
- Run the installation silently and capture logs in the event of an issue
- Run post-installation configuration tasks to customize for your environment
- Prompt the user to restart their computer if required, immediately, on a timer and with a deadline

## Getting Started

-> [System Requirements](https://psappdeploytoolkit.com/docs/getting-started/requirements)
-> [Downloading](https://psappdeploytoolkit.com/docs/getting-started/download)

### PSAppDeployToolkit Links

-> [Homepage](https://psappdeploytoolkit.com)
-> [Documentation](https://psappdeploytoolkit.com/docs)
-> [Function & Variable References](https://psappdeploytoolkit.com/docs/reference)
-> [Download Latest Release](https://github.com/PSAppDeployToolkit/PSAppDeployToolkit/releases)
-> [News](https://psappdeploytoolkit.com/blog)

### Community Links

-> [Discourse Forum](https://discourse.psappdeploytoolkit.com/)
-> [Discord Chat](https://discord.com/channels/618712310185197588/627204361545842688)
-> [Reddit](https://reddit.com/r/psadt)

## License

The PowerShell App Deployment Tool is free software: you can redistribute it and/or modify it under the terms of the GNU Lesser General Public License as published by the Free Software Foundation, either version 3 of the License, or any later version.

This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU Lesser General Public License for more details.
