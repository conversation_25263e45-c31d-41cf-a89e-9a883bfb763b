﻿
try
    {
            [System.Net.Sockets.TcpClient]$client = New-Object System.Net.Sockets.TcpClient('ftp.jonoke.com',21)
            $found = $true
    }
    catch
    {
            $found = $false
    }

$found

if (!$found) {
    "Jonoke FTP is down, emailing alert"
    Send-MailMessage -To <EMAIL> -From “<EMAIL>” -SmtpServer mail.qhrtech.com -Subject “Powershell Alert - Jonoke FTP down” -body "Powershell detected Jonoke FTP is down. `nLog in as administrator and run 'sudo reboot', login again and run 'sudo ifup eth0'. `n`nThis script runs on QTADMIN1 daily at 6am as a scheduled task"
    }

else {"Jonoke FTP is up"}