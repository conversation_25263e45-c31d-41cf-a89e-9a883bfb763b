<#
    Title: Check employees without an All Staff group, addressing domain alias issues and partial mismatch.
#>

#set tls1.2 as default or exchange will fail to connect
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

##############################################################################
### STEP 1: Connect to Exchange Online
##############################################################################
Write-Host "=== STEP 1: Connect to Exchange Online ==="
Connect-ExchangeOnline

##############################################################################
### STEP 2: Connect to Microsoft Graph
##############################################################################
Write-Host "`n=== STEP 2: Connect to Microsoft Graph ==="
Connect-MgGraph -Scopes "Group.Read.All", "User.Read.All", "Directory.Read.All"

##############################################################################
### STEP 3: Initialize Arrays & Functions
##############################################################################
Write-Host "`n=== STEP 3: Initialize Arrays and Helper Functions ==="

# Will store final membership addresses from all EXO and Graph All-Staff groups
$AllStaffMembers = [System.Collections.Generic.List[string]]::new()

# Will store which groups we have "seen" to avoid infinite loops when expanding nested groups
$Global:SeenGroups = @()

# Helper function: Write a verbose debug line easily
function Debug-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message
    )
    Write-Host "[DEBUG] $Message" -ForegroundColor DarkCyan
}

# ----------------------------------------------------------------------------
# 3.1) A hashtable to map old or alternative domains -> the official domain
#      e.g., "qhrtechnologies.com" -> "qhrtech.com"
# ----------------------------------------------------------------------------
$DomainAliasMap = @{
    "qhrtechnologies.com" = "qhrtech.com"
    "optimedsoftware.com" = "qhrtech.com" 
    # Add more known domain translations here if needed
    # "oldcompany.com"      = "newcompany.com"
}

# ----------------------------------------------------------------------------
# 3.2) A function to unify domain aliases
#      For example, <EMAIL> => <EMAIL>
# ----------------------------------------------------------------------------
function Normalize-Email {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Address
    )
    # Lowercase
    $Address = $Address.ToLower()

    # Split local-part + domain
    $parts = $Address.Split("@")
    if ($parts.Count -eq 2) {
        $localPart = $parts[0]
        $domain = $parts[1]

        # If we have a known domain alias, swap it
        if ($DomainAliasMap.ContainsKey($domain)) {
            $targetDomain = $DomainAliasMap[$domain]
            if ($targetDomain -and $targetDomain -ne $domain) {
                Debug-Log "Auto-correcting domain from '$domain' => '$targetDomain' for address '$Address'"
                return "$localPart@$targetDomain"
            }
        }
    }

    return $Address
}

# ----------------------------------------------------------------------------
# 3.3) RECURSIVE function to get all members of a mail-enabled distribution group
#      with domain alias normalization
# ----------------------------------------------------------------------------
function Get-DistGroupMembersRecursive {
    param(
        [Parameter(Mandatory = $true)]
        [string]$GroupIdentity
    )

    # Avoid infinite loops if group is encountered again
    if ($Global:SeenGroups -contains $GroupIdentity) {
        Debug-Log "Skipping group '$GroupIdentity' (already processed)."
        return
    }
    else {
        $Global:SeenGroups += $GroupIdentity
    }

    Debug-Log "Retrieving members for distro/group mailbox: $GroupIdentity"
    try {
        $members = Get-DistributionGroupMember -Identity $GroupIdentity -ResultSize Unlimited -ErrorAction Stop
    }
    catch {
        Write-Warning "Get-DistributionGroupMember failed for '$GroupIdentity': $($_.Exception.Message)"
        $members = $null
    }

    if ($members) {
        Debug-Log "Found $($members.Count) immediate member(s) in group: $GroupIdentity"
        foreach ($member in $members) {
            if ($member.RecipientType -match 'Group') {
                Debug-Log "Nested group found: $($member.Name). Recursing..."
                Get-DistGroupMembersRecursive $member.Name
            }
            else {
                $addr = $member.PrimarySmtpAddress
                if (-not $addr) { $addr = $member.WindowsEmailAddress }

                Debug-Log "Member: $($member.Name) | Address: $addr"
                if ($addr) {
                    # Normalize domain
                    $normalized = Normalize-Email -Address $addr
                    $AllStaffMembers.Add($normalized)
                }
            }
        }
    }
    else {
        Debug-Log "No members or retrieval error for group: $GroupIdentity"
    }
}

# ----------------------------------------------------------------------------
# 3.4) Fallback function to get membership from Unified Group
# ----------------------------------------------------------------------------
function Get-UnifiedGroupLinksMembership {
    param(
        [Parameter(Mandatory = $true)]
        [string]$GroupIdentity
    )

    Debug-Log "Retrieving members via Get-UnifiedGroupLinks for group: $GroupIdentity"
    try {
        $links = Get-UnifiedGroupLinks -Identity $GroupIdentity -LinkType Members -ResultSize Unlimited -ErrorAction Stop
        if ($links) {
            Debug-Log "Found $($links.Count) unified group link member(s)."
            foreach ($lnk in $links) {
                $addr = $lnk.PrimarySmtpAddress
                if (-not $addr) { $addr = $lnk.WindowsEmailAddress }
                Debug-Log "Member: $($lnk.Name) | Address: $addr"
                if ($addr) {
                    $AllStaffMembers.Add( (Normalize-Email $addr) )
                }
            }
        }
        else {
            Debug-Log "No members returned by Get-UnifiedGroupLinks for $GroupIdentity"
        }
    }
    catch {
        Write-Warning "Get-UnifiedGroupLinks failed for '$GroupIdentity': $($_.Exception.Message)"
    }
}

# ----------------------------------------------------------------------------
# 3.5) Function to handle dynamic distribution group membership
# ----------------------------------------------------------------------------
function Get-DynamicGroupMembership {
    param(
        [Parameter(Mandatory = $true)]
        [string]$GroupIdentity
    )

    Debug-Log "Retrieving dynamic distribution group info for: $GroupIdentity"
    try {
        $dynGroup = Get-DynamicDistributionGroup -Identity $GroupIdentity -ErrorAction Stop
    }
    catch {
        Write-Warning "Get-DynamicDistributionGroup failed for '$GroupIdentity': $($_.Exception.Message)"
        return
    }

    if ($dynGroup) {
        $filter = $dynGroup.RecipientFilter
        $orgUnit = $dynGroup.OrganizationUnit
        Debug-Log "Dynamic group filter: $filter"
        Debug-Log "Dynamic group orgUnit: $orgUnit"

        if ($filter) {
            try {
                $members = Get-Recipient -RecipientPreviewFilter $filter -OrganizationalUnit $orgUnit -ResultSize Unlimited -ErrorAction SilentlyContinue
                if ($members) {
                    Debug-Log "Found $($members.Count) dynamic members for $GroupIdentity"
                    foreach ($m in $members) {
                        $addr = $m.PrimarySmtpAddress
                        if (-not $addr) { $addr = $m.WindowsEmailAddress }
                        Debug-Log "Dyn. Member: $($m.Name) | Address: $addr"
                        if ($addr) {
                            $AllStaffMembers.Add( (Normalize-Email $addr) )
                        }
                    }
                }
                else {
                    Debug-Log "No members found for dynamic group $GroupIdentity"
                }
            }
            catch {
                Write-Warning "Get-Recipient with filter failed for '$GroupIdentity': $($_.Exception.Message)"
            }
        }
        else {
            Debug-Log "Dynamic group has no RecipientFilter?!"
        }
    }
}

##############################################################################
### STEP 4: Get AD Users
##############################################################################
Write-Host "`n=== STEP 4: Collect AD user list ==="
try {
    $userList = Get-ADUser -Filter {
        (Title -ne "Service Account") -and
        (Name -notlike "*Domain*") -and
        (Name -notlike "*test*") -and
        (Name -notlike "*svc*")
    } | Select-Object -ExpandProperty UserPrincipalName
}
catch {
    Write-Error "ERROR: Failed retrieving AD users. Please run this on a machine with AD cmdlets."
    return
}
Write-Host "Found $($userList.Count) AD user(s)."

##############################################################################
### STEP 5: Retrieve EXO Groups Named All Staff
##############################################################################
Write-Host "`n=== STEP 5: Get EXO groups with 'All Staff/allstaff' in DisplayName or SMTP ==="

$exoGroups = Get-EXORecipient `
    -RecipientTypeDetails MailUniversalDistributionGroup, GroupMailbox, DynamicDistributionGroup `
    -Filter "((DisplayName -like '*all staff*') -or (PrimarySmtpAddress -like '*allstaff*'))" `
    -ResultSize Unlimited -ErrorAction Stop

Write-Host "Found $($exoGroups.Count) matching group(s) in EXO."

foreach ($grp in $exoGroups) {
    Write-Host ("  [EXO] {0} / {1} / {2}" -f $grp.DisplayName, $grp.PrimarySmtpAddress, $grp.RecipientTypeDetails)
}

##############################################################################
### STEP 6: Retrieve/Expand Each EXO Group
##############################################################################
Write-Host "`n=== STEP 6: Expand each EXO group membership (with recursion, dynamic, fallback) ==="

foreach ($grp in $exoGroups) {
    $grpName = $grp.DisplayName
    $grpAddress = $grp.PrimarySmtpAddress
    $grpType = $grp.RecipientTypeDetails

    switch ($grpType) {

        'MailUniversalDistributionGroup' {
            # Standard distribution group => do recursive expansion
            Get-DistGroupMembersRecursive -GroupIdentity $grpAddress
        }

        'GroupMailbox' {
            # Try standard approach with recursion
            try {
                Get-DistGroupMembersRecursive -GroupIdentity $grpAddress
            }
            catch {
                Write-Warning "GroupMailbox recursion failed for $grpAddress. Attempting Get-UnifiedGroupLinks fallback."
                Get-UnifiedGroupLinksMembership -GroupIdentity $grpAddress
            }
        }

        'DynamicDistributionGroup' {
            # Use dynamic approach
            Get-DynamicGroupMembership -GroupIdentity $grp.Identity
        }

        default {
            Write-Host "Skipping unknown type: $grpType"
        }
    }
}

##############################################################################
### STEP 7: Retrieve M365 (Unified) Groups from Graph
##############################################################################
Write-Host "`n=== STEP 7: Retrieve M365 (Unified) groups from Graph with 'All Staff/allstaff' ==="

$m365AllGroups = Get-MgGroup -All `
    -Filter "groupTypes/any(c:c eq 'Unified')" `
    -ConsistencyLevel eventual `
    -CountVariable m365Count

Write-Host ("Retrieved {0} M365 Unified groups from Graph." -f $m365Count)

$m365Groups = $m365AllGroups | Where-Object {
    $_.DisplayName -imatch 'all staff' -or
    $_.Mail -imatch 'allstaff'
}

Write-Host ("Filtered to {0} M365 group(s)." -f $m365Groups.Count)
foreach ($g in $m365Groups) {
    Write-Host "  [M365] $($g.DisplayName) / $($g.Mail) / $($g.Id)"
}

##############################################################################
### STEP 8: Get Membership from Each M365 Group (via Graph)
##############################################################################
Write-Host "`n=== STEP 8: Expand membership from each M365 group ==="

foreach ($g in $m365Groups) {
    Write-Host ("Retrieving membership for M365 group: {0}" -f $g.DisplayName)
    $page = $null
    $members = @()

    try {
        $page = Get-MgGroupMember -GroupId $g.Id -PageSize 100
        $members += $page
        while ($page.NextLink) {
            $page = Invoke-MgGraphRequest -Uri $page.NextLink -Method Get
            $members += $page.value
        }
    }
    catch {
        Write-Warning "Failed retrieving members for Graph group '$($g.DisplayName)': $($_.Exception.Message)"
        continue
    }

    # Filter only user objects
    $userMembers = $members | Where-Object { $_.'@odata.type' -eq '#microsoft.graph.user' }
    Write-Host ("  [M365] Found {0} user(s) in group '{1}'" -f $userMembers.Count, $g.DisplayName)

    foreach ($u in $userMembers) {
        $addr = $u.UserPrincipalName
        if (-not $addr) { $addr = $u.Mail }
        Debug-Log "Graph user: $($u.DisplayName) | Address: $addr"
        if ($addr) {
            $AllStaffMembers.Add( (Normalize-Email $addr) )
        }
    }
}

##############################################################################
### STEP 9: Remove Duplicates & Output Debug
##############################################################################
Write-Host "`n=== STEP 9: Deduplicate and output debug ==="

$AllStaffMembers = $AllStaffMembers | Sort-Object -Unique

Write-Host ("Total unique addresses from EXO + Graph All Staff groups: {0}" -f $AllStaffMembers.Count)

# Optional: Write all addresses to a file for deep debugging
$AllStaffMembers | Out-File "C:\Temp\AllStaffMembers.txt"
Write-Host "All staff membership addresses logged to: C:\Temp\AllStaffMembers.txt"

##############################################################################
### STEP 10: Compare AD User List to Combined Membership
##############################################################################
Write-Host "`n=== STEP 10: Compare AD user list to combined membership ==="

$IsNotInAllStaff = @()
$InAllStaff = @()

foreach ($employeeUPN in $userList) {
    # Lower the user’s AD UPN
    $empLower = $employeeUPN.ToLower()

    if ($AllStaffMembers -contains $empLower) {
        $InAllStaff += $employeeUPN
        Write-Host "[IN]  $employeeUPN" -ForegroundColor Green
    }
    else {
        $IsNotInAllStaff += $employeeUPN
        Write-Host "[OUT] $employeeUPN" -ForegroundColor Yellow
    }
}

##############################################################################
### STEP 11: Double-Check Flagged Users
##############################################################################
Write-Host "`n=== STEP 11: Double-check flagged users ==="
$FinalMissing = @()

foreach ($missingUser in $IsNotInAllStaff) {
    if ($AllStaffMembers -contains $missingUser.ToLower()) {
        Write-Host "  [INFO] $missingUser is actually in the final membership array. Skipping removal." -ForegroundColor Cyan
    }
    else {
        $FinalMissing += $missingUser
    }
}

##############################################################################
### STEP 12: Output Missing Users in a Colored List
##############################################################################
Write-Host "`n=== STEP 12: The following employees have NO All Staff group ===" -ForegroundColor Magenta

$missingObjects = $FinalMissing | Sort-Object | ForEach-Object {
    [PSCustomObject]@{ 'UserPrincipalName' = $_ }
}

if ($missingObjects.Count -eq 0) {
    Write-Host "Great news! No missing users found." -ForegroundColor Green
}
else {
    $missingObjects | ForEach-Object {
        Write-Host ("  " + $_.UserPrincipalName) -ForegroundColor Red
    }
}

##############################################################################
### STEP 13: Disconnect
##############################################################################
Write-Host "`n=== STEP 13: Disconnecting from Exchange Online & Microsoft Graph ==="

Disconnect-ExchangeOnline -Confirm:$false
Disconnect-MgGraph

Write-Host "`nAll done!"
