# Check if Active Directory module is available, if not, install it
$adModule = Get-Module -Name ActiveDirectory -ListAvailable
if ($adModule -eq $null) {
    Install-WindowsFeature RSAT-AD-PowerShell
    Import-Module ActiveDirectory
}

# Define the new manager's username
$newManager = "NewManagerUsername"

# List of users to be updated
$usersToUpdate = @("User1", "User2", "User3")

# Iterate through each user and update the manager
foreach ($user in $usersToUpdate) {
    # Get the user's current manager
    $currentManager = Get-AdUser -Identity $user -Properties manager | Select-Object -ExpandProperty manager

    # If the current manager is different from the new manager, update the manager
    if ($currentManager -ne $newManager) {
        Set-AdUser -Identity $user -Manager $newManager
        Write-Host "Updated manager for user $user to $newManager"
    } else {
        Write-Host "User $user already has the correct manager ($newManager)"
    }
}
