# Ensure admin privileges
If (-Not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Please run this script as Administrator!" -ForegroundColor Red
    Exit 1
}

# Function to remove registry keys safely
Function Remove-RegistryKey {
    param (
        [string]$Path,
        [string]$Name
    )
    If (Test-Path $Path) {
        Remove-ItemProperty -Path $Path -Name $Name -Force -ErrorAction SilentlyContinue
    }
}

# Function to restore default registry keys
Function Restore-RegistryValue {
    param (
        [string]$Path,
        [string]$Name,
        [int]$Value
    )
    If (!(Test-Path $Path)) { New-Item -Path $Path -Force | Out-Null }
    Set-ItemProperty -Path $Path -Name $Name -Value $Value -Type DWord -Force
}

Write-Host "Rolling back all TLS, GPO, Intune, Cryptographic, and Networking changes..." -ForegroundColor Yellow

# 🔄 1. Restore TLS & SecureProtocols settings
Write-Host "Restoring SecureProtocols & TLS settings..." -ForegroundColor Cyan
$DefaultSecureProtocols = 0x00000800 -bor 0x00002000 -bor 0x00008000  # Default Windows TLS 1.0, 1.1, 1.2

Restore-RegistryValue -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols" -Value $DefaultSecureProtocols
Restore-RegistryValue -Path "HKLM:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols" -Value $DefaultSecureProtocols

# Remove Forced TLS 1.2 & 1.3 (Revert to System Default)
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" -Name "Enabled"
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" -Name "DisabledByDefault"
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Server" -Name "Enabled"
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Server" -Name "DisabledByDefault"

Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Client" -Name "Enabled"
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Client" -Name "DisabledByDefault"
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Server" -Name "Enabled"
Remove-RegistryKey -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Server" -Name "DisabledByDefault"

# 🔄 2. Restore Default .NET TLS Settings
Write-Host "Restoring .NET Framework TLS settings..." -ForegroundColor Cyan
Remove-RegistryKey -Path "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" -Name "SystemDefaultTlsVersions"
Remove-RegistryKey -Path "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" -Name "SchUseStrongCrypto"
Remove-RegistryKey -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\.NETFramework\v4.0.30319" -Name "SystemDefaultTlsVersions"
Remove-RegistryKey -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\.NETFramework\v4.0.30319" -Name "SchUseStrongCrypto"

# 🔄 3. Restore Cryptographic Settings
Write-Host "Restoring Windows cryptographic settings..." -ForegroundColor Cyan
certutil -setreg chain\EnableWeakSignatureFlags 1
certutil -setreg chain\MinRsaPubKeyBitLength 1024
certutil -setreg chain\WeakSha1ThirdPartyFlags 1
certutil -setreg chain\WeakSha1ThirdPartyMitigation 1
certutil -setreg chain\WeakSha1NoTimeStamp 1

# 🔄 4. Restore Internet Explorer Settings
Write-Host "Restoring Internet Explorer settings..." -ForegroundColor Cyan
Remove-RegistryKey -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols"
Remove-RegistryKey -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols"

# 🔄 5. Restore Microsoft Edge IE Mode Settings
Write-Host "Restoring Microsoft Edge IE Mode settings..." -ForegroundColor Cyan
Remove-RegistryKey -Path "HKLM:\SOFTWARE\Policies\Microsoft\Edge" -Name "InternetExplorerIntegrationLevel"

# 🔄 6. Restore Windows Networking
Write-Host "Restoring Windows networking settings..." -ForegroundColor Cyan
netsh int ip reset
netsh winsock reset
netsh winhttp reset proxy

# 🔄 7. Restore Windows Security Policies
Write-Host "Restoring Windows security policies..." -ForegroundColor Cyan
secedit /configure /cfg %windir%\inf\defltbase.inf /db secedit.sdb /verbose

# 🔄 8. Reapply Default Windows Policies & Refresh
Write-Host "Refreshing Group Policies and System Settings..." -ForegroundColor Cyan
gpupdate /force
Start-Process -FilePath "C:\Windows\System32\mdmclient.exe" -ArgumentList "Provisioning" -NoNewWindow -Wait
Start-Process -FilePath "C:\Windows\System32\MDMBridgeProv.exe" -ArgumentList "/reportnow" -NoNewWindow -Wait

# 🔄 9. Restart Windows Explorer
Write-Host "Restarting Windows Explorer..." -ForegroundColor Cyan
Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
Start-Process "explorer.exe"

# ✅ Final Message
Write-Host "Rollback Complete! Please RESTART your computer to fully apply changes." -ForegroundColor Green
Exit 0
