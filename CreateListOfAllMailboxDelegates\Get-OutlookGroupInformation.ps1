<#
.SYNOPSIS
    Retrieves Outlook group information (delegates, members, owners) or user-associated groups.

.DESCRIPTION
    This script provides options to:
    1) Export details of all Outlook groups and their delegates to a CSV file.
    2) Find all Outlook groups associated with a specific user (member, owner, delegate) and export to a CSV file.

.NOTES
    Author:      [Your Name]
    Created:     [Today's Date]
    Revised:     [Today's Date]
    Version:     1.1
    PowerShell:  Requires PowerShell 7+ and the ExchangeOnlineManagement module.
#>

# Ensure ExchangeOnlineManagement module is installed
try {
    Get-Module ExchangeOnlineManagement -ListAvailable
} catch {
    Install-Module ExchangeOnlineManagement -Force -AllowClobber
    Import-Module ExchangeOnlineManagement
}

Connect-ExchangeOnline

# Function to retrieve delegate members of an Outlook group
function Get-OutlookGroupDelegates {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string] $GroupName
    )

    Write-Verbose "Retrieving delegates for group: $GroupName"

    try {
        $delegates = Get-DistributionGroup $GroupName | 
            Get-DistributionGroupMember -ResultSize Unlimited |
            Select-Object @{Name = 'UserName'; Expression = {'N/A'}},
                          @{Name = 'GroupName'; Expression = { $GroupName }},
                          Name, 
                          PrimarySmtpAddress, 
                          @{Name = 'AccessType'; Expression = {'Delegate'}}

        if ($delegates) {
            Write-Verbose "Found $($delegates.Count) delegate(s) for $GroupName"
            return $delegates
        } else {
            Write-Verbose "No delegates found for $GroupName"
            return $null  # Return null if no delegates found
        }
    }
    catch {
        Write-Warning "Failed to retrieve delegates for $GroupName. $($_.Exception.Message)"
        return $null  # Return null on error
    }
}



# Function to retrieve user-associated groups (combines Exchange and Graph)
function Get-UserAssociatedGroups {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string] $Username
    )
    try {
        # Retrieve Exchange Groups
        Write-Verbose "Retrieving Exchange groups associated with user: $Username"
        $exchangeGroups = Get-Recipient -Filter "PrimarySmtpAddress -eq '$Username' -and RecipientTypeDetails -eq 'UserMailbox'"
        $exchangeFullAccessGroups = @()
        
        if ($exchangeGroups) {
            foreach ($group in $exchangeGroups) {
                $permissions = Get-MailboxPermission -Identity $group | Where-Object {
                    $_.User -eq $Username -and $_.AccessRights -contains 'FullAccess'
                }
                if ($permissions) {
                    $exchangeFullAccessGroups += [PSCustomObject]@{
                        UserName = $Username
                        GroupName = $group.DisplayName
                        GroupEmail = $group.PrimarySmtpAddress
                        AccessType = 'FullAccess'
                    }
                }
            }
        }

        # Retrieve Microsoft Graph Groups
        Write-Verbose "Retrieving Microsoft Graph groups associated with user: $Username"
        Connect-MgGraph -Scopes "User.Read.All","Group.ReadWrite.All"
        $userId = (Get-MgUser -Filter "userPrincipalName eq '$Username'").Id
        
        if ($userId) { 
            $graphGroups = Get-MgUserMemberOf -UserId $userId -All | Where-Object { $_.ODataType -eq '#microsoft.graph.group' } | 
                Select-Object @{Name = 'UserName'; Expression = { $Username }},
                              DisplayName, 
                              @{Name = 'GroupEmail'; Expression = {$_.Mail}}, 
                              @{Name = 'AccessType'; Expression = { if ($_.odata.type -eq '#microsoft.graph.group') { 'Member' } else { 'Owner' }}}
        } else {
            $graphGroups = @() # Initialize as empty array if no user found
        }

        # Combine and return groups
        $allGroups = $exchangeFullAccessGroups + $graphGroups
        return $allGroups
    }
    catch {
        Write-Warning "Error retrieving user-associated groups: $($_.Exception.Message)"
        return $null
    }
}



# Function to export group details to a CSV file
function Export-GroupDetailsToCSV {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [array] $GroupDetails,
        [Parameter(Mandatory = $true)]
        [string] $CsvPath
    )
    Write-Verbose "Exporting group details to CSV: $CsvPath"
    if ($GroupDetails) {
        try {
            $GroupDetails | Export-Csv -Path $CsvPath -NoTypeInformation
            Write-Host "CSV file created at $CsvPath"
        }
        catch {
            Write-Error "Failed to export to CSV. $($_.Exception.Message)"
        }
    } else {
        Write-Warning "No data to export."
    }
}

# --- Main Script Logic ---

$choice = Read-Host "Choose an option:`n1) Export all group info`n2) Find user-associated groups"

if ($choice -eq '1') {
    Write-Verbose "Retrieving all distribution groups..."
    $allGroups = Get-DistributionGroup -ResultSize Unlimited
    Write-Verbose "Found $($allGroups.Count) distribution group(s)"
    $allGroupDetails = $allGroups | ForEach-Object {
        $groupName = $_.DisplayName
        $groupDelegates = Get-OutlookGroupDelegates -GroupName $groupName
        if ($groupDelegates) { $groupDelegates } else {
            [PSCustomObject]@{ 'UserName' = 'N/A'; 'GroupName' = $groupName; 'Delegate Name' = 'N/A'; 'Delegate Email' = 'N/A'; 'AccessType' = 'Distribution List' }
        }
    }

    $csvPath = Read-Host "Enter CSV path (e.g., C:\group_details.csv):"
    Export-GroupDetailsToCSV -GroupDetails $allGroupDetails -CsvPath $csvPath

} elseif ($choice -eq '2') {
    $username = Read-Host "Enter username (e.g., <EMAIL>):"
    $userGroups = Get-UserAssociatedGroups -Username $username
    
    if ($userGroups) {
        $csvPath = Read-Host "Enter CSV path (e.g., C:\user_groups.csv):"
        Export-GroupDetailsToCSV -GroupDetails $userGroups -CsvPath $csvPath
    } else {
        Write-Host "No groups found for $username."
    } 
} else {
    Write-Warning "Invalid choice."
}
