﻿#install-PackageProvider -Name NuGet -MinimumVersion ********* -Force
#install-module  MicrosoftTeams -RequiredVersion '2.5.1'
#Import-Module MicrosoftTeams  #use da-account
Connect-MicrosoftTeams 
$identity = read-host "Please provide the email"
$phonenumber = read-host "Please provide the phone number"
#Lines 7+8 change the phone number, line 9 confirms changes, line 10 disconnects connection. Line 4 connects connection. Place the number on the end of line 7
#Set-CsPhoneNumberAssignment -Identity $identity -EnterpriseVoiceEnabled $true
#Grant-CsOnlineVoiceRoutingPolicy -Identity $identity -PolicyName "ThinkTel" -PhoneNumber $phonenumber -PhoneNumberType DirectRouting

#Get-CsOnlineUser -identity "<EMAIL>" | fl *Voice*, *PSTN*, *lineuri*


##
#you can only clear their lines in the Teams Admin Portal now
#To Re-Set a Teams Line use the next two commands
Grant-CsOnlineVoiceRoutingPolicy -Identity $identity -PolicyName Thinktel #need to clear their number at the Teams Admin Portal
set-CsPhoneNumberAssignment -Identity 	$identity -PhoneNumber $phonenumber


##


Get-CsOnlineUser -identity 	$identity | fl *Voice*, *PSTN*, *lineuri*
#Get-CsOnlineUser -identity "<EMAIL>" | fl *Voice*, *PSTN*, *lineuri*

Disconnect-MicrosoftTeams
