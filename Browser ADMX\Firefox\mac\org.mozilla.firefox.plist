<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>EnterprisePoliciesEnabled</key>
	<true/>
	<key>AllowedDomainsForApps</key>
	<string>managedfirefox.com,example.com</string>
	<key>AppAutoUpdate</key>
	<true/>
	<key>AppUpdatePin</key>
	<string>106.</string>
	<key>AppUpdateURL</key>
	<string>https://www.example.com/update.xml</string>
	<key>Authentication</key>
	<dict>
		<key>SPNEGO</key>
		<array>
			<string>mydomain.com</string>
			<string>https://myotherdomain.com</string>
		</array>
		<key>Delegated</key>
		<array>
			<string>mydomain.com</string>
			<string>https://myotherdomain.com</string>
		</array>
		<key>NTLM</key>
		<array>
			<string>mydomain.com</string>
			<string>https://myotherdomain.com</string>
		</array>
		<key>AllowNonFQDN</key>
		<dict>
			<key>SPNEGO</key>
			<true/>
			<key>NTLM</key>
			<true/>
		</dict>
		<key>AllowProxies</key>
		<dict>
			<key>SPNEGO</key>
			<true/>
			<key>NTLM</key>
			<true/>
		</dict>
		<key>PrivateBrowsing</key>
		<true/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>AutoLaunchProtocolsFromOrigins</key>
	<array>
		<dict>
			<key>protocol</key>
			<string>zoommtg</string>
			<key>allowed_origins</key>
			<array>
				<string>https://somesite.zoom.us</string>
			</array>
		</dict>
	</array>
	<key>BlockAboutAddons</key>
	<true/>
	<key>BlockAboutConfig</key>
	<true/>
	<key>BlockAboutProfiles</key>
	<true/>
	<key>BlockAboutSupport</key>
	<true/>
	<key>Bookmarks</key>
	<array>
		<dict>
			<key>Title</key>
			<string>Example1</string>
			<key>URL</key>
			<string>https://www.example.org</string>
			<key>Favicon</key>
			<string>https://www.example.org/favicon.ico</string>
			<key>Placement</key>
			<string>toolbar</string>
			<key>Folder</key>
			<string>Example1Folder</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Example2</string>
			<key>URL</key>
			<string>https://www.example.com</string>
			<key>Favicon</key>
			<string>https://www.example.com/favicon.ico</string>
			<key>Placement</key>
			<string>menu</string>
			<key>Folder</key>
			<string>Example2Folder</string>
		</dict>
	</array>
	<key>CaptivePortal</key>
	<false/>
	<key>Certificates</key>
	<dict>
		<key>ImportEnterpriseRoots</key>
		<true/>
		<key>Install</key>
		<array>
			<string>cert1.der</string>
			<string>cert2.pem</string>
		</array>
	</dict>
	<key>Default</key>
	<dict>
		<key>Containers</key>
		<array>
			<dict>
				<key>name</key>
				<string>My container</string>
				<key>icon</key>
				<string>pet</string>
				<key>color</key>
				<string>turquoise</string>
			</dict>
		</array>
	</dict>
	<key>Cookies</key>
	<dict>
		<key>Allow</key>
		<array>
			<string>https://www.example.org/</string>
		</array>
		<key>Allowsession</key>
		<array>
			<string>https://www.example.edu/</string>
		</array>
		<key>Block</key>
		<array>
			<string>https://www.example.edu/</string>
		</array>
		<key>Behavior</key>
		<string>limit-foreign</string>
		<key>Locked</key>
		<true/>
	</dict>
	<key>DefaultDownloadDirectory</key>
	<string>${home}/Downloads</string>
	<key>DownloadDirectory</key>
	<string>${home}/Downloads</string>
	<key>DNSOverHTTPS</key>
	<dict>
		<key>Enabled</key>
		<false/>
		<key>ProviderURL</key>
		<string>URL_TO_ALTERNATE_PROVIDER</string>
		<key>Locked</key>
		<true/>
		<key>ExcludedDomains</key>
		<array>
			<string>example.com</string>
		</array>
	</dict>
	<key>DisableAppUpdate</key>
	<true/>
	<key>DisableBuiltinPDFViewer</key>
	<true/>
	<key>DisabledCiphers</key>
	<dict>
		<key>TLS_DHE_RSA_WITH_AES_128_CBC_SHA</key>
		<true/>
		<key>TLS_DHE_RSA_WITH_AES_256_CBC_SHA</key>
		<true/>
		<key>TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA</key>
		<true/>
		<key>TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA</key>
		<true/>
		<key>TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256</key>
		<true/>
		<key>TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256</key>
		<true/>
		<key>TLS_RSA_WITH_AES_128_CBC_SHA</key>
		<true/>
		<key>TLS_RSA_WITH_AES_256_CBC_SHA</key>
		<true/>
		<key>TLS_RSA_WITH_3DES_EDE_CBC_SHA</key>
		<true/>
		<key>TLS_RSA_WITH_AES_128_GCM_SHA256</key>
		<false/>
		<key>TLS_RSA_WITH_AES_256_GCM_SHA384</key>
		<false/>
	</dict>
	<key>DisableDeveloperTools</key>
	<true/>
	<key>DisableEncryptedClientHello</key>
	<true/>
	<key>DisableFeedbackCommands</key>
	<true/>
	<key>DisableFirefoxAccounts</key>
	<true/>
	<key>DisableFirefoxScreenshots</key>
	<true/>
	<key>DisableFirefoxStudies</key>
	<true/>
	<key>DisableForgetButton</key>
	<true/>
	<key>DisableFormHistory</key>
	<true/>
	<key>DisableMasterPasswordCreation</key>
	<true/>
	<key>DisablePasswordReveal</key>
	<true/>
	<key>DisablePrivateBrowsing</key>
	<true/>
	<key>DisableProfileImport</key>
	<true/>
	<key>DisableProfileRefresh</key>
	<true/>
	<key>DisableSafeMode</key>
	<true/>
	<key>DisableSecurityBypass</key>
	<dict>
		<key>InvalidCertificate</key>
		<true/>
		<key>SafeBrowsing</key>
		<true/>
	</dict>
	<key>DisableSetDesktopBackground</key>
	<true/>
	<key>DisableSystemAddonUpdate</key>
	<true/>
	<key>DisableTelemetry</key>
	<true/>
	<key>DisplayBookmarksToolbar</key>
	<true/>
	<key>DontCheckDefaultBrowser</key>
	<true/>
	<key>EnableTrackingProtection</key>
	<dict>
		<key>Value</key>
		<true/>
		<key>Locked</key>
		<true/>
		<key>Cryptomining</key>
		<true/>
		<key>Fingerprinting</key>
		<true/>
		<key>EmailTracking</key>
		<true/>
		<key>Exceptions</key>
		<array>
			<string>https://example.com</string>
		</array>
	</dict>
	<key>EncryptedMediaExtensions</key>
		<dict>
		<key>Enabled</key>
		<false/>
		<key>Locked</key>
		<false/>
	</dict>
	<key>ExemptDomainFileTypePairsFromFileTypeDownloadWarnings</key>
	<array>
		<dict>
			<key>file_extension</key>
			<string>jnlp</string>
			<key>domains</key>
			<array>
				<string>example.com</string>
			</array>
		</dict>
	</array>
	<key>Extensions</key>
	<dict>
		<key>Install</key>
		<array>
			<string>https://addons.mozilla.org/firefox/downloads/file/1053714/ghostery_privacy_ad_blocker-8.2.4-an+fx.xpi</string>
		</array>
		<key>Uninstall</key>
		<array/>
		<key>Locked</key>
		<array>
			<string><EMAIL></string>
		</array>
	</dict>
	<key>ExtensionSettings</key>
	<dict>
		<key>*</key>
		<dict>
			<key>blocked_install_message</key>
			<string>Custom error message.</string>
			<key>install_sources</key>
			<array>
				<string>https://addons.mozilla.org/</string>
			</array>
			<key>installation_mode</key>
			<string>blocked</string>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>installation_mode</key>
			 <string>force_installed</string>
			<key>install_url</key>
			<string>https://addons.mozilla.org/firefox/downloads/latest/ublock-origin/latest.xpi</string>
		</dict>
	</dict>
	<key>ExtensionUpdate</key>
	<false/>
	<key>FirefoxHome</key>
	<dict>
		<key>Search</key>
		<true/>
		<key>TopSites</key>
		<true/>
		<key>SponsoredTopSites</key>
		<false/>
		<key>Highlights</key>
		<true/>
		<key>Pocket</key>
		<false/>
		<key>SponsoredPocket</key>
		<false/>
		<key>Snippets</key>
		<false/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>FirefoxHome</key>
	<dict>
		<key>WebSuggestions</key>
		<true/>
		<key>SponsoredSuggestions</key>
		<false/>
		<key>ImproveSuggest</key>
		<false/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>GoToIntranetSiteForSingleWordEntryInAddressBar</key>
	<true/>
	<key>Handlers</key>
	<dict>
		<key>mimeTypes</key>
		<dict>
			<key>application/msword</key>
			<dict>
				<key>action</key>
				<string>useSystemDefault</string>
				<key>ask</key>
				<false/>
			</dict>
		</dict>
		<key>schemes</key>
		<dict>
			<key>mailto</key>
			<dict>
				<key>action</key>
				<string>useHelperApp</string>
				<key>ask</key>
				<false/>
				<key>handlers</key>
				<array>
					<dict>
						<key>name</key>
						<string>Gmail</string>
						<key>uriTemplate</key>
						<string>https://mail.google.com/mail/?extsrc=mailto&amp;url=%s</string>
					</dict>
				</array>
			</dict>
		</dict>
		<key>extensions</key>
		<dict>
			<key>pdf</key>
			<dict>
				<key>action</key>
				<string>useHelperApp</string>
				<key>ask</key>
				<false/>
				<key>handlers</key>
				<array>
					<dict>
						<key>name</key>
						<string>Adobe Acrobat</string>
						<key>path</key>
						<string>/System/Applications/Preview.app</string>
					</dict>
				</array>
			</dict>
		</dict>
	</dict>
	<key>HardwareAcceleration</key>
	<false/>
	<key>Homepage</key>
	<dict>
		<key>URL</key>
		<string>http://example.com</string>
		<key>Locked</key>
		<true/>
		<key>Additional</key>
		<array>
			<string>https://www.example.com/extra-home1.htm</string>
			<string>https://www.example.com/extra-home2.htm</string>
			<string>https://www.example.com/extra-home3.htm</string>
		</array>
		<key>StartPage</key>
		<string>homepage</string>
	</dict>
	<key>HttpAllowlist</key>
	<array>
		<string>http://example.org</string>
		<string>http://example.edu</string>
	</array>
	<key>HttpsOnlyMode</key>
	<string>disallowed</string>
	<key>InstallAddonsPermission</key>
	<dict>
		<key>Allow</key>
		<array>
			<string>https://example.org</string>
			<string>https://example.edu</string>
		</array>
		<key>Default</key>
		<false/>
	</dict>
	<key>LocalFileLinks</key>
	<array>
		<string>http://example.org</string>
		<string>http://example.edu</string>
	</array>
	<key>PrimaryPassword</key>
	<true/>
	<key>MicrosoftEntraSSO</key>
	<true/>
	<key>NetworkPrediction</key>
	<false/>
	<key>NewTabPage</key>
	<false/>
	<key>NoDefaultBookmarks</key>
	<true/>
	<key>OfferToSaveLogins</key>
	<false/>
	<key>OfferToSaveLoginsDefault</key>
	<true/>
	<key>OverrideFirstRunPage</key>
	<string>https://www.example.com</string>
	<key>OverridePostUpdatePage</key>
	<string></string>
	<key>PasswordManagerEnabled</key>
	<false/>
	<key>PDFjs</key>
		<dict>
		<key>Enabled</key>
		<false/>
		<key>EnablePermissions</key>
		<false/>
	</dict>
	<key>PasswordManagerExceptions</key>
	<array>
		<string>https://example.org</string>
		<string>https://example.edu</string>
	</array>
	<key>Permissions</key>
	<dict>
		<key>Camera</key>
		<dict>
			<key>Allow</key>
			<array>
				<string>https://example.org</string>
			</array>
			<key>Block</key>
			<array>
				<string>https://example.edu</string>
			</array>
			<key>BlockNewRequests</key>
			<true/>
			<key>Locked</key>
			<true/>
		</dict>
		<key>Microphone</key>
		<dict>
			<key>Allow</key>
			<array>
				<string>https://example.org</string>
			</array>
			<key>Block</key>
			<array>
				<string>https://example.edu</string>
			</array>
			<key>BlockNewRequests</key>
			<true/>
			<key>Locked</key>
			<true/>
		</dict>
		<key>Location</key>
		<dict>
			<key>Allow</key>
			<array>
				<string>https://example.org</string>
			</array>
			<key>Block</key>
			<array>
				<string>https://example.edu</string>
			</array>
			<key>BlockNewRequests</key>
			<true/>
			<key>Locked</key>
			<true/>
		</dict>
		<key>Notifications</key>
		<dict>
			<key>Allow</key>
			<array>
				<string>https://example.org</string>
			</array>
			<key>Block</key>
			<array>
				<string>https://example.edu</string>
			</array>
			<key>BlockNewRequests</key>
			<true/>
			<key>Locked</key>
			<true/>
		</dict>
		<key>Autoplay</key>
		<dict>
			<key>Allow</key>
			<array>
				<string>https://example.org</string>
			</array>
			<key>Block</key>
			<array>
				<string>https://example.edu</string>
			</array>
			<key>Default</key>
				<string>block-audio</string>
			<key>Locked</key>
			<true/>
		</dict>
		<key>VirtualReality</key>
		<dict>
			<key>Allow</key>
			<array>
				<string>https://example.org</string>
			</array>
			<key>Block</key>
			<array>
				<string>https://example.edu</string>
			</array>
			<key>BlockNewRequests</key>
			<true/>
			<key>Locked</key>
			<true/>
		</dict>
	</dict>
	<key>PictureInPicture</key>
	<dict>
		<key>Enabled</key>
		<false/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>PopupBlocking</key>
	<dict>
		<key>Allow</key>
		<array>
			<string>https://www.example.org</string>
			<string>https://www.example.edu</string>
		</array>
		<key>Default</key>
		<true/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>PostQuantumKeyAgreementEnabled</key>
	<false/>
	<key>Preferences</key>
	<dict>
		<key>accessibility.force_disabled</key>
		<dict>
			<key>Value</key>
			<integer>1</integer>
			<key>Status</key>
			<string>default</string>
			</dict>
		<key>browser.cache.disk.parent_directory</key>
		<dict>
			<key>Value</key>
			<string>SOME_NATIVE_PATH</string>
			<key>Status</key>
			<string>user</string>
		</dict>
		<key>browser.tabs.warnOnClose</key>
		<dict>
			<key>Value</key>
			<false/>
			<key>Status</key>
			<string>locked</string>
		</dict>
	</dict>
	<key>PrintingEnabled</key>
	<false/>
	<key>PrivateBrowsingModeAvailability</key>
	<integer>1</integer>
	<key>Proxy</key>
	<dict>
		<key>Mode</key>
		<string>manual</string>
		<key>HTTPProxy</key>
		<string>proxy.example.com:80</string>
		<key>UseHTTPProxyForAllProtocols</key>
		<true/>
		<key>SSLProxy</key>
		<string>proxy.example.com:80</string>
		<key>FTPProxy</key>
		<string>proxy.example.com:80</string>
		<key>SOCKSProxy</key>
		<string>proxy.example.com:80</string>
		<key>SocksVersion</key>
		<string>4</string>
		<key>Passthrough</key>
		<string>&lt;local&gt;</string>
		<key>AutoConfigURL</key>
		<string>URL_TO_AUTOCONFIG</string>
		<key>AutoLogin</key>
		<true/>
		<key>UseProxyForDNS</key>
		<true/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>RequestedLocales</key>
	<array>
		<string>de</string>
		<string>en-US</string>
	</array>
	<key>SanitizeOnShutdown</key>
	<dict>
		<key>Cache</key>
		<true/>
		<key>Cookies</key>
		<true/>
		<key>History</key>
		<true/>
		<key>Sessions</key>
		<true/>
		<key>SiteSettings</key>
		<true/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>SearchBar</key>
	<string>separate</string>
	<key>SecurityDevices</key>
	<dict>
		<key>NAME_OF_DEVICE</key>
		<string>PATH_TO_LIBRARY_FOR_DEVICE</string>
	</dict>
	<key>ShowHomeButton</key>
	<true/>
	<key>SkipTermsOfUse</key>
	<true/>
	<key>SSLVersionMin</key>
	<string>tls1.2</string>
	<key>SSLVersionMax</key>
	<string>tls1.3</string>
	<key>StartDownloadsInTempDirectory</key>
	<true/>
	<key>SupportMenu</key>
	<dict>
		<key>Title</key>
		<string>Click here for help</string>
		<key>URL</key>
		<string>http://example.edu/</string>
		<key>AccessKey</key>
		<string>C</string>
	</dict>
	<key>TranslateEnabled</key>
	<true/>
	<key>UserMessaging</key>
	<dict>
		<key>ExtensionRecommendations</key>
		<false/>
		<key>FeatureRecommendations</key>
		<false/>
		<key>UrlbarInterventions</key>
		<false/>
		<key>SkipOnboarding</key>
		<true/>
		<key>MoreFromMozilla</key>
		<true/>
		<key>FirefoxLabs</key>
		<true/>
		<key>Locked</key>
		<true/>
	</dict>
	<key>UseSystemPrintDialog</key>
	<true/>
	<key>WebsiteFilter</key>
	<dict>
		<key>Block</key>
		<array>
			<string>&lt;all_urls&gt;</string>
		</array>
		<key>Exceptions</key>
		<array>
			<string>https://www.google.com/*</string>
			<string>https://www.yahoo.com/*</string>
		</array>
	</dict>
</dict>
</plist>
