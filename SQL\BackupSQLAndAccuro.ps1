<#
.SYNOPSIS
    This script is designed to back up all SQL Server databases and Accuro-related directories to the user's OneDrive.

.DESCRIPTION
    - Backs up all SQL Server databases from all instances detected on the local machine.
    - Recursively backs up all directories containing "Accuro" under "C:\Program Files (x86)".
    - Stores backups in OneDrive in specific folders:
        - "SQL Backups" for database backups.
        - "Accuro Backups" for Accuro-related files.
    - Creates OneDrive subfolders if they do not exist.
    - Logs detailed, verbose information during each step of the process for troubleshooting.

.PARAMETER None
    This script does not take any parameters. It resolves all necessary paths and performs operations dynamically.

.NOTES
    Author   : <PERSON><PERSON>ppard
    Date     : 2024-11-14
    Version  : 1.0
    Script Name: BackupSQLAndAccuro.ps1
    Purpose  : To automate SQL database and Accuro backup for easy transferability.

.EXAMPLE
    This script is intended to be run by Intune or a system context:
    
    .\BackupSQLAndAccuro.ps1

    The script will perform the backups and log all operations to a log file located in C:\Temp.

#>

# Define the log file path (single log file for all output)
$LogFile = "C:\Temp\BackupSQLAndAccuro.log"

# Ensure the Temp directory exists
if (-not (Test-Path -Path "C:\Temp")) {
    New-Item -ItemType Directory -Path "C:\Temp" -Force | Out-Null
}

# Suppress all console output globally
$ErrorActionPreference = "Stop"
$InformationPreference = "SilentlyContinue"
$WarningPreference = "SilentlyContinue"
$ProgressPreference = "SilentlyContinue"

# Global variable to store the OneDrive path, resolved once
$global:OneDrivePath = $null

Function Write-LogMessage {
    param (
        [string]$Message,
        [string]$Level = "INFO"  # Levels: INFO, WARNING, ERROR, SUCCESS
    )

    switch ($Level) {
        "INFO"    { $Prefix = "[INFO]    " }
        "WARNING" { $Prefix = "[WARNING] " }
        "ERROR"   { $Prefix = "[ERROR]   " }
        "SUCCESS" { $Prefix = "[SUCCESS] " }
        default   { $Prefix = "[LOG]     " }
    }

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "$timestamp $Prefix $Message"

    # Implement a file lock retry mechanism to avoid file-in-use errors
    for ($i = 0; $i -lt 5; $i++) {
        try {
            Add-Content -Path $LogFile -Value $logEntry
            break  # Exit loop if successful
        } catch {
            Start-Sleep -Milliseconds 100  # Wait briefly before retrying
            if ($i -eq 4) {
                # Log to fallback file if we fail after retries
                $fallbackLog = "C:\Temp\Fallback-BackupSQLAndAccuro.log"
                Add-Content -Path $fallbackLog -Value "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] [ERROR] Could not write to primary log file after multiple attempts: $($_.Exception.Message)"
            }
        }
    }
}

Function Get-OneDriveFolder {
    if ($null -ne $global:OneDrivePath) {
        # Return cached OneDrive path if already resolved
        return $global:OneDrivePath
    }

    Write-LogMessage "Attempting to locate the OneDrive folder..." -Level "INFO"

    try {
        $oneDrivePaths = Get-ChildItem -Path "$env:USERPROFILE" -Directory -Filter "OneDrive*" -ErrorAction Stop |
            Where-Object { $_.FullName -like "*OneDrive - QHR Technologies*" }

        if ($oneDrivePaths.Count -eq 1) {
            $global:OneDrivePath = $oneDrivePaths[0].FullName
            Write-LogMessage "OneDrive path resolved to: $($global:OneDrivePath)" -Level "SUCCESS"
            return $global:OneDrivePath
        } elseif ($oneDrivePaths.Count -gt 1) {
            $correctPath = $oneDrivePaths | Where-Object { $_.FullName -like "*OneDrive - QHR Technologies*" } | Select-Object -First 1
            if ($correctPath) {
                $global:OneDrivePath = $correctPath.FullName
                Write-LogMessage "Multiple OneDrive paths found. Using: $($global:OneDrivePath)" -Level "WARNING"
                return $global:OneDrivePath
            } else {
                throw "No specific OneDrive path containing 'OneDrive - QHR Technologies' could be resolved."
            }
        } else {
            throw "OneDrive folder not found."
        }
    } catch {
        Write-LogMessage "Failed to resolve OneDrive folder: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

Function Test-Environment {
    Write-LogMessage "Validating environment prerequisites..." -Level "INFO"

    # Check PowerShell version compatibility
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        Write-LogMessage "PowerShell 5.0 or higher is required. Current version: $($PSVersionTable.PSVersion)" -Level "ERROR"
        exit 1
    }

    # Check for Invoke-Sqlcmd availability
    if (-not (Get-Command -Name "Invoke-Sqlcmd" -ErrorAction SilentlyContinue)) {
        Write-LogMessage "Invoke-Sqlcmd is not available. Ensure SQL Server tools are installed." -Level "WARNING"
    } else {
        Write-LogMessage "Invoke-Sqlcmd is available." -Level "INFO"
    }

    # Validate OneDrive directory
    Get-OneDriveFolder | Out-Null
}

Function New-BackupFolder {
    param (
        [string]$FolderName
    )

    Write-LogMessage "Creating or validating backup folder: $FolderName" -Level "INFO"

    $OneDrivePath = Get-OneDriveFolder
    $BackupFolder = Join-Path -Path $OneDrivePath -ChildPath $FolderName

    try {
        if (-not (Test-Path $BackupFolder)) {
            New-Item -ItemType Directory -Path $BackupFolder -Force | Out-Null
            Write-LogMessage "Backup folder created: $BackupFolder" -Level "SUCCESS"
        } else {
            Write-LogMessage "Backup folder already exists: $BackupFolder" -Level "INFO"
        }
    } catch {
        Write-LogMessage "Error creating or accessing backup folder: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }

    return $BackupFolder
}

Function Backup-SQLDatabases {
    $SqlBackupFolder = New-BackupFolder -FolderName "SQL Backups"

    # Detect all SQL Server instances and attempt backups
    $sqlInstances = Get-Service | Where-Object { $_.DisplayName -like "SQL Server*" } | Select-Object -ExpandProperty Name

    if ($sqlInstances.Count -eq 0) {
        Write-LogMessage "No SQL Server instances found on this machine." -Level "WARNING"
        return
    }

    foreach ($instance in $sqlInstances) {
        $instanceName = if ($instance -eq "MSSQLSERVER") { "." } else { ".\$instance" }
        Write-LogMessage "Starting backup for SQL instance: $instanceName" -Level "INFO"

        try {
            $databases = Invoke-Sqlcmd -Query "SELECT name FROM sys.databases WHERE state = 0" -ServerInstance $instanceName
            foreach ($db in $databases.name) {
                $backupPath = Join-Path -Path $SqlBackupFolder -ChildPath "$($db).bak"
                Write-LogMessage "Backing up database '$($db)' to $backupPath" -Level "INFO"

                Invoke-Sqlcmd -Query "BACKUP DATABASE [$db] TO DISK = '$backupPath'" -ServerInstance $instanceName
                Write-LogMessage "Successfully backed up database '$($db)'." -Level "SUCCESS"
            }
        } catch {
            Write-LogMessage "Failed to back up databases for instance $instanceName. Error: $($_.Exception.Message)" -Level "ERROR"
        }
    }
}

Function Backup-AccuroFiles {
    $AccuroBackupFolder = New-BackupFolder -FolderName "Accuro Backups"
    $AccuroRootPath = "C:\Program Files (x86)"

    Write-LogMessage "Starting backup of Accuro files under $AccuroRootPath..." -Level "INFO"
    try {
        $accuroFolders = Get-ChildItem -Path $AccuroRootPath -Directory -Recurse -ErrorAction Stop | Where-Object { $_.Name -like "*Accuro*" }
        foreach ($folder in $accuroFolders) {
            $destination = Join-Path -Path $AccuroBackupFolder -ChildPath $folder.Name
            Write-LogMessage "Copying Accuro folder $($folder.FullName) to $destination" -Level "INFO"

            try {
                Copy-Item -Path $folder.FullName -Destination $destination -Recurse -Force
                Write-LogMessage "Successfully backed up Accuro folder $($folder.FullName)" -Level "SUCCESS"
            } catch {
                Write-LogMessage "Failed to copy Accuro folder $($folder.FullName). Error: $($_.Exception.Message)" -Level "ERROR"
            }
        }
        if ($accuroFolders.Count -eq 0) {
            Write-LogMessage "No Accuro folders found for backup." -Level "WARNING"
        }
    } catch {
        Write-LogMessage "Error locating Accuro files: $($_.Exception.Message)" -Level "ERROR"
    }
}

try {
    Test-Environment

    Write-LogMessage "Starting SQL and Accuro backup process..." -Level "INFO"

    Backup-SQLDatabases
    Backup-AccuroFiles

    Write-LogMessage "Backup process completed successfully." -Level "SUCCESS"
    $global:ExitCode = 0
} catch {
    Write-LogMessage "Unexpected script failure: $($_.Exception.Message)" -Level "ERROR"
    $global:ExitCode = 1
} finally {
    Write-LogMessage "Script execution completed. Exit code: $global:ExitCode" -Level "INFO"
    exit $global:ExitCode
}




