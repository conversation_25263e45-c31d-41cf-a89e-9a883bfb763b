﻿## Connect to Office 365 (http://technet.microsoft.com/library/jj151815.aspx#BKMK_connect):
#$LiveCred = Get-Credential <EMAIL>
#$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
#Import-PSSession $Session -AllowClobber
#coConnect-MsolService -Credential $LiveCred

Try {
  $connected = Get-mailbox <EMAIL>
  Write-Host "Already Connected to SharePoint Online!`n`n" -ForegroundColor Green
}
Catch {
  $LiveCred = Get-Credential <EMAIL>

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
Connect-MsolService -Credential $LiveCred
 }

$disabledlic = get-msoluser -all | ? {$_.blockcredential -eq $true -and $_.islicensed -eq $true }| select UserPrincipalName,isLicensed,BlockCredential
Write-Host "`n`nDisabled Accounts that are Licensed!!!"
foreach ($disuser in $disabledlic) {
    $changemailbox = Read-Host "Change $($disuser.userprincipalname) to a Shared Mailbox (y/n)???"
    if ($changemailbox -eq "y") {
        Write-Host "Changing attributes..."
        Set-Mailbox $disuser.UserPrincipalName -Type shared
        $samaccount = $disuser.UserPrincipalName.ToLower().Replace('@qhrtech.com','')
        set-aduser $samaccount -Replace @{msExchRemoteRecipientType = 100; msExchRecipientTypeDetails = ***********}
    }
}

$checklic = Get-Mailbox -Filter * | ? recipienttypedetails -eq "SharedMailbox" | get-msoluser |? islicensed -eq $true | select DisplayName

if ($checklic -ne $null) {

$checklic
$removelic = Read-Host "Do you want to remove the above licenses? (y/n)"
if ($removelic -eq "y") {
    Get-Mailbox -Filter * | ? recipienttypedetails -eq "SharedMailbox" | get-msoluser |? islicensed -eq $true | Set-MsolUserLicense -RemoveLicenses qhrtech:ENTERPRISEPACK
    Write-Host "Licenses Removed!!!"
}
}
else {
    Write-Host "No Licenses Found"
}


Exit-PSSession