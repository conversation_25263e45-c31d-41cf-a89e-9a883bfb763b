﻿$ExcludeList = @('leavemgmt','delphiuser','osdevah','careers','HR','CDS-BackupStatus','qhrtechprivacyoffice','IT','accuro','Investor','Implementations','qti_events','qtstaffmeetings','OSAdmin','TFSREPORTS','QTAdmin','cds-info','CDS-BackupStatus','ONInvoices','infracalendar','CSR','accounting','opt-graphics','qtmitelccm','expenses','NPUMAdmin','qtmarketing','markevents','FinancialsSupport','Marketing','helpdesk','certification','qhrsoftwareprivacyof','qssales','qstw','FinDev','qscw','supfeedback','ahfsup','afwsup','OPT_Support','optimedprivacyoffice','optdev','cc-support','OSCareers','osse','cwts','HS','hssupport','pictatesupport','TS','Optcslt','publicO','smanager','sadmin','dataanalyst','opttraining')
Get-RemoteMailbox -ResultSize Unlimited  |where {$_.HiddenFromAddressListsEnabled -eq $false -and $_.EmailAddressPolicyEnabled -eq $false -and $_.RecipientTypeDetails -eq "RemoteUserMailbox" -and $ExcludeList -notcontains $_.SAMAccountName} | Select Name, SAMAccountName, primarysmtpaddress