# Check if Microsoft.Graph module is installed, if not, install it
if (-not (Get-Module -Name Microsoft.Graph -ListAvailable)) {
    Write-Verbose "Installing Microsoft.Graph module..."
    Install-Module -Name Microsoft.Graph -Force -Scope CurrentUser -Verbose:$false
}

# Check if Microsoft.Graph module is imported
if (-not (Get-Module -Name Microsoft.Graph -ListAvailable)) {
    Write-Verbose "Microsoft.Graph module not found. Attempting to import Microsoft.Graph module..."
    try {
        Import-Module -Name Microsoft.Graph -ErrorAction Stop
    }
    catch {
        Write-Error "Failed to import Microsoft.Graph module. $_"
        exit
    }
}
else {
    Write-Verbose "Microsoft.Graph module already imported."
}

# Set VerbosePreference to 'Continue'
$VerbosePreference = 'Continue'

# Connect to Microsoft Graph
try {
    Write-Verbose "Connecting to Microsoft Graph..."
    Connect-MgGraph -Scopes "User.Read.All", "Reports.Read.All"
}
catch {
    Write-Error "Failed to connect to Microsoft Graph. $_"
    exit
}

# Ask the user for the output file path
$outputFilePath = Read-Host -Prompt "Enter the path where you would like to save the report (e.g., C:\path\to\your\report.csv)"

try {
    # Retrieve Teams user activity report
    Write-Verbose "Retrieving Teams user activity report..."
    $report = Get-MgReportTeamUserActivityUserDetail -Period "D7" -ErrorAction Stop -OutFile $outputFilePath
}
catch {
    Write-Error "Failed to retrieve Teams user activity report: $_"
    exit
}

if (-not $report) {
    Write-Error "The report is empty or null. Check if there is data available for the specified period."
    exit
}

# Check the properties available in the report
$report[0] | Format-List *

# Initialize variables to count users
$oldTeamsUsers = 0
$newTeamsUsers = 0

# Analyze the report data
foreach ($row in $report) {
    if ($row.UserPrincipalName -match "@qhrtech.com" -and $row.ClientVersion -match ".*3\.\d+\.\d+\.\d*") {
        $oldTeamsUsers++
    }
    elseif ($row.UserPrincipalName -match "@qhrtech.com" -and $row.ClientVersion -match ".*4\.\d+\.\d+\.\d*") {
        $newTeamsUsers++
    }
}


# Output the results to a CSV file
$results = @{
    "Number of users on old Teams (Teams Classic)" = $oldTeamsUsers
    "Number of users on new Teams" = $newTeamsUsers
}

$results.GetEnumerator() | Export-Csv -Path $outputFilePath -NoTypeInformation

Write-Host "Report has been saved to $outputFilePath"
