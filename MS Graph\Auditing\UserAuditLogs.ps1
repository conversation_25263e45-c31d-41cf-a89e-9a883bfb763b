<#
.SYNOPSIS
    Retrieves Microsoft 365 audit logs for a specific user and date range,
    implements pagination for large result sets, and exports to Excel with
    deeply flattened JSON data, placing RawAuditData last.

.DESCRIPTION
    This script prompts for UPN and date range, connects to Exchange Online, searches
    the Microsoft Purview unified audit log (which includes Entra ID, Exchange, SharePoint,
    Teams, etc.), and exports findings to 'C:\temp\userauditlogs'.

    Key Features:
    - Pagination: Automatically fetches records in batches of 5000 to retrieve
      more comprehensive data for the given date range.
    - Deep JSON Flattening: Recursively flattens nested JSON objects and parts of arrays
      from the 'AuditData' field into individual columns (prefixed 'AD_').
    - Raw Data Last: The 'RawAuditData' column is placed at the far right in Excel.

    Required modules (ExchangeOnlineManagement, ImportExcel) are checked and installation offered.
    Permissions: "Audit Logs" or "View-Only Audit Logs" role in Purview needed.

.PARAMETER UserPrincipalName
    User Principal Name (email) for whom to retrieve audit logs. Prompts if not provided.

.PARAMETER StartDateInput
    Optional. Start date (e.g., 'YYYY-MM-DD'). Prompts if not provided.

.PARAMETER EndDateInput
    Optional. End date (e.g., 'YYYY-MM-DD'). Prompts if not provided.

.EXAMPLE
    .\Get-UserAuditLogs.ps1
    (Prompts for UPN, Start Date, End Date, then sign-in.)

.EXAMPLE
    .\Get-UserAuditLogs.ps1 -UserPrincipalName "<EMAIL>" -StartDateInput "2025-05-01" -EndDateInput "2025-05-31"

.NOTES
    Author: Slader Sheppard
    Version: 1.8
    Date: May 30, 2025

    Log Sources: Search-UnifiedAuditLog queries the central Microsoft Purview audit log,
    aggregating data from numerous M365 services (Entra ID, Exchange, SharePoint, etc.).

    Flattening Details:
    - MaxDepth for object flattening is 2 levels deep (e.g., AD_Parent_Child_Grandchild).
    - MaxArrayElements (default 3) are flattened for arrays of objects.
    - Adjust MaxDepth/MaxArrayElements in Flatten-Object call for different detail levels.

    Prerequisites: PowerShell 5.1+, Internet, M365 permissions.
    Execution Policy: May need 'Set-ExecutionPolicy RemoteSigned -Scope CurrentUser'.
#>                                                                                                                                                                                              
[CmdletBinding()]
param (
    [Parameter(Mandatory = $false, HelpMessage = "Enter the User Principal Name (e.g., <EMAIL>)")]
    [string]$UserPrincipalName,

    [Parameter(Mandatory = $false, HelpMessage = "Enter the Start Date for the audit log search (e.g., 'YYYY-MM-DD').")]
    [string]$StartDateInput,

    [Parameter(Mandatory = $false, HelpMessage = "Enter the End Date for the audit log search (e.g., 'YYYY-MM-DD').")]
    [string]$EndDateInput
)

# --- Script Variables ---
$OutputDirectory = "C:\temp\userauditlogs"
$TranscriptFile = "UserAuditLogScript_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
$Global:ScriptTranscriptPath = Join-Path -Path $OutputDirectory -ChildPath $TranscriptFile

# --- Helper Function to Ensure Modules are Installed ---
function Test-ModuleInstalled {
    param (
        [string]$ModuleName,
        [string]$ModuleFriendlyName
    )
    Process {
        Write-Verbose "Checking if module '$ModuleName' ($ModuleFriendlyName) is installed..."
        try {
            $InstalledModule = Get-Module -Name $ModuleName -ListAvailable -ErrorAction SilentlyContinue
            if (-not $InstalledModule) {
                Write-Warning "Module '$ModuleName' ($ModuleFriendlyName) is not found."
                $Choice = Read-Host "Do you want to attempt to install module '$ModuleName' from the PowerShell Gallery? (Y/N)"
                if ($Choice -match '^Y$') {
                    Write-Host "Installing module '$ModuleName'..."
                    Install-Module $ModuleName -Scope CurrentUser -Force -AllowClobber -Confirm:$false -ErrorAction Stop
                    Write-Host "Module '$ModuleName' installed successfully."
                    Import-Module $ModuleName -ErrorAction Stop
                    Write-Verbose "Module '$ModuleName' imported."
                }
                else {
                    Write-Error "Module '$ModuleName' ($ModuleFriendlyName) is required."
                    throw "Module '$ModuleName' not available."
                }
            }
            else {
                Write-Verbose "Module '$ModuleName' ($ModuleFriendlyName) is already installed."
                if (-not (Get-Module -Name $ModuleName -ErrorAction SilentlyContinue)) {
                    Import-Module $ModuleName -ErrorAction Stop
                    Write-Verbose "Module '$ModuleName' ($ModuleFriendlyName) imported."
                }
            }
            return $true
        }
        catch {
            Write-Error "An error occurred with module '$ModuleName' ($ModuleFriendlyName): $($_.Exception.Message)"
            throw
        }
    }
}

# --- Recursive Object Flattening Function ---
function ConvertTo-FlatObject {
    param (
        $InputObject,
        [string]$BasePrefix = "",
        [int]$MaxDepth = 2,      
        [int]$CurrentDepth = 0,
        [int]$MaxArrayElements = 3 
    )
    $Result = [ordered]@{}
    if ($CurrentDepth -gt $MaxDepth) {
        $SafeBasePrefix = $BasePrefix.TrimEnd('_'); if ([string]::IsNullOrEmpty($SafeBasePrefix)) { $SafeBasePrefix = "Value_MaxDepthReached" }
        $Result[$SafeBasePrefix] = $InputObject | ConvertTo-Json -Depth 2 -Compress -WarningAction SilentlyContinue 
        Write-Verbose "Max depth ($MaxDepth) reached for prefix '$BasePrefix'. Remainder converted to JSON."
        return $Result
    }
    if ($InputObject -is [array]) {
        $i = 0
        foreach ($item in $InputObject) {
            if ($i -ge $MaxArrayElements) {
                Write-Verbose "Max array elements ($MaxArrayElements) for '$BasePrefix'. Remaining not individually flattened."
                $RemainingPrefix = ($BasePrefix + $i + "_PlusRemainingItems").TrimEnd('_')
                try { $Result[$RemainingPrefix] = ($InputObject[$i..($InputObject.Count - 1)] | ConvertTo-Json -Depth ($MaxDepth - $CurrentDepth + 1) -Compress -WarningAction SilentlyContinue) } catch {}
                break 
            }
            $ItemPrefix = "${BasePrefix}$($i)_" 
            if ($item -is [System.Management.Automation.PSCustomObject] -or $item -is [array]) {
                $NestedFlattened = ConvertTo-FlatObject -InputObject $item -BasePrefix $ItemPrefix -MaxDepth $MaxDepth -CurrentDepth ($CurrentDepth + 1) -MaxArrayElements $MaxArrayElements
                $NestedFlattened.GetEnumerator() | ForEach-Object { $Result[$_.Name] = $_.Value }
            }
            else { $Result[$ItemPrefix.TrimEnd('_')] = $item }
            $i++
        }
    }
    elseif ($InputObject -is [System.Management.Automation.PSCustomObject]) {
        foreach ($property in $InputObject.PSObject.Properties) {
            $NewPrefix = if ([string]::IsNullOrEmpty($BasePrefix)) { "$($property.Name)_" } else { "$($BasePrefix)$($property.Name)_" }
            if ($property.Value -is [System.Management.Automation.PSCustomObject] -or $property.Value -is [array]) {
                $NestedFlattened = ConvertTo-FlatObject -InputObject $property.Value -BasePrefix $NewPrefix -MaxDepth $MaxDepth -CurrentDepth ($CurrentDepth + 1) -MaxArrayElements $MaxArrayElements
                $NestedFlattened.GetEnumerator() | ForEach-Object { $Result[$_.Name] = $_.Value }
            }
            else { $Result[$NewPrefix.TrimEnd('_')] = $property.Value }
        }
    }
    elseif ($null -ne $InputObject) { 
        $SafeBasePrefix = $BasePrefix.TrimEnd('_'); if ([string]::IsNullOrEmpty($SafeBasePrefix)) { $SafeBasePrefix = "Value" }
        $Result[$SafeBasePrefix] = $InputObject
    }
    return $Result
}

# --- Main Script Body ---
try {
    if (-not (Test-Path $OutputDirectory)) {
        Write-Verbose "Creating output directory: $OutputDirectory"
        try { New-Item -ItemType Directory -Path $OutputDirectory -Force -ErrorAction Stop | Out-Null; Write-Host "Successfully created directory: $OutputDirectory" } 
        catch { Write-Error "Failed to create output directory '$OutputDirectory': $($_.Exception.Message)"; exit 1 }
    }
    try { Start-Transcript -Path $Global:ScriptTranscriptPath -Append -Force -ErrorAction Stop; Write-Host "Script execution logging started. Transcript: $Global:ScriptTranscriptPath" } 
    catch { Write-Warning "Could not start transcript: $($_.Exception.Message)"; $Global:ScriptTranscriptPath = $null }

    Write-Host "Starting User Audit Log Retrieval Script (v1.8 - Pagination & Deep Flattening)..."
    Write-Host "`n--- Checking Prerequisites ---"
    if (-not (Test-ModuleInstalled -ModuleName "ExchangeOnlineManagement" -ModuleFriendlyName "Exchange Online Management")) { throw "Prerequisite module ExchangeOnlineManagement failed." }
    if (-not (Test-ModuleInstalled -ModuleName "ImportExcel" -ModuleFriendlyName "ImportExcel for Excel export")) { throw "Prerequisite module ImportExcel failed." }
    Write-Host "Prerequisite checks completed."

    Write-Host "`n--- User Input ---"
    if ([string]::IsNullOrWhiteSpace($UserPrincipalName)) {
        $UserPrincipalName = Read-Host -Prompt "Please enter User Principal Name (UPN) (e.g., <EMAIL>)"
    }
    else { Write-Host "User Principal Name provided: $UserPrincipalName" }
    if ([string]::IsNullOrWhiteSpace($UserPrincipalName) -or $UserPrincipalName -notlike "*@*") { Write-Error "Invalid UPN: '$UserPrincipalName'." ; throw "Invalid UPN." }
    Write-Host "Target user: $UserPrincipalName"

    $CmdStartDate = $null; $CmdEndDate = $null
    # Corrected: Date format hint in prompt
    $InputFormatHint = "(e.g., YYYY-MM-DD)" 

    if (-not [string]::IsNullOrWhiteSpace($StartDateInput) -and -not [string]::IsNullOrWhiteSpace($EndDateInput)) {
        Write-Host "Using provided StartDateInput '$StartDateInput' and EndDateInput '$EndDateInput'."
        try {
            $CmdStartDate = [datetime]::Parse($StartDateInput, [System.Globalization.CultureInfo]::InvariantCulture).ToUniversalTime()
            $CmdEndDate = ([datetime]::Parse($EndDateInput, [System.Globalization.CultureInfo]::InvariantCulture).Date.AddDays(1).AddSeconds(-1)).ToUniversalTime()
        }
        catch { Write-Error "Invalid date format from params. $($_.Exception.Message)"; throw "Invalid date input." }
    }
    else {
        Write-Host "Prompting for date range."
        do {
            $PromptStartDateString = Read-Host -Prompt "Enter Start Date $InputFormatHint (blank for 7 days ago)"
            if ([string]::IsNullOrWhiteSpace($PromptStartDateString)) { $CmdStartDate = (Get-Date).Date.AddDays(-7).ToUniversalTime(); $ValidStartDate = $true; Write-Host "Defaulting Start Date to: $($CmdStartDate.ToLocalTime()) (UTC: $CmdStartDate)" }
            else { try { $CmdStartDate = [datetime]::Parse($PromptStartDateString, [System.Globalization.CultureInfo]::InvariantCulture).ToUniversalTime(); $ValidStartDate = $true } catch { Write-Warning "Invalid Start Date. Try again."; $ValidStartDate = $false } }
        } while (-not $ValidStartDate)
        do {
            $PromptEndDateString = Read-Host -Prompt "Enter End Date $InputFormatHint (blank for today)"
            if ([string]::IsNullOrWhiteSpace($PromptEndDateString)) { $CmdEndDate = (Get-Date).Date.AddDays(1).AddSeconds(-1).ToUniversalTime(); $ValidEndDate = $true; Write-Host "Defaulting End Date to: $($CmdEndDate.ToLocalTime()) (UTC: $CmdEndDate)" }
            else { try { $CmdEndDate = ([datetime]::Parse($PromptEndDateString, [System.Globalization.CultureInfo]::InvariantCulture).Date.AddDays(1).AddSeconds(-1)).ToUniversalTime(); $ValidEndDate = $true } catch { Write-Warning "Invalid End Date. Try again."; $ValidEndDate = $false } }
        } while (-not $ValidEndDate)
    }
    if ($CmdStartDate -ge $CmdEndDate) { Write-Error "Start Date must be before End Date."; throw "Invalid date range." }
    Write-Host "Effective audit log search range: From $($CmdStartDate.ToLocalTime()) (UTC: $CmdStartDate) to $($CmdEndDate.ToLocalTime()) (UTC: $CmdEndDate)."
    if ((New-TimeSpan -Start $CmdStartDate -End $CmdEndDate).TotalDays -gt 90) { Write-Warning ">90 day range selected. Audit availability depends on license." }
    
    $CleanUser = $UserPrincipalName -replace "[^a-zA-Z0-9_.-]", "-"; $Timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $ExcelFilePath = Join-Path -Path $OutputDirectory -ChildPath "${CleanUser}_AuditLog_From_$($CmdStartDate.ToString('yyyyMMdd'))_To_$($CmdEndDate.ToString('yyyyMMdd'))_$Timestamp.xlsx"
    Write-Verbose "Output Excel: $ExcelFilePath"

    Write-Host "`n--- Connecting to Exchange Online ---"
    try {
        if (Get-PSSession | Where-Object { $_.ConfigurationName -eq 'Microsoft.Exchange' -and $_.State -eq 'Opened' }) { Write-Host "Already connected." }
        else { Write-Host "Connecting..."; Connect-ExchangeOnline -ShowBanner:$false -ErrorAction Stop; Write-Host "Connected." }
    }
    catch { Write-Error "Failed to connect to EXO: $($_.Exception.Message)"; throw "EXO connection failed." }

    $AllAuditRecords = [System.Collections.Generic.List[PSObject]]::new()
    $CurrentSearchLoopEndDate = $CmdEndDate 
    $PageNumber = 0
    $MaxPages = 20 
    $BatchSize = 5000

    Write-Host "`n--- Searching Audit Logs (with Pagination) ---"
    Write-Warning "Script will retrieve records in batches of up to $BatchSize."
    do {
        $PageNumber++
        Write-Host "Fetching Page $PageNumber (EndDate for this batch: $($CurrentSearchLoopEndDate.ToLocalTime()) UTC)..."
        $CurrentBatch = @()
        try {
            $CurrentBatch = Search-UnifiedAuditLog -UserIds $UserPrincipalName -StartDate $CmdStartDate -EndDate $CurrentSearchLoopEndDate -ResultSize $BatchSize -SessionCommand ReturnLargeSet -ErrorAction Stop
        }
        catch {
            Write-Error "Error during audit log search (Page $PageNumber): $($_.Exception.Message)"
            if ($AllAuditRecords.Count -gt 0) { Write-Warning "Proceeding with $($AllAuditRecords.Count) records fetched so far."; break }
            else { throw "Audit log search failed on initial page." }
        }

        if ($CurrentBatch) {
            Write-Host "Page $PageNumber{}: Found $($CurrentBatch.Count) records."
            $CurrentBatch | ForEach-Object { $AllAuditRecords.Add($_) }
            if ($CurrentBatch.Count -eq $BatchSize) {
                $OldestRecordInBatch = $CurrentBatch | Sort-Object CreationDate | Select-Object -First 1
                $CurrentSearchLoopEndDate = ($OldestRecordInBatch.CreationDate).AddTicks(-1) 
                if ($CurrentSearchLoopEndDate -lt $CmdStartDate) { Write-Host "Next batch EndDate before overall StartDate. Stopping."; $CurrentBatch = @() } 
                else { Write-Host "Potentially more records. Next batch will end before $($OldestRecordInBatch.CreationDate.ToLocalTime()) UTC." }
            }
            else { Write-Host "Page $PageNumber{}: Fetched all remaining records for this slice (less than $BatchSize)." }
        }
        else { Write-Host "Page $PageNumber{}: No records found in this slice." }
        
        if ($PageNumber -ge $MaxPages) { Write-Warning "Reached max page limit ($MaxPages)."; break }
    } while ($CurrentBatch -and $CurrentBatch.Count -eq $BatchSize)

    $AuditLogSearchResults = $AllAuditRecords
    Write-Host "`nTotal audit records fetched across all pages: $($AuditLogSearchResults.Count)."

    if (-not $AuditLogSearchResults) {
        Write-Warning "No audit records found for '$UserPrincipalName' in the specified range after all attempts."
    }
    else {
        Write-Host "`n--- Processing Audit Log Data (Deep Flattening JSON) ---"
        $ProcessedAuditRecords = @()
        $RecordCounter = 0; $TotalToProcess = $AuditLogSearchResults.Count

        foreach ($RawRecord in $AuditLogSearchResults) {
            $RecordCounter++; Write-Progress -Activity "Processing Audit Data" -Status "Record $RecordCounter of $TotalToProcess" -PercentComplete (($RecordCounter / $TotalToProcess) * 100)
            $BaseProperties = [ordered]@{
                CreationDate = $RawRecord.CreationDate; UserIds = $RawRecord.UserIds; Operation = $RawRecord.Operations
                RecordType = $RawRecord.RecordType; RecordTypeFriendly = ($RawRecord.RecordType).ToString()
                ResultStatus = $RawRecord.ResultStatus; OrganizationId = $RawRecord.OrganizationId
            } 
            $AuditDataContent = $null
            if (-not [string]::IsNullOrWhiteSpace($RawRecord.AuditData)) {
                try { $AuditDataContent = $RawRecord.AuditData | ConvertFrom-Json -ErrorAction Stop } 
                catch { Write-Warning "Rec $($RawRecord.CreationDate) Op '$($RawRecord.Operations)': Failed to parse AuditData. Err: $($_.Exception.Message)"; $BaseProperties.Add("AD_ParsingError", $_.Exception.Message) }
            }
            else { $BaseProperties.Add("AD_Status", "Empty or Null") }

            if ($AuditDataContent) {
                $FlattenedAuditData = ConvertTo-FlatObject -InputObject $AuditDataContent -BasePrefix "AD_" -MaxDepth 2 -MaxArrayElements 3 
                $FlattenedAuditData.GetEnumerator() | ForEach-Object {
                    $PropName = $_.Name
                    if ($PropName.Length -gt 200) { Write-Warning "Prop name '$PropName' truncated."; $PropName = $PropName.Substring(0, 197) + "..." }
                    if ($BaseProperties.Keys -contains $PropName) { Write-Warning "Duplicate key '$PropName'. Overwriting." }
                    $BaseProperties[$PropName] = $_.Value
                }
            }
            $BaseProperties['RawAuditData'] = $RawRecord.AuditData 
            $ProcessedAuditRecords += New-Object PSObject -Property $BaseProperties
        }
        Write-Host "Processing of $($ProcessedAuditRecords.Count) records complete."

        if ($ProcessedAuditRecords.Count -gt 0) {
            Write-Host "`n--- Exporting to Excel ---"
            Write-Host "Exporting to: $ExcelFilePath"
            try {
                $ProcessedAuditRecords | Export-Excel -Path $ExcelFilePath -WorksheetName "AuditLogs" -AutoSize -FreezeTopRow -BoldTopRow -TableStyle Medium6 -ErrorAction Stop
                Write-Host "Successfully exported to '$ExcelFilePath' ($($ProcessedAuditRecords.Count) records)."
            }
            catch {
                Write-Error "Failed to export to Excel '$ExcelFilePath': $($_.Exception.Message)"
                $CsvFallbackPath = $ExcelFilePath -replace '\.xlsx$', '.csv'
                Write-Warning "Attempting CSV fallback: $CsvFallbackPath"
                try { $ProcessedAuditRecords | Export-Csv -Path $CsvFallbackPath -NoTypeInformation -Encoding UTF8 -ErrorAction Stop; Write-Host "Successfully exported to CSV: $CsvFallbackPath" } 
                catch { Write-Error "CSV fallback failed: $($_.Exception.Message)" }
            }
        }
        else { Write-Warning "No processed records to export." }
    }
    Write-Host "`nScript operations completed."
}
catch {
    Write-Error "`nCRITICAL ERROR: $($_.Exception.Message)"
    if ($_.ScriptStackTrace) { Write-Verbose "StackTrace: $($_.ScriptStackTrace)" }
    Write-Error "Review messages and transcript log."
    exit 1
}
finally {
    Write-Host "`n--- Finalizing Script ---"
    try {
        if (Get-PSSession | Where-Object { $_.ConfigurationName -eq 'Microsoft.Exchange' -and $_.State -eq 'Opened' }) {
            Write-Host "Disconnecting from Exchange Online..."; Disconnect-ExchangeOnline -Confirm:$false -ErrorAction SilentlyContinue
            Get-PSSession | Where-Object { $_.Name -like "ExchangeOnline*" } | Remove-PSSession -ErrorAction SilentlyContinue
            Write-Host "Disconnected."
        }
        else { Write-Verbose "No active EXO session to disconnect." }
    }
    catch { Write-Warning "Issue during EXO disconnect: $($_.Exception.Message)" }
    if ($Global:ScriptTranscriptPath) { 
        Write-Host "Stopping transcript logging..."
        Stop-Transcript 
        if (Test-Path $Global:ScriptTranscriptPath) { Write-Host "Transcript saved to: $Global:ScriptTranscriptPath" }
        else { Write-Warning "Transcript file '$($Global:ScriptTranscriptPath)' not found after stop attempt." }
    }
    Write-Host "Script execution finished."
}
