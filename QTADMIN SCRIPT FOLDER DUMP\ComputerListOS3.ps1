#del C:\scripts\ComputerListOS2.csv
$computerlist = Get-Content c:\scripts\computerlist2.csv
foreach ($computer in $computerlist) {
    if((Test-Connection -Cn $computer -BufferSize 16 -Count 1 -ea 0 -quiet))
    {   
        Get-WMIObject Win32_OperatingSystem -ComputerName $computer | 
        select-object CSName, Caption, Version | export-csv c:\scripts\ComputerListOS.csv -Append -NoTypeInformation
    }
}