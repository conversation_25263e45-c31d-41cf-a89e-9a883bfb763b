Import-Module ActiveDirectory
$users = get-AdGroupMember -identity "MFAExempt -Temp Users"
$Users | Export-Csv C:\Scripts\MFAExcludedUsers\mfaexludedmembers.csv -NoTypeInformation
$csvpath = "C:\Scripts\MFAExcludedUsers\mfaexludedmembers.csv"

$users = Get-ADGroupMember "MFAExempt -Temp Users" | ForEach-Object {Remove-ADGroupMember "MFAExempt -Temp Users" $_ -Confirm:$false}

Send-MailMessage -To <EMAIL> -From "<EMAIL>" -SmtpServer mail.qhrtech.com -Attachments $csvpath -Subject "Alert - These accounts have been removed from Mfa exluded list." -body "NOTE: The attachment will not give any data if there were no users contained in the group. This script runs on QTADMIN1 every Monday and Thursday at 11 PM as a scheduled task (Remove_mfaexluded_members.ps1)." -BodyAsHtml 






