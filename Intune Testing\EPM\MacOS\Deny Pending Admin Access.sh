#!/bin/zsh
#=================================================================================
# SCRIPT: Deny Pending Admin Access (Final v4.4)
#=================================================================================
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') | $1"
}
log "=== SCRIPT START: Deny Admin Access & Demote User (v4.4) ==="

# --- Configuration ---
JAMF_BINARY="/usr/local/bin/jamf"; STAT_CMD="/usr/bin/stat"
jamfProURL="https://qhrtech.jamfcloud.com"; apiClientID="fe4d6778-a92d-4895-9cb8-772347f2b161"; apiClientSecret="qtMGn-mHe6BiWZxPTbE85YuBTw6KLHQU7IobRxc-7OAseqShG3iInQaxR8wluOfZ"
ACTION_EA_NAME="Temp Admin - Action Status"; DURATION_EA_NAME="Temp Admin - Approved Duration (Minutes)"

# --- Get User Info ---
consoleUser=$($STAT_CMD -f%Su /dev/console)
if [[ -z "$consoleUser" ]] || [[ "$consoleUser" == "root" ]] || [[ "$consoleUser" == "loginwindow" ]]; then log "ERROR: No valid interactive user detected. Exiting."; exit 1; fi
log "Identified console user: ${consoleUser}"

# --- CORRECTED: Comprehensive Demotion Block ---
log "Checking admin status for '${consoleUser}'..."
if dsmemberutil checkmembership -U "$consoleUser" -G admin | /usr/bin/grep -q "is a member"; then
    log "User is currently an admin. Attempting comprehensive demotion."
    
    userUUID=$(/usr/bin/dscl . -read "/Users/<USER>" GeneratedUID | /usr/bin/awk '{print $2}')
    
    # Attempt to remove from both possible group membership attributes.
    # It is expected that one of these may fail if the attribute doesn't exist.
    log "Attempting to remove by UUID from GroupMembers..."
    /usr/bin/dscl . -delete /Groups/admin GroupMembers "$userUUID"
    
    log "Attempting to remove by short name from GroupMembership..."
    /usr/bin/dscl . -delete /Groups/admin GroupMembership "$consoleUser"
    
    # Final verification is the true test of success.
    if dsmemberutil checkmembership -U "$consoleUser" -G admin | /usr/bin/grep -q "is a member"; then
        log "ERROR: Verification failed. User is still a member of the admin group after all demotion attempts."
    else
        log "VERIFIED: User has been successfully demoted to a standard user."
    fi
else
    log "INFO: User '${consoleUser}' is already a standard user. No action needed."
fi
# --- End of Corrected Block ---

message="Your request for temporary administrator access has been denied. Your account has been set to standard user status for security compliance."; $JAMF_BINARY displayMessage -message "$message"

# --- Clear Extension Attributes ---
apiTokenResponse=$(/usr/bin/curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" --data-urlencode "client_id=${apiClientID}" --data-urlencode "client_secret=${apiClientSecret}" --data-urlencode "grant_type=client_credentials" "${jamfProURL}/api/oauth/token")
bearerToken=$(echo "$apiTokenResponse" | /usr/bin/plutil -extract "access_token" raw -); if [[ -z "$bearerToken" ]]; then log "ERROR: Could not retrieve bearer token."; exit 1; fi
serialNumber=$(/usr/sbin/system_profiler SPHardwareDataType | /usr/bin/awk '/Serial Number/{print $4}')
computerDetails=$(/usr/bin/curl -s -H "Authorization: Bearer ${bearerToken}" -H "accept: application/json" "${jamfProURL}/api/v1/computers-inventory?filter=hardware.serialNumber==%22${serialNumber}%22")
minified_json=$(echo "${computerDetails}" | tr -d ' \n\r'); computerID=$(echo "${minified_json}" | sed -n 's/.*"results":\[{"id":"\([^"]*\)".*/\1/p')
if [[ -z "$computerID" ]] || ! [[ "$computerID" =~ ^[0-9]+$ ]]; then log "ERROR: Could not determine the computer's Jamf Pro ID via API."; exit 1; fi
log "This computer's Jamf Pro ID is: ${computerID}"
api_payload="<?xml version=\"1.0\" encoding=\"UTF-8\"?><computer><extension_attributes><extension_attribute><name>${ACTION_EA_NAME}</name><value></value></extension_attribute><extension_attribute><name>${DURATION_EA_NAME}</name><value></value></extension_attribute></extension_attributes></computer>"
http_status=$(/usr/bin/curl -s -o /dev/null -w "%{http_code}" -X PUT -H "Authorization: Bearer ${bearerToken}" -H "Content-Type: application/xml" -d "$api_payload" "${jamfProURL}/JSSResource/computers/id/${computerID}")
if [[ "$http_status" == "201" ]]; then log "SUCCESS: API call returned status ${http_status}. EAs have been cleared instantly."; else log "ERROR: API call failed with HTTP status ${http_status}. EAs were not cleared."; exit 1; fi
log "=== SCRIPT END ==="; exit 0