@echo off

    :: BatchGotAdmin (Run as Admin code starts)
    REM --> Check for permissions
    >nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
    REM --> If error flag set, we do not have admin.
    if '%errorlevel%' NEQ '0' (
    echo Requesting administrative privileges...
    goto UACPrompt
    ) else ( goto gotAdmin )
    :UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    exit /B
    :gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
    :: BatchGotAdmin (Run as Admin code ends)
    :: Your codes should start from the following line
    
    rem Setting all UWD inf names
    set substring_1=iacamera64.inf
    set substring_2=iactrllogic64.inf
    set substring_3=iaisp64.inf
    set substring_4=hm1092.inf
    set substring_5=hm2170.inf
    set substring_6=ov02c10.inf
    set substring_7=ov01a.inf
    set substring_8=ov01a1b.inf
    set substring_9=iacamera64_extension_dell_mayabay.inf
    set substring_10=iacamera64_extension_dell_mayabay_vpu.inf
    set substring_11=iacamera64_extension_dell_teton.inf
    set substring_12=iacamera64_extension_dell_teton_vpu.inf
    set substring_13=iacamera64_iacamera64_extension_dell_oasis.inf
    set substring_14=iacamera64_extension_dell_xps.inf
    set substring_15=iacamera64_extension_dell_consumer.inf
    set substring_16=usbspi.inf
    set substring_17=usbi2c.inf
    set substring_18=usbgpio.inf
    set substring_19=cvfusbdfuextension.inf
    set substring_20=cvfusbdfu.inf
    set substring_21=cvfusbbridgeextension.inf
    set substring_22=cvfusbbridge.inf
    set substring_23=ishheci.inf
    set substring_24=spioed.inf
    set substring_25=ivsc.inf
    
    set counter1=0
    set counter2=0
    set counter3=0
    set counter4=0
    set counter5=0
    set counter6=0
    set counter7=0
    set counter8=0
    set counter9=0
    set counter10=0
    set counter11=0
    set counter12=0
    set counter13=0
    set counter14=0
    set counter15=0
    set counter16=0
    set counter17=0
    set counter18=0
    set counter19=0
    set counter20=0
    set counter21=0
    set counter22=0
    set counter23=0
    set counter24=0
    set counter25=0
    
    set prev_line=""
    
    echo ****** Searching and removing original camera drivers... ******
    echo.
    pnputil /enum-drivers > drivers.txt
    
    set "File2Read=drivers.txt"
    If Not Exist "%File2Read%" (Goto :Error)
    
    rem This will read a file into an array of variables and populate it 
    setlocal EnableExtensions EnableDelayedExpansion
    for /f "delims=" %%a in ('Type "%File2Read%"') do (
        set /a count+=1
        set "Line[!count!]=%%a"
    )
    
    For /L %%i in (1,1,%Count%) do (
        echo "!Line[%%i]!" | findstr /C:"!substring_1!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter1+=1
        )
        
        echo "!Line[%%i]!" | findstr /C:"!substring_2!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter2+=1
        )
        
        echo "!Line[%%i]!" | findstr /C:"!substring_3!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter3+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_4!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter4+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_5!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter5+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_6!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter6+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_7!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter7+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_8!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter8+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_9!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter9+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_10!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter10+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_11!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter11+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_12!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter12+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_13!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter13+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_14!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter14+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_15!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter15+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_16!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter16+=1
        )
        
        echo "!Line[%%i]!" | findstr /C:"!substring_17!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter17+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_18!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter18+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_19!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter19+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_20!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter20+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_21!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter21+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_22!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter22+=1
        ) 
    
        echo "!Line[%%i]!" | findstr /C:"!substring_23!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter23+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_24!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter24+=1
        )
    
        echo "!Line[%%i]!" | findstr /C:"!substring_25!" 1>nul
        if errorlevel 1 (
            rem pattern not found
            rem echo "did not find "!substring_1!" in !Line[%%i]!"
        ) ELSE (
            rem got zero found pattern
            call :getOemNum "!Line[%%i]!" "!prev_line!"
            set /a counter25+=1
        )
    
        set "prev_line=!Line[%%i]!"
    )
    
    echo "%substring_1% found and removed: %counter1% times"
    echo "%substring_2% found and removed: %counter2% times"
    echo "%substring_3% found and removed: %counter3% times"
    echo "%substring_4% found and removed: %counter4% times"
    echo "%substring_5% found and removed: %counter5% times"
    echo "%substring_6% found and removed: %counter6% times"
    echo "%substring_7% found and removed: %counter7% times"
    echo "%substring_8% found and removed: %counter8% times"
    echo "%substring_9% found and removed: %counter9% times"
    echo "%substring_10% found and removed: %counter10% times"
    echo "%substring_11% found and removed: %counter11% times"
    echo "%substring_12% found and removed: %counter12% times"
    echo "%substring_13% found and removed: %counter13% times"
    echo "%substring_14% found and removed: %counter14% times"
    echo "%substring_15% found and removed: %counter15% times"
    echo "%substring_16% found and removed: %counter16% times"
    echo "%substring_17% found and removed: %counter17% times"
    echo "%substring_18% found and removed: %counter18% times"
    echo "%substring_19% found and removed: %counter19% times"
    echo "%substring_20% found and removed: %counter20% times"
    echo "%substring_21% found and removed: %counter21% times"
    echo "%substring_22% found and removed: %counter22% times"
    echo "%substring_23% found and removed: %counter23% times"
    echo "%substring_24% found and removed: %counter24% times"
    echo "%substring_25% found and removed: %counter25% times"
    del drivers.txt /Q
    
    echo.
    echo ****** Driver uninstall complete ******
    echo.
    pause
    exit /b 0
    
    :getOemNum
    rem echo "proccesing %1, searching for %~2"
    for /F "tokens=2 delims=:" %%a in ("%~2%") do ( 
       call :deleteOEM %%a
    )
    exit /b
    
    :deleteOEM
    set strDelete="remove %1"
    echo %strDelete%
    pnputil /delete-driver %1 /force /uninstall
    exit /b
    
    :ERROR 
    ECHO Uninstall failed, refer to logFile.
    echo   The file "%File2Read%" does not exist !
    exit /b 1