﻿Import-Module LyncOnlineConnector 
$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)
$CSSession = New-CsOnlineSession -TargetServer admin0a.online.lync.com -Credential $LiveCred 
Import-PSSession $CSSession -AllowClobber 

# move-csuser -Identity USERNAME -target sipfed.online.lync.com -credential $cred -HostedMigrationOverrideUrl https://admin0a.online.lync.com/HostedMigration/hostedmigrationservice.svc 
# Grant-CsClientPolicy -PolicyName ClientPolicyEnableSkypeUI -Identity <EMAIL>

# Early access to SKYPE for BUSINESS
# Grant-CsClientPolicy -PolicyName ClientPolicyEnableSkypeUI -Identity <EMAIL>