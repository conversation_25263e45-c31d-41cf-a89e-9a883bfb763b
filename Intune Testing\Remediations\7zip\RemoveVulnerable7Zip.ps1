<#
.SYNOPSIS
    Silently uninstalls outdated versions of 7-Zip from a Windows system,
    providing audit logs for deleted items. Excludes a specified recent version.

.DESCRIPTION
    This script identifies installed versions of 7-Zip by querying the Windows registry
    (both 64-bit and 32-bit locations). It compares the found version against a specified
    version to keep (e.g., 24.09) and skips uninstallation if the installed version matches
    or is newer.

    For older versions, it attempts to uninstall them silently using their registered
    UninstallString (MSI or EXE) or by finding the default Uninstall.exe.

    It stops running 7-Zip processes before attempting uninstallation. Post-uninstall,
    it performs cleanup of the installation directory and registry key if the uninstall
    was successful or if no automated method was available for the older version.

    Designed for silent operation, only outputting deleted item paths, a 'not found' message,
    and a final completion message by default. Use the -Verbose switch to see detailed step-by-step
    logs specifically related to 7-Zip actions.

.PARAMETER Verbose
    Include this switch to enable detailed logging of the script's actions related to 7-Zip.

.EXAMPLE
    .\Uninstall-7Zip-v3.4.ps1
    (Runs silently, uninstalling versions older than specified, showing only deleted paths and completion message)

.EXAMPLE
    .\Uninstall-7Zip-v3.4.ps1 -Verbose
    (Runs with detailed step-by-step logging focused on 7-Zip discovery and actions)

.NOTES
    Version: 3.4
    Date: 2025-04-24
    Requires: Administrator privileges. Compatible with Windows PowerShell 5.1+.
    Feature: Excludes uninstalling versions matching or newer than $versionToKeep.
    Feature: Retains Audit Logs for deleted items in standard output.
    Feature: Added message if no 7-Zip instances are found.
    Update: Reduced verbosity to focus on 7-Zip related actions when using -Verbose.
#>
[CmdletBinding()] # Enables common parameters like -Verbose
param()

#region --- Configuration ---

# Specify the minimum version of 7-Zip to KEEP installed. Versions older than this will be targeted.
$versionToKeep = "24.09"

#endregion --- Configuration ---

#region --- Functions ---

function Test-IsAdmin {
    Write-Verbose "Checking for Administrator privileges..."
    try {
        $identity = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = [System.Security.Principal.WindowsPrincipal]::new($identity)
        $isAdmin = $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
        Write-Verbose ("Is Administrator: {0}" -f $isAdmin)
        return $isAdmin
    } catch {
        Write-Warning "Error checking administrator privileges: $($_.Exception.Message)"
        return $false # Assume not admin if check fails
    }
}

function Stop-7ZipProcesses {
    $processNames = @("7zFM", "7zG", "7z") # Add other related process names if needed
    Write-Verbose "Attempting to stop 7-Zip processes: $($processNames -join ', ')"
    $processes = Get-Process -Name $processNames -ErrorAction SilentlyContinue
    if ($processes) {
        foreach ($process in $processes) {
            Write-Verbose "Stopping process: $($process.Name) (ID: $($process.Id))"
            try {
                Stop-Process -Id $process.Id -Force -ErrorAction Stop
                Write-Verbose "Successfully stopped $($process.Name) (ID: $($process.Id))."
            } catch {
                Write-Warning "Failed to stop process $($process.Name) (ID: $($process.Id)): $($_.Exception.Message)"
            }
        }
    } else {
        Write-Verbose "No running 7-Zip processes found."
    }
}

function Uninstall-MSI($productCode) {
    # Ensure product code has braces {} - Should be guaranteed by regex but double-check
    if ($productCode -notmatch '^\{.*\}$') {
        $productCode = "{$productCode}"
        Write-Verbose "Added braces to product code: $productCode"
    }

    Write-Verbose "Attempting MSI uninstall for ProductCode: $productCode"
    $arguments = "/x $productCode /qn /norestart" # Silent uninstall, no restart
    Write-Verbose "Executing: msiexec.exe $arguments"
    try {
        $proc = Start-Process msiexec.exe -ArgumentList $arguments -Wait -PassThru -ErrorAction Stop
    } catch {
        Write-Warning "Failed to start msiexec.exe process: $($_.Exception.Message)"
        return $false # Indicate failure
    }

    # Check if process handle was obtained
    if ($null -eq $proc) {
        Write-Warning "Failed to get process handle for msiexec.exe execution."
        return $false
    }

    $exitCode = $proc.ExitCode
    Write-Verbose "MSIexec exit code: $exitCode"

    # Check for known success codes
    if ($exitCode -eq 0 -or $exitCode -eq 3010) { # 0 = Success, 3010 = Success, Reboot Required
        Write-Verbose "MSI uninstall successful (Exit Code: $exitCode)."
        if ($exitCode -eq 3010) { Write-Verbose "A reboot may be required." }
        return $true
    } else {
        $errorMessage = "MSI uninstall failed (Exit Code: $exitCode)."
        # Add context for common errors
        switch ($exitCode) {
            1603 { $errorMessage += " Fatal error during installation/uninstallation." }
            1605 { $errorMessage += " This action is only valid for products that are currently installed (may already be uninstalled)." }
            1618 { $errorMessage += " Another installation is already in progress." }
        }
        Write-Warning $errorMessage
        return $false
    }
}

function Uninstall-EXE($uninstallCmd, $defaultSilentArg = '/S') {
    Write-Verbose "Attempting EXE uninstall."
    # Trim quotes which might interfere with Start-Process or argument parsing
    $commandPath = $uninstallCmd.Trim('"')
    $arguments = $defaultSilentArg # Default 7-Zip silent switch is /S (case-sensitive)

    # Simple check if the command might already contain arguments (e.g., space after .exe)
    # This isn't foolproof but avoids doubling up common switches.
    $commandParts = $commandPath -split '\.exe', 2 # Split at first .exe
    if ($commandParts.Length -gt 1 -and $commandParts[1].Trim() -ne '') {
         Write-Verbose "Uninstall command '$commandPath' appears to already have arguments. Executing as is."
         # Use the original command, potentially ignoring $defaultSilentArg if logic determines args already present
         # For simplicity here, we'll just run the detected command as-is if it looks like it has args.
         $arguments = $null # Don't add default silent arg if others seem present
         $executable = $commandPath # Assume the whole string is the command + args for cmd.exe
         $processArgs = "/c `"$executable`"" # Execute via cmd /c
         $executable = "cmd.exe" # Change executable to cmd.exe
    } else {
         # Assume it's just the path to the executable
         $executable = $commandPath
         $processArgs = $arguments # Use the default silent argument
         Write-Verbose "Executing: `"$executable`" $processArgs"
    }

    try {
         if ($executable -eq 'cmd.exe'){
             $proc = Start-Process -FilePath $executable -ArgumentList $processArgs -Wait -PassThru -ErrorAction Stop
         } else {
             # Only add arguments if they exist (i.e., are not $null)
             if ($arguments) {
                 $proc = Start-Process -FilePath $executable -ArgumentList $arguments -Wait -PassThru -ErrorAction Stop
             } else {
                 $proc = Start-Process -FilePath $executable -Wait -PassThru -ErrorAction Stop
             }
         }
    } catch {
        Write-Warning "Failed to start EXE uninstaller process ('$executable'): $($_.Exception.Message)"
        return $false # Indicate failure
    }

    if ($null -eq $proc) {
        Write-Warning "Failed to get process handle for EXE uninstaller execution."
        return $false
    }

    $exitCode = $proc.ExitCode
    Write-Verbose "EXE Uninstaller ('$executable') exit code: $exitCode"

    # For EXE, exit code 0 is typically success. Others are less standard.
    # 3010 is common for reboot needed, treat as success.
    if ($exitCode -eq 0 -or $exitCode -eq 3010) {
        Write-Verbose "EXE uninstall potentially successful (Exit Code: $exitCode)."
        if ($exitCode -eq 3010) { Write-Verbose "A reboot may be required." }
        return $true
    } else {
        Write-Warning "EXE uninstall failed or status unknown (Exit Code: $exitCode)."
        return $false
    }
}

#endregion --- Functions ---

#region --- Main Script ---

# Check for Admin privileges
if (-not (Test-IsAdmin)) {
    Write-Error "This script requires Administrator privileges. Please run it in an elevated PowerShell session."
    exit 1 # Stop execution
}

Write-Verbose "Starting 7-Zip Uninstall Script..."
Write-Verbose "Minimum version to keep: $versionToKeep"

# Convert the version string to keep into a [version] object for comparison
$minVersionToKeepObj = [version]'0.0' # Initialize
try {
    $minVersionToKeepObj = [version]$versionToKeep
    Write-Verbose "Successfully parsed '$versionToKeep' as [version] object: $minVersionToKeepObj"
} catch {
    Write-Warning "Could not parse version string '$versionToKeep' as a standard [version] object. Will rely on string comparison fallback."
    # Keep $minVersionToKeepObj as 0.0, forcing fallback logic later if needed
}


# Registry paths to check (covers 64-bit and 32-bit apps on 64-bit OS)
$registryRoots = @(
    "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall",
    "HKLM:\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
)

$processedKeys = [System.Collections.Generic.List[string]]::new() # To avoid processing the same app twice if listed in both hives incorrectly
$foundAny7Zip = $false # Flag to track if any 7-Zip instance was found

foreach ($regRoot in $registryRoots) {
    Write-Verbose "Checking registry root: $regRoot"
    if (-not (Test-Path $regRoot)) {
        Write-Verbose "Registry path $regRoot does not exist. Skipping."
        continue
    }

    # Get uninstall subkeys
    $subKeys = Get-ChildItem -Path $regRoot -ErrorAction SilentlyContinue
    if ($null -eq $subKeys) {
        Write-Verbose "No application subkeys found under '$regRoot'."
        continue
    }

    foreach ($key in $subKeys) {
        $appKeyPath = $key.PSPath # Get the full path including provider

        # Skip if we've already processed this key (e.g., via a different hive pointing to same info)
        if ($processedKeys.Contains($appKeyPath)) {
            # Write-Verbose "Skipping already processed key: $appKeyPath" # Reduced verbosity
            continue
        }

        # Write-Verbose "Processing Key: $appKeyPath" # Reduced verbosity - Commented Out
        # Get specific properties using -LiteralPath to handle special chars in key names
        $props = Get-ItemProperty -LiteralPath $appKeyPath -Name DisplayName, DisplayVersion, UninstallString, InstallLocation, Publisher -ErrorAction SilentlyContinue

        if ($null -eq $props) {
            # Write-Verbose "Could not retrieve properties for key '$appKeyPath'. Skipping." # Reduced verbosity - Commented Out
            continue
        }

        # --- Check if this is 7-Zip ---
        $is7Zip = $false
        if ($null -ne $props.PSObject.Properties['DisplayName'] -and $props.DisplayName -like '7-Zip*') {
            $is7Zip = $true
        }

        if ($is7Zip) {
            $foundAny7Zip = $true # Set flag indicating we found at least one instance
            $appName = $props.DisplayName
            $appVersionStr = $props.DisplayVersion # Raw version string from registry (might be null or complex)
            Write-Verbose "Found 7-Zip: '$appName' (Version String: $($appVersionStr -or 'N/A')) at $appKeyPath" # KEEP - Important context
            $processedKeys.Add($appKeyPath) # Mark as processed

            # --- Check if this version should be kept ---
            $keepThisVersion = $false
            if (-not [string]::IsNullOrWhiteSpace($appVersionStr)) {
                $currentAppVersionObj = [version]'0.0' # Default if parsing fails
                $parsedSuccessfully = $false
                try {
                    # Clean common non-version characters (like edition info in brackets/parentheses) before parsing
                    $cleanedVersionString = ($appVersionStr -split '[ (\[]')[0].Trim() # Take part before space, bracket, or parenthesis
                    if ($cleanedVersionString -match '^\d+(\.\d+){1,3}$') { # Basic check if it looks like Major.Minor[.Build[.Revision]]
                       $currentAppVersionObj = [version]$cleanedVersionString
                       Write-Verbose "Parsed '$appVersionStr' as [version]: $currentAppVersionObj"
                       $parsedSuccessfully = $true
                    } else {
                        Write-Verbose "Cleaned version string '$cleanedVersionString' does not match expected version format."
                    }
                } catch {
                    Write-Warning "Could not parse version string '$appVersionStr' as [version] object: $($_.Exception.Message)"
                }

                if ($parsedSuccessfully) {
                    # Use robust [version] comparison if parsing worked
                    if ($currentAppVersionObj -ge $minVersionToKeepObj) {
                        $keepThisVersion = $true
                        Write-Verbose "Version $currentAppVersionObj matches or is newer than minimum version to keep ($minVersionToKeepObj)."
                    } else {
                        Write-Verbose "Version $currentAppVersionObj is older than minimum version to keep ($minVersionToKeepObj). Targeting for uninstall."
                    }
                } else {
                    # Fallback to string comparison if [version] parsing failed
                    Write-Verbose "Using string comparison fallback for version '$appVersionStr'."
                    if ($appVersionStr -like "$versionToKeep*") {
                        $keepThisVersion = $true
                        Write-Verbose "Version string '$appVersionStr' starts with version to keep '$versionToKeep'."
                    } else {
                         Write-Verbose "Version string '$appVersionStr' does not start with version to keep '$versionToKeep'. Targeting for uninstall."
                    }
                }
            } else {
                Write-Verbose "Version information is missing for '$appName'. Assuming it should be uninstalled."
                # $keepThisVersion remains false, proceed to uninstall
            }

            # If this version should be kept, skip to the next key
            if ($keepThisVersion) {
                Write-Verbose "Skipping uninstall action for '$appName' version '$($appVersionStr -or 'N/A')'."
                continue # Go to the next key in the foreach ($key in $subKeys) loop
            }

            # --- If we reached here, the version is older and should be uninstalled ---
            Write-Verbose "Proceeding with uninstall for '$appName' version '$($appVersionStr -or 'N/A')'..."

            # --- Remediation Steps ---
            $uninstallSuccess = $false
            $noUninstallMethodFound = $false

            # 1. Stop Processes
            Stop-7ZipProcesses

            # 2. Attempt Uninstall
            $uninstallString = $null
            if ($props.PSObject.Properties['UninstallString']) { # Check if property exists and is not null
                $uninstallString = $props.UninstallString.Trim()
            }

            if (-not [string]::IsNullOrWhiteSpace($uninstallString)) {
                Write-Verbose "UninstallString found: '$uninstallString'"
                # Check for MSI Product Code (GUID format)
                if ($uninstallString -match '(\{[\w-]{8}-[\w-]{4}-[\w-]{4}-[\w-]{4}-[\w-]{12}\})') {
                    $productCode = $matches[1]
                    Write-Verbose "Detected MSI Product Code: $productCode"
                    $uninstallSuccess = Uninstall-MSI $productCode
                }
                # Check for EXE - crude check, assumes .exe is the uninstaller itself
                elseif ($uninstallString -match '\.exe') {
                    Write-Verbose "Detected EXE-based UninstallString."
                    # Use '/S' for default 7-Zip Uninstall.exe
                    $uninstallSuccess = Uninstall-EXE $uninstallString '/S'
                }
                # Add checks for other types like rundll32 if needed
                else {
                    Write-Warning "Unrecognized UninstallString format: '$uninstallString'. Cannot attempt automated uninstall via this string."
                    $noUninstallMethodFound = $true # Mark that we couldn't use the string
                }
            } else {
                # No UninstallString, try fallback using InstallLocation + default uninstaller name
                Write-Verbose "No UninstallString found in registry."
                $installLocation = $null
                if ($props.PSObject.Properties['InstallLocation']) { # Check if property exists and is not null
                    $installLocation = $props.InstallLocation.Trim().TrimEnd('\')
                }

                if (-not [string]::IsNullOrWhiteSpace($installLocation) -and (Test-Path $installLocation -PathType Container)) {
                    $defaultUninstallerPath = Join-Path -Path $installLocation -ChildPath "Uninstall.exe"
                    Write-Verbose "Checking for default uninstaller at '$defaultUninstallerPath'"
                    if (Test-Path $defaultUninstallerPath -PathType Leaf) {
                        Write-Verbose "Found default uninstaller. Attempting execution with /S."
                        $uninstallSuccess = Uninstall-EXE $defaultUninstallerPath '/S'
                    } else {
                        Write-Verbose "Default uninstaller 'Uninstall.exe' not found in InstallLocation '$installLocation'."
                        $noUninstallMethodFound = $true
                    }
                } else {
                    Write-Verbose "InstallLocation not found or invalid. Cannot attempt fallback uninstall."
                    $noUninstallMethodFound = $true
                }
            }

            # 3. Cleanup (Files and Registry) - Only if uninstall succeeded OR no method was found
            $cleanupAllowed = $uninstallSuccess -or $noUninstallMethodFound
            Write-Verbose "Cleanup allowed based on uninstall attempt: $cleanupAllowed"

            # File Cleanup
            $installLocationPath = $null
            if ($props.PSObject.Properties['InstallLocation']) { # Check if property exists and is not null
                $installLocationPath = $props.InstallLocation.Trim().TrimEnd('\')
            }

            if ($cleanupAllowed -and -not [string]::IsNullOrWhiteSpace($installLocationPath) -and (Test-Path $installLocationPath -PathType Container)) {
                Write-Verbose "Attempting to remove installation directory: '$installLocationPath'"
                try {
                    Remove-Item -Path $installLocationPath -Recurse -Force -ErrorAction Stop
                    # Log only *after* successful removal attempt (no terminating error)
                    Write-Output "Deleted directory: $installLocationPath" # AUDIT LOG
                } catch {
                    Write-Warning "Failed to remove directory '$installLocationPath': $($_.Exception.Message)"
                }
            } elseif ($cleanupAllowed -and -not [string]::IsNullOrWhiteSpace($installLocationPath)) {
                 Write-Verbose "Installation directory '$installLocationPath' not found or already removed."
            }

            # Registry Key Cleanup
            if ($cleanupAllowed -and (Test-Path -LiteralPath $appKeyPath)) {
                 Write-Verbose "Attempting to remove registry key: '$appKeyPath'"
                 try {
                     Remove-Item -LiteralPath $appKeyPath -Recurse -Force -ErrorAction Stop
                     # Log only *after* successful removal attempt (no terminating error)
                     Write-Output "Deleted registry key: $appKeyPath" # AUDIT LOG
                 } catch {
                    Write-Warning "Failed to remove registry key '$appKeyPath': $($_.Exception.Message)"
                 }
            } elseif ($cleanupAllowed) {
                 Write-Verbose "Registry key '$appKeyPath' not found or already removed."
            }

            # Add a small delay in verbose mode to allow reading output before next item
            if ($PSBoundParameters['Verbose']) { Start-Sleep -Seconds 1 }

        } # End if ($is7Zip)
    } # End foreach ($key in $subKeys)
} # End foreach ($regRoot in $registryRoots)

# Add final message if no 7-Zip was found at all
if (-not $foundAny7Zip) {
    Write-Output "No instances of 7-Zip were found in the checked registry locations."
}

Write-Output "7-Zip uninstall and cleanup script completed." # FINAL OUPUT

#endregion --- Main Script ---