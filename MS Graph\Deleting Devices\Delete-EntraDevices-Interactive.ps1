# 1. CONFIGURE THESE TWO VARIABLES
$csvPath = "C:\path\to\your\devices.csv"
$deviceIdColumn = "deviceId"

# 2. CONNECT TO MICROSOFT GRAPH
# A sign-in window will pop up. Use an admin account.
Connect-MgGraph -Scopes "DeviceManagementManagedDevices.ReadWrite.All"

# 3. RUN THE DELETION
Import-Csv -Path $csvPath | ForEach-Object {
    # Get the unique Device ID from the CSV
    $entraDeviceId = $_.$deviceIdColumn
    
    # Skip any row where the Device ID is blank
    if ([string]::IsNullOrWhiteSpace($entraDeviceId)) {
        Write-Warning "Skipping row with blank Device ID."
        return # 'return' is used here instead of 'continue' because of the ForEach-Object block
    }

    # Find the specific device in Intune using its unique Entra Device ID
    $intuneDevice = Get-MgDeviceManagementManagedDevice -Filter "azureADDeviceId eq '$entraDeviceId'"

    if ($intuneDevice) {
        # --- SAFETY FIRST: DRY RUN IS ENABLED BY DEFAULT ---
        # Run the script once with this line active to see what will be deleted.
        Write-Host "[DRY RUN] Would delete device '$($intuneDevice.DeviceName)' with ID '$($intuneDevice.AzureADDeviceId)'" -ForegroundColor Yellow

        # --- TO ACTUALLY DELETE ---
        # 1. Comment out the "Write-Host" line above (add a # at the start).
        # 2. Uncomment the "Remove-MgDeviceManagementManagedDevice" line below (remove the #).
        # Remove-MgDeviceManagementManagedDevice -ManagedDeviceId $intuneDevice.Id
        
    }
    else {
        Write-Warning "Device with Device ID '$entraDeviceId' was not found in Intune."
    }
}

# 4. DISCONNECT
Disconnect-MgGraph
Write-Host "Script finished."