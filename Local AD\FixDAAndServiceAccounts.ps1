# -------------------------------------------------------
# Updated PowerShell Script:
# 1. Prompt user in loop for specific email.
# 2. Set local AD attribute "msExchHideFromAddressLists" to TRUE.
# 3. Log previous Manager and Direct Reports before clearing.
# 4. Store previous Manager (CN) into ExtensionAttribute10.
# 5. Improved error handling and console output.
# 6. Single persistent log file, updated per execution.
# -------------------------------------------------------

# Prerequisites Check
if (-not (Get-Module -ListAvailable -Name ActiveDirectory)) {
    Write-Error "ActiveDirectory module not found. Please install RSAT tools."
    exit
}

# Import modules
Import-Module ActiveDirectory

# Log file setup (persistent log file)
$LogDirectory = "C:\temp\Helpdesk\LocalADAttributes\FixDaAndServiceAccounts"
New-Item -Path $LogDirectory -ItemType Directory -Force | Out-Null
$LogFile = "$LogDirectory\UserAttributeChanges.log"

# Loop to prompt for user email
while ($true) {
    $UserPrincipalName = Read-Host -Prompt "Enter UserPrincipalName (or type 'exit' to finish)"

    if ($UserPrincipalName -eq 'exit') {
        break
    }

    Write-Host "Processing user: $UserPrincipalName" -ForegroundColor Cyan

    # Local AD updates
    Try {
        $ADUser = Get-ADUser -Filter {UserPrincipalName -eq $UserPrincipalName} -Properties Manager, extensionAttribute10

        if ($ADUser) {
            Write-Host "Updating local AD attributes..." -ForegroundColor Green

            $logDetails = @()

            # Log and store current manager into ExtensionAttribute10 (using full CN)
            if ($ADUser.Manager) {
                $ManagerObject = Get-ADUser $ADUser.Manager
                $ManagerCN = $ManagerObject.DistinguishedName
                Set-ADUser -Identity $ADUser.SamAccountName -Replace @{extensionAttribute10=$ManagerCN}
                Set-ADUser -Identity $ADUser.SamAccountName -Clear Manager
                $logDetails += "Manager removed and stored in ExtensionAttribute10: $ManagerCN"
            }

            # Set HideFromAddressLists
            Set-ADUser -Identity $ADUser.SamAccountName -Replace @{msExchHideFromAddressLists=$true}
            $logDetails += "msExchHideFromAddressLists set to TRUE"

            # Log and clear direct reports
            $DirectReports = Get-ADUser -Filter {Manager -eq $ADUser.DistinguishedName}
            foreach ($Report in $DirectReports) {
                Set-ADUser -Identity $Report.SamAccountName -Clear Manager
                $logDetails += "Removed direct report: $($Report.UserPrincipalName)"
            }

            # Prepare log entry
            $logEntry = @"
===========================================================
Date/Time   : $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Admin User  : $env:USERNAME
Target User : $($ADUser.SamAccountName)

Changes Made:
$($logDetails -join "`n")
===========================================================

"@
            # Output to console and file
            Write-Host $logEntry -ForegroundColor Yellow
            $logEntry | Out-File -FilePath $LogFile -Append -Encoding UTF8
        }
        else {
            Write-Warning "Local AD User not found: $UserPrincipalName"
        }
    }
    Catch {
        Write-Warning "Error updating local AD for user ${UserPrincipalName}: $_"
    }
}

Write-Host "Script execution completed. Log available at $LogFile" -ForegroundColor Yellow