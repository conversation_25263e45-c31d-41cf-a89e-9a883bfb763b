<# 
.SYNOPSIS 
Sync Intune Policies on All Intune-Managed Devices at once
 
.DESCRIPTION 
Below script will force Initiate Intune Sync on All Intune Managed devices
.NOTES     
        Name       : Sync-IntunePolicies.ps1
        Author     : <PERSON><PERSON><PERSON>  
        Version    : 1.0.0  
        DateCreated: 23-Nov-2023
        Blog       : https://cloudinfra.net
         
.LINK 
https://cloudinfra.net 

#>

function Install-Dependencies {
    # Check if the main Microsoft.Graph module is already installed
    if (-not (Get-Module -ListAvailable Microsoft.Graph)) {
        # Install the main Microsoft.Graph module
        Install-Module Microsoft.Graph
    }

    # Get all available Microsoft.Graph.* modules (excluding the already installed ones)
    $GraphModules = Get-Module -ListAvailable Microsoft.Graph.* | Where-Object { $_.Name -ne "Microsoft.Graph" }

    # Install each module (if not already installed)
    foreach ($Module in $GraphModules) {
        if (-not (Get-Module -ListAvailable $Module.Name)) {
            Write-Host "Installing Module: $($Module.Name) $($Module.Version)"
            Install-Module $Module.Name -RequiredVersion $Module.Version -ErrorAction Stop -AllowClobber
        }
    }
}

try {
    # Install required modules
    #Install-Module -Name Microsoft.Graph -Force -ErrorAction Stop -AllowClobber
    #Install-Module -Name Microsoft.Graph.DeviceManagement.Actions -Force -ErrorAction Stop -AllowClobber
    Install-Dependencies

    # Import required modules
    Import-Module -Name Microsoft.Graph -ErrorAction Stop
    #Import-Module -Name Microsoft.Graph.DeviceManagement.Actions -ErrorAction Stop

    # Connect to Microsoft Graph
    Connect-MgGraph -scope DeviceManagementManagedDevices.PrivilegedOperations.All, DeviceManagementManagedDevices.ReadWrite.All, DeviceManagementManagedDevices.Read.All -ErrorAction Stop

    # Get all managed devices
    $managedDevices = Get-MgDeviceManagementManagedDevice -All -ErrorAction Stop

    # Synchronize each managed device
    foreach ($device in $managedDevices) {
        try {
            Sync-MgDeviceManagementManagedDevice -ManagedDeviceId $device.Id -ErrorAction Stop
            Write-Host "Invoking Intune Sync for $($device.DeviceName)" -ForegroundColor Yellow
        }
        catch {
            Write-Error "Failed to sync device $($device.DeviceName). Error: $_"
        }
    }
}
catch {
    Write-Error "An error occurred. Error: $_"
}
finally {
    # Cleanup
    #Remove-Module -Name Microsoft.Graph.DeviceManagement -ErrorAction SilentlyContinue
    #Remove-Module -Name Microsoft.Graph.DeviceManagement.Actions -ErrorAction SilentlyContinue
}
