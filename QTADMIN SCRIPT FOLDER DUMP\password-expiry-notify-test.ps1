﻿
##################################################################################################################
# Please Configure the following variables....
$smtpServer="mail.qhrtech.com"
$expireindays = 300
$from = "PASSWORD EXPIRATION <<EMAIL>>"
###################################################################################################################

#Get Users From AD who are enabled
Import-Module ActiveDirectory
$users = get-aduser -identity Jeffrey.Bell -properties Name, samaccountname, PasswordNeverExpires, PasswordExpired, PasswordLastSet, EmailAddress |where {$_.EmailAddress -ne $null -and $_.Enabled -eq "True" -and $_.PasswordNeverExpires -eq $false -and $_.passwordexpired -eq $false }

echo $users
pause
foreach ($user in $users)
{
  $Name = (Get-ADUser $user | foreach { $_.Name})
  $emailaddress = $user.emailaddress
  $passwordSetDate = (get-aduser $user -properties * | foreach { $_.PasswordLastSet })
  $PasswordPol = (Get-AduserResultantPasswordPolicy $user)
  #$image1 = "c:\scripts\JamfConnectStartMenu.jpg"
  #$attachment = Get-item C:\scripts\JamfConnectStartMenu.jpg
  Send-MailMessage -To <EMAIL> -From “<EMAIL>” -SmtpServer mail.qhrtech.com -Subject $passwordSetDate -body $users
  
}
  