Import-Module ActiveDirectory

# All of the properties you'd like to pull from Get-ADUser
$properties = @(
    'givenName',
    'sn',
    'mail',
    'objectSid',
    'department',
    'title',
    'extensionAttribute10',
    'extensionAttribute11',
    'description',
    'whenCreated',
    'userPrincipalName',
    'lastLogon',
    'sAMAccountName',
    'DistinguishedName' # Added 'DistinguishedName' property for OU location
)

# All of the expressions you want with all of the filtering, etc. you'd like done to them
$expressions = @(
    @{Expression={$_.givenName};Label="First Name"},
    @{Expression={$_.sn};Label="Last Name"},
    @{Expression={$_.mail};Label="Email Address"},
    @{Expression={$_.department};Label="Department"},
    @{Expression={$_.title};Label="Title"},
    @{Expression={$_.extensionAttribute10};Label="Extension attribute 10"},
    @{Expression={$_.extensionAttribute10};Label="Extension attribute 11"},
    @{Expression={$_.description};Label="Description"},
    @{Expression={$_.whenCreated};Label="Account Created on"},
    @{Expression={[DateTime]::FromFileTime([Int64]::Parse($_.lastLogon))};Label="Last Log-on Date"},
    @{Expression={$_.userPrincipalName};Label="Full User Logon"},
    @{Expression={$_.objectSid};Label="Unique Account ID"},
    @{Expression={$_.sAMAccountName};Label="User Logon"},
    @{Expression={$_.DistinguishedName};Label="OU Location"} # Added OU Location expression
)

$path_to_file = "C:\scripts\admin-and-service-user-report_$((Get-Date).ToString('yyyy-MM-dd_hh-mm-ss')).csv"

$groups = "Enterprise Admins","Domain Admins", "Schema Admins"
$results = foreach ($group in $groups) {
    Get-ADGroupMember $group | Get-ADUser -Properties $properties | Select $expressions
}
$results += Get-ADGroupMember $group | Get-ADUser -Filter {Title -eq "Service Account"} -Properties $properties | Select $expressions
$results += Get-ADGroupMember $group | Get-ADUser -Filter {Name -like "*Domain*"} -Properties $properties | Select $expressions
$results += Get-ADGroupMember $group | Get-ADUser -Filter {sAMAccountName -like '*test*'} -Properties $properties | Select $expressions
$results += Get-ADGroupMember $group | Get-ADUser -Filter {sAMAccountName -like '*svc*'} -Properties $properties | Select $expressions

$results
$results | Export-CSV $path_to_file -NoTypeInformation -Encoding UTF8
