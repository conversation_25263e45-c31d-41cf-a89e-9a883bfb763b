"samaccountname","name"
"qhrnetadmin","QHRNetAdmin"
"asigra","Service Account - Asigra"
"christie.magee","<PERSON>"
"accounting","QHR Accounting"
"SQL-OSWebSQL01","SQL-OSWebSQL01"
"sharon.gupta","<PERSON>"
"callow","Callow Associates"
"punita.gosar","Punita Gosar"
"LM5FL3IToffice","LM5fl3 IToffice"
"forge.architectmr","Room Kelowna 1st Floor - Forge Architect Room"
"tyler.loney","<PERSON> Loney"
"darwin.palma","Darwin Palma"
"katie.southgate","Katie Southgate"
"UnifiedMessage365","UnifiedMessage365"
"sepsync","SEP LDAP SYNC"
"testsqlserver","Test SQL Server Service Account"
"matthew.tatalovic","<PERSON>"
"<PERSON>","<PERSON>"
"Mike.Balneaves","<PERSON>"
"pythian-awithers","<PERSON><PERSON> (Pythian)"
"mimecast.splunk","Mimecast Splunk"
"splunk.alerts","Splunk Alerts"
"cintia.schutt","<PERSON>int<PERSON>hu<PERSON>"
"carla.v<PERSON>e","<PERSON><PERSON>"
