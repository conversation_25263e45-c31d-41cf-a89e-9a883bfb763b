﻿# Connect to Office 365 (http://technet.microsoft.com/library/jj151815.aspx#BKMK_connect):
$creduser = Read-Host "Enter Domain Admin account (eg. <EMAIL>)"
#$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
# $LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)
$LiveCred = Get-Credential $creduser

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://outlook.office365.com/powershell-liveid/ -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
#Connect-MsolService -Credential $LiveCred

$again = "y"
# Find email address:
while ($again -ne "n") {

$findaddr = Read-Host "`n`nEnter part of email address to find (eg. sales*)"
$findaddr = 'smtp:' + $findaddr

$mbox = Get-mailbox -Identity * | ? {$_.EmailAddresses -like $findaddr} 
Write-Host "`n`nIdentity (Mailbox): $mbox`n" -ErrorAction SilentlyContinue
$mbox | select -ExpandProperty Emailaddresses -ErrorAction SilentlyContinue

$contct = Get-Contact -Identity * | ? {$_.EmailAddresses -like $findaddr} 
Write-Host "`n`nIdentity (Contact):$contct`n" -ErrorAction SilentlyContinue
$contct | select -ExpandProperty emailaddresses -ErrorAction SilentlyContinue

$distgrp = Get-DistributionGroup | ? {$_.EmailAddresses -like $findaddr}
Write-Host "`n`nIdentity (Dist Group):$distgrp`n" -ErrorAction SilentlyContinue
$distgrp | select -ExpandProperty emailaddresses -ErrorAction SilentlyContinue
 
$again = Read-Host "`n`nSearch Again??? (y/n)"
clear
}


# Get mailbox info

# Get Calendar info
# get-mailboxfolderpermission -identity email:\calendar | select user,accessrights

# Set Cal Permissions
# Add-MailboxFolderPermission -Identity email:\calendar -AccessRights Editor -user "Display Name"
# OR
# Set-MailboxFolderPermission -Identity email:\calendar -AccessRights Editor -user "Display Name"

# Update Exchange Online GAL
# Update-GlobalAddressList -Identity "Fourth Coffee"

# Check user's access to other mailboxes
# Get-Mailbox -ResultSize Unlimited | Get-MailboxPermission -User <EMAIL> | Format-Table Identity, AccessRights, Deny

# Check Forwarding
# Get-Mailbox <your mailbox identity>| Ft ForwardingSMTPAddress

# Enable Forwarding
# <AUTHOR> <EMAIL> -DeliverToMailboxAndForward $true -ForwardingSMTPAddress <<EMAIL>>

# Disable Forwarding
# <AUTHOR> <EMAIL> -DeliverToMailboxAndForward $false -ForwardingSMTPAddress $null

# Disable Forwarding for an entire OU
# get-aduser -filter * | ? distinguishedname -match medeo | foreach { set-mailbox -Identity $_.userprincipalname -DeliverToMailboxAndForward $false -ForwardingSMTPAddress $null }
