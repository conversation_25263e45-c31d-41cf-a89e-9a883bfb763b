# Run this script with administrative privileges
# Define registry paths
$registryPaths = @(
    "HKLM:\SOFTWARE\Policies\Microsoft\Edge\ExtensionInstallForcelist",
    "HKLM:\SOFTWARE\Policies\Mozilla\Firefox",
    "HKLM:\SOFTWARE\Policies\Google\Chrome\ExtensionInstallForcelist"
)
foreach ($path in $registryPaths) {
    if (Test-Path $path) {
        Write-Host "Found: $path" -ForegroundColor Green
        try {
            # Get all value names under the key
            $values = Get-ItemProperty -Path $path | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name
            foreach ($value in $values) {
                Remove-ItemProperty -Path $path -Name $value -ErrorAction Stop
                Write-Host "Removed value: $value from $path" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Warning "Error removing values from $path{}: $_"
        }
    }
    else {
        Write-Host "Registry path not found: $path" -ForegroundColor Gray
    }
}
Write-Host "Registry cleanup completed." -ForegroundColor Cyan