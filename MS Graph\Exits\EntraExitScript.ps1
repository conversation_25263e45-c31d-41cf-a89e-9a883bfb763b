<#
.SYNOPSIS
Provides a GUI to terminate access for specified Microsoft Entra ID users.
It disables the user account, revokes sign-in sessions, and disables registered devices using Microsoft Graph.

.DESCRIPTION
This script presents a simple Windows Form GUI. The user inputs one or more User Principal Names (UPNs)
into a text box (separated by newlines or commas). Clicking the "Terminate Access" button initiates
the process for each user listed.

The script performs the following actions for each user:
1. Connects silently to Microsoft Graph (requires necessary permissions).
2. Finds the user object based on the UPN.
3. Disables the user account (`accountEnabled = $false`).
4. Revokes all Microsoft Entra ID refresh tokens (`Revoke-MgUserSignInSession`).
5. Finds all devices registered by the user.
6. Disables each registered device (`accountEnabled = $false`).

All actions and results (success or failure) are logged verbosely to a text box within the GUI
and also output to the PowerShell console host.

Ensure the Microsoft Graph PowerShell SDK modules (`Microsoft.Graph.Authentication`, `Microsoft.Graph.Users`,
`Microsoft.Graph.Users.Actions`, `Microsoft.Graph.DirectoryObjects`) are installed.
The script requires appropriate Graph API permissions (e.g., User.ReadWrite.All, Directory.ReadWrite.All, Directory.AccessAsUser.All).

.PARAMETER UserList
A string containing User Principal Names (UPNs), typically separated by newlines or commas. This is entered via the GUI text box.

.EXAMPLE
1. Run the script.
2. Enter UPNs like '<EMAIL>', '<EMAIL>' into the top text box.
3. Click "Terminate Access".
4. Observe the log output in the bottom text box and the console.

.NOTES
Author: Slader Sheppard
Date: 2025-03-31 (Corrected Version 3)
Requires: PowerShell 5.1 or later, .NET Framework (for Windows Forms), Microsoft Graph PowerShell SDK.
Permissions: User.ReadWrite.All, Directory.ReadWrite.All (or Device.ReadWrite.All), Directory.AccessAsUser.All.
Ensure the account running the script has the necessary administrative roles/permissions in Entra ID.
#>

#region Prerequisites Check
# Keep prerequisite check messages as they are helpful for initial setup
Write-Host "Checking for required Microsoft Graph PowerShell modules..." -ForegroundColor Yellow

$requiredModules = @(
    "Microsoft.Graph.Authentication",
    "Microsoft.Graph.Users",
    "Microsoft.Graph.Users.Actions",
    "Microsoft.Graph.DirectoryObjects" # For Get-MgUserRegisteredDevice and Update-MgDevice
)
$missingModules = @()

foreach ($moduleName in $requiredModules) {
    if (-not (Get-Module -ListAvailable -Name $moduleName)) {
        $missingModules += $moduleName
    }
}

if ($missingModules.Count -gt 0) {
    Write-Warning "The following required modules are missing: $($missingModules -join ', ')"
    Write-Warning "Please install them by running the following command in an elevated PowerShell session:"
    Write-Warning "Install-Module $($missingModules -join ', ') -Scope CurrentUser -Force"
    Write-Error "Script cannot continue without required modules. Please install them and re-run."
    exit
}
else {
    Write-Host "Required modules found." -ForegroundColor Green
}

# Import necessary modules silently
Import-Module Microsoft.Graph.Authentication -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.Users -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.Users.Actions -ErrorAction SilentlyContinue
Import-Module Microsoft.Graph.DirectoryObjects -ErrorAction SilentlyContinue
#endregion Prerequisites Check

#region Windows Forms GUI Setup
# --- GUI Definition Remains Unchanged ---
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

$form = New-Object System.Windows.Forms.Form
$form.Text = 'Entra ID User Termination Tool'
$form.Size = New-Object System.Drawing.Size(600, 550)
$form.StartPosition = 'CenterScreen'
$form.FormBorderStyle = 'FixedDialog'
$form.MaximizeBox = $false
$form.MinimizeBox = $true

$labelInput = New-Object System.Windows.Forms.Label
$labelInput.Location = New-Object System.Drawing.Point(10, 10)
$labelInput.Size = New-Object System.Drawing.Size(560, 20)
$labelInput.Text = 'Enter User Principal Names (UPNs - one per line or comma-separated):'
$form.Controls.Add($labelInput)

$textBoxInput = New-Object System.Windows.Forms.TextBox
$textBoxInput.Location = New-Object System.Drawing.Point(10, 35)
$textBoxInput.Size = New-Object System.Drawing.Size(560, 80)
$textBoxInput.Multiline = $true
$textBoxInput.ScrollBars = 'Vertical'
$textBoxInput.Font = New-Object System.Drawing.Font("Consolas", 9)
$textBoxInput.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$form.Controls.Add($textBoxInput)

$buttonTerminate = New-Object System.Windows.Forms.Button
$buttonTerminate.Location = New-Object System.Drawing.Point(225, 125)
$buttonTerminate.Size = New-Object System.Drawing.Size(150, 30)
$buttonTerminate.Text = 'Terminate Access'
$buttonTerminate.Font = New-Object System.Drawing.Font("Segoe UI", 9, [System.Drawing.FontStyle]::Bold)
$buttonTerminate.Anchor = [System.Windows.Forms.AnchorStyles]::Top
$form.Controls.Add($buttonTerminate)

$labelLog = New-Object System.Windows.Forms.Label
$labelLog.Location = New-Object System.Drawing.Point(10, 170)
$labelLog.Size = New-Object System.Drawing.Size(560, 20)
$labelLog.Text = 'Log Output:'
$form.Controls.Add($labelLog)

$textBoxLog = New-Object System.Windows.Forms.TextBox
$textBoxLog.Location = New-Object System.Drawing.Point(10, 195)
$textBoxLog.Size = New-Object System.Drawing.Size(560, 300)
$textBoxLog.Multiline = $true
$textBoxLog.ScrollBars = 'Vertical'
$textBoxLog.ReadOnly = $true
$textBoxLog.Font = New-Object System.Drawing.Font("Consolas", 8)
$textBoxLog.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$form.Controls.Add($textBoxLog)
#endregion Windows Forms GUI Setup

#region Helper Function - Logging
# --- Logging Function Remains Unchanged ---
function Write-Log ($Message, $Type = "Info") {
    $timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    $logEntry = "[$timestamp] [$Type] $Message"

    switch ($Type) {
        "Info"    { Write-Host $logEntry -ForegroundColor Gray }
        "Success" { Write-Host $logEntry -ForegroundColor Green }
        "Warning" { Write-Host $logEntry -ForegroundColor Yellow }
        "Error"   { Write-Error $logEntry }
        "Action"  { Write-Host $logEntry -ForegroundColor Cyan }
        default   { Write-Host $logEntry }
    }

    if ($textBoxLog -and $textBoxLog.IsHandleCreated) {
         try {
            if ($textBoxLog.InvokeRequired) {
                $textBoxLog.Invoke([System.Action]{
                    $textBoxLog.AppendText("$logEntry`r`n")
                })
            } else {
                 $textBoxLog.AppendText("$logEntry`r`n")
            }
         } catch {
             Write-Warning "Failed to write log to GUI text box (Invoke): $($_.Exception.Message)"
         }
    } elseif ($textBoxLog) {
        try {
            $textBoxLog.AppendText("$logEntry`r`n")
        } catch {
            Write-Warning "Failed to write log to GUI text box (Append): $($_.Exception.Message)"
        }
    }
}
#endregion Helper Function - Logging

#region Main Logic - Button Click Event Handler
$buttonTerminate.Add_Click({
    $buttonTerminate.Enabled = $false
    $buttonTerminate.Text = 'Processing...'
    $form.Cursor = [System.Windows.Forms.Cursors]::WaitCursor
    $textBoxLog.Clear()

    Write-Log "Termination process started. (Timezone: $([System.TimeZoneInfo]::Local.DisplayName))" "Action"

    # --- Connect to Microsoft Graph (Silently) ---
    $requiredScopes = @(
        "User.ReadWrite.All",
        "Directory.ReadWrite.All",
        "Directory.AccessAsUser.All",
        "AuditLog.Read.All"
    )

    try {
        # Write-Log "Attempting to connect to Microsoft Graph..." "Info" # Removed verbose log
        $currentContext = Get-MgContext -ErrorAction SilentlyContinue
        $missingScopes = $requiredScopes | Where-Object {$currentContext.Scopes -notcontains $_}
        if ($null -eq $currentContext -or $missingScopes)
        {
             # Write-Log "No existing/suitable Graph connection found. Initiating..." "Info" # Removed verbose log
             Disconnect-MgGraph -ErrorAction SilentlyContinue
             # Connect silently without the default welcome message
             Connect-MgGraph -Scopes $requiredScopes -ErrorAction Stop # NoWelcome parameter doesn't exist, connect is silent by default unless error
             $currentContext = Get-MgContext # Refresh context needed to check success
             if ($null -eq $currentContext) {
                # If context is still null after Connect attempt, it failed.
                Throw "Failed to establish connection to Microsoft Graph."
             }
             # Write-Log "Successfully connected/refreshed Graph connection." "Success" # Removed verbose log
        } else {
             # Write-Log "Using existing Graph connection." "Success" # Removed verbose log
        }
    } catch {
        Write-Log "Failed to connect to Microsoft Graph. Error: $($_.Exception.Message)" "Error"
        Write-Log "Ensure the account has permissions and MFA/CA allows sign-in." "Warning"
        if ($form -and $form.IsHandleCreated) { # Check form still exists
            $form.Invoke([System.Action]{
                 if ($buttonTerminate -and -not $buttonTerminate.IsDisposed) { $buttonTerminate.Enabled = $true; $buttonTerminate.Text = 'Terminate Access'}
                 if ($form -and -not $form.IsDisposed) {$form.Cursor = [System.Windows.Forms.Cursors]::Default}
             })
        }
        return
    }

    # --- Process User List ---
    $userInput = $textBoxInput.Text.Trim()
    if ([string]::IsNullOrWhiteSpace($userInput)) {
        Write-Log "User input field is empty. No users to process." "Warning"
    } else {
        $userPrincipalNames = $userInput -split '[\r\n,]+' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

        if ($userPrincipalNames.Count -eq 0) {
             Write-Log "No valid User Principal Names found after parsing input." "Warning"
        } else {
             Write-Log "Processing the following users: $($userPrincipalNames -join ', ')" "Action"

             foreach ($upn in $userPrincipalNames) {
                 Write-Log "---------------- Processing User: $upn ----------------" "Info"
                 $userId = $null
                 $User = $null

                 try {
                     # 1. Get User Object
                     Write-Log "Attempting to find user '$upn' via Filter..." "Info"
                     $User = Get-MgUser -Filter "UserPrincipalName eq '$upn'" -ConsistencyLevel eventual -ErrorAction SilentlyContinue -Select Id, UserPrincipalName, AccountEnabled, DisplayName
                     if ($null -eq $User) {
                         Write-Log "Filter did not find '$upn', trying Search..." "Warning"
                         $User = Get-MgUser -Search "UserPrincipalName:$upn" -ConsistencyLevel eventual -ErrorAction SilentlyContinue -Top 1 -Select Id, UserPrincipalName, AccountEnabled, DisplayName
                         if ($null -ne $User -and $User.UserPrincipalName -ne $upn) {
                              Write-Log "Search found '$($User.UserPrincipalName)', but UPN doesn't match '$upn'. Skipping." "Warning"
                              $User = $null
                         }
                     }

                     # --- Main processing block ---
                     if ($null -ne $User) {
                         if ($User -is [array]) {
                              Write-Log "Search returned multiple results unexpectedly for '$upn'. Skipping." "Warning"
                              continue
                         }
                         Write-Log "Found user '$($User.UserPrincipalName)' (ID: $($User.Id))" "Success"
                         $userId = $User.Id
                         Write-Log "Current AccountEnabled status: $($User.AccountEnabled)" "Info"

                         # 2. Disable User Account
                         if ($User.AccountEnabled -eq $true) {
                             Write-Log "Attempting to disable account for user '$upn' (ID: $userId)..." "Action"
                             try { Update-MgUser -UserId $userId -AccountEnabled:$false -ErrorAction Stop; Write-Log "Successfully disabled account for '$upn'." "Success" }
                             catch { Write-Log "Failed to disable account for '$upn'. Error: $($_.Exception.Message)" "Error" }
                         } else { Write-Log "Account for '$upn' is already disabled." "Info" }

                         # 3. Revoke Sign-in Sessions
                         Write-Log "Attempting to revoke sign-in sessions for user '$upn' (ID: $userId)..." "Action"
                         try { Revoke-MgUserSignInSession -UserId $userId -ErrorAction Stop; Write-Log "Successfully revoked sign-in sessions for '$upn'." "Success" }
                         catch {
                             if ($_.Exception.Message -match 'User has no valid session|invalid refresh token') { Write-Log "No active/valid sign-in sessions found to revoke for '$upn'." "Info" }
                             else { Write-Log "Failed to revoke sign-in sessions for '$upn'. Error: $($_.Exception.Message)" "Error" }
                         }

                         # 4. Disable User's Registered Devices
                         Write-Log "Attempting to find and disable registered devices for user '$upn' (ID: $userId)..." "Action"
                         try {
                             # ---> Clear $Error before calling Get-MgUserRegisteredDevice <---
                             $Error.Clear()
                             $Devices = $null
                             $getDeviceError = $null # Reset error capture variable

                             $Devices = Get-MgUserRegisteredDevice -UserId $userId -ErrorAction SilentlyContinue -All
                             $getDeviceError = $Error[0] # Capture error specifically from this command

                             if ($null -ne $Devices -and $Devices.Count -gt 0) {
                                 Write-Log "Found $($Devices.Count) registered device(s) for '$upn'." "Info"
                                 foreach ($Device in $Devices) {
                                     Write-Log "Processing Device ID: $($Device.Id), Name: $($Device.DisplayName), OS: $($Device.OperatingSystem), Enabled: $($Device.AccountEnabled)" "Info"
                                     if ($Device.PSObject.Properties.Name -contains 'AccountEnabled' -and $Device.AccountEnabled -ne $false) {
                                         Write-Log "Attempting to disable device ID '$($Device.Id)'..." "Action"
                                         try { Update-MgDevice -DeviceId $Device.Id -AccountEnabled:$false -ErrorAction Stop; Write-Log "Successfully disabled device ID '$($Device.Id)'." "Success" }
                                         catch { Write-Log "Failed to disable device ID '$($Device.Id)'. Error: $($_.Exception.Message)" "Error" }
                                     } elseif ($Device.PSObject.Properties.Name -contains 'AccountEnabled' -and $Device.AccountEnabled -eq $false) { Write-Log "Device ID '$($Device.Id)' is already disabled." "Info" }
                                     else { Write-Log "Device ID '$($Device.Id)' AccountEnabled status unknown/missing." "Warning" }
                                 }
                             } elseif ($null -eq $Devices -and $getDeviceError -ne $null) {
                                 # Error occurred during Get-MgUserRegisteredDevice
                                 if ($getDeviceError.CategoryInfo.Reason -eq 'ResourceNotFoundException' -or $getDeviceError.Exception.Message -match 'Resource .* does not exist') { Write-Log "User '$upn' not found when querying for devices (potential inconsistency or delay)." "Warning" }
                                 # ---> Check for the specific odd error message observed <---
                                 elseif ($getDeviceError.Exception.Message -match 'No application to sign out from.') { Write-Log "Querying devices failed with unusual error: $($getDeviceError.Exception.Message). This might indicate an issue unrelated to device listing for user '$upn'." "Warning" }
                                 else {
                                     # Log the generic error if it's not ResourceNotFound or the specific odd one
                                     Write-Log "An error occurred querying devices for '$upn'. Error: $($getDeviceError.Exception.Message)" "Error"
                                     # Optionally log more details: Write-Log "Full device query error details: $($getDeviceError | Out-String)" "Error"
                                 }
                                 $Error.Clear() # Clear the error after logging
                             } else {
                                  Write-Log "No registered devices found for '$upn'." "Info"
                             }
                         } catch {
                             Write-Log "An unexpected error occurred during device processing block for '$upn'. Error: $($_.Exception.Message)" "Error"
                         }
                     } else {
                         Write-Log "User '$upn' not found in Entra ID after checking Filter and Search. Skipping." "Warning"
                     }
                 } catch {
                     Write-Log "An UNEXPECTED error occurred processing user '$upn'. Error: $($_.Exception.Message)" "Error"
                     Write-Log "Script StackTrace: $($_.ScriptStackTrace)" "Error"
                 } finally {
                      Write-Log "---------------- Finished processing user: $upn ----------------`n" "Info"
                      $Error.Clear()
                 }
             } # End foreach user
        }
    } # End else for non-empty user input

    Write-Log "Termination process completed." "Action"

    # Re-enable button and reset cursor safely
    if ($form -and $form.IsHandleCreated) {
        $form.Invoke([System.Action]{
            if ($buttonTerminate -and -not $buttonTerminate.IsDisposed) { $buttonTerminate.Enabled = $true; $buttonTerminate.Text = 'Terminate Access'}
            if ($form -and -not $form.IsDisposed) { $form.Cursor = [System.Windows.Forms.Cursors]::Default }
        })
    }
})
#endregion Main Logic - Button Click Event Handler

#region Show GUI
$form.Add_Shown({
    if ($form -and -not $form.IsDisposed) { $form.Activate() }
    if ($textBoxInput -and -not $textBoxInput.IsDisposed) { $textBoxInput.Select() }
})

Write-Log "GUI Initialized. Ready for input. Current Time: $(Get-Date)" "Info"
if ($PSVersionTable.PSEdition -ne 'Desktop' -and $host.Name -match 'Integrated Console'){
    Write-Warning "Host may require STA mode. If GUI fails/hangs, try: powershell.exe -Sta -File `"$($MyInvocation.MyCommand.Path)`""
}
[System.Windows.Forms.Application]::EnableVisualStyles()

try {
    # Show form and discard result
    [void]$form.ShowDialog()
} finally {
    # ---> Log disposal BEFORE disposing <---
    if($null -ne $form -and -not $form.IsDisposed) {
        Write-Log "GUI Form closing and disposing." "Info" # Changed message slightly
        $form.Dispose()
    }
}

# Disconnect silently after GUI closes
# Write-Host "GUI closed. Disconnecting from Microsoft Graph (if connected)." -ForegroundColor Yellow # Removed verbose log
Disconnect-MgGraph -ErrorAction SilentlyContinue
#endregion Show GUI

Write-Host "Script finished. Time: $(Get-Date))"