﻿"First Name","Last Name","Email Address","Department","Title","Mobile","Extension attribute 10","Extension attribute 11","Description","Last Log-on Date","Full User Logon","Unique Account ID","User Logon"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"JIRA","LDAP SYNC",,,"Service Account",,,,"Service Account: Corp IT - Jira and Confluence Service Account",,"<EMAIL>","S-1-5-21-**********-**********-*********-22624","jirasync"
"Domain Admin","<PERSON>","da-chou<PERSON><PERSON>@qhrtech.com",,,,,,,"3/23/2022 7:47:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24706","da-chounsham"
"Domain Admin -","Andrew McFadden","<EMAIL>",,,,,,,"3/11/2022 10:13:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22274","da-amcfadden"
"Domain Admin -","Kevin Kendall","<EMAIL>",,,,,,,"9/9/2021 1:24:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,,"2/25/2022 9:52:21 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24060","da-smay"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,,"12/15/2021 6:19:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-25149","da-tdrescher"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,,"1/31/2022 7:13:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,,"12/16/2020 5:23:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19009","da-nated"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,,"9/13/2021 12:09:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26188","da-mkandy"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,,"4/16/2021 8:44:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,,"3/23/2022 1:49:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,,"2/25/2022 3:48:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,,"3/23/2022 7:23:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,,"3/24/2022 12:23:41 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22187","da-sbradford"
"da-","mhoppe","<EMAIL>",,,,,,,"2/28/2022 8:34:39 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23206","da-mhoppe"
"Arnold","Nzailu","<EMAIL>",,,,,,,"6/24/2020 8:16:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26610","da-anzailu"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,,"6/2/2020 11:42:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24838","da-balbrecht"
"Paul","Farry",,,,,,,,"3/24/2022 8:44:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26431","da-pfarry"
"Greg","Harshenin","<EMAIL>",,,,,,,"3/17/2022 4:51:52 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12624","da-gharshenin"
"O","Perator","<EMAIL>",,"Service Account",,,,"Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","1/17/2022 8:44:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1161","operator"
"Malcolm","Kennedy",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24632","da-mkennedy"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,,"3/24/2022 8:06:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24717","da-jbell"
"Domain Admin","Nyel English","<EMAIL>",,,,,,,"3/24/2022 8:34:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,,"2/18/2022 1:54:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,,"1/13/2022 3:15:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,,"8/31/2021 8:46:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,,"3/2/2021 3:07:56 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15393","da-amehl"
"Jarrid","Pond",,,,,,,,"3/24/2022 10:15:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26768","da-jpond"
"Domain Admin","Dilcia Torres",,,,,,,,"3/23/2022 4:08:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26392","da-dtorres"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"MDT",,,,"Service Account",,,,"Service Account: Windows deployment account","3/24/2022 10:27:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-13593","MDT"
"O","Perator","<EMAIL>",,"Service Account",,,,"Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","1/17/2022 8:44:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1161","operator"
"Callow","Associates",,,"Service Account",,,,"Service Account: Finance - Dynamics Consultants ","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23935","callow"
"MitelRemote",,,,"Service Account","+****************","Nick Janzen","Nick Janzen","External Contractor User Account Alan Marjoribanks CTS","3/26/2021 7:58:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20129","mitelremote"
"Gluu","LDAP Sync",,,"Service Account",,,,"Service Account: Development - Gluu Test Account",,"<EMAIL>","S-1-5-21-**********-**********-*********-22628","gluusync"
"JIRA","LDAP SYNC",,,"Service Account",,,,"Service Account: Corp IT - Jira and Confluence Service Account",,"<EMAIL>","S-1-5-21-**********-**********-*********-22624","jirasync"
"QHR Infrastructure","Operator",,,"Service Account",,,,"Service Account: Corp IT - Used by WSUS reporting on qhrops",,"<EMAIL>","S-1-5-21-**********-**********-*********-17511","qtitoperator"
"OneLoginADC",,,,"Service Account",,,,"Service Account: Corp IT -  this service account was created for the OneLogin ADC","12/14/2020 8:31:35 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22793","OneLoginADC"
"QTADMIN1 service",,,,"Service Account",,,,"Service Account: Corp IT - Account used to run scheduled scripts on qtadmin1; Admin monitoring scripts","3/24/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22370","qtadmin1service"
"Optimed","Sales","<EMAIL>","Sales","Service Account",,,,"Service Account:Email Account for Optimed Software Sales",,"<EMAIL>","S-1-5-21-**********-**********-*********-1456","OPT_Sales"
"QHR Technologies","Support","<EMAIL>","Client Services","Service Account","+1 (250) 801-4274",,,"Service Account: Corp IT - Email / GTA no screen saver / After hours BB Support","3/13/2022 4:11:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1454","OPT_Support"
"Opt","Development",,,"Service Account",,,,"Service Account: Development - SQL Server Account","6/10/2021 7:26:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1426","opt_development"
"e","learning","<EMAIL>",,"Service Account",,,,"Service Account:For learning and Development",,"<EMAIL>","S-1-5-21-**********-**********-*********-1260","elearning"
"IT","Admin","<EMAIL>",,"Service Account",,,,"Service Account:  Corp IT - Shared email inbox","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18118","itadmin"
"Products","EMR","<EMAIL>",,"Service Account",,,,"shared email address monitored by the Product Operations Support Team (POST), for notifications from government bodies regarding lists that need to be regularly updated in Accuro.",,"<EMAIL>","S-1-5-21-**********-**********-*********-18339","Products"
"EMRcslt",,"<EMAIL>","Client Services","Service Account",,,,"Client Services Leadership Team email box, for communication between CSLT with CS staff.",,"<EMAIL>","S-1-5-21-**********-**********-*********-17324","Optcslt"
"OptimedImpW",,"<EMAIL>","Implementations","Service Account",,,,"Linked to an outlook calendar for implementer booking",,"<EMAIL>","S-1-5-21-**********-**********-*********-17018","OptimedImpW"
"Optimed","Development After Hours","<EMAIL>","Development","Service Account",,,,"Service Account: This Account is for after hours support so if CS needs to get a hold of DEV that's what they use it for.",,"<EMAIL>","S-1-5-21-**********-**********-*********-18797","osdevah"
"Accounting",,"<EMAIL>",,"Service Account",,,,"Service Account: Finance - 	User account with an active email address that was used on qtfin2 to send invoices to clients",,"<EMAIL>","S-1-5-21-**********-**********-*********-18796","accounting"
"Optimed","Admin","<EMAIL>",,"Service Account",,,,"Master inbox that is used for receiving sales e-fax and general sales related emails.",,"<EMAIL>","S-1-5-21-**********-**********-*********-14774","OSAdmin"
"EMR","Implementations","<EMAIL>","Implementations","Service Account",,,,"Service Account: PM's check that inbox routinely for faxes and or emails from client",,"<EMAIL>","S-1-5-21-**********-**********-*********-1458","Implementations"
"EMR","Development Inbox","<EMAIL>","Development","Service Account",,,,"Service Account: central developer account for app stores and alerts","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-9650","optdev"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT -  Accounts used by Exchange for health checks ","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20291","SM_aacdd242cde649b98"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT -  Accounts used by Exchange for health checks ","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20315","SM_ea45985bae3e4d2f9"
"qtmitelccm",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Service account on qtmitelcsm, also has scripts running with those credentials; Mitel Services",,"<EMAIL>","S-1-5-21-**********-**********-*********-17022","qtmitelccm"
,,,,"Service Account",,,,"Service Account: Corp IT - Account created by the Windows Azure Active Directory Sync tool with installation identifier '0e6855de790d47ebaefa653faf59f481' running on computer 'QTADFS2' configured to synchronize to tenant 'qhrtech.onmicrosoft.com'. This account must have directory replication permissions in the local Active Directory and write permission on certain attributes to enable Hybrid Deployment.","3/23/2022 1:09:13 PM",,"S-1-5-21-**********-**********-*********-20390","MSOL_0e6855de790d"
"bomgarIC",,,,"Service Account",,,,"Service Account: Corp IT - User for Bomgar Integration Client Scheduler service running on QTAdmin1","3/20/2022 3:17:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23635","bomgarIC"
"QHR backup","smtp","<EMAIL>",,"Service Account",,,,"Service Account: Tech Infrastructure - Backup notifications to C. Hounsham (SF Case ********)",,"<EMAIL>","S-1-5-21-**********-**********-*********-23228","qhrbackupsmtp"
"QHR Technologies IT",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Contact for IT assistance",,"<EMAIL>","S-1-5-21-**********-**********-*********-13309","IT"
"OXIDIZEDSA",,,,"Service Account",,,,"Service Account: Networking backups and scripts","1/14/2022 3:10:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23534","OXIDIZEDSA"
"Splunk","Alerts",,,"Service Account",,,,"Service Account: Pprovide Alerts generated from Splunk service.",,"<EMAIL>","S-1-5-21-**********-**********-*********-22911","splunk.alerts"
"IT User",,,,"Service Account",,,,"Nyel confirmed to Disable this on July 17 2020","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17367","ITUser"
"GPeconnect",,,,"Service Account",,,,"Service Account: Finance - Dynamics service account used on qtfin3",,"<EMAIL>","S-1-5-21-**********-**********-*********-23840","GPeconnect"
"Mimecast","LDAPS Sync",,,"Service Account",,,,"Service Account: Security - Secure LDAP account for Mimecast",,"<EMAIL>","S-1-5-21-**********-**********-*********-19814","mimecastsync"
"Trevor","Trainee","<EMAIL>",,"Service Account",,,,"Service Account:  HR - Used for Training Video/Document","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24176","trevor.trainee"
"PFsense","Service Account",,,"Service Account",,,,"Service Account: ","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17144","pfsense"
"econnect",,,,"Service Account",,,,"Service Account: Finance - for Dynamics running on qtfin3","2/28/2022 2:13:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23165","econnect"
"Dynamics MR","Service",,,"Service Account",,,,"Service Account: add-on to GP that provides/creates financial reports","3/14/2022 2:12:58 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20096","mrsvc"
"JiraConfshare",,,,"Service Account",,,,"Service Account: Corp IT - Sync account between JIRA servers","3/24/2022 3:47:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22590","JiraConfshare"
"SQL-osqasql1",,,,"Service Account",,,,"Service Account: QA - SQL Service Account","3/24/2021 5:01:18 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18681","sql-osqasql1"
"SQL-EMRDATASQL01",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2008R2
EMRDATASQL01\SQL2012
and EMRDATASQL01\SQL2017 MSSQL instances.","3/21/2022 1:02:08 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22632","SQL-EMRDATASQL01"
"SQL-OSDEVSQL2012",,,,"Service Account",,,,"Service Account: Development - SQL Service Account; used to run OSDEVSQL2012\OSDEVSQL2012
OSDEVSQL2012\MSSQL2014
and OSDEVSQL2012\MSSQL2016
MSSQL instances.","2/15/2022 3:02:02 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22629","SQL-OSDEVSQL2012"
"sftp","service",,,"Service Account",,,,"Service Account: Corp IT  - Used to mount shares on qtsftp1. Also used by newer Cerberus SFTP service (on QTSFTP1982 that never made it to production) ","4/8/2021 3:01:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20262","sftp"
"KonicaMinolta",,"<EMAIL>",,"Service Account",,,,"Service Account: LDAP & Email (for scans)",,"<EMAIL>","S-1-5-21-**********-**********-*********-24092","konicaminolta"
"Kace","User",,,"Service Account",,,,"Service Account: Corp IT - Read only user for LDAP imports (do NOT delete)","1/13/2021 3:18:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16673","KACE_User"
"SQL-QTVersionOne",,,,"Service Account",,,,"Service Account: Development - SQL Server account for VersionOne. Legacy system - replaced by JIRA - still referred to.","2/19/2021 5:22:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19680","sql-qtversionone"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Service account for VMWare","3/24/2022 10:56:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25154","vcenter-skyline"
"Commvault","vCenter User - cvvcuser",,,"Service Account",,,,"Service Account: Commvault vCenter User","3/24/2022 12:18:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22544","cvvcuser"
"jenkins",,,,"Service Account",,,,"Service Account: Development/QA - QA Test Account","3/24/2021 2:21:20 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19879","jenkins"
"VMware Reporting","Service Account",,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","3/24/2022 12:39:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23835","vmwarereport"
"SQL-osdevsql01",,,,"Service Account",,,,"Service Account: Data - SQL service acct. for Lisa St Laurent - DMD. 4 SQL instances. Server (old OS) should be replaced/deprecated.","8/10/2020 1:02:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-14680","SQL-osdevsql01"
"rhel-virt-who",,,,"Service Account",,,,"Service Account: RHEL Services Account","3/24/2022 12:01:27 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22598","rhel-virt-who"
"ActionTec",,,,"Service Account",,,,"Service Account: User required for wireless connection of ActionTec devices to ""Quadrant Network""",,"<EMAIL>","S-1-5-21-**********-**********-*********-22489","actiontec"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","3/24/2022 12:38:10 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23012","vcenter-ro"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Backup account for Asigra","12/16/2020 10:30:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23290","asigra-vmware"
"Mitel","Recording",,,"Service Account",,,,"Service Account: Corp IT - User account used on qtmitelscreen and qtmitelcall for accessing shares on those servers","3/23/2022 2:00:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22799","mitelrecording"
,,,,"Service Account",,,,"Service Account: AD account that it connects to its database with on infrasql where specific permissions are configured","3/24/2022 2:17:35 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26194","solarwindsdbservice"
"KantechEmail","Weekly Report",,,"Service Account",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26612","kantechemail"
"CS","Training",,"CS","Service Account",,,,"Client Services, used for booking training for employees.","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26631","cs.training"
"Intune","NDES",,,"Service Account",,,,"Service Account : Corp IT-NDES to enroll certificates from Certificate Authority to AADJ Devices","10/16/2021 7:18:50 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24813","IntuneNDES"
"ca","secscan",,,"Service Account",,,,"Service Account: Security - This AD Account was created in response to salesforce ticket 01309659 to access QHR servers and report back to the CIS platform dashboard. This account ensure servers are configured Securely.","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26674","ca_secscan"
"OME","User",,,"Service Account",,,,"Service Account: Corp IT - Windows account to access SMB Share on Dell-DRM","8/13/2020 3:28:11 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26682","omeuser"
"SQL-EMRDATASQL02",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2019 MSSQL instance.","3/21/2022 1:05:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24900","SQL-EMRDATASQL02"
"OXIDIZEDSABASH",,,,"Service Account",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26745","OXIDIZEDSABASH"
"Service-Now","Blob-Reader",,,"Service Account",,,,"Service Account: Corp IT - Used for Azure blob storage and Service-Now Prod impersonation connection",,"<EMAIL>","S-1-5-21-**********-**********-*********-26331","servicenow.blob"
"sa-paloalto","userid",,,"Service Account",,,,"Service Account: Networking - used by corp palo alto firewalls to perform user-id lookups","12/15/2021 4:30:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26753","sa-paloalto-userid"
"Jira","Test",,,"Service Account",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26886","jiratesting"
