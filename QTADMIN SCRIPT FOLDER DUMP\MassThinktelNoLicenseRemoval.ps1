﻿#Update path to CSV list
$path = "C:\temp\TeamsExport.csv"

Connect-MicrosoftTeams

#This loop loops through each line of the CSV, takes the username and looks up the AD User, afterwards it will apply the Alias.
Import-Csv $path | Foreach-Object { 

Grant-CsOnlineVoiceRoutingPolicy -Identity $($_.Name) -PolicyName $null
Set-CsUser -identity $($_.Name) -EnterpriseVoiceEnabled $false -HostedVoicemail $false -OnPremlineURI $null
Set-CsUserPstnSettings -identity $($_.Name) -HybridPstnSite $null
Set-CsUser -identity $($_.Name) -EnterpriseVoiceEnabled $true -HostedVoicemail $true -OnPremlineURI $($_.Phone)
Grant-CsOnlineVoiceRoutingPolicy -Identity $($_.Name) -PolicyName "ThinkTel"

}

pause