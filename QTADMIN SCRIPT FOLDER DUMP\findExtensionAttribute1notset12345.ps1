﻿$userAccounts = Get-ADUser -Filter {Enabled -eq $true -and PasswordNeverExpires -eq $False} -Properties * | Where-Object {$_.UserPrincipalName -match "@qhrtech.com" -and $_.UserPrincipalName -notmatch "da-" -and !($_.extensionAttribute1 -notmatch "1" -and $_.extensionAttribute1 -notmatch "2" -and $_.extensionAttribute1 -notmatch "3" -and $_.extensionAttribute1 -notmatch "4" -and $_.extensionAttribute1 -notmatch "5")} | select -Property UserPrincipalName, extensionAttribute1
$userAccounts

$userAccounts | export-csv C:\Scripts\extensionOutputNo1234.csv
