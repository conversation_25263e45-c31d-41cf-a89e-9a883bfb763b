# Check if Active Directory module is available, if not, install it
$adModule = Get-Module -Name ActiveDirectory -ListAvailable
if ($adModule -eq $null) {
    Install-WindowsFeature RSAT-AD-PowerShell
    Import-Module ActiveDirectory
}

# Path to the CSV file containing usernames
$csvFilePath = "C:\Scripts\Under Construction\Graveyard Cleanup Script\June 2024\UsersToDelete.csv"

# Path to the log file
$logFilePath = "C:\Scripts\Under Construction\Graveyard Cleanup Script\June 2024\DeleteUserLog.txt"

# Clear the log file if it exists
Clear-Content -Path $logFilePath -ErrorAction SilentlyContinue

# Read the usernames from the CSV file
$usersToDelete = Import-Csv -Path $csvFilePath | Select-Object -ExpandProperty Username

 Loop through each user to delete
foreach ($user in $usersToDelete) {
    try {
        # Check if user exists
        $existingUser = Get-ADUser -Identity $user -ErrorAction SilentlyContinue
        if ($existingUser -ne $null) {
            # Delete the user
            Remove-ADUser -Identity $user -Confirm:$false -ErrorAction Stop
            # Log success
            $logMessage = "User '$user' deleted successfully."
            Add-Content -Path $logFilePath -Value $logMessage
            Write-Output $logMessage
        } else {
            # Log failure if user doesn't exist
            $logMessage = "User '$user' does not exist."
            Add-Content -Path $logFilePath -Value $logMessage
            Write-Output $logMessage
        }
    } catch {
        # Log failure if any error occurs during deletion
        $errorMessage = $_.Exception.Message
        $logMessage = "Failed to delete user '$user'. Error: $errorMessage"
        Add-Content -Path $logFilePath -Value $logMessage
        Write-Output $logMessage
    }
}

# Display log contents
Get-Content -Path $logFilePath
