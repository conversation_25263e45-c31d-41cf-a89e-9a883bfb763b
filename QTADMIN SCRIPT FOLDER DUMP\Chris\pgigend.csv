﻿#TYPE Selected.Microsoft.ActiveDirectory.Management.ADUser
"Name","telephonenumber","mobile","office","userprincipalname","title"
"<PERSON>","+****************","+****************","Toronto","<PERSON><PERSON><PERSON><PERSON>@QHRtech.com","Account Executive-<PERSON>"
"<PERSON>","+**************** ,3101","+****************","300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","Director of IT & Shared Services"
"<PERSON> Kitella","+**************** ,6319","+****************","300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","System Administrator"
"<PERSON>y","+**************** ,3108","+****************","300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","Junior System Administrator"
"<PERSON>","+**************** ,3102","+****************","300 - 1620 <PERSON> <PERSON>, <PERSON><PERSON>a, BC V1Y 9Y2","<EMAIL>","IT Team Lead"
"Stefanie Giddens","+**************** ,3527","+****************","300 - 1620 <PERSON> Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","Marketing Manager"
"Ken Gordon","+**************** ,3110",,"300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","IT Business Solutions Lead"
"Ken Royea","+**************** ,3111",,"300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","IT Business Solutions Analyst"
"Robert Armstrong","+**************** ,3113","+****************","300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","System Administrator"
"Yeison Rios","+**************** , 3115",,"Toronto","<EMAIL>","Helpdesk Analyst"
"Danny Srepel","+****************, 3103","+1 (250) 808-3017","300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","Lead System Administrator"
"Martin Weiss","+**************** ,3104","250-212-2353","300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2","<EMAIL>","Helpdesk Analyst"
"Devin Nate","+**************** ,6330","+1 (403) 650-0871","Calgary","<EMAIL>","Director of Technology"
"Mark McLean","+**************** ,6329","+1 (403) 617-8489","Calgary","<EMAIL>","Sr. IT Systems Engineer"