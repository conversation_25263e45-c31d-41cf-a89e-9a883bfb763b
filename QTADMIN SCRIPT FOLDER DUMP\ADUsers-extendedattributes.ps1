﻿Get-ADUser -Filter 'Company -like "*QHR*"' -Identity "EMR - CS All" -Properties *  | Select -Property EmailAddress,GivenName,Surname,DisplayName,Title,Department,Office,OfficePhone,MobilePhone,extensionAttribute1,extensionAttribute2,extensionAttribute3,extensionAttribute4,extensionAttribute5,extensionAttribute6,extensionAttribute7,extensionAttribute8,extensionAttribute9,extensionAttribute10,extensionAttribute11,extensionAttribute12,extensionAttribute13,extensionAttribute14,extensionAttribute15 | Export-CSV "C:\\Temp\\ADusers.csv" -NoTypeInformation -Encoding UTF8