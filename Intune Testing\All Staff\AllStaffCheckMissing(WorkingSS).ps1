<#
    Title: Check employees without an All Staff group, addressing domain alias issues and partial mismatch.
#>

#set tls1.2 as default or exchange will fail to connect
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

##############################################################################
### STEP 1: Connect to Exchange Online
##############################################################################
Write-Host "=== STEP 1: Connect to Exchange Online ==="
Connect-ExchangeOnline

##############################################################################
### STEP 2: Connect to Microsoft Graph
##############################################################################
Write-Host "`n=== STEP 2: Connect to Microsoft Graph ==="
Connect-MgGraph -Scopes "Group.Read.All", "User.Read.All", "Directory.Read.All"

##############################################################################
### STEP 3: Initialize Arrays & Functions
##############################################################################
Write-Host "`n=== STEP 3: Initialize Arrays and Helper Functions ==="

# This Dictionary maps a user's email to a list of the All-Staff groups they belong to.
$AllStaffMembership = [System.Collections.Generic.Dictionary[string, System.Collections.Generic.List[string]]]::new()

# Stores which groups we have "seen" to avoid infinite loops when expanding nested groups
$Global:SeenGroups = @()

# Helper function: Write a verbose debug line easily
function Debug-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message
    )
    Write-Host "[DEBUG] $Message" -ForegroundColor DarkCyan
}

# 3.1) Hashtable to map old or alternative domains -> the official domain
$DomainAliasMap = @{
    "qhrtechnologies.com" = "qhrtech.com"
    "optimedsoftware.com" = "qhrtech.com"
}

# 3.2) Function to unify domain aliases
function Convert-EmailDomain {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Address
    )
    $Address = $Address.ToLower()
    $parts = $Address.Split("@")
    if ($parts.Count -eq 2) {
        $localPart = $parts[0]
        $domain = $parts[1]
        if ($DomainAliasMap.ContainsKey($domain)) {
            $targetDomain = $DomainAliasMap[$domain]
            if ($targetDomain -and $targetDomain -ne $domain) {
                Debug-Log "Auto-correcting domain from '$domain' => '$targetDomain' for address '$Address'"
                return "$localPart@$targetDomain"
            }
        }
    }
    return $Address
}

# 3.3) Helper function to add a member and their group to our main mapping dictionary
function Add-MemberToGroupMapping {
    param(
        [Parameter(Mandatory = $true)][string]$Email,
        [Parameter(Mandatory = $true)][string]$GroupName
    )
    $normalizedEmail = Convert-EmailDomain -Address $Email
    if (-not $AllStaffMembership.ContainsKey($normalizedEmail)) {
        $AllStaffMembership[$normalizedEmail] = [System.Collections.Generic.List[string]]::new()
    }
    if (-not $AllStaffMembership[$normalizedEmail].Contains($GroupName)) {
        $AllStaffMembership[$normalizedEmail].Add($GroupName)
    }
}

# 3.4) RECURSIVE function to get all members of a mail-enabled distribution group
function Get-DistGroupMembersRecursive {
    param(
        [Parameter(Mandatory = $true)][string]$GroupIdentity,
        [Parameter(Mandatory = $true)][string]$TopLevelGroupName
    )
    if ($Global:SeenGroups -contains $GroupIdentity) { return }
    else { $Global:SeenGroups += $GroupIdentity }

    Debug-Log "Retrieving members for distro/group mailbox: $GroupIdentity (part of '$TopLevelGroupName')"
    try {
        $members = Get-DistributionGroupMember -Identity $GroupIdentity -ResultSize Unlimited -ErrorAction Stop
        foreach ($member in $members) {
            if ($member.RecipientType -match 'Group') {
                Debug-Log "Nested group found: $($member.Name). Recursing..."
                Get-DistGroupMembersRecursive $member.Name $TopLevelGroupName
            }
            else {
                $addr = $member.PrimarySmtpAddress
                if (-not $addr) { $addr = $member.WindowsEmailAddress }
                if ($addr) {
                    Add-MemberToGroupMapping -Email $addr -GroupName $TopLevelGroupName
                }
            }
        }
    }
    catch {
        Write-Warning "Get-DistributionGroupMember failed for '$GroupIdentity': $($_.Exception.Message)"
    }
}

# 3.5) Fallback function to get membership from Unified Group
function Get-UnifiedGroupLinksMembership {
    param(
        [Parameter(Mandatory = $true)][string]$GroupIdentity,
        [Parameter(Mandatory = $true)][string]$TopLevelGroupName
    )
    Debug-Log "Retrieving members via Get-UnifiedGroupLinks for group: $GroupIdentity (part of '$TopLevelGroupName')"
    try {
        $links = Get-UnifiedGroupLinks -Identity $GroupIdentity -LinkType Members -ResultSize Unlimited -ErrorAction Stop
        foreach ($lnk in $links) {
            $addr = $lnk.PrimarySmtpAddress
            if (-not $addr) { $addr = $lnk.WindowsEmailAddress }
            if ($addr) {
                Add-MemberToGroupMapping -Email $addr -GroupName $TopLevelGroupName
            }
        }
    }
    catch {
        Write-Warning "Get-UnifiedGroupLinks failed for '$GroupIdentity': $($_.Exception.Message)"
    }
}

# 3.6) Function to handle dynamic distribution group membership
function Get-DynamicGroupMembership {
    param(
        [Parameter(Mandatory = $true)][string]$GroupIdentity,
        [Parameter(Mandatory = $true)][string]$TopLevelGroupName
    )
    Debug-Log "Retrieving dynamic distribution group info for: $GroupIdentity (part of '$TopLevelGroupName')"
    try {
        $dynGroup = Get-DynamicDistributionGroup -Identity $GroupIdentity -ErrorAction Stop
        $filter = $dynGroup.RecipientFilter
        $orgUnit = $dynGroup.OrganizationUnit
        if ($filter) {
            $members = Get-Recipient -RecipientPreviewFilter $filter -OrganizationalUnit $orgUnit -ResultSize Unlimited -ErrorAction SilentlyContinue
            foreach ($m in $members) {
                $addr = $m.PrimarySmtpAddress
                if (-not $addr) { $addr = $m.WindowsEmailAddress }
                if ($addr) {
                    Add-MemberToGroupMapping -Email $addr -GroupName $TopLevelGroupName
                }
            }
        }
    }
    catch {
        Write-Warning "Get-DynamicDistributionGroup failed for '$GroupIdentity': $($_.Exception.Message)"
    }
}

# ---[ CHANGE START ]---
# ----------------------------------------------------------------------------
# 3.7) Define groups to explicitly exclude from the search
# ----------------------------------------------------------------------------
# Exclusion by unique Object ID (most reliable method)
$ExcludedGroupObjectIDs = @(
    'c71749db-af7f-4571-ba00-70760131943d' # QHR-ALL-STAFF
)

# Exclusion by Display Name (case-insensitive)
$ExcludedGroupDisplayNames = @(
    'All Staff - Shared Services',
    '* All Staff - Shared Services',
    'Clinicare - All Staff',
    'All Staff - Medeo',
    '* All Staff - Medeo'
)

Write-Host "NOTE: The following Group Object IDs will be excluded from all searches: $($ExcludedGroupObjectIDs -join ', ')" -ForegroundColor Cyan
Write-Host "NOTE: The following Group Display Names will be excluded from all searches: $($ExcludedGroupDisplayNames -join ', ')" -ForegroundColor Cyan
# ---[ CHANGE END ]---

##############################################################################
### STEP 4: Get AD Users
##############################################################################
Write-Host "`n=== STEP 4: Collect AD user list ==="
try {
    $userList = Get-ADUser -Filter { (Title -ne "Service Account") -and (Name -notlike "*Domain*") -and (Name -notlike "*test*") -and (Name -notlike "*svc*") } | Select-Object -ExpandProperty UserPrincipalName
}
catch {
    Write-Error "ERROR: Failed retrieving AD users. Please run this on a machine with AD cmdlets."
    return
}
Write-Host "Found $($userList.Count) AD user(s)."

##############################################################################
### STEP 5: Retrieve EXO Groups Named All Staff
##############################################################################
Write-Host "`n=== STEP 5: Get EXO groups with 'All Staff/allstaff' in DisplayName or SMTP ==="

# ---[ CHANGE ]--- Added exclusion for Display Names to the Where-Object clause.
$exoGroups = Get-EXORecipient `
    -RecipientTypeDetails MailUniversalDistributionGroup, GroupMailbox, DynamicDistributionGroup `
    -Filter "((DisplayName -like '*all staff*') -or (PrimarySmtpAddress -like '*allstaff*'))" `
    -ResultSize Unlimited -ErrorAction Stop |
Where-Object {
    ($_.ExternalDirectoryObjectId -notin $ExcludedGroupObjectIDs) -and
    ($_.DisplayName -notin $ExcludedGroupDisplayNames)
}

Write-Host "Found $($exoGroups.Count) matching group(s) in EXO (after exclusions)."
foreach ($grp in $exoGroups) {
    Write-Host ("   [EXO] {0} / {1} / {2}" -f $grp.DisplayName, $grp.PrimarySmtpAddress, $grp.RecipientTypeDetails)
}

##############################################################################
### STEP 6: Retrieve/Expand Each EXO Group
##############################################################################
Write-Host "`n=== STEP 6: Expand each EXO group membership (with recursion, dynamic, fallback) ==="

foreach ($grp in $exoGroups) {
    $grpName = $grp.DisplayName
    $grpAddress = $grp.PrimarySmtpAddress
    $grpType = $grp.RecipientTypeDetails

    switch ($grpType) {
        'MailUniversalDistributionGroup' { Get-DistGroupMembersRecursive -GroupIdentity $grpAddress -TopLevelGroupName $grpName }
        'GroupMailbox' { try { Get-DistGroupMembersRecursive -GroupIdentity $grpAddress -TopLevelGroupName $grpName } catch { Get-UnifiedGroupLinksMembership -GroupIdentity $grpAddress -TopLevelGroupName $grpName } }
        'DynamicDistributionGroup' { Get-DynamicGroupMembership -GroupIdentity $grp.Identity -TopLevelGroupName $grpName }
        default { Write-Host "Skipping unknown type: $grpType" }
    }
}

##############################################################################
### STEP 7: Retrieve M365 (Unified) Groups from Graph
##############################################################################
Write-Host "`n=== STEP 7: Retrieve M365 (Unified) groups from Graph with 'All Staff/allstaff' ==="

$m365AllGroups = Get-MgGroup -All -Filter "groupTypes/any(c:c eq 'Unified')" -ConsistencyLevel eventual -CountVariable m365Count
Write-Host ("Retrieved {0} M365 Unified groups from Graph." -f $m365Count)

# ---[ CHANGE ]--- Added exclusion for Display Names to the Where-Object clause.
$m365Groups = $m365AllGroups | Where-Object {
    ($_.DisplayName -imatch 'all staff' -or $_.Mail -imatch 'allstaff') -and
    ($_.Id -notin $ExcludedGroupObjectIDs) -and
    ($_.DisplayName -notin $ExcludedGroupDisplayNames)
}

Write-Host ("Filtered to {0} M365 group(s) (after exclusions)." -f $m365Groups.Count)
foreach ($g in $m365Groups) {
    Write-Host "  [M365] $($g.DisplayName) / $($g.Mail) / $($g.Id)"
}

##############################################################################
### STEP 8: Get Membership from Each M365 Group (via Graph)
##############################################################################
Write-Host "`n=== STEP 8: Expand membership from each M365 group ==="

foreach ($g in $m365Groups) {
    Write-Host ("Retrieving membership for M365 group: {0}" -f $g.DisplayName)
    try {
        $members = Get-MgGroupMember -GroupId $g.Id -All
        $userMembers = $members | Where-Object { $_.'@odata.type' -eq '#microsoft.graph.user' }
        Write-Host ("   [M365] Found {0} user(s) in group '{1}'" -f $userMembers.Count, $g.DisplayName)

        foreach ($u in $userMembers) {
            $addr = $u.UserPrincipalName
            if (-not $addr) { $addr = $u.Mail }
            if ($addr) {
                Add-MemberToGroupMapping -Email $addr -GroupName $g.DisplayName
            }
        }
    }
    catch {
        Write-Warning "Failed retrieving members for Graph group '$($g.DisplayName)': $($_.Exception.Message)"
    }
}

##############################################################################
### STEP 9: Generate Detailed Membership Log
##############################################################################
Write-Host "`n=== STEP 9: Generate detailed membership log ==="

$logOutput = foreach ($email in $AllStaffMembership.Keys | Sort-Object) {
    $groups = $AllStaffMembership[$email] -join ', '
    "$email > $groups"
}

$logOutput | Out-File "C:\Temp\AllStaffMembers.txt"
Write-Host "All staff membership addresses logged with group details to: C:\Temp\AllStaffMembers.txt"
Write-Host ("Total unique users found in All Staff groups: {0}" -f $AllStaffMembership.Count)

##############################################################################
### STEP 10: Compare AD User List to Combined Membership
##############################################################################
Write-Host "`n=== STEP 10: Compare AD user list to combined membership ==="

$IsNotInAllStaff = @()
$InAllStaff = @()

foreach ($employeeUPN in $userList) {
    $empLower = $employeeUPN.ToLower()
    
    if ($AllStaffMembership.ContainsKey($empLower)) {
        $InAllStaff += $employeeUPN
        $groups = $AllStaffMembership[$empLower] -join ', '
        Write-Host "[IN]  $employeeUPN (Member of: $groups)" -ForegroundColor Green
    }
    else {
        $IsNotInAllStaff += $employeeUPN
        Write-Host "[OUT] $employeeUPN" -ForegroundColor Yellow
    }
}

##############################################################################
### STEP 11: Double-Check Flagged Users
##############################################################################
Write-Host "`n=== STEP 11: Double-check flagged users ==="
$FinalMissing = @()

foreach ($missingUser in $IsNotInAllStaff) {
    if ($AllStaffMembership.ContainsKey($missingUser.ToLower())) {
        $groups = $AllStaffMembership[$missingUser.ToLower()] -join ', '
        Write-Host "   [INFO] $missingUser is actually in the final membership mapping. (Member of: $groups). Skipping." -ForegroundColor Cyan
    }
    else {
        $FinalMissing += $missingUser
    }
}

##############################################################################
### STEP 12: Output Missing Users in a Colored List
##############################################################################
Write-Host "`n=== STEP 12: The following employees have NO All Staff group ===" -ForegroundColor Magenta

if ($FinalMissing.Count -eq 0) {
    Write-Host "Great news! No missing users found." -ForegroundColor Green
}
else {
    $FinalMissing | Sort-Object | ForEach-Object {
        Write-Host ("   " + $_) -ForegroundColor Red
    }
}

##############################################################################
### STEP 13: Disconnect
##############################################################################
Write-Host "`n=== STEP 13: Disconnecting from Exchange Online & Microsoft Graph ==="

Disconnect-ExchangeOnline -Confirm:$false
Disconnect-MgGraph

Write-Host "`nAll done!"