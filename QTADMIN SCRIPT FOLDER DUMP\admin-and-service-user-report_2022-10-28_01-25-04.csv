﻿"First Name","Last Name","Email Address","Department","Title","Mobile","Extension attribute 10","Extension attribute 11","Description","Last Log-on Date","Full User Logon","Unique Account ID","User Logon"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"Domain Admin -","<PERSON>","<EMAIL>",,,,,,,"2/7/2022 8:36:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22274","da-amcfadden"
"Domain Admin -","<PERSON>","<EMAIL>",,,,,,,"10/3/2022 11:17:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,,"9/16/2022 8:06:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24060","da-smay"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,,"10/28/2022 6:33:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,,"1/21/2020 3:05:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19009","da-nated"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,,"4/16/2021 8:46:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,,"10/28/2022 11:56:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,,"10/28/2022 10:31:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,,"10/25/2022 2:39:53 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,,"10/28/2022 10:52:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22187","da-sbradford"
"da-","mhoppe","<EMAIL>",,,,,,,"12/21/2021 1:48:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23206","da-mhoppe"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,,"8/23/2022 11:35:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25149","da-tdrescher"
"Domain Admin","Craig Hounsham","<EMAIL>",,,,,,,"3/22/2022 12:18:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24706","da-chounsham"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,,"9/13/2021 12:09:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26188","da-mkandy"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,,"12/16/2020 11:48:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24838","da-balbrecht"
"Paul","Farry",,,,,,,,"10/28/2022 8:19:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26431","da-pfarry"
"Domain Admin - Sudeep","Mool",,,,,,,,"7/5/2022 11:19:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26892","da-smool"
"Domain Admin","Justin Germain",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26481","da-jgermain"
"Domain Admin -","Muhammad Ali","<EMAIL>",,,,,,,"5/16/2022 1:46:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26483","da-mali"
"Greg","Harshenin","<EMAIL>",,,,,,,"10/27/2022 3:23:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12624","da-gharshenin"
"O","Perator","<EMAIL>",,"Service Account",,,,"Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","1/18/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1161","operator"
"Malcolm","Kennedy",,,,,,,,"9/28/2020 11:52:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24632","da-mkennedy"
"Domain Admin","Nyel English","<EMAIL>",,,,,,,"10/27/2022 1:06:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,,"8/18/2022 6:10:50 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,,"1/13/2022 11:06:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,,"9/19/2022 8:52:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,,"3/2/2021 2:53:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15393","da-amehl"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,,"10/28/2022 11:03:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24717","da-jbell"
"Jarrid","Pond",,,,,,,,"10/27/2022 11:00:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26768","da-jpond"
"Domain Admin","Dilcia Torres","<EMAIL>",,,,,,,"10/20/2022 3:21:47 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26392","da-dtorres"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"MDT",,,,"Service Account",,,,"Service Account: Windows deployment account","10/28/2022 9:43:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-13593","MDT"
"O","Perator","<EMAIL>",,"Service Account",,,,"Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","1/18/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1161","operator"
"MitelRemote",,,,"Service Account","+****************","Nick Janzen","Nick Janzen","External Contractor User Account Alan Marjoribanks CTS","3/29/2021 8:17:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20129","mitelremote"
"Gluu","LDAP Sync",,,"Service Account",,,,"Service Account: Development - Gluu Test Account",,"<EMAIL>","S-1-5-21-**********-**********-*********-22628","gluusync"
"JIRA","LDAP SYNC",,,"Service Account",,,,"Service Account: Corp IT - Jira and Confluence Service Account","11/8/2021 12:29:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22624","jirasync"
"QHR Infrastructure","Operator",,,"Service Account",,,,"Service Account: Corp IT - Used by WSUS reporting on qhrops","1/27/2020 4:29:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17511","qtitoperator"
"OneLoginADC",,,,"Service Account",,,,"Service Account: Corp IT -  this service account was created for the OneLogin ADC","12/15/2020 8:56:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22793","OneLoginADC"
"QTADMIN1 service",,,,"Service Account",,,,"Service Account: Corp IT - Account used to run scheduled scripts on qtadmin1; Admin monitoring scripts","10/28/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22370","qtadmin1service"
"Optimed","Sales","<EMAIL>","Sales","Service Account",,,,"Service Account:Email Account for Optimed Software Sales",,"<EMAIL>","S-1-5-21-**********-**********-*********-1456","OPT_Sales"
"Opt","Development",,,"Service Account",,,,"Service Account: Development - SQL Server Account","9/21/2021 9:10:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1426","opt_development"
"QHR Technologies","Support","<EMAIL>","Client Services","Service Account","+1 (250) 801-4274",,,"Service Account: Corp IT - Email / GTA no screen saver / After hours BB Support","10/16/2022 3:50:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1454","OPT_Support"
"e","learning","<EMAIL>",,"Service Account",,,,"Service Account:For learning and Development","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1260","elearning"
"IT","Admin","<EMAIL>",,"Service Account",,,,"Service Account:  Corp IT - Shared email inbox",,"<EMAIL>","S-1-5-21-**********-**********-*********-18118","itadmin"
"Products","EMR","<EMAIL>",,"Service Account",,,,"shared email address monitored by the Product Operations Support Team (POST), for notifications from government bodies regarding lists that need to be regularly updated in Accuro.",,"<EMAIL>","S-1-5-21-**********-**********-*********-18339","Products"
"EMRcslt",,"<EMAIL>","Client Services","Service Account",,,,"Client Services Leadership Team email box, for communication between CSLT with CS staff.",,"<EMAIL>","S-1-5-21-**********-**********-*********-17324","Optcslt"
"OptimedImpW",,"<EMAIL>","Implementations","Service Account",,,,"Linked to an outlook calendar for implementer booking",,"<EMAIL>","S-1-5-21-**********-**********-*********-17018","OptimedImpW"
"Optimed","Development After Hours","<EMAIL>","Development","Service Account",,,,"Service Account: This Account is for after hours support so if CS needs to get a hold of DEV that's what they use it for.",,"<EMAIL>","S-1-5-21-**********-**********-*********-18797","osdevah"
"Accounting",,"<EMAIL>",,"Service Account",,,,"Service Account: Finance - 	User account with an active email address that was used on qtfin2 to send invoices to clients","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18796","accounting"
"Optimed","Admin","<EMAIL>",,"Service Account",,,,"Master inbox that is used for receiving sales e-fax and general sales related emails.",,"<EMAIL>","S-1-5-21-**********-**********-*********-14774","OSAdmin"
"EMR","Implementations","<EMAIL>","Implementations","Service Account",,,,"Service Account: PM's check that inbox routinely for faxes and or emails from client",,"<EMAIL>","S-1-5-21-**********-**********-*********-1458","Implementations"
"EMR","Development Inbox","<EMAIL>","Development","Service Account",,,,"Service Account: central developer account for app stores and alerts","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-9650","optdev"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT -  Accounts used by Exchange for health checks ","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20291","SM_aacdd242cde649b98"
"qtmitelccm",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Service account on qtmitelcsm, also has scripts running with those credentials; Mitel Services","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17022","qtmitelccm"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT -  Accounts used by Exchange for health checks ","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20315","SM_ea45985bae3e4d2f9"
,,,,"Service Account",,,,"Service Account: Corp IT - Account created by the Windows Azure Active Directory Sync tool with installation identifier '0e6855de790d47ebaefa653faf59f481' running on computer 'QTADFS2' configured to synchronize to tenant 'qhrtech.onmicrosoft.com'. This account must have directory replication permissions in the local Active Directory and write permission on certain attributes to enable Hybrid Deployment.","10/27/2022 3:44:59 AM",,"S-1-5-21-**********-**********-*********-20390","MSOL_0e6855de790d"
"bomgarIC",,,,"Service Account",,,,"Service Account: Corp IT - User for Bomgar Integration Client Scheduler service running on QTAdmin1","9/30/2022 2:51:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23635","bomgarIC"
"QHR backup","smtp","<EMAIL>",,"Service Account",,,,"Service Account: Tech Infrastructure - Backup notifications to C. Hounsham (SF Case ********)",,"<EMAIL>","S-1-5-21-**********-**********-*********-23228","qhrbackupsmtp"
"QHR Technologies IT",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Contact for IT assistance",,"<EMAIL>","S-1-5-21-**********-**********-*********-13309","IT"
"OXIDIZEDSA",,,,"Service Account",,,,"Service Account: Networking backups and scripts","1/14/2022 3:10:15 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23534","OXIDIZEDSA"
"Trevor","Trainee","<EMAIL>",,"Service Account",,,,"Service Account:  HR - Used for Training Video/Document",,"<EMAIL>","S-1-5-21-**********-**********-*********-24176","trevor.trainee"
"SQL-osqasql1",,,,"Service Account",,,,"Service Account: QA - SQL Service Account","3/23/2021 5:01:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18681","sql-osqasql1"
"VMware Reporting","Service Account",,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","10/28/2022 1:24:10 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23835","vmwarereport"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Backup account for Asigra","12/17/2020 1:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23290","asigra-vmware"
"econnect",,,,"Service Account",,,,"Service Account: Finance - for Dynamics running on qtfin3","9/15/2022 2:40:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23165","econnect"
"ActionTec",,,,"Service Account",,,,"Service Account: User required for wireless connection of ActionTec devices to ""Quadrant Network""",,"<EMAIL>","S-1-5-21-**********-**********-*********-22489","actiontec"
"jenkins",,,,"Service Account",,,,"Service Account: Development/QA - QA Test Account","3/21/2021 4:59:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19879","jenkins"
"SQL-OSDEVSQL2012",,,,"Service Account",,,,"Service Account: Development - SQL Service Account; used to run OSDEVSQL2012\OSDEVSQL2012
OSDEVSQL2012\MSSQL2014
and OSDEVSQL2012\MSSQL2016
MSSQL instances.","8/10/2022 8:26:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22629","SQL-OSDEVSQL2012"
"Mimecast","LDAPS Sync",,,"Service Account",,,,"Service Account: Security - Secure LDAP account for Mimecast",,"<EMAIL>","S-1-5-21-**********-**********-*********-19814","mimecastsync"
"Splunk","Alerts",,,"Service Account",,,,"Service Account: Pprovide Alerts generated from Splunk service.",,"<EMAIL>","S-1-5-21-**********-**********-*********-22911","splunk.alerts"
"IT User",,,,"Service Account",,,,"Nyel confirmed to Disable this on July 17 2020",,"<EMAIL>","S-1-5-21-**********-**********-*********-17367","ITUser"
"GPeconnect",,,,"Service Account",,,,"Service Account: Finance - Dynamics service account used on qtfin3",,"<EMAIL>","S-1-5-21-**********-**********-*********-23840","GPeconnect"
"PFsense","Service Account",,,"Service Account",,,,"Service Account: ",,"<EMAIL>","S-1-5-21-**********-**********-*********-17144","pfsense"
"SQL-QTVersionOne",,,,"Service Account",,,,"Service Account: Development - SQL Server account for VersionOne. Legacy system - replaced by JIRA - still referred to.","3/4/2021 3:03:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19680","sql-qtversionone"
"sftp","service",,,"Service Account",,,,"Service Account: Corp IT  - Used to mount shares on qtsftp1. Also used by newer Cerberus SFTP service (on QTSFTP1982 that never made it to production) ","4/1/2021 3:01:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20262","sftp"
"KonicaMinolta",,"<EMAIL>",,"Service Account",,,,"Service Account: LDAP & Email (for scans)","6/30/2020 9:42:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24092","konicaminolta"
"rhel-virt-who",,,,"Service Account",,,,"Service Account: RHEL Services Account","10/28/2022 1:01:29 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22598","rhel-virt-who"
"Commvault","vCenter User - cvvcuser",,,"Service Account",,,,"Service Account: Commvault vCenter User","10/28/2022 1:15:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22544","cvvcuser"
"JiraConfshare",,,,"Service Account",,,,"Service Account: Corp IT - Sync account between JIRA servers","10/28/2022 3:47:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22590","JiraConfshare"
"Kace","User",,,"Service Account",,,,"Service Account: Corp IT - Read only user for LDAP imports (do NOT delete)","1/13/2021 3:22:21 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16673","KACE_User"
"SQL-osdevsql01",,,,"Service Account",,,,"Service Account: Data - SQL service acct. for Lisa St Laurent - DMD. 4 SQL instances. Server (old OS) should be replaced/deprecated.","8/7/2020 9:51:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-14680","SQL-osdevsql01"
"SQL-EMRDATASQL01",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2008R2
EMRDATASQL01\SQL2012
and EMRDATASQL01\SQL2017 MSSQL instances.","10/24/2022 1:02:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22632","SQL-EMRDATASQL01"
"Dynamics MR","Service",,,"Service Account",,,,"Service Account: add-on to GP that provides/creates financial reports","10/27/2022 3:31:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20096","mrsvc"
"Mitel","Recording",,,"Service Account",,,,"Service Account: Corp IT - User account used on qtmitelscreen and qtmitelcall for accessing shares on those servers","8/24/2022 12:02:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22799","mitelrecording"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Service account for VMWare","10/28/2022 11:38:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25154","vcenter-skyline"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","10/28/2022 1:21:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23012","vcenter-ro"
,,,,"Service Account",,,,"Service Account: AD account that it connects to its database with on infrasql where specific permissions are configured","10/28/2022 12:17:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26194","solarwindsdbservice"
"KantechEmail","Weekly Report",,,"Service Account",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26612","kantechemail"
"CS","Training",,"CS","Service Account",,,,"Client Services, used for booking training for employees.",,"<EMAIL>","S-1-5-21-**********-**********-*********-26631","cs.training"
"Intune","NDES",,,"Service Account",,,,"Service Account : Corp IT-NDES to enroll certificates from Certificate Authority to AADJ Devices","10/17/2021 7:24:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24813","IntuneNDES"
"ca","secscan",,,"Service Account",,,,"Service Account: Security - This AD Account was created in response to salesforce ticket 01309659 to access QHR servers and report back to the CIS platform dashboard. This account ensure servers are configured Securely.",,"<EMAIL>","S-1-5-21-**********-**********-*********-26674","ca_secscan"
"OME","User",,,"Service Account",,,,"Service Account: Corp IT - Windows account to access SMB Share on Dell-DRM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26682","omeuser"
"SQL-EMRDATASQL02",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2019 MSSQL instance.","8/10/2022 9:04:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24900","SQL-EMRDATASQL02"
"OXIDIZEDSABASH",,,,"Service Account",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26745","OXIDIZEDSABASH"
"Service-Now","Blob-Reader",,,"Service Account",,,,"Service Account: Corp IT - Used for Azure blob storage and Service-Now Prod impersonation connection","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26331","servicenow.blob"
"sa-paloalto","userid",,,"Service Account",,,,"Service Account: Networking - used by corp palo alto firewalls to perform user-id lookups","1/15/2022 4:24:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26753","sa-paloalto-userid"
"DGPSQLServiceUser",,,,"Service Account",,,,"Ran on QTFINSQL for sql service","10/27/2022 3:46:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26499","DGPSQLServiceUser"
"DGPeConnectService",,,,"Service Account",,,,"Econnect service on QTFINSQL and QTFINRDS","10/27/2022 3:46:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26500","DGPeConnectService"
"Greg","Harshenin","<EMAIL>",,,,,,,"10/27/2022 3:23:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12624","da-gharshenin"
"Malcolm","Kennedy",,,,,,,,"9/28/2020 11:52:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24632","da-mkennedy"
"Domain Admin","Nyel English","<EMAIL>",,,,,,,"10/27/2022 1:06:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,,"8/18/2022 6:10:50 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,,"1/13/2022 11:06:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,,"9/19/2022 8:52:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,,"3/2/2021 2:53:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15393","da-amehl"
"Domain Admin -","Andrew Stavert","<EMAIL>",,,,,,,"11/25/2021 5:49:46 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22643","da-astavert"
"Domain Admin -","Andrew McFadden","<EMAIL>",,,,,,,"2/7/2022 8:36:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22274","da-amcfadden"
"Domain Admin -","Kevin Kendall","<EMAIL>",,,,,,,"10/3/2022 11:17:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,,"9/16/2022 8:06:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24060","da-smay"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,,"10/28/2022 6:33:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,,"1/21/2020 3:05:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19009","da-nated"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,,"4/16/2021 8:46:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,,"10/28/2022 11:56:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,,"10/28/2022 10:31:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,,"10/25/2022 2:39:53 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,,"10/28/2022 10:52:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22187","da-sbradford"
"da-","mhoppe","<EMAIL>",,,,,,,"12/21/2021 1:48:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23206","da-mhoppe"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,,"8/23/2022 11:35:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25149","da-tdrescher"
"Domain Admin","Craig Hounsham","<EMAIL>",,,,,,,"3/22/2022 12:18:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24706","da-chounsham"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,,"10/28/2022 11:03:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24717","da-jbell"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,,"9/13/2021 12:09:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26188","da-mkandy"
"Arnold","Nzailu","<EMAIL>",,,,,,,"4/11/2022 11:24:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26610","da-anzailu"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,,"12/16/2020 11:48:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24838","da-balbrecht"
"Domain Admin -","Simon Ummard","<EMAIL>",,,,,,,"11/23/2021 11:18:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24911","da-summard"
"Jarrid","Pond",,,,,,,,"10/27/2022 11:00:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26768","da-jpond"
"Nirmol","Bajwa",,,,,,,,"11/25/2021 2:42:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26775","da-nbajwa"
"Jordan","Pinske",,,,,,,,"6/16/2022 2:14:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26776","da-jpinske"
"Tayo","Aruleba",,,,,,,,"10/21/2022 1:17:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26381","DA-TAruleba"
"Jeff","Fleming",,,,,,,,"10/27/2022 9:21:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26391","da-jfleming"
"Domain Admin","Dilcia Torres","<EMAIL>",,,,,,,"10/20/2022 3:21:47 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26392","da-dtorres"
"Domain Admin","Fred Xiao",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26411","da-fxiao"
"Paul","Farry",,,,,,,,"10/28/2022 8:19:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26431","da-pfarry"
"Chrisaine","Brown-Humphrey",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26873","da-cbrown-humphrey"
"Domain Admin - Sudeep","Mool",,,,,,,,"7/5/2022 11:19:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26892","da-smool"
"Domain Admin -","Kent Ellis",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26480","da-kellens"
"Domain Admin","Justin Germain",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26481","da-jgermain"
"Domain Admin -","Muhammad Ali","<EMAIL>",,,,,,,"5/16/2022 1:46:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26483","da-mali"
"Nathan","Taylor",,,,,,,,"10/18/2022 7:31:40 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26929","da-ntaylor"
"Test","Passwriteback","<EMAIL>",,,,,,"For Password Writeback and MDM testing Azure","9/15/2020 8:38:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24700","Testpw"
"Test","Phoneforward",,,,,,,"Service Account: Corp IT - Testing Teams Phone Forwarding",,"<EMAIL>","S-1-5-21-**********-**********-*********-26793","test.phoneforward"
"Dynamics MR","Service",,,"Service Account",,,,"Service Account: add-on to GP that provides/creates financial reports","10/27/2022 3:31:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20096","mrsvc"
"svc","Flexapp",,,,,,,"Service Account:Do not change - service account for Flexapp",,"<EMAIL>","S-1-5-21-**********-**********-*********-24884","svc-flexapp"
"SVC","Jenkins",,,,,,,"DO not change - Jenkins slave account for Flexapp","10/28/2022 3:48:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24885","svc-jenkins"
"CommVault","Backup Admin",,,,,,,,"10/25/2022 2:57:39 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26725","svc-commvault"
"MDI","sensor",,,,,,,"Service Account: Security - Microsoft Defender for Identity Sensor for QTDC01/02 and QTADFS2",,"<EMAIL>","S-1-5-21-**********-**********-*********-26767","svc-mdisensor"
"Splunk","Sync",,,,,,,"Service Account: Security - Used for Splunk Syncing",,"<EMAIL>","S-1-5-21-**********-**********-*********-26840","svc_splunksync"
"svc-sqlbackups",,,,,,,,,"10/28/2022 12:30:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26449","svc-sqlbackups"
"svc-vmbackups",,,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26454","svc-vmbackups"
"svc-mstrueup",,,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26456","svc-mstrueup"
