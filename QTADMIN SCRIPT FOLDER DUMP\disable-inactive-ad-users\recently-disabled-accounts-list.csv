"SamAccountName","Name"
"18king","QHR Toronto Office"
"accvm","Accounting Voicemail"
"ADMTUser1","ADMT User1"
"ADRmsSQL","ADRmsSQL"
"amanda.korecki","<PERSON>"
"amehlx","amehlx blah"
"angelo.mariano","<PERSON>"
"anothony.filipovich","Anothony Fi<PERSON>ovich"
"axis","AXIS"
"BDCService","BDC Service"
"carole.darcy","Carole D'Arcy"
"CLINICARE$","CLINICARE$"
"ContentAccess","ContentAccess"
"crgroup","CR Group"
"danielle.semple","<PERSON>"
"DevPPService","DevPPService"
"devtrainer","DevTrainer"
"EMRleads","EMRleads"
"FastSearch","FastSearch"
"FepSQLAdmin","FepSQLAdmin"
"FEPSQLRPT","FEPSQLRPT"
"hristo.kerezov","<PERSON><PERSON><PERSON>"
"IIS-DevQHRnet","IIS-DevQHRnet"
"IIS-DevQHRnet2","IIS-DevQHRnet2"
"ITUser","IT User"
"kaylee.barker","Kaylee Barker"
"LM5BRPC","LM5BRPC"
"lmkiosk","LM Kiosk"
"lmreception","LM Reception"
"ManagedMetadata","ManagedMetadata"
"matt.prices","Matt Prices"
"matti.kalijarvi","Matti Kalij?rvi"
"MichaelCopy","MichaelCopy"
"NEWeconnect","NEWeconnect"
"ops.jira","ops jira"
"patient.prep","Patient Prep"
"PowerPivot","PowerPivot"
"pythian-pforce","Patrick Force (Pythian)"
"qhrbackupsmtp","QHR backup smtp"
"qtialerts","qtialerts"
"QTSPSQL1","QTSPSQL1"
"QTSPTestSPSetup","QTSPTestSPSetup"
"QTSPTestSQLService","QTSPTestSQLService"
"QTSQL01-Reporting","QTSQL01-Reporting"
"raccess","Restricted Access"
"reception","QHR Reception"
"SecureStore","SecureStore"
"ServerFarm","Server Farm"
"sonicwall","sonicwall"
"SpiceWorksSA","SpiceWorksSA"
"SPSearch","SpSearch"
"SPSetup","SPSetup"
"SPUserProfile","SPUserProfile"
"SQL-QHRSQL03","SQL-QHRSQL03"
"SQL-qhrsql03SQL2K5","SQL-qhrsql03SQL2K5"
"SQL-QHRSQL03sql2k5v1","SQL-QHRSQL03sql2k5v1dm"
"SQL-QTSQL01","SQL-QTSQL01"
"SRV-OptOnline","SRV-OptOnline"
"srv-virtualBackup","srv-virtualBackup"
"srv-VSBackup","srv-VSBackup"
"SuperReader","SuperReader"
"SuperUser","SuperUser"
"test.jira","Test Jira"
"test.onprem","Test OnPrem"
"testuser2","test user2"
"testvpn","TestVPN"
"tfsservice","TFSSERVICE"
"TsInternetUser","TsInternetUser"
"VSAdmin","VSAdmin"
"wds","WDS"
