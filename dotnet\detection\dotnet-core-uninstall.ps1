# Region: Helper Functions

function Remove-DotNetCoreRuntime {
    param(
        [Parameter(Mandatory)]
        [string]
        $Version
    )

    $dotnetUninstallTool = "$env:ProgramFiles\dotnet\dotnet-core-uninstall.ps1"
    if (Test-Path $dotnetUninstallTool) {
        Write-Output "Removing .NET Core Runtime $Version..."
        & $dotnetUninstallTool --version $Version --sdk --runtime
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to remove .NET Core Runtime $Version"
            return $false
        }
    }
    return $true
}

# EndRegion

$uninstallFailed = $false
$dotnetRuntimesFound = $false

# Uninstall .NET Framework Runtimes (except 6.0 - 6.9)
$runtimes = Get-ChildItem 'HKLM:\SOFTWARE\Microsoft\NET Framework Setup\NDP' -Recurse |
    Where-Object { $_.PSChildName -match '^\d+\.\d+$' }

foreach ($runtime in $runtimes) {
    $dotnetRuntimesFound = $true
    $version = $runtime.PSChildName

    if ([version]$version -lt '6.0' -or [version]$version -ge '7.0') {
        Write-Output "Found .NET Framework Runtime (outside 6.0-6.9): $version"
        Write-Output "Uninstalling .NET Framework Runtime $version..."
        $uninstallString = $runtime.GetValue('InstallPath') + '\Setup\NDP\v$version\Setup.exe'
        Start-Process $uninstallString -ArgumentList '/uninstall /q' -Wait 
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to remove .NET Framework Runtime $version"
            $uninstallFailed = $true
        }
    }
    else {
        Write-Output "Found .NET Framework Runtime (within 6.0-6.9): $version (Preserving)"
    }
}

# Uninstall .NET Core Runtimes (except 6.0 - 6.9)
$installedCoreVersions = Get-ChildItem 'HKLM:\SOFTWARE\dotnet\Setup\InstalledVersions',
                        'HKLM:\SOFTWARE\WOW6432Node\dotnet\Setup\InstalledVersions\x64\sharedfx\Microsoft.WindowsDesktop.App' -Recurse |
    Where-Object { $_.PSChildName -match '^\d+\.\d+\.\d+$' }


foreach ($version in $installedCoreVersions) {
    $dotnetRuntimesFound = $true
    
    if ([version]$version -lt '6.0' -or [version]$version -ge '7.0') {
        Write-Output "Found .NET Core Runtime (outside 6.0-6.9): $version"
        if (-not (Remove-DotNetCoreRuntime $version)) {
            $uninstallFailed = $true
        }
    }
    else {
        Write-Output "Found .NET Core Runtime (within 6.0-6.9): $version (Preserving)"
    }
}

# Determine Exit Code
if (-not $dotnetRuntimesFound) {
    Write-Output "No .NET runtimes found. Exiting with success code 0."
    exit 0
}

if ($uninstallFailed) {
    Write-Error "One or more uninstalls failed. Exiting with error code 1."
    exit 1
} else {
    Write-Output "All unwanted .NET runtimes successfully removed. Exiting with success code 0."
    exit 0
}
