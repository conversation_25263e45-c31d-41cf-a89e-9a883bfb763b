﻿Connect-SPOService -Url https://qhrtech-admin.sharepoint.com
do{
$userName = Read-Host "Enter the email address of user you want to see 365 groups for"
$grouplist = Get-SPOUser -Site https://qhrtech.sharepoint.com -LoginName  $userName | select -ExpandProperty groups

Write-Host "`n----------"

$grouplist
$strQuit = Read-Host "`n Stop looking at user Sharepoint groups? (y/n)"
}
Until ($strQuit -eq "y")