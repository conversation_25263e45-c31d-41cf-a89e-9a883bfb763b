#
# Script to find AD users with specific job titles and export their details to a CSV.
#

# Step 1: Import the Active Directory module to ensure commands are available.
# On modern systems, this often happens automatically, but it's good practice to include it.

Import-Module ActiveDirectory
if (-not (Get-Module -Name ActiveDirectory)) {
    Write-Error "Active Directory module is not available. Please ensure you have the RSAT tools installed."
    exit
}
# Ensure the script is running with the necessary permissions to access Active Directory.

try {
    # Step 2: Define the job titles to search for. 
    # You can easily add or remove titles from this list.
    $jobTitles = @(
        "*Director*",
        "*President*",
        "*Executive*"
    )

    # Step 3: Build the filter string for the Get-ADUser command.
    # This creates a filter like: (Title -like "*Director*") -or (Title -like "*President*") -or ...
    $filter = ($jobTitles | ForEach-Object { "(Title -like '$_')" }) -join " -or "

    # Step 4: Define the path for the output file. 
    # This will save a file named "ExecutiveReport.csv" to the current user's desktop.
    # You can change this path if you'd like to save it elsewhere.
    $exportPath = "$env:USERPROFILE\Desktop\ExecutiveReport.csv"

    Write-Host "Searching for users with specified job titles..."

    # Step 5: Get the users from Active Directory.
    # -Filter: Finds users matching the job titles. The '*' is a wildcard for any characters.
    # -Properties: Specifies that we need the 'mail' and 'Title' attributes, as they are not returned by default.
    $users = Get-ADUser -Filter $filter -Properties mail, Title | Select-Object Name, mail, Title

    # Step 6: Export the collected user data to a CSV file.
    # -NoTypeInformation: Removes the "#TYPE" header line from the CSV, making it cleaner for Excel.
    # -Encoding UTF8: Ensures proper handling of any special characters in names or titles.
    $users | Export-Csv -Path $exportPath -NoTypeInformation -Encoding UTF8

    Write-Host "Success! Report has been generated at: $exportPath"
    Write-Host "Found $($users.Count) matching users."
    if ($users.Count -eq 0) {
        Write-Host "No users found with the specified job titles."
    }
    
}
catch {
    # This will catch any errors, like if the AD module isn't found.
    Write-Error "An error occurred: $_"
    Write-Error "Please ensure you are running this on a Domain Controller or a machine with RSAT/Active Directory tools installed."
}