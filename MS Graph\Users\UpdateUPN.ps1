<#
.SYNOPSIS
Updates the User Principal Name (UPN) domain suffix for a specific user in Microsoft Entra ID.

.DESCRIPTION
This script prompts for a user's current UPN. It checks if the UPN ends with '@qhrtech.com' (lowercase).
If it does, it updates the UPN to use the correctly capitalized domain suffix '@QHRtech.com'.
The script requires the Microsoft.Graph PowerShell module and appropriate permissions (e.g., User.ReadWrite.All).
It will repeatedly ask if you want to update another user until you choose to exit.

.NOTES
Version:        1.0
Author:         <PERSON><PERSON> Sheppard
Creation Date:  2025-04-29
Prerequisites:  Microsoft.Graph module installed and connected with necessary permissions.
                The target domain '@QHRtech.com' MUST be verified in the Entra ID tenant.

.EXAMPLE
.\Update-UserUPNCasing.ps1
#>

#Requires -Modules Microsoft.Graph

# --- Configuration ---
$lowerCaseDomainSuffix = "@qhrtech.com"
$correctCaseDomainSuffix = "@QHRtech.com" # The desired final suffix

# --- Script Start ---

Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Yellow

# Check if already connected, otherwise connect
$connection = Get-MgContext
if ($null -eq $connection) {
    # Define required scopes
    $scopes = @("User.ReadWrite.All") # Ensure you have these permissions granted

    Write-Host "No existing Microsoft Graph connection found. Attempting to connect."
    Write-Host "Please authenticate in the browser window/prompt."
    Connect-MgGraph -Scopes $scopes
} else {
    Write-Host "Already connected to Microsoft Graph as:" -ForegroundColor Green
    Write-Host "  Account: $($connection.Account)"
    Write-Host "  Tenant: $($connection.TenantId)"
    # Optional: Check if required scopes are present in the existing token
    # $connection.Scopes -contains "User.ReadWrite.All"
}

# Verify connection after attempt
$connection = Get-MgContext
if ($null -eq $connection) {
    Write-Error "Failed to connect to Microsoft Graph. Please ensure the module is installed, you have internet access, and necessary permissions. Script cannot continue."
    Exit
} else {
     Write-Host "Successfully connected to Microsoft Graph." -ForegroundColor Green
}


Write-Host "`n--- UPN Update Script ---" -ForegroundColor Cyan
Write-Host "This script will update UPNs ending in '$lowerCaseDomainSuffix' to '$correctCaseDomainSuffix'."
Write-Warning "Ensure the domain '$correctCaseDomainSuffix' is verified in your Entra ID tenant!"

do {
    # --- Get User Input ---
    $currentUserUPN = Read-Host -Prompt "Enter the current UPN of the user to update (e.g., <EMAIL>)"

    if ([string]::IsNullOrWhiteSpace($currentUserUPN)) {
        Write-Warning "No UPN entered. Please try again."
        continue # Skip to the next iteration of the loop
    }

    # --- Process User ---
    Write-Host "Processing user: $currentUserUPN"

    try {
        # Get the user object - Select only necessary properties
        $user = Get-MgUser -UserId $currentUserUPN -Property Id, UserPrincipalName -ErrorAction Stop

        if ($null -eq $user) {
             Write-Warning "User '$currentUserUPN' not found in Entra ID."
        } else {
            $currentUPN = $user.UserPrincipalName
            Write-Host "Found user. Current UPN is: $currentUPN"

            # Check if the UPN ends with the lowercase suffix
            if ($currentUPN.EndsWith($lowerCaseDomainSuffix, [System.StringComparison]::OrdinalIgnoreCase)) {
                # Check if it's already the correct case (prevent unnecessary updates)
                 if ($currentUPN.EndsWith($correctCaseDomainSuffix, [System.StringComparison]::Ordinal)) {
                     Write-Host "User UPN '$currentUPN' already has the correct suffix '$correctCaseDomainSuffix'. No update needed." -ForegroundColor Green
                 } else {
                    # Construct the new UPN
                    $upnParts = $currentUPN.Split('@')
                    $userNamePart = $upnParts[0]
                    $newUPN = "$userNamePart$correctCaseDomainSuffix"

                    Write-Host "UPN needs update. Changing '$currentUPN' to '$newUPN'..." -ForegroundColor Yellow

                    # Update the user's UPN
                    Update-MgUser -UserId $user.Id -UserPrincipalName $newUPN -ErrorAction Stop
                    Write-Host "Successfully updated UPN for '$currentUserUPN' to '$newUPN'." -ForegroundColor Green
                 }
            } else {
                Write-Warning "User UPN '$currentUPN' does not end with '$lowerCaseDomainSuffix'. No update performed."
            }
        }
    } catch {
        Write-Error "An error occurred while processing user '$currentUserUPN':"
        Write-Error $_.Exception.Message
        # You might want to add more specific error handling here if needed
    }

    # --- Ask to Continue ---
    Write-Host # Add a blank line for readability
    $continueInput = Read-Host -Prompt "Do you want to update another user? (y/n)"

} while ($continueInput -eq 'y' -or $continueInput -eq 'yes')

Write-Host "Script finished."

# Optional: Disconnect from Microsoft Graph
Write-Host "Disconnecting from Microsoft Graph..."
Disconnect-MgGraph