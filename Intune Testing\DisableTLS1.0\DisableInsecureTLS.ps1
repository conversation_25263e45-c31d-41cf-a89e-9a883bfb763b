# Function to create and set registry values safely
function Set-RegistryValue {
    param (
        [string]$Path,
        [string]$Name,
        [int]$Value
    )
    try {
        if (!(Test-Path $Path)) {
            New-Item -Path $Path -Force | Out-Null
        }
        New-ItemProperty -Path $Path -Name $Name -Value $Value -PropertyType DWORD -Force | Out-Null
    } catch {
        Exit 1  # Fail if registry update fails
    }
}

# Function to ensure SCHANNEL protocol settings exist and are correct
function Configure-TLS {
    param (
        [string]$Protocol,
        [int]$EnabledValue,
        [int]$DisabledByDefaultValue
    )

    $BasePath = "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\$Protocol"

    # Ensure Client and Server subkeys exist
    if (-not (Test-Path "$BasePath\Client")) {
        New-Item -Path "$BasePath\Client" -Force | Out-Null
    }
    if (-not (Test-Path "$BasePath\Server")) {
        New-Item -Path "$BasePath\Server" -Force | Out-Null
    }

    # Set values for Client and Server
    Set-RegistryValue -Path "$BasePath\Client" -Name "Enabled" -Value $EnabledValue
    Set-RegistryValue -Path "$BasePath\Client" -Name "DisabledByDefault" -Value $DisabledByDefaultValue
    Set-RegistryValue -Path "$BasePath\Server" -Name "Enabled" -Value $EnabledValue
    Set-RegistryValue -Path "$BasePath\Server" -Name "DisabledByDefault" -Value $DisabledByDefaultValue
}

# Disable TLS 1.0 and TLS 1.1 (Fail Safe)
Configure-TLS -Protocol "TLS 1.0" -EnabledValue 0 -DisabledByDefaultValue 1
Configure-TLS -Protocol "TLS 1.1" -EnabledValue 0 -DisabledByDefaultValue 1

# Enable TLS 1.2 and TLS 1.3 (Fail Safe)
Configure-TLS -Protocol "TLS 1.2" -EnabledValue 1 -DisabledByDefaultValue 0
Configure-TLS -Protocol "TLS 1.3" -EnabledValue 1 -DisabledByDefaultValue 0

# Force SecureProtocols in Internet Options (User & System-Wide)
$SecureProtocolsValue = 0x00020000 -bor 0x00800000  # TLS 1.2 + TLS 1.3

Set-RegistryValue -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols" -Value $SecureProtocolsValue
Set-RegistryValue -Path "HKLM:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols" -Value $SecureProtocolsValue
Set-RegistryValue -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols" -Value $SecureProtocolsValue

# Additional Enforcement (Prevent Modification)
Set-RegistryValue -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols" -Value $SecureProtocolsValue

# Enable TLS 1.3 (Fail Safe for HTTP3 on Windows 11 & Server 2022)
Set-RegistryValue -Path "HKLM:\SYSTEM\CurrentControlSet\Services\HTTP\Parameters" -Name "EnableHttp3" -Value 1

# Enforce TLS settings in .NET Framework (for older applications)
Set-RegistryValue -Path "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" -Name "SystemDefaultTlsVersions" -Value 1
Set-RegistryValue -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\.NETFramework\v4.0.30319" -Name "SystemDefaultTlsVersions" -Value 1
Set-RegistryValue -Path "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" -Name "SchUseStrongCrypto" -Value 1
Set-RegistryValue -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\.NETFramework\v4.0.30319" -Name "SchUseStrongCrypto" -Value 1

# Force Group Policy Update (Standard)
gpupdate /force | Out-Null

# Force Policy Refresh Using SecEdit
secedit /configure /cfg %windir%\security\templates\policies.inf /db secedit.sdb /quiet


Exit 0  # Success
