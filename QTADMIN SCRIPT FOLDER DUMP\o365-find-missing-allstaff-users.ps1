﻿# This script is run daily at 5pm as a task in QTADMIN1
#Finds users that are not apart of at leat one "All Staff" group


#only works if running as the qtadmin1service account
#$LiveCred = Import-CliXml -Path "C:\Scripts\Credentials\office365scripts-qtadmin1service.cred"

#Use below instead if running manually but remember to comment it back after 
#$LiveCred = Get-Credential <EMAIL>

    Clear
    Write-Host "Connecting to Exchange Online..."

#    $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
#   Import-PSSession $Session -AllowClobber
    Connect-ExchangeOnline
#    Connect-MsolService



#Get 365 Active Licensed Users
#$O365MailboxObjects = Get-MsolUser -All | Where-Object {$_.IsLicensed -eq $true -and $_.BlockCredential -eq $false}
#$O365MailboxUsers = $O365MailboxObjects.DisplayName

#Get all "All Staff*" Group Members
[System.Collections.ArrayList]$AllStaffMembers=@()

(Get-DistributionGroup -Identity 'All Staff*').identity | ForEach-Object{
    $DistributionGroupName = $_
    Get-DistributionGroupMember -Identity $_ | ForEach-Object{
        [PSCustomObject]@{
            DistributionGroup = $DistributionGroupName
            MemberName = $_.Name

            #Other recipientproperties here
        }
        $AllStaffMembers.Add($_.Name)
    }
}
#Onprem users and users to manually skip due to name issues that are already on all staff
#$skipusers = @("Domain Admin - Robert Armstrong","Domain Admin - Mark McLean","Splunk Alerts","Admin MFA","IT365scripts","Domain Admin - Devin Nate","UnifiedMessage365","QHR Azure Dev Account","Christopher Roseberry","EMR Development Inbox","Domain Admin - Nyel English","Domain Admin - Allen McCarty","QHR Azure Prod Account","EMR Support","Admin @ QHR Tech","IT & DC Helpdesk","opsjira","Roxanne Geiger","Terri-Lyn Kardel","Tony Elumir","Jerry Diener","EMR Development","QHR Accounting","HS After Hours Support","Allen Jonoke","NPUMAdmin","Service Account - Asigra","QHR backup smtp","QHR Reception","EMR Development After Hours","Marlene Sullivan","Nazia Tarikh","Saad Hussain","Stella Cruz","Emmanuel Izuorgu","Aaron Blair","Natalia Ros","Nancy Chapeskie","Anothony Filipovich","Lydia Chan","Shelly Quick","Rafay Siddiqui","Oleg Serebryany","Cirrus Cloud","Terry Wagner","George Papadogolus","Nadeen Aljamal","Deanna Gourley","Greg Harpell","qtmitelccm","Josephine Kwong","Forge Architect","Room Kelowna 1st Floor - Forge Architect Room")
[System.Collections.ArrayList]$userList = @()
Get-aduser -filter {enabled -eq $true -and Title -ne "Service Account" -and Name -notlike "*Domain*" -and sAMAccountName -notlike '*test*' -and sAMAccountName -notlike '*svc*'} |Where-Object{$_.DistinguishedName -notlike '*OU=External*'} | ForEach-Object{
        
        [PSCustomObject]@{
            MemberName = $_.Name

            #Other recipientproperties here
            }
        $userList.Add($_.Name)
    }


write-host
write-host


$results = $userList| ?{$AllStaffMembers -notcontains $_}
#$results += $skipusers | ?{$AllStaffMembers -notcontains $_}



if ($results) {
write-host "VV Users that are not in all staff VV" -ForegroundColor Red
$results
write-host    "! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! Missing users in all staff ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! !" -ForegroundColor Red
#    Send-MailMessage -To <EMAIL> -From “<EMAIL>” -SmtpServer mail.qhrtech.com -Subject “Powershell Alert - Fix All Staff Users Missing!” -body "`n $results `n`n The employees listed above are missing from the apporiate all staff email group in Office 365. `n Please add to the most detailed subgroup they can be in. EX. 'All Staff Kelowna LM5-300' `n`n NOTE: This script runs on QTADMIN1 daily at 5pm as a scheduled task (o365-find-missing-allstaff-users.ps1). `n Users in the 'QHR Tech - Internal IT Alerts' group receive this email"
    }

else {"No all staff users missing"}
write-host
write-host
write-host
write-host
Disconnect-ExchangeOnline