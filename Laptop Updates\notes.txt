help me with a powershell script, it is very long you may have to do multiple parts to avoid crashing or having a network error:

It seems like you didn't use a bunch of the functions provided, such as the following functions (Maybe some of them do the same things?):



Get-DellDriverPackXML



Get-DCUVersion



Set-DCUSettings



Get-DellBIOSUpdates, check all of them for code references throughout the entire script as it seems to be missing a lot.







Also, I want the following settings set within Dell Command Update itself:



Update settings > Check for Updates > Automatic Updates



Update Settings > When updates are found > Download and install updates (notify after complete) > NO DEFERRAL



Advanced Driver Restore > Enable > Download driver library from Dell Support Site (Recommended)



BIOS > Prompt the user running the script to enter a BIOS password if one is not already set within Dell command update



BIOS > Suspend Bitlocker > Automatically Suspend Bitlocker







Also, I noticed these from PSScriptAnalyzer:

The variable 'dellRebootNeeded' is assigned but never used. Line 667

The variable 'dellRebootNeeded' is assigned but never used. Line 1163





Print the entire modified script so I can test it again.



Here is the script to start with:

<#

.SYNOPSIS

    Automates Dell Command | Update (DCU) processes followed by Windows Updates.

    Integrates functions inspired by <PERSON> (GARYTOWN.COM) for DCU management.



.DESCRIPTION

    This script performs a comprehensive system update, prioritizing Dell updates first.

    It includes the following steps:

    1. Checks for Administrator privileges.

    2. Sets up a central logging directory (Default: C:\temp\LaptopUpdateLogs).

    3. Starts a transcript log of all console output.

    4. Defines a library of functions for interacting with Dell Command | Update (based on Garytown script).

    5. Checks if the system is a Dell machine. Exits Dell portion if not.

    6. Checks if Dell Command | Update is installed using Get-DCUInstallDetails.

    7. If DCU is not installed, attempts to automatically download and install the correct version using Install-DCU.

    8. If DCU is present (or successfully installed), it invokes DCU to scan for and apply ALL available Dell updates (BIOS, Drivers, Firmware, Apps) using Invoke-DCU.

       - BitLocker is set to be suspended automatically by DCU if needed (-autoSuspendBitLocker Enable).

       - DCU is instructed not to force an immediate reboot (-reboot Disable); reboot is handled centrally later.

    9. Checks the DCU exit code to determine success and if a reboot is required.

    10. Proceeds to the Windows Update phase.

    11. Ensures the PSWindowsUpdate PowerShell module is installed and imported.

    12. Scans for available Windows Updates (excluding drivers).

    13. Downloads and installs approved Windows Updates, ignoring immediate reboot requests from the module.

    14. Checks if Windows Updates require a reboot.

    15. Determines if a reboot is needed based on Dell OR Windows update results.

    16. If a reboot is required, prompts the user or reboots automatically based on the -AutoRebootIfNeeded parameter (after a 30-second delay).

    17. Stops the transcript log.



.PARAMETER LogDirectory

    Specifies the root directory where all log files (Transcript, DCU logs, PSWindowsUpdate logs) should be stored.

    Defaults to "C:\temp\LaptopUpdateLogs".



.PARAMETER AutoRebootIfNeeded

    If specified, the script will automatically reboot the computer after a short delay if any update process (Dell or Windows) requires it.

    If not specified (default), the user will be notified that a reboot is needed but it will not happen automatically via this script.



.NOTES

    Author:        Slader Sheppard (Integrating user request with Garytown functions)

    Date:          2025-04-09 (Based on user provided script)

    Requires:      PowerShell 5.1+, Administrator Privileges, Internet Connection.

    Dependencies:  Relies on functions adapted from GARYTOWN.COM (unofficial, use with testing).

                   PSWindowsUpdate PowerShell Module (will attempt auto-install).

                   Dell Command | Update (will attempt auto-install if missing on a Dell system).

    Disclaimer:    Based on publicly available information and scripts. THIS IS NOT AN OFFICIAL DELL SCRIPT. TEST THOROUGHLY in a lab environment before deploying. Use at your own risk.



.EXAMPLE

    .\Update-DellAndWindows.ps1

    Runs the script, performing Dell updates then Windows updates. Logs to C:\temp\LaptopUpdateLogs. Notifies if reboot is needed.



.EXAMPLE

    .\Update-DellAndWindows.ps1 -AutoRebootIfNeeded

    Runs the script, performing Dell updates then Windows updates. Logs to C:\temp\LaptopUpdateLogs. Automatically reboots if required after a 30-second delay.



.EXAMPLE

    .\Update-DellAndWindows.ps1 -LogDirectory "C:\SystemUpdateLogs" -AutoRebootIfNeeded

    Runs the script, performing Dell updates then Windows updates. Logs to C:\SystemUpdateLogs. Automatically reboots if required.

#>

param(

    [Parameter(Mandatory = $false)]

    [string]$LogDirectory = "C:\temp\LaptopUpdateLogs",



    [Parameter(Mandatory = $false)]

    [switch]$AutoRebootIfNeeded

)



#==============================================================================

# Script Setup and Pre-checks

#==============================================================================



# Check for Administrator Privileges

Write-Host "Checking for Administrator privileges..." -ForegroundColor Cyan

if (-not ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {

    Write-Error "This script must be run as Administrator."

    # Attempt to relaunch as admin - this might prompt UAC

    try {

        Start-Process powershell.exe -ArgumentList ("-NoProfile -ExecutionPolicy Bypass -File `"{0}`" {1}" -f $PSCommandPath, $PSBoundParameters.Keys.ForEach({'-{0} {1}' -f $_, $PSBoundParameters[$_]})) -Verb RunAs -ErrorAction Stop

    } catch {

        Write-Error "Failed to relaunch as Administrator. Please run the script manually as Administrator."

    }

    Exit # Exit the current non-admin instance

}

Write-Host "Administrator privileges confirmed." -ForegroundColor Green



# Ensure the Log Directory exists

if (-not (Test-Path -Path $LogDirectory -PathType Container)) {

    Write-Host "Creating log directory: $LogDirectory" -ForegroundColor Yellow

    try {

        New-Item -Path $LogDirectory -ItemType Directory -Force -ErrorAction Stop | Out-Null

    } catch {

        Write-Error "Failed to create log directory: $LogDirectory. Error: $($_.Exception.Message)"

        Exit 1

    }

}



# Define other paths based on LogDirectory

$Global:MainLogPath = $LogDirectory # Make accessible to functions if needed via $Global scope

$Global:DellTempPath = Join-Path -Path $LogDirectory -ChildPath "DellTemp"

$Global:DellLogPath = $LogDirectory # DCU logs go directly into the main log path

$Global:WinLogFile = Join-Path -Path $LogDirectory -ChildPath "PSWindowsUpdate_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

$transcriptLog = Join-Path -Path $LogDirectory -ChildPath "UpdateScript_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"



# Create Dell Temp Directory if it doesn't exist

if (-not (Test-Path -Path $Global:DellTempPath -PathType Container)) {

    Write-Host "Creating Dell temporary directory: $($Global:DellTempPath)" -ForegroundColor Yellow

    try {

        New-Item -Path $Global:DellTempPath -ItemType Directory -Force -ErrorAction Stop | Out-Null

    } catch {

         Write-Error "Failed to create Dell temp directory: $($Global:DellTempPath). Error: $($_.Exception.Message)"

         # Decide if critical - for now, continue but warn Dell functions might fail

         Write-Warning "Dell updates might fail due to temp directory creation error."

    }

}



# Start Transcript Logging

try {

    # Ensure transcript stops if script exits unexpectedly

    $Global:IsTranscriptActive = $true

    Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action { if ($Global:IsTranscriptActive) { Stop-Transcript } } | Out-Null

    Start-Transcript -Path $transcriptLog -Append -Force -ErrorAction Stop

} catch {

    Write-Error "Failed to start transcript logging to $transcriptLog. Error: $($_.Exception.Message)"

    Write-Warning "Transcript logging failed. Script execution will continue without full console log."

    $Global:IsTranscriptActive = $false

}



Write-Host "Script started. Overall log transcript: $transcriptLog" -ForegroundColor Green

Write-Host "Dell temporary files path: $($Global:DellTempPath)" -ForegroundColor Green

Write-Host "Log files output path: $($Global:DellLogPath)" -ForegroundColor Green



# Initialize Reboot Required Flags

$dellRebootNeeded = $false

$windowsRebootNeeded = $false



#==============================================================================

# DELL COMMAND UPDATE FUNCTIONS (Adapted from Garytown.com)

#==============================================================================

#region Dell Functions



# --- Functions based on Gary Blok's script (GARYTOWN.COM) ---

# --- Modifications made to use $Global paths and improve error handling/logging ---



$ScriptVersion = '25.2.20.4_Integrated_v2' # Indicate modification

Write-Host "Loading Dell Command Update Functions - Version $ScriptVersion" -ForegroundColor Yellow



# Modified Get-DellSupportedModels to use global paths

function Get-DellSupportedModels {

    [CmdletBinding()]

    param() # Removed unused ProxyServer parameter for simplicity



    $CabPathIndex = Join-Path -Path $Global:DellTempPath -ChildPath "CatalogIndexPC.cab" # Use Global Path

    $DellCabExtractPath = Join-Path -Path $Global:DellTempPath -ChildPath "DellCabExtract_SupportedModels" # Unique extraction path



    # Pull down Dell XML CAB used in Dell Command Update ,extract and Load

    if (!(Test-Path $DellCabExtractPath)){

        try { New-Item -Path $DellCabExtractPath -ItemType Directory -Force -ErrorAction Stop | Out-Null }

        catch { Write-Error "Failed to create extract path $DellCabExtractPath. Error: $($_.Exception.Message)"; return $null }

    }

    Write-Host "Downloading Dell Supported Models Catalog Index..." -ForegroundColor Yellow

    try {

        Invoke-WebRequest -Uri "https://downloads.dell.com/catalog/CatalogIndexPC.cab" -OutFile $CabPathIndex -UseBasicParsing -ErrorAction Stop

    } catch {

        Write-Error "Failed to download CatalogIndexPC.cab. Error: $($_.Exception.Message)"

        return $null

    }



    $xmlExtractPath = Join-Path -Path $DellCabExtractPath -ChildPath "CatalogIndexPC.xml"

    If(Test-Path $xmlExtractPath){Remove-Item -Path $xmlExtractPath -Force}

    # Clean up old extraction dir content before expanding

    if (Test-Path $DellCabExtractPath){

        try { Get-ChildItem -Path $DellCabExtractPath -Recurse | Remove-Item -Force -Recurse -ErrorAction Stop }

        catch { Write-Warning "Could not fully clean extract path $DellCabExtractPath before expansion. Error: $($_.Exception.Message)" }

    }



    Write-Host "Expanding the Cab File..." -ForegroundColor Yellow

    $xmlFinalPath = Join-Path -Path $DellCabExtractPath -ChildPath "CatalogIndexPC.xml" # Define expected XML path

    try {

        Expand-Archive -Path $CabPathIndex -DestinationPath $DellCabExtractPath -Force -ErrorAction Stop

    } catch {

        Write-Warning "Expand-Archive failed. Error: $($_.Exception.Message). Attempting expand.exe fallback..."

        try {

            # Expand.exe needs the output path to be just the filename if outputting a single file

            $expandOutputTarget = $xmlFinalPath # Use the full path here for expand.exe

            cmd.exe /c "expand `"$CabPathIndex`" `"$expandOutputTarget`""

             if (!(Test-Path $xmlFinalPath)) { throw "expand.exe command completed, but $xmlFinalPath not found."}

             Write-Host "expand.exe fallback succeeded." -ForegroundColor Green

        } catch {

             Write-Error "Failed to expand $CabPathIndex using expand.exe too. Error: $($_.Exception.Message)"

             return $null

        }

    }



    # Ensure the XML file actually exists after expansion attempt

    if (!(Test-Path $xmlFinalPath)) {

         Write-Error "$xmlFinalPath not found after attempting expansion in $DellCabExtractPath."

         # Check if maybe a different XML filename was extracted?

         $otherXml = Get-ChildItem -Path $DellCabExtractPath -Filter "*.xml" | Select-Object -First 1

         if ($otherXml) { Write-Warning "Found $($otherXml.Name) instead. Check catalog structure." }

         return $null

    }



    Write-Host "Loading Dell Catalog XML ($xmlFinalPath)... can take awhile" -ForegroundColor Yellow

    try {

        [xml]$XMLIndex = Get-Content -Path $xmlFinalPath -ErrorAction Stop

    } catch {

        Write-Error "Failed to load XML content from $xmlFinalPath. Error: $($_.Exception.Message)"

        return $null

    }



    $SupportedModels = $XMLIndex.ManifestIndex.GroupManifest

    if ($null -eq $SupportedModels) {

        Write-Warning "No 'GroupManifest' found in the catalog XML. Structure might have changed."

        return @() # Return empty array

    }

    $SupportedModelsObject = @()

    foreach ($SupportedModel in $SupportedModels){

        try { # Add try/catch for robustness against malformed XML nodes

            $systemID = $SupportedModel.SupportedSystems.Brand.Model.systemID

            $modelName = $SupportedModel.SupportedSystems.Brand.Model.Display.'#cdata-section'

            $urlPath = $SupportedModel.ManifestInformation.path

            $versionDate = $SupportedModel.ManifestInformation.version



            # Basic validation

            if ([string]::IsNullOrWhiteSpace($systemID) -or [string]::IsNullOrWhiteSpace($modelName) -or [string]::IsNullOrWhiteSpace($urlPath)) {

                Write-Warning "Skipping model entry due to missing critical data (SystemID, Model, or Path)."

                continue

            }



            $SPInventory = New-Object -TypeName PSObject

            $SPInventory | Add-Member -MemberType NoteProperty -Name "SystemID" -Value $systemID -Force

            $SPInventory | Add-Member -MemberType NoteProperty -Name "Model" -Value $modelName -Force

            $SPInventory | Add-Member -MemberType NoteProperty -Name "URL" -Value $urlPath -Force

            $SPInventory | Add-Member -MemberType NoteProperty -Name "Date" -Value $versionDate -Force # Keep original version string as Date field might be ambiguous

            $SupportedModelsObject += $SPInventory

        } catch {

            Write-Warning "Skipping a model entry due to parsing error: $($_.Exception.Message)"

        }

    }

    Write-Host "Finished processing $($SupportedModelsObject.Count) supported models." -ForegroundColor Green

    return $SupportedModelsObject

}



# Modified Get-DellDriverPackXML to use global paths

function Get-DellDriverPackXML {

    [CmdletBinding()]

    param() # Removed unused ProxyServer parameter



    $CabPathIndex = Join-Path -Path $Global:DellTempPath -ChildPath "DriverPackCatalog.cab" # Use Global Path

    $DellCabExtractPath = Join-Path -Path $Global:DellTempPath -ChildPath "DellCabExtract_DriverPack" # Unique extraction path



    if (!(Test-Path $DellCabExtractPath)){

        try { New-Item -Path $DellCabExtractPath -ItemType Directory -Force -ErrorAction Stop | Out-Null }

        catch { Write-Error "Failed to create extract path $DellCabExtractPath. Error: $($_.Exception.Message)"; return $null }

    }

    Write-Host "Downloading Dell Driver Pack Catalog..." -ForegroundColor Yellow

    try {

        Invoke-WebRequest -Uri "https://downloads.dell.com/catalog/DriverPackCatalog.cab" -OutFile $CabPathIndex -UseBasicParsing -ErrorAction Stop

    } catch {

        Write-Error "Failed to download DriverPackCatalog.cab. Error: $($_.Exception.Message)"

        return $null

    }



    $xmlExtractPath = Join-Path -Path $DellCabExtractPath -ChildPath "DriverPackCatalog.xml"

    If(Test-Path $xmlExtractPath){Remove-Item -Path $xmlExtractPath -Force}

     # Clean up old extraction dir content before expanding

    if (Test-Path $DellCabExtractPath){

        try { Get-ChildItem -Path $DellCabExtractPath -Recurse | Remove-Item -Force -Recurse -ErrorAction Stop }

        catch { Write-Warning "Could not fully clean extract path $DellCabExtractPath before expansion. Error: $($_.Exception.Message)" }

    }



    Write-Host "Expanding the Driver Pack Cab File..." -ForegroundColor Yellow

    $xmlFinalPath = Join-Path -Path $DellCabExtractPath -ChildPath "DriverPackCatalog.xml" # Define expected path

     try {

        Expand-Archive -Path $CabPathIndex -DestinationPath $DellCabExtractPath -Force -ErrorAction Stop

    } catch {

        Write-Warning "Expand-Archive failed. Error: $($_.Exception.Message). Attempting expand.exe fallback..."

        try {

            $expandOutputTarget = $xmlFinalPath

            cmd.exe /c "expand `"$CabPathIndex`" `"$expandOutputTarget`""

             if (!(Test-Path $xmlFinalPath)) { throw "expand.exe command completed, but $xmlFinalPath not found."}

             Write-Host "expand.exe fallback succeeded." -ForegroundColor Green

        } catch {

             Write-Error "Failed to expand $CabPathIndex using expand.exe too. Error: $($_.Exception.Message)"

             return $null

        }

    }



     if (!(Test-Path $xmlFinalPath)) {

         Write-Error "$xmlFinalPath not found after attempting expansion in $DellCabExtractPath."

          $otherXml = Get-ChildItem -Path $DellCabExtractPath -Filter "*.xml" | Select-Object -First 1

         if ($otherXml) { Write-Warning "Found $($otherXml.Name) instead. Check catalog structure." }

         return $null

     }



    Write-Host "Loading Dell Driver Pack Catalog XML ($xmlFinalPath)..." -ForegroundColor Yellow

    try {

        [xml]$XMLIndex = Get-Content $xmlFinalPath -ErrorAction Stop

    } catch {

        Write-Error "Failed to load XML content from $xmlFinalPath. Error: $($_.Exception.Message)"

        return $null

    }

    Write-Host "Finished processing driver pack catalog." -ForegroundColor Green

    return $XMLIndex

}





Function Get-DCUVersion {

    # Simplified version using Get-DCUInstallDetails

    $details = Get-DCUInstallDetails

    if ($null -ne $details) {

        return $details.Version

    } else {

        return $null # Return null if not found

    }

}



Function Get-DCUInstallDetails {

    #Declare Variables for Universal app if RegKey AppCode is Universal or if Regkey AppCode is Classic and declares their variables otherwise reports not installed

    $settingsPath = "HKLM:\SOFTWARE\DELL\UpdateService\Clients\CommandUpdate\Preferences\Settings"

    $appCode = $null

    $Version = $null

    $DCUPath = $null

    $AppType = $null



    try {

        $regKey = Get-Item -Path $settingsPath -ErrorAction SilentlyContinue

        if ($null -ne $regKey) {

            $appCode = $regKey.GetValue("AppCode", $null)

            $Version = $regKey.GetValue("ProductVersion", $null)

        }

    } catch {

        Write-Warning "Error accessing registry key $settingsPath{}: $($_.Exception.Message)"

    }



    If($appCode -eq "Universal"){

        $AppType = "Universal"

        $DCUPath = 'C:\Program Files\Dell\CommandUpdate\'

    }

    ElseIf($appCode -eq "Classic"){

        $AppType = "Classic"

        $DCUPath = 'C:\Program Files (x86)\Dell\CommandUpdate\'

    }



    # Check if DCUPath exists physically and contains dcu-cli.exe

    $cliPath = $null

    if ($DCUPath) {

        $cliPath = Join-Path -Path $DCUPath -ChildPath "dcu-cli.exe"

        if (!(Test-Path $cliPath -PathType Leaf)){

             Write-Warning "DCU registry entry found ($AppType v$Version) but dcu-cli.exe not present at $cliPath. Installation might be corrupt or incomplete."

             return $null # Indicate not properly installed/found

        }

    }



    if ($Version -and $AppType -and $DCUPath -and $cliPath){

        $DCU = [PSCustomObject]@{

            Version = $Version

            AppType = $AppType

            DCUPath = $DCUPath

            CliPath = $cliPath

        }

         return $DCU

    }

    Else{

        # If any component is missing, consider it not installed or details incomplete

         return $null

    }

}



Function Get-DCUExitInfo {

    [CmdletBinding()]

    param(

        [Parameter(Mandatory=$true)]

        [int]$DCUExit

    )

    # Source: Dell Command | Update Version 4.x CLI Reference Guide (approx)

    $DCUExitInfo = @{

        # Generic application return codes

        0    = @{Description = "Success"; Resolution = "The operation completed successfully."}

        1    = @{Description = "Error: Reboot required by DUP"; Resolution = "A Dell Update Package (DUP) run by DCU required a reboot. This code often comes from the DUP itself, not DCU CLI directly."} # Clarified source

        2    = @{Description = "Success: Reboot Required"; Resolution = "The ApplyUpdates operation completed, but requires a system reboot."} # Common for ApplyUpdates

        3    = @{Description = "Error: Not a Dell system"; Resolution = "Dell Command | Update can only be run on Dell systems."}

        4    = @{Description = "Error: Not running as Administrator"; Resolution = "Invoke the Dell Command | Update CLI with administrative privileges"}

        5    = @{Description = "Error: Reboot pending"; Resolution = "A reboot was pending from a previous operation. Reboot the system to complete the operation."}

        6    = @{Description = "Error: Another instance running"; Resolution = "Close any running instance of Dell Command | Update UI or CLI and retry the operation."}

        7    = @{Description = "Error: Invalid command line parameters"; Resolution = "Review the command line syntax and parameters."}

        # Scan Specific

        500  = @{Description = "Scan completed: No updates found"; Resolution = "The system is up to date or no updates were found for the provided filters. Modify the filters and rerun the commands if needed."}

        501  = @{Description = "Scan Error: Scan failed"; Resolution = "An error occurred during the scan operation. Check logs for details."}

        502  = @{Description = "Scan Error: Catalog file not found"; Resolution = "Ensure the catalog file exists at the specified location or network connectivity is available for default catalog."}

        503  = @{Description = "Scan Error: Invalid catalog format"; Resolution = "The catalog file is corrupt or not a valid Dell catalog."}

        504  = @{Description = "Scan Error: Inventory collector failed"; Resolution = "An internal error occurred while gathering system inventory. Check DCU logs."}

        # ApplyUpdates Specific

        1000 = @{Description = "ApplyUpdates Error: Failed retrieving results"; Resolution = "An error occurred when retrieving the result of the apply updates operation. Retry the operation."}

        1001 = @{Description = "ApplyUpdates Info: Canceled"; Resolution = "The cancellation was initiated, Hence, the apply updates operation is canceled. Retry the operation if needed."}

        1002 = @{Description = "ApplyUpdates Error: Download failed"; Resolution = "An error occurred while downloading a file during the apply updates operation. Check network connection and retry."}

        1003 = @{Description = "ApplyUpdates Error: Dependency/Prerequisite failure"; Resolution = "An update failed because a dependency or prerequisite was not met. Check DCU logs."}

        1004 = @{Description = "ApplyUpdates Error: Installation failure"; Resolution = "One or more updates failed to install. Check DCU logs and individual DUP logs."}

        1005 = @{Description = "ApplyUpdates Error: Insufficient disk space"; Resolution = "Free up disk space on the system drive."}

        # Service Related

        3000 = @{Description = "Service Error: DCMS not running"; Resolution = "Start the 'Dell Client Management Service' in Windows services."}

        3001 = @{Description = "Service Error: DCMS communication timeout"; Resolution = "The CLI could not communicate with the Dell Client Management Service. Ensure the service is running and responsive."}

        # Configuration Related

        4000 = @{Description = "Configuration Error: Invalid setting"; Resolution = "One or more configuration settings provided were invalid. Check the syntax and valid values."}

        4001 = @{Description = "Configuration Error: Failed to apply setting"; Resolution = "An error occurred while saving the configuration. Check permissions and DCU logs."}

        # BIOS Password Related

        8000 = @{Description = "BIOS Password Error: Password required"; Resolution = "A BIOS password is set, but none was provided."}

        8001 = @{Description = "BIOS Password Error: Invalid password"; Resolution = "The BIOS password provided was incorrect."}

        8002 = @{Description = "BIOS Password Error: Cannot set password"; Resolution = "An error occurred trying to set or clear the BIOS password."}

    }



    $result = $DCUExitInfo[$DCUExit] # Use hashtable lookup

    if ($null -eq $result) {

        # Provide a default message if the code isn't explicitly listed

        return @{ExitCode = $DCUExit; Description = "Unknown or Undocumented Exit Code"; Resolution = "Refer to Dell Command Update documentation or logs (check $Global:DellLogPath) for details."}

    } else {

        # Add the ExitCode to the returned object for completeness

        $result = [PSCustomObject]@{ ExitCode = $DCUExit } + $result

        return $result

    }

}



Function Get-DUPExitInfo {

    [CmdletBinding()]

    param(

         [Parameter(Mandatory=$true)]

        [int]$DUPExit

    )

    # Source: Standard Dell Update Package (DUP) exit codes

    $DUPExitInfo = @{

        -1 = @{DisplayName = "DUP Error (Timeout)"; Description = "DCU may have terminated the DUP execution due to timeout."} # Added context

         0 = @{DisplayName = "Success (no reboot needed)"; Description = "The DUP operation completed successfully, no reboot needed by this specific package."} # Clarified scope

         1 = @{DisplayName = "DUP Error (General Failure)"; Description = "An unspecified error occurred during the DUP update process; the update was not successful."}

         2 = @{DisplayName = "Reboot Required"; Description = "The DUP requires a reboot to complete the operation. The system MAY reboot automatically depending on DUP flags."} # Added nuance

         3 = @{DisplayName = "DUP Info (Soft Dependency/Version)"; Description = "Update skipped. Attempted update to same version, or downgrade was disallowed by default."}

         4 = @{DisplayName = "DUP Error (Hard Dependency)"; Description = "The required prerequisite software was not found on the computer."}

         5 = @{DisplayName = "DUP Error (Qualification Error)"; Description = "A hard qualification error occurred (e.g., wrong model/OS). Cannot be suppressed with /f."}

         6 = @{DisplayName = "Reboot Initiated"; Description = "The DUP package itself has initiated a system reboot."} # DUP forced the reboot

         7 = @{DisplayName = "DUP Error (Password Validation)"; Description = "Password not provided or incorrect password provided for BIOS/firmware update."}

         8 = @{DisplayName = "DUP Error (Downgrade Not Allowed)"; Description = "Downgrading the BIOS/firmware to the version attempted is explicitly blocked."}

         9 = @{DisplayName = "DUP Error (RPM Verification)"; Description = "RPM signature verification failed (Linux DUPs)."}

        10 = @{DisplayName = "DUP Error (Other)"; Description = "An unknown error occurred within the DUP package."}

        # Note: Some specific DUPs might have custom exit codes not listed here.

    }

    $result = $DUPExitInfo[$DUPExit]

     if ($null -eq $result) {

        return @{ExitCode = $DUPExit; DisplayName = "Unknown"; Description = "Unknown or Undocumented DUP Exit Code. Check specific DUP logs."}

    } else {

        $result = [PSCustomObject]@{ ExitCode = $DUPExit } + $result

        return $result

    }

}



# Modified Install-DCU to use global paths and add more logging/error handling

Function Install-DCU {

    [CmdletBinding(SupportsShouldProcess=$true)]

    param(

        [Switch]$UseWebRequest # Use Invoke-WebRequest instead of BITS

    )



    $LogFilePath = $Global:DellLogPath # For installer log file destination

    $DellCabDownloadsPath = $Global:DellTempPath # For downloads

    $DellCabExtractPath = Join-Path -Path $Global:DellTempPath -ChildPath "DellCabExtract_InstallDCU" # Unique path for catalog extraction



    if ($PSCmdlet.ShouldProcess("Dell Command | Update", "Install/Update")) {



        Write-Host "Attempting to install/update Dell Command | Update..." -ForegroundColor Cyan



        $Manufacturer = ""

        try { $Manufacturer = (Get-CimInstance -ClassName Win32_ComputerSystem -ErrorAction Stop).Manufacturer } catch {}

        if ($Manufacturer -notmatch "Dell"){ Write-Warning "This Function is only for Dell Systems. Manufacturer: '$Manufacturer'. Skipping DCU Install."; return $false }



        # Create Folders if they don't exist

        if (!(Test-Path -Path $LogFilePath)){ try { New-Item -Path $LogFilePath -ItemType Directory -Force -ErrorAction Stop | Out-Null } catch { Write-Error "Failed create log path $LogFilePath. Error: $($_.Exception.Message)"; return $false }}

        if (!(Test-Path -Path $DellCabDownloadsPath)){ try { New-Item -Path $DellCabDownloadsPath -ItemType Directory -Force -ErrorAction Stop | Out-Null } catch { Write-Error "Failed create downloads path $DellCabDownloadsPath. Error: $($_.Exception.Message)"; return $false }}

        if (!(Test-Path -Path $DellCabExtractPath)){ try { New-Item -Path $DellCabExtractPath -ItemType Directory -Force -ErrorAction Stop | Out-Null } catch { Write-Error "Failed create extract path $DellCabExtractPath. Error: $($_.Exception.Message)"; return $false }}



        # Get System SKU

        $SystemSKUNumber = ""

        try { $SystemSKUNumber = (Get-CimInstance -ClassName Win32_ComputerSystem -ErrorAction Stop).SystemSKUNumber } catch { Write-Error "Could not determine system SKU Number. Error: $($_.Exception.Message)"; return $false }

        if ([string]::IsNullOrWhiteSpace($SystemSKUNumber)) { Write-Error "System SKU Number is blank or null."; return $false }



        Write-Host "System SKU: $SystemSKUNumber. Finding appropriate DCU version..."



        # Get supported models (requires Get-DellSupportedModels function defined)

        $supportedModels = Get-DellSupportedModels # Assumes this function works and returns data

        if ($null -eq $supportedModels) {

             Write-Error "Could not retrieve list of supported Dell models. Cannot proceed with DCU install."

             return $false

        }

        # Handle potential variations in SKU (e.g., leading 0)

        $DellSKU = $supportedModels | Where-Object {$SystemSKUNumber -match $_.systemID} | Select-Object -First 1

        if (!$DellSKU) { Write-Error "System SKU '$SystemSKUNumber' not found in the supported models list catalog. Cannot determine correct DCU version automatically."; return $false }



        Write-Host "Found entry for Model: $($DellSKU.Model) [Matching SKU Pattern: $($DellSKU.SystemID)]. Using catalog URL fragment: $($DellSKU.URL)"



        # Download and Extract Model-Specific Catalog

        $CabPathIndexModel = Join-Path -Path $DellCabDownloadsPath -ChildPath "CatalogIndex_$($SystemSKUNumber).cab"

        $xmlExtractPath = Join-Path -Path $DellCabExtractPath -ChildPath "CatalogIndex_$($SystemSKUNumber).xml" # Expected XML file name



        if (Test-Path $CabPathIndexModel){Remove-Item -Path $CabPathIndexModel -Force}

        if (Test-Path $xmlExtractPath){Remove-Item -Path $xmlExtractPath -Force}

         # Clean extraction dir

         if (Test-Path $DellCabExtractPath){

             try { Get-ChildItem -Path $DellCabExtractPath -Recurse | Remove-Item -Force -Recurse -ErrorAction Stop }

             catch { Write-Warning "Could not fully clean extract path $DellCabExtractPath before expansion. Error: $($_.Exception.Message)" }

         }



        $modelCatalogUri = "https://downloads.dell.com/$($DellSKU.URL)" # Assume HTTPS

        try {

            Write-Host "Downloading model-specific catalog from $modelCatalogUri..." -ForegroundColor Yellow

            Invoke-WebRequest -Uri $modelCatalogUri -OutFile $CabPathIndexModel -UseBasicParsing -ErrorAction Stop

        } catch {

            Write-Error "Failed to download model-specific catalog $modelCatalogUri. Error: $($_.Exception.Message)"

            return $false

        }



        if (Test-Path $CabPathIndexModel){

            Write-Host "Extracting model-specific catalog ($CabPathIndexModel)..." -ForegroundColor Yellow

            $xmlFinalPath = $xmlExtractPath # Use the expected path

             try {

                Expand-Archive -Path $CabPathIndexModel -DestinationPath $DellCabExtractPath -Force -ErrorAction Stop

            } catch {

                 Write-Warning "Expand-Archive failed. Error: $($_.Exception.Message). Attempting expand.exe fallback..."

                 try {

                    $expandOutputTarget = $xmlFinalPath

                    cmd.exe /c "expand `"$CabPathIndexModel`" `"$expandOutputTarget`""

                    if (!(Test-Path $xmlFinalPath)) { throw "expand.exe command completed, but $xmlFinalPath not found."}

                    Write-Host "expand.exe fallback succeeded." -ForegroundColor Green

                 } catch {

                     Write-Error "Failed to expand $CabPathIndexModel using expand.exe too. Error: $($_.Exception.Message)"

                     return $false

                 }

            }



            if (!(Test-Path $xmlFinalPath)) {

                Write-Error "$xmlFinalPath not found after attempting expansion in $DellCabExtractPath."

                 $otherXml = Get-ChildItem -Path $DellCabExtractPath -Filter "*.xml" | Select-Object -First 1

                if ($otherXml) { Write-Warning "Found $($otherXml.Name) instead. Check catalog structure." }

                return $false

            }



            [xml]$XMLIndexCAB = $null

            try {

                Write-Host "Loading model XML $xmlFinalPath..." -ForegroundColor Yellow

                $XMLIndexCAB = Get-Content -Path $xmlFinalPath -ErrorAction Stop

            } catch {

                Write-Error "Failed to load model-specific XML content from $xmlFinalPath. Error: $($_.Exception.Message)"

                return $false

            }



            # Find the latest DCU Application Package (APAC) for x64

            Write-Host "Parsing catalog for Dell Command | Update application..."

            $DCUAppsAvailable = $XMLIndexCAB.Manifest.SoftwareComponent | Where-Object {$_.ComponentType.value -eq "APAC"}

            if ($null -eq $DCUAppsAvailable) { Write-Warning "No APAC components found in catalog."; return $false }



            # Filter for DCU specifically, x64 arch, sort by version descending

            $AppDCU = $DCUAppsAvailable |

                Where-Object {

                    $_.path -match 'command[_\-]?update' -and # Match variations like command-update, command_update

                    $_.SupportedOperatingSystems.OperatingSystem.osArch -match "x64"

                } |

                Sort-Object -Property @{Expression={[version]($_.vendorVersion -replace '[^0-9.].*','')}} -Descending | # Robust version sort

                Select-Object -First 1



            if ($AppDCU){

                $DellItem = $AppDCU

                # Compare with installed version

                $DCUInstalledDetails = Get-DCUInstallDetails

                [Version]$CurrentVersion = [Version]'0.0.0.0' # Default to 0.0.0.0 if not installed

                if ($DCUInstalledDetails) {

                     try { [Version]$CurrentVersion = $DCUInstalledDetails.Version } catch { Write-Warning "Could not parse installed DCU version: $($DCUInstalledDetails.Version)"}

                }



                [Version]$DCUVersionAvailable = [Version]'0.0.0.0'

                try { [Version]$DCUVersionAvailable = ($DellItem.vendorVersion -replace '[^0-9.].*','') } catch {Write-Error "Could not parse available DCU version from catalog: $($DellItem.vendorVersion)"; return $false }



                $TargetLink = "https://downloads.dell.com/$($DellItem.path)" # Assume HTTPS

                $TargetFileName = ($DellItem.path).Split("/") | Select-Object -Last 1

                $TargetFilePathName = Join-Path -Path $DellCabDownloadsPath -ChildPath $TargetFileName



                Write-Host "Latest available DCU version found in catalog: $DCUVersionAvailable"

                Write-Host "Currently installed DCU version: $CurrentVersion"



                if ($DCUVersionAvailable -gt $CurrentVersion){

                    Write-Host "Newer version ($DCUVersionAvailable) found. Proceeding with download and installation." -ForegroundColor Green

                    Write-Host "  Title: $($DellItem.Name.Display.'#cdata-section')"

                    Write-Host "  Release Date: $($DellItem.releaseDate)"

                    Write-Host "  Download URL: $TargetLink"



                    # Download

                    Write-Host "Downloading $TargetFileName..." -ForegroundColor Yellow

                    if (Test-Path $TargetFilePathName) { Remove-Item $TargetFilePathName -Force } # Ensure fresh download

                    try {

                        if ($UseWebRequest){

                            Write-Host "(Using Invoke-WebRequest)"

                            Invoke-WebRequest -Uri $TargetLink -OutFile $TargetFilePathName -UseBasicParsing -Verbose -ErrorAction Stop

                        }

                        else{

                            Write-Host "(Using Start-BitsTransfer)"

                            Import-Module BitsTransfer -ErrorAction SilentlyContinue # Ensure module is loaded

                            Start-BitsTransfer -Source $TargetLink -Destination $TargetFilePathName -DisplayName "Downloading $TargetFileName" -Priority High -ErrorAction Stop

                        }

                    } catch {

                        Write-Error "Download failed for $TargetFileName from $TargetLink. Error: $($_.Exception.Message)"

                        # Attempt fallback if primary method failed?

                        if (!$UseWebRequest -and ($_.Exception.Message -match 'BITS')) { # If BITS failed

                            Write-Warning "BITS failed, attempting fallback with Invoke-WebRequest..."

                            try {

                                 Invoke-WebRequest -Uri $TargetLink -OutFile $TargetFilePathName -UseBasicParsing -Verbose -ErrorAction Stop

                            } catch {

                                 Write-Error "Invoke-WebRequest fallback also failed. Error: $($_.Exception.Message)"

                                 return $false

                            }

                        } else {

                             return $false # If WebRequest was primary or fallback failed.

                        }

                    }



                    # Confirm Download and Install

                    if (Test-Path $TargetFilePathName){

                        Write-Host "Download successful: $TargetFilePathName" -ForegroundColor Green

                        $InstallerLogFileName = Join-Path -Path $LogFilePath -ChildPath "$($TargetFileName -replace '\.exe$|\.msi$','')_Install_$(Get-Date -Format 'yyyyMMddHHmmss').log"

                        # Common DUP silent args: /s (silent), /l=<path> (log), /passthrough ... (for MSI)

                        # Determine if EXE or MSI

                        $Arguments = ""

                        if ($TargetFileName -like "*.exe") {

                             $Arguments = "/s /l=`"$InstallerLogFileName`""

                        } elseif ($TargetFileName -like "*.msi") {

                             # Basic silent MSI install

                             $Arguments = "/i `"$TargetFilePathName`" /qn /l*v `"$InstallerLogFileName`""

                             # Adjust FilePath for msiexec

                             $InstallerExe = "msiexec.exe"

                        } else {

                            Write-Error "Downloaded file '$TargetFileName' is not a recognized EXE or MSI installer."

                            return $false

                        }



                        # Use original filename if msiexec is not used

                        if ($InstallerExe -ne "msiexec.exe") { $InstallerExe = $TargetFilePathName }





                        Write-Host "Starting silent installation of DCU... Log file: $InstallerLogFileName" -ForegroundColor Yellow

                        Write-Host "Executing: $InstallerExe $Arguments"

                        try {

                            $Process = Start-Process -FilePath $InstallerExe -ArgumentList $Arguments -Wait -PassThru -ErrorAction Stop

                            Write-Host "DCU Installer Exit Code: $($Process.ExitCode)"



                            # Check DUP Exit Codes (assuming EXE installer is a DUP) or MSI codes

                            $ExitInfo = $null

                            if ($TargetFileName -like "*.exe") {

                                $ExitInfo = Get-DUPExitInfo -DUPExit $Process.ExitCode

                                Write-Host "DUP Exit Code Meaning: $($ExitInfo.DisplayName) - $($ExitInfo.Description)"

                            } else { # Basic MSI codes

                                switch ($Process.ExitCode) {

                                    0 { Write-Host "MSI Exit Code Meaning: Success" }

                                    1603 { Write-Host "MSI Exit Code Meaning: Fatal error during installation." -ForegroundColor Red }

                                    1641 { Write-Host "MSI Exit Code Meaning: Success, reboot initiated." -ForegroundColor Yellow }

                                    3010 { Write-Host "MSI Exit Code Meaning: Success, reboot required." -ForegroundColor Yellow }

                                    default { Write-Host "MSI Exit Code Meaning: Unknown or other error code." }

                                }

                            }



                            # Common Success/Reboot codes for DUPs and MSI

                            if ($Process.ExitCode -in @(0, 3010)) { # DUP 0=Success, MSI 0=Success, 3010=Success/Reboot Needed

                                Write-Host "Dell Command | Update installed/updated successfully." -ForegroundColor Green

                                if ($Process.ExitCode -eq 3010) { # MSI Reboot needed

                                    Write-Host "Installation requires a reboot." -ForegroundColor Yellow

                                    $Global:dellRebootNeeded = $true

                                }

                                return $true # Indicate success

                            } elseif ($Process.ExitCode -in @(2, 6, 1641)) { # DUP 2=Reboot Req, DUP 6=Rebooting, MSI 1641=Reboot Initiated

                                Write-Host "Dell Command | Update installed/updated. A reboot is required/initiated." -ForegroundColor Yellow

                                $Global:dellRebootNeeded = $true

                                return $true # Indicate success but reboot needed/happening

                            }

                            else {

                                Write-Error "DCU installation failed with exit code $($Process.ExitCode). Check log: $InstallerLogFileName"

                                return $false # Indicate failure

                            }

                        } catch {

                            Write-Error "Failed to start or complete DCU installation process. Error: $($_.Exception.Message)"

                            return $false # Indicate failure

                        }

                    }

                    else{

                        Write-Error "DCU download file not found after download attempt: $TargetFilePathName"

                        return $false # Indicate failure

                    }

                } else {

                     Write-Host "Installed DCU version ($CurrentVersion) is already the latest or newer than catalog version ($DCUVersionAvailable). Skipping installation." -ForegroundColor Green

                     return $true # Indicate success (already installed/up-to-date)

                }

            }

            else {

                Write-Warning "Could not find a suitable Dell Command Update x64 Application package in the model catalog."

                return $false # Indicate failure

            }

        } else {

             Write-Error "Model-specific catalog CAB file not found after download attempt: $CabPathIndexModel"

             return $false # Indicate failure

        }



    } else {

        Write-Warning "Skipping DCU install/update due to -WhatIf parameter."

        return $false # Indicate skipped

    }

}





# Modified Set-DCUSettings to use global paths and add Get-DCUInstallDetails call

function Set-DCUSettings {

    [CmdletBinding(SupportsShouldProcess=$true)]

    param (

        # Add parameter descriptions for clarity if desired

        [ValidateSet('Enable','Disable')]

        [string]$advancedDriverRestore,

        [ValidateSet('Enable','Disable')]

        [string]$autoSuspendBitLocker, # Note: ApplyUpdates command overrides this during run

        [ValidateSet('Enable','Disable')]

        [string]$installationDeferral,

        [ValidateRange(0,99)]

        [int]$deferralInstallInterval = 3,

        [ValidateRange(0,9)]

        [int]$deferralInstallCount = 5,

        [ValidateSet('Enable','Disable')]

        [string]$systemRestartDeferral,

        [ValidateRange(0,99)]

        [int]$deferralRestartInterval = 3,

        [ValidateRange(0,9)]

        [int]$deferralRestartCount = 5,

        [ValidateSet('NotifyAvailableUpdates','DownloadAndNotify','DownloadInstallAndNotify')]

        [string]$scheduleAction,

        [switch]$scheduleAuto,

        [string]$CustomCatalogPath

    )



    $dcuDetails = Get-DCUInstallDetails

    if (!$dcuDetails) {

        Write-Error "Cannot set DCU settings. DCU installation details not found."

        return

    }

    $DCUCLI = $dcuDetails.CliPath # Use path from Get-DCUInstallDetails



    $LogPathBase = $Global:DellLogPath

    $DateTimeStamp = Get-Date -Format "yyyyMMdd-HHmmss"



    # Helper function to run a config command

    Function Invoke-DCUConfigure {

        param(

            [string]$SettingName,

            [string[]]$ArgumentList # Use string array

        )

        $logFileName = Join-Path -Path $LogPathBase -ChildPath "DCU-CLI-Configure-$($SettingName)_$($DateTimeStamp).log"

        # Always add configure command and log path

        $fullArgs = @("/configure") + $ArgumentList + "-outputlog=`"$logFileName`""

        $fullArgString = $fullArgs -join " " # Join into a single string for Start-Process



        Write-Host "Configuring DCU setting '$SettingName'..." -ForegroundColor Yellow

        Write-Host "  Arguments: $fullArgString"

        Write-Host "  Log File: $logFileName"



        if ($PSCmdlet.ShouldProcess("$DCUCLI", "Configure setting $SettingName")) {

            try {

                $DCUConfig = Start-Process -FilePath $DCUCLI -ArgumentList $fullArgString -NoNewWindow -PassThru -Wait -ErrorAction Stop



                $ExitInfo = Get-DCUExitInfo -DCUExit $DCUConfig.ExitCode

                Write-Host "  Exit Code: $($DCUConfig.ExitCode)"

                if ($DCUConfig.ExitCode -ne 0){

                    Write-Warning "  Description: $($ExitInfo.Description)"

                    Write-Warning "  Resolution: $($ExitInfo.Resolution)"

                } else {

                    Write-Host "  Configuration successful." -ForegroundColor Green

                }

            } catch {

                Write-Error "  Failed to run dcu-cli.exe /configure for $SettingName. Error: $($_.Exception.Message)"

            }

        } else {

             Write-Warning "Skipping configuration for $SettingName due to -WhatIf."

        }

         Write-Host # Blank line for readability

    }



    # Apply settings ONLY if the parameter was actually passed in this call

    if ($PSBoundParameters.ContainsKey('advancedDriverRestore')){

        Invoke-DCUConfigure -SettingName "advancedDriverRestore" -ArgumentList "-advancedDriverRestore=$advancedDriverRestore"

    }

    if ($PSBoundParameters.ContainsKey('autoSuspendBitLocker')){

        Invoke-DCUConfigure -SettingName "autoSuspendBitLocker" -ArgumentList "-autoSuspendBitLocker=$autoSuspendBitLocker"

    }

     if ($PSBoundParameters.ContainsKey('scheduleAction')){

        Invoke-DCUConfigure -SettingName "scheduleAction" -ArgumentList "-scheduleAction=$scheduleAction"

    }

    if ($scheduleAuto.IsPresent){ # Check switch correctly

        Invoke-DCUConfigure -SettingName "scheduleAuto" -ArgumentList "-scheduleAuto" # Switch parameter just needs to be present

    }

    # Installation Deferral

    if ($PSBoundParameters.ContainsKey('installationDeferral')){

        $installDeferArgs = @("-installationDeferral=$installationDeferral")

        if ($installationDeferral -eq 'Enable'){

             $installDeferArgs += "-deferralInstallInterval=$deferralInstallInterval"

             $installDeferArgs += "-deferralInstallCount=$deferralInstallCount"

        }

        Invoke-DCUConfigure -SettingName "installationDeferral" -ArgumentList $installDeferArgs

    }

    # System Restart Deferral

     if ($PSBoundParameters.ContainsKey('systemRestartDeferral')){

        $restartDeferArgs = @("-systemRestartDeferral=$systemRestartDeferral")

        if ($systemRestartDeferral -eq 'Enable'){

             $restartDeferArgs += "-deferralRestartInterval=$deferralRestartInterval"

             $restartDeferArgs += "-deferralRestartCount=$deferralRestartCount"

        }

        Invoke-DCUConfigure -SettingName "RestartDeferral" -ArgumentList $restartDeferArgs

    }

    # Custom Catalog Path

     if ($PSBoundParameters.ContainsKey('CustomCatalogPath') -and ![string]::IsNullOrWhiteSpace($CustomCatalogPath)){

        # Need to enable XML allow if setting custom path

        Invoke-DCUConfigure -SettingName "CustomCatalogPath" -ArgumentList "-catalogLocation=`"$CustomCatalogPath`"", "-allowXML=enable"

    } elseif ($PSBoundParameters.ContainsKey('CustomCatalogPath') -and [string]::IsNullOrWhiteSpace($CustomCatalogPath)) {

        # If parameter exists but is empty, maybe reset to default? Consult DCU docs. For now, just warn.

        Write-Warning "CustomCatalogPath parameter was provided but value is empty. No change made."

    }

}



# Modified Invoke-DCU to use global paths and add Get-DCUInstallDetails call

function Invoke-DCU {

    [CmdletBinding(SupportsShouldProcess=$true)]

    param (

        [ValidateSet('security','critical','recommended','optional')]

        [String[]]$updateSeverity, # Default to all if not specified? DCU default is critical, recommended, optional. Security needs explicit add? Check docs. Let's default to ALL if unset.

        [ValidateSet('bios','firmware','driver','application','others')]

        [String[]]$updateType = @('bios','firmware','driver','application','others'), # Default to ALL types

        [ValidateSet('audio','video','network','chipset','storage','input','others')]

        [String[]]$updateDeviceCategory, # No default, let DCU decide based on other filters if unset

        [ValidateSet('Enable','Disable')]

        [string]$autoSuspendBitLocker = 'Enable', # Default to Enable for safety during apply

        [ValidateSet('Enable','Disable')]

        [string]$reboot = 'Disable', # Default to Disable, control reboot centrally

        [ValidateSet('Enable','Disable')]

        [string]$forceupdate = 'Disable', # Default to Disable (don't force downgrade/reinstall)

        [switch]$scan,

        [switch]$applyUpdates

    )



    $dcuDetails = Get-DCUInstallDetails

    if (!$dcuDetails) {

        Write-Error "Cannot invoke DCU. DCU installation details not found."

        return $null # Return null or specific error object

    }

    $DCUCLI = $dcuDetails.CliPath



    $LogPathBase = $Global:DellLogPath

    $DateTimeStamp = Get-Date -Format "yyyyMMdd-HHmmss"

    $baseArgs = @() # Start with empty array for arguments



    # Build Argument Strings for each parameter filter

    # If severity is not specified by user, default to all common ones for applyUpdates, none for scan? Let's use DCU default unless specified.

    if ($PSBoundParameters.ContainsKey('updateSeverity')){

        $baseArgs += "-updateSeverity=$($updateSeverity -join ',')"

    } elseif ($applyUpdates) {

         # If applying updates and no severity specified, perhaps default to critical/recommended? Or all?

         # DCU default covers most, let's stick to DCU default unless specified.

    }



    # Default to all types if not specified

    $baseArgs += "-updateType=$($updateType -join ',')"



    if ($PSBoundParameters.ContainsKey('updateDeviceCategory')){

         $baseArgs += "-updateDeviceCategory=$($updateDeviceCategory -join ',')"

    }



    # Determine Action

    $Action = ""

    $ActionCommand = ""

    $reportPath = Join-Path -Path $LogPathBase -ChildPath "DCU-Report-$($DateTimeStamp).xml" # Report path for scan



    if ($applyUpdates) {

        $Action = "applyUpdates"

        $ActionCommand = "/applyUpdates"

        # Add options relevant to applyUpdates

        $baseArgs += "-autoSuspendBitLocker=$autoSuspendBitLocker"

        $baseArgs += "-reboot=$reboot" # Crucial: Set to Disable here

        $baseArgs += "-forceupdate=$forceupdate"

    } elseif ($scan) {

         $Action = "scan"

         $ActionCommand = "/scan"

         # Add report path only for scan action

         $baseArgs += "-report=`"$reportPath`""

    } else {

         # If neither -scan nor -applyUpdates is specified, default to scan

         $Action = "scan"

         $ActionCommand = "/scan"

         $baseArgs += "-report=`"$reportPath`""

         Write-Warning "Neither -scan nor -applyUpdates specified. Defaulting to /scan."

    }



    $logFileName = Join-Path -Path $LogPathBase -ChildPath "DCU-CLI-$($Action)_$($DateTimeStamp).log"

    $finalArgs = @($ActionCommand) + $baseArgs + "-outputlog=`"$logFileName`"" # Combine command, base args, and log file

    $finalArgString = $finalArgs -join " "



    Write-Host "Invoking DCU Action: $ActionCommand" -ForegroundColor Cyan

    Write-Host "  Arguments: $finalArgString"

    Write-Host "  Log File: $logFileName"

    if ($Action -eq "scan") { Write-Host "  Report File: $reportPath" }



    $processResult = $null

    if ($PSCmdlet.ShouldProcess("$DCUCLI", "Run DCU action $Action")) {

        try {

            # Using Write-Host to ensure arguments are logged clearly before execution

            Write-Host "Executing: `"$DCUCLI`" $finalArgString"



            # Start the process

            $processResult = Start-Process -FilePath $DCUCLI -ArgumentList $finalArgString -NoNewWindow -PassThru -Wait -ErrorAction Stop

            Write-Host "DCU process completed." -ForegroundColor Green



            $ExitInfo = Get-DCUExitInfo -DCUExit $processResult.ExitCode

            Write-Host "  Exit Code: $($processResult.ExitCode)"

            Write-Host "  Exit Code Meaning: $($ExitInfo.Description)"

             if ($processResult.ExitCode -ne 0 -and $processResult.ExitCode -ne 500) { # Don't warn for success or 'no updates found' on scan

                 Write-Warning "  Resolution Hint: $($ExitInfo.Resolution)"

             }



             # Return the process object so the caller can check ExitCode etc.

             return $processResult



        } catch {

            Write-Error "Failed to run dcu-cli.exe $ActionCommand. Error: $($_.Exception.Message)"

            return $null # Indicate failure

        }

    } else {

        Write-Warning "Skipping DCU action $Action due to -WhatIf."

        return $null # Indicate skipped

    }

}



# Note: The Get-DellBIOSUpdates function with -Flash is kept for potential manual use,

# but the main script uses Invoke-DCU -applyUpdates which handles BIOS internally.

# The BitLocker check within Get-DellBIOSUpdates -Flash is specific to that manual flash scenario.



Function Get-DellBIOSUpdates {

    [CmdletBinding(SupportsShouldProcess=$true)] # Added WhatIf support

    param (

        [Parameter(Mandatory=$False)]

        [string]$SystemSKUNumber, # Removed length validation, CIM query handles it

        [switch]$Latest, # List only the latest BIOS found in catalog

        [switch]$Check, # Check if currently installed BIOS is older than latest in catalog

        [switch]$Flash, # Attempt to download and flash the latest BIOS (USE WITH CAUTION)

        [SecureString]$Password, # BIOS password if needed for flashing

        [string]$DownloadPath # Path to download only, without flashing

    )



    # Auto-detect SKU if not provided

     $Manufacturer = ""

     try { $Manufacturer = (Get-CimInstance -ClassName Win32_ComputerSystem -ErrorAction Stop).Manufacturer } catch {}



    if ([string]::IsNullOrWhiteSpace($SystemSKUNumber)) {

        if ($Manufacturer -notmatch "Dell"){ Write-Warning "This Function is only for Dell Systems unless SKU provided."; return $null }

        try {

             $SystemSKUNumber = (Get-CimInstance -ClassName Win32_ComputerSystem -ErrorAction Stop).SystemSKUNumber

             if ([string]::IsNullOrWhiteSpace($SystemSKUNumber)) { throw "System SKU Number from WMI is blank or null." }

             Write-Host "Auto-detected System SKU: $SystemSKUNumber"

        } catch {

            Write-Error "Could not auto-determine system SKU Number. Error: $($_.Exception.Message)"; return $null

        }

    }



    # Required paths

    $DellDownloadsPath = $Global:DellTempPath # Use global path for downloads

    $DellLogPath = $Global:DellLogPath # Use global path for logs



    # --- Get Latest BIOS Info from Catalog ---

    Write-Host "Getting latest BIOS information for SKU $SystemSKUNumber from catalog..."

    # Use the Get-DCUUpdateList function (assumes it's defined above)

    $LatestBIOS = $null

    try {

         # Ensure -updateType is correct ('bios' not 'BIOS')

        $LatestBIOS = Get-DCUUpdateList -SystemSKUNumber $SystemSKUNumber -updateType 'bios' -Latest -ErrorAction Stop

    } catch {

         Write-Error "Error calling Get-DCUUpdateList for BIOS: $($_.Exception.Message)"

         return $null

    }



    if ($null -eq $LatestBIOS) {

        Write-Warning "Could not find any BIOS updates listed in the catalog for SKU $SystemSKUNumber."

        return $null

    }

    # Handle if Get-DCUUpdateList returned multiple (shouldn't with -Latest, but be safe)

    if ($LatestBIOS -is [array]) {

        Write-Warning "Multiple 'latest' BIOS found by Get-DCUUpdateList, selecting the absolute newest by ReleaseDate."

        $LatestBIOS = $LatestBIOS | Sort-Object -Property @{Expression={try{[datetime]$_.ReleaseDate}catch{Get-Date "1900-01-01"}}} -Descending | Select-Object -First 1

    }



    [version]$LatestVersion = [version]'0.0.0.0'

    try { [version]$LatestVersion = ($LatestBIOS.DellVersion -replace '[^0-9.].*','') } catch { Write-Warning "Could not parse latest BIOS version string from catalog: $($LatestBIOS.DellVersion)"}



    $releaseDateStr = "Unknown"

    if ($LatestBIOS.ReleaseDate -is [datetime]) { $releaseDateStr = $LatestBIOS.ReleaseDate.ToString('yyyy-MM-dd') }

    elseif ($LatestBIOS.ReleaseDate) { $releaseDateStr = $LatestBIOS.ReleaseDate } # Use string if parse failed



    Write-Host "Latest Available Catalog BIOS Version: $LatestVersion (Name: $($LatestBIOS.Name), Date: $releaseDateStr)"



    # --- Get Current BIOS Info ---

    [Version]$CurrentBIOSVersion = [version]'0.0.0.0'

    $currentBIOSInfo = $null

    if ($Manufacturer -match "Dell") { # Only check local BIOS if it's a Dell machine

        try {

            $currentBIOSInfo = Get-CimInstance -ClassName Win32_BIOS -ErrorAction Stop

            [Version]$CurrentBIOSVersion = $currentBIOSInfo.SMBIOSBIOSVersion

             Write-Host "Current Installed BIOS Version: $CurrentBIOSVersion"

        } catch {

            Write-Warning "Could not retrieve current BIOS version via WMI. Error: $($_.Exception.Message)"

            # Cannot perform Check or safe Flash without current version

            if ($Check -or $Flash) {

                Write-Error "Cannot proceed with Check or Flash as current BIOS version unknown."

                return $null

            }

        }

    } elseif ($Check -or $Flash) {

         Write-Error "Cannot perform Check or Flash on non-Dell system."

         return $null

    }





    # --- Check Action ---

    if ($Check.IsPresent){

        if ($CurrentBIOSVersion -lt $LatestVersion){

            Write-Host "New BIOS Update Available ($CurrentBIOSVersion -> $LatestVersion)" -ForegroundColor Yellow

            # Return the details of the update that's available

            return $LatestBIOS

        }

        else {

            Write-Host "Current BIOS ($CurrentBIOSVersion) is up-to-date or newer compared to catalog ($LatestVersion)." -ForegroundColor Green

            return $null # Indicate no update needed/available relative to current

        }

    }



    # --- Download Only Action ---

    if ($PSBoundParameters.ContainsKey('DownloadPath')){

         if ([string]::IsNullOrWhiteSpace($DownloadPath)) {

             Write-Error "-DownloadPath specified but value is empty."

             return $null

         }

         Write-Host "Download Only action selected for latest BIOS..."

         if (!(Test-Path -Path $DownloadPath -PathType Container)) {

              Write-Host "Creating download directory: $DownloadPath" -ForegroundColor Yellow

              try { New-Item -Path $DownloadPath -ItemType Directory -Force -ErrorAction Stop | Out-Null }

              catch { Write-Error "Failed to create download directory $DownloadPath. Error: $($_.Exception.Message)"; return $null }

         }

        $UpdatePathUri = $LatestBIOS.Path # Should be full URI from Get-DCUUpdateList

        if (-not ($UpdatePathUri -like "http*")) { Write-Error "Catalog path '$UpdatePathUri' doesn't look like a valid URL."; return $null }



        $UpdateFileName = $UpdatePathUri -split "/" | Select-Object -Last 1

        $UpdateLocalPath = Join-Path -Path $DownloadPath -ChildPath $UpdateFileName



        Write-Host "Downloading $UpdateFileName to $UpdateLocalPath..." -ForegroundColor Yellow

         if ($PSCmdlet.ShouldProcess($UpdatePathUri, "Download BIOS to $UpdateLocalPath")) {

            if (Test-Path $UpdateLocalPath) { Remove-Item $UpdateLocalPath -Force }

            try {

                Import-Module BitsTransfer -ErrorAction SilentlyContinue

                Start-BitsTransfer -DisplayName "Downloading BIOS $UpdateFileName" -Source $UpdatePathUri -Destination $UpdateLocalPath -Description "Downloading BIOS for SKU $SystemSKUNumber" -Priority High -ErrorAction Stop

                Write-Host "BIOS Download complete: $UpdateLocalPath" -ForegroundColor Green

                return $UpdateLocalPath # Return the path to the downloaded file

            } catch {

                Write-Error "Failed to download BIOS from $UpdatePathUri. Error: $($_.Exception.Message)"

                # Optional: Fallback with Invoke-WebRequest if BITS fails?

                return $null

            }

         } else {

              Write-Warning "Skipping BIOS download due to -WhatIf."

              return $null

         }

    }



    # --- Flash Action ---

    # WARNING: This performs a direct flash outside of DCU's control flow. Use with extreme caution.

    # DCU's "-autoSuspendBitLocker Enable" is the preferred method when using Invoke-DCU -applyUpdates.

    if ($Flash.IsPresent){

         Write-Warning "Flash Latest BIOS action selected. This attempts a direct flash." -ForegroundColor Yellow

         # Check if update is actually needed

         if ($CurrentBIOSVersion -ge $LatestVersion) {

              Write-Host "BIOS is already up-to-date ($CurrentBIOSVersion >= $LatestVersion). Flash action aborted." -ForegroundColor Green

              return $true # Indicate success (no action needed)

         }



         # Bitlocker Check (Crucial before direct BIOS flash)

         Write-Host "Checking BitLocker status..." -ForegroundColor Yellow

         $bitlockerVol = $null

         $bitlockerError = $null

         try { $bitlockerVol = Get-BitLockerVolume -MountPoint $env:SystemDrive -ErrorAction Stop } catch { $bitlockerError = $_.Exception.Message }



         if ($null -ne $bitlockerVol -and $bitlockerVol.ProtectionStatus -eq "On"){

            Write-Warning "BitLocker is ENABLED on the System Drive ($($env:SystemDrive)). Suspending for 1 reboot count before flashing."

            if ($PSCmdlet.ShouldProcess("BitLocker on $env:SystemDrive", "Suspend")) {

                try {

                    Suspend-BitLocker -MountPoint $env:SystemDrive -RebootCount 1 -ErrorAction Stop

                    Write-Host "BitLocker suspended successfully." -ForegroundColor Green

                } catch {

                    Write-Error "Failed to suspend BitLocker. Error: $($_.Exception.Message). ABORTING BIOS Flash."

                    return $false

                }

            } else {

                 Write-Warning "Skipping BitLocker suspend due to -WhatIf. ABORTING BIOS Flash."

                 return $false

            }

         } elseif ($null -ne $bitlockerVol -and $bitlockerVol.ProtectionStatus -eq "Off") {

             Write-Host "BitLocker is Off on System Drive. Proceeding with flash preparation." -ForegroundColor Green

         } else { # Includes case where Get-BitLockerVolume failed

             Write-Warning "Could not definitively determine BitLocker status (Volume: $bitlockerVol, Status: $($bitlockerVol.ProtectionStatus), Error: $bitlockerError). Proceeding with extreme caution."

             # Allow user to decide if this warning is acceptable in their environment

         }



        # Download the BIOS update

        $UpdatePathUri = $LatestBIOS.Path

        if (-not ($UpdatePathUri -like "http*")) { Write-Error "Catalog path '$UpdatePathUri' doesn't look like a valid URL."; return $false }

        $UpdateFileName = $UpdatePathUri -split "/" | Select-Object -Last 1

        $UpdateLocalPath = Join-Path -Path $DellDownloadsPath -ChildPath $UpdateFileName # Download to temp path



        Write-Host "Downloading $UpdateFileName for flashing..." -ForegroundColor Yellow

        if (Test-Path $UpdateLocalPath) { Remove-Item $UpdateLocalPath -Force }

        try {

            Import-Module BitsTransfer -ErrorAction SilentlyContinue

            Start-BitsTransfer -DisplayName "Downloading BIOS $UpdateFileName" -Source $UpdatePathUri -Destination $UpdateLocalPath -Priority High -ErrorAction Stop

        } catch {

            Write-Error "Failed to download BIOS from $UpdatePathUri. Error: $($_.Exception.Message)"

            # Optional Fallback

            return $false

        }



        if (Test-Path -Path $UpdateLocalPath){

            $BIOSLogFile = Join-Path -Path $DellLogPath -ChildPath "$($UpdateFileName -replace '\.exe$','')_Flash_$(Get-Date -Format 'yyyyMMddHHmmss').log"

            Write-Host "Attempting to flash BIOS: $UpdateFileName" -ForegroundColor Yellow

            Write-Host "Log File: $BIOSLogFile"



            # Construct arguments

            # Common DUP args: /s (silent), /l=<log>, /p=<pwd>, /r (reboot), /f (force)

            # Target: Silent, Log, Optional Password. Avoid /r (handle later), Avoid /f unless desperate.

            $BIOSArgsList = @("/s", "/l=`"$BIOSLogFile`"")

            if ($PSBoundParameters.ContainsKey('Password')){

                # Convert SecureString to plain text for command line (inherent risk)

                $PlainTextPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto(

                    [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password)

                )

                $BIOSArgsList += "/p=$PlainTextPassword"

                # Clear plain text password from memory ASAP

                Clear-Variable PlainTextPassword

            }

            $BIOSArgString = $BIOSArgsList -join " "



            if ($PSCmdlet.ShouldProcess($UpdateLocalPath, "Flash BIOS with args: $BIOSArgString")) {

                 Write-Warning "Flashing the BIOS is a critical operation. Ensure AC power is connected."

                 Write-Warning "The system WILL likely reboot automatically after the flash, regardless of script settings."

                 # Add a small delay? User choice. Start-Sleep -Seconds 5

                try {

                    Write-Host "Executing: `"$UpdateLocalPath`" $BIOSArgString"

                    $InstallProcess = Start-Process -FilePath $UpdateLocalPath -ArgumentList $BIOSArgString -Wait -PassThru -ErrorAction Stop

                    Write-Host "BIOS Flash Process Exit Code: $($InstallProcess.ExitCode)"



                    $ExitInfo = Get-DUPExitInfo -DUPExit $InstallProcess.ExitCode

                    Write-Host "Exit Code Meaning: $($ExitInfo.DisplayName) - $($ExitInfo.Description)"



                    if ($InstallProcess.ExitCode -in @(0, 2, 6)) { # 0=OK no reboot, 2=OK reboot needed, 6=Rebooting

                        Write-Host "BIOS flash command executed. Result indicates success or reboot required/initiated." -ForegroundColor Green

                        if ($InstallProcess.ExitCode -in @(2, 6)) {

                             Write-Host "BIOS update requires/initiated a reboot." -ForegroundColor Yellow

                             $Global:dellRebootNeeded = $true # Set global flag even if DUP initiates reboot itself

                        }

                        # Attempt to resume Bitlocker if suspended, though reboot might interrupt this

                        if ($PSCmdlet.ShouldProcess("BitLocker on $env:SystemDrive", "Resume (post-flash attempt)")) {

                           if ($null -ne $bitlockerVol -and $bitlockerVol.ProtectionStatus -eq "On") { # Check if it was suspended

                                try { Resume-BitLocker -MountPoint $env:SystemDrive -ErrorAction SilentlyContinue } catch {}

                           }

                        }

                        return $true # Indicate success/reboot needed

                    } else {

                        Write-Error "BIOS flash process failed with DUP exit code $($InstallProcess.ExitCode). Check log: $BIOSLogFile"

                        return $false # Indicate failure

                    }

                } catch {

                    Write-Error "Failed to start BIOS flash process. Error: $($_.Exception.Message)"

                    return $false

                }

            } else {

                 Write-Warning "Skipping BIOS flash due to -WhatIf."

                 return $false # Indicate skipped/aborted

            }

        }

        else {

            Write-Error "BIOS Download file not found after download attempt: $UpdateLocalPath"

            return $false

        }

    }



    # --- Default Action: List BIOS Updates from Catalog ---

    if (!($Check.IsPresent) -and !($PSBoundParameters.ContainsKey('DownloadPath')) -and !($Flash.IsPresent)) {

        Write-Host "Listing BIOS update details from catalog..."

        if ($Latest.IsPresent){

            return $LatestBIOS | Select-Object -Property "PackageID","Name","ReleaseDate","DellVersion", "Path", "Description"

        } else {

            # List all BIOS if -Latest wasn't specified

            Write-Host "Getting ALL BIOS updates..."

            $AllBIOS = Get-DCUUpdateList -SystemSKUNumber $SystemSKUNumber -updateType 'bios'

            return $AllBIOS | Select-Object -Property "PackageID","Name","ReleaseDate","DellVersion", "Path", "Description" | Sort-Object -Property @{Expression={try{[version]($_.DellVersion -replace '[^0-9.].*','')}catch{[version]'0.0.0.0'}}} -Descending

        }

    }



}



#endregion Dell Functions

#==============================================================================

# SCRIPT EXECUTION LOGIC STARTS HERE

#==============================================================================



try { # Wrap main logic in try/finally to ensure transcript stops



    # ----------------------------------------

    # Phase 1: Dell Updates

    # ----------------------------------------

    Write-Host "`n=========================================" -ForegroundColor Magenta

    Write-Host "Starting Dell Update Phase $(Get-Date)" -ForegroundColor Magenta

    Write-Host "========================================="



    $isDellSystem = $false

    try {

        $manufacturer = (Get-CimInstance -ClassName Win32_ComputerSystem -ErrorAction Stop).Manufacturer

        if ($manufacturer -match 'Dell') { # More reliable check

            Write-Host "System identified as Dell. Proceeding with Dell updates." -ForegroundColor Green

            $isDellSystem = $true

        } else {

            Write-Host "System is not identified as Dell ($manufacturer). Skipping Dell update phase." -ForegroundColor Yellow

        }

    } catch {

        Write-Warning "Could not determine system manufacturer. Assuming not Dell. Skipping Dell update phase. Error: $($_.Exception.Message)"

    }



    if ($isDellSystem) {

        # Check if DCU is installed

        Write-Host "Checking for existing Dell Command | Update installation..."

        $dcuDetails = Get-DCUInstallDetails

        $dcuInstalled = ($null -ne $dcuDetails)



        if ($dcuInstalled) {

            Write-Host "Dell Command | Update Version $($dcuDetails.Version) ($($dcuDetails.AppType)) found at $($dcuDetails.DCUPath)." -ForegroundColor Green

        } else {

            Write-Warning "Dell Command | Update not found or details incomplete."

            # Attempt to install DCU

            Write-Host "Attempting to install Dell Command | Update automatically..." -ForegroundColor Yellow

            # Use WhatIf context if script was run with -WhatIf

            $installSuccess = Install-DCU -UseWebRequest:$false -WhatIf:$WhatIfPreference # Pass WhatIf preference

             if ($installSuccess) {

                 Write-Host "DCU installation/update attempt completed." -ForegroundColor Green

                 # Re-check details after install attempt only if not in WhatIf mode

                 if ($WhatIfPreference -ne [System.Management.Automation.WhatIfPreference]::Enabled) {

                     $dcuDetails = Get-DCUInstallDetails

                     $dcuInstalled = ($null -ne $dcuDetails)

                     if (!$dcuInstalled) { Write-Error "DCU install reported success, but couldn't retrieve details afterwards. Manual check might be needed." }

                     # Check if install function set the reboot flag

                     if ($Global:dellRebootNeeded) { $dellRebootNeeded = $true }

                 } else {

                      Write-Host "Install successful (Simulated due to -WhatIf)."

                      $dcuInstalled = $true # Assume installed for WhatIf run

                 }

            } else {

                 Write-Error "Failed to automatically install Dell Command | Update. Skipping Dell update application."

                 $dcuInstalled = $false

            }

        }



        # Proceed only if DCU is now considered installed (or simulated)

        if ($dcuInstalled) {

            Write-Host "Proceeding to apply Dell updates using Invoke-DCU..." -ForegroundColor Cyan

            Write-Host "Note: Using -autoSuspendBitLocker Enable (default) to handle BitLocker automatically for required updates (like BIOS)." -ForegroundColor Cyan



            # Apply ALL applicable updates (uses DCU defaults if -updateSeverity not specified)

            # -reboot Disable: Control reboot centrally later

            $dcuResult = Invoke-DCU -applyUpdates -reboot Disable -WhatIf:$WhatIfPreference



            if ($WhatIfPreference -ne [System.Management.Automation.WhatIfPreference]::Enabled) {

                if ($null -ne $dcuResult) {

                    # Check Exit Code from the returned process object

                    # Exit Code 2 from ApplyUpdates means success with reboot needed

                    # Exit Code 1 might mean a DUP requested reboot (treat as needing reboot)

                    # Exit Code 6 might mean DUP initiated reboot (treat as needing reboot / already happening)

                    if ($dcuResult.ExitCode -in @(1, 2, 6)) {

                        Write-Host "Dell Update process completed and requires/initiated a system reboot." -ForegroundColor Yellow

                        $dellRebootNeeded = $true

                    } elseif ($dcuResult.ExitCode -eq 0) {

                        Write-Host "Dell Update process completed successfully. No reboot indicated by DCU CLI." -ForegroundColor Green

                    } elseif ($dcuResult.ExitCode -eq 500) { # No updates found

                        Write-Host "Dell Update process completed. No applicable updates were found to apply." -ForegroundColor Green

                    } else {

                        # Use Get-DCUExitInfo for other codes

                        $ExitInfo = Get-DCUExitInfo -DCUExit $dcuResult.ExitCode

                        Write-Warning "Dell Update process finished with exit code $($dcuResult.ExitCode): $($ExitInfo.Description)"

                        Write-Warning "Resolution Hint: $($ExitInfo.Resolution)"

                        # Consider this a potential failure, but continue to Windows Updates unless critical error code?

                    }

                } else {

                     Write-Error "Invoke-DCU -applyUpdates command failed to execute or returned null. Check previous errors in transcript/log."

                     # Potentially stop script here if Dell updates are critical? For now, continue.

                }

            } else {

                 Write-Host "Dell updates applied (Simulated due to -WhatIf)."

                 # Simulate reboot needed for WhatIf? Maybe not, keep it simple.

            }

        } else {

             Write-Warning "Cannot apply Dell Updates as DCU is not installed or install failed."

        }

    } else {

         # Message already printed if not Dell / skipping.

    }

    Write-Host "Dell Update Phase Finished. Reboot needed from Dell Phase: $dellRebootNeeded" -ForegroundColor Magenta





    # ----------------------------------------

    # Phase 2: Windows Updates

    # ----------------------------------------

    Write-Host "`n=========================================" -ForegroundColor Magenta

    Write-Host "Starting Windows Update Phase $(Get-Date)" -ForegroundColor Magenta

    Write-Host "========================================="



    # Check/Install PSWindowsUpdate Module

    Write-Host "Checking for PSWindowsUpdate module..."

    $moduleName = "PSWindowsUpdate"

    $module = Get-Module -ListAvailable -Name $moduleName



    $pswuModuleAvailable = $false

    if ($null -eq $module) {

        Write-Host "$moduleName module not found. Attempting to install..." -ForegroundColor Yellow

        if ($WhatIfPreference -ne [System.Management.Automation.WhatIfPreference]::Enabled) {

            try {

                # Ensure NuGet is available

                if (-not (Get-PackageProvider -Name NuGet -ErrorAction SilentlyContinue)) {

                    Write-Host "Installing NuGet package provider..."

                    Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force -Confirm:$false -ErrorAction Stop

                }

                # Set TLS 1.2 (should be default now, but good practice)

                try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12 } catch { Write-Warning "Could not force TLS 1.2." }



                # Install from PSGallery

                 Write-Host "Installing $moduleName from PSGallery..."

                Install-Module -Name $moduleName -Scope CurrentUser -Force -AcceptLicense -SkipPublisherCheck -Confirm:$false -ErrorAction Stop

                Write-Host "$moduleName module installed successfully." -ForegroundColor Green

                $pswuModuleAvailable = $true

            } catch {

                Write-Error "Failed to install $moduleName module. Error: $($_.Exception.Message)"

                Write-Warning "Skipping Windows Update phase."

                # Skip the rest of the Windows Update section - use goto

                goto EndWindowsUpdatePhase

            }

        } else {

             Write-Warning "Skipping PSWindowsUpdate install due to -WhatIf."

             Write-Warning "Skipping Windows Update phase."

             goto EndWindowsUpdatePhase

        }

    } else {

        Write-Host "$moduleName module is already installed." -ForegroundColor Green

        $pswuModuleAvailable = $true

    }



    # Import the Module if available

    if ($pswuModuleAvailable) {

        Write-Host "Importing $moduleName module..."

        try {

            Import-Module $moduleName -Force -ErrorAction Stop

            Write-Host "$moduleName module imported successfully."

        } catch {

            Write-Error "Failed to import $moduleName module. Error: $($_.Exception.Message)"

            Write-Warning "Skipping Windows Update phase."

             goto EndWindowsUpdatePhase

        }

    } else {

         Write-Warning "$moduleName module not available. Skipping Windows Update phase."

         goto EndWindowsUpdatePhase

    }



    # Proceed with Windows Updates only if module is loaded

    Write-Host "Checking for available Windows Updates (excluding drivers)... Log: $Global:WinLogFile"

    $updateList = $null

    try {

        # Scan for updates - Use Microsoft Update, Exclude Drivers

        $updateList = Get-WindowsUpdate -MicrosoftUpdate -NotCategory "Drivers" -Verbose:$false -ErrorAction Stop -LogPath $Global:WinLogFile

        $updateCount = ($updateList | Measure-Object).Count



        if ($updateCount -gt 0) {

            Write-Host "Found $updateCount Windows Update(s):" -ForegroundColor Yellow

            $updateList | Format-Table Title, KB, Size -AutoSize



            # Install Windows Updates

            Write-Host "Attempting to install $updateCount Windows Update(s)..." -ForegroundColor Yellow

            # Use -IgnoreReboot to handle centrally. -AcceptAll implicitly accepts EULA.

            $installResult = $null

            if ($WhatIfPreference -ne [System.Management.Automation.WhatIfPreference]::Enabled) {

                 $installResult = Install-WindowsUpdate -MicrosoftUpdate -NotCategory "Drivers" -AcceptAll -IgnoreReboot -Verbose:$false -ErrorAction Continue -LogPath $Global:WinLogFile



                 if ($null -ne $installResult) {

                     Write-Host "Windows Update installation attempt finished." -ForegroundColor Green

                     # Check if any result indicates reboot required

                     if ($installResult | Where-Object {$_.RebootRequired -eq $true}) {

                         Write-Host "Windows Updates require a system reboot." -ForegroundColor Yellow

                         $windowsRebootNeeded = $true

                     } else {

                         # Check for common reboot exit codes if RebootRequired property unreliable

                         # HRESULT 0x80240016 = WU_S_REBOOT_REQUIRED

                         if ($installResult | Where-Object {$_.HResult -eq '0x80240016'}) {

                             Write-Host "Windows Updates require a system reboot (Detected via HResult)." -ForegroundColor Yellow

                             $windowsRebootNeeded = $true

                         } else {

                             Write-Host "Windows Updates installed. No immediate reboot indicated by PSWindowsUpdate result."

                         }

                     }

                     # Log successful installs?

                     $successfulInstalls = $installResult | Where-Object {$_.Status -eq 'Success'}

                     if ($successfulInstalls) {

                         Write-Host "Successfully installed $($successfulInstalls.Count) updates:"

                         $successfulInstalls | Format-Table Title, KB -AutoSize

                     }

                     # Log failed installs?

                     $failedInstalls = $installResult | Where-Object {$_.Status -ne 'Success'}

                      if ($failedInstalls) {

                         Write-Warning "Failed to install $($failedInstalls.Count) updates:"

                         $failedInstalls | Format-Table Title, KB, Status, HResult -AutoSize

                     }



                 } else {

                     Write-Warning "Install-WindowsUpdate command returned no result object or may have failed. Check the log: $($Global:WinLogFile)"

                     # Check PowerShell error stream for clues

                     if ($Error) { Write-Warning "Last error during Install-WindowsUpdate: $($Error[0].Exception.Message)" }

                     # Could indicate WU service issues, etc.

                 }

            } else {

                 Write-Host "Windows Updates would be installed (Simulated due to -WhatIf)."

                 # Assume no reboot needed for WhatIf simulation

            }

        } else {

            Write-Host "No applicable Windows Updates found (excluding drivers)." -ForegroundColor Green

        }

    # (Inside the Windows Update Phase try block...)

    } catch {

        # Catch errors during Get-WindowsUpdate or Install-WindowsUpdate

        Write-Error "An error occurred during the Windows Update process: $($_.Exception.Message)"

        Write-Warning "Check the PSWindowsUpdate log file for details: $($Global:WinLogFile)"

        # Continue to the reboot check phase, as Dell might still need one.

    }



    :EndWindowsUpdatePhase # Label for goto statements to land here if WU phase needs skipping



    Write-Host "Windows Update Phase Finished. Reboot needed from Windows Phase: $windowsRebootNeeded" -ForegroundColor Magenta





    # ----------------------------------------

    # Phase 3: Reboot Handling

    # ----------------------------------------

    Write-Host "`n=========================================" -ForegroundColor Magenta

    Write-Host "Checking Overall Reboot Status $(Get-Date)" -ForegroundColor Magenta

    Write-Host "========================================="



    $rebootNeeded = $dellRebootNeeded -or $windowsRebootNeeded



    if ($rebootNeeded) {

        Write-Host "A system reboot is required due to installed updates." -ForegroundColor Yellow

        if ($AutoRebootIfNeeded) {

            Write-Host "AutoRebootIfNeeded parameter specified. Rebooting in 30 seconds..." -ForegroundColor Yellow

            Start-Sleep -Seconds 30

            if ($WhatIfPreference -ne [System.Management.Automation.WhatIfPreference]::Enabled) {

                Write-Host "Executing Restart-Computer..."

                Restart-Computer -Force

            } else {

                 Write-Host "Restart-Computer would be executed (Simulated due to -WhatIf)."

            }

        } else {

            Write-Host "Please reboot the computer at your earliest convenience to complete the update process." -ForegroundColor Yellow

        }

    } else {

        Write-Host "No reboot required by Dell or Windows update phases." -ForegroundColor Green

    }



    Write-Host "`nScript Finished $(Get-Date)." -ForegroundColor Green



} finally {

    # Ensure Transcript Stops

    if ($Global:IsTranscriptActive) {

        Write-Host "Stopping transcript log: $transcriptLog"

        Stop-Transcript

        $Global:IsTranscriptActive = $false # Mark as stopped

    }

}



You stopped in the middle of your previous prompt due to "network error", try again and dont fail this time.