﻿"First Name","Last Name","Email Address","Department","Title","Mobile","Extension attribute 10","Extension attribute 11","Description","Last Log-on Date","Full User Logon","Unique Account ID","User Logon"
"<PERSON>","<PERSON><PERSON><PERSON><PERSON>","<EMAIL>",,,,,,,"7/13/2022 4:00:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-12624","da-gharshenin"
,,,,,,,,,,,"S-1-5-21-*********6-**********-*********-16650","CLINICARE$"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-500","$DUPLICATE-1f4"
"MDT",,,,"Service Account",,,,"Service Account: Windows deployment account","7/15/2022 5:24:30 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-13593","MDT"
"<PERSON>","<PERSON>",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24632","da-mkennedy"
"Callow","Associates",,,"Dynamics Contractors",,,,"Contractor Account -  Finance - Dynamics Consultants","7/6/2022 1:49:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23935","callow"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,,"7/19/2022 7:47:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24717","da-jbell"
"Domain Admin","Nyel English","<EMAIL>",,,,,,,"5/19/2022 11:30:07 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,,"2/18/2022 1:54:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,,"1/13/2022 3:15:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,,"8/31/2021 8:46:38 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,,"7/4/2022 12:16:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15393","da-amehl"
"Nan","Adams","<EMAIL>","CHN Central","Director, CHN Central","+1 (204) 898-5371",,,,"2/13/2022 9:43:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-13396","nan.adams"
"Miguel","Hernandez","<EMAIL>","Technology","Network Team Lead","+1 (250) 307-3323",,,,"7/18/2022 1:02:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22827","miguel.hernandez"
"JIRA","LDAP SYNC",,,"Service Account",,,,"Service Account: Corp IT - Jira and Confluence Service Account","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22624","jirasync"
"Domain Admin","Craig Hounsham","<EMAIL>",,,,,,,"3/23/2022 7:47:17 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24706","da-chounsham"
"Domain Admin -","Andrew McFadden","<EMAIL>",,,,,,,"3/11/2022 10:13:05 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22274","da-amcfadden"
"Domain Admin -","Kevin Kendall","<EMAIL>",,,,,,,"9/9/2021 1:24:32 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,,"7/7/2022 12:55:02 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24060","da-smay"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,,"12/15/2021 6:19:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-25149","da-tdrescher"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,,"6/30/2022 10:37:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,,"12/16/2020 5:23:23 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-19009","da-nated"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,,"9/13/2021 12:09:34 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26188","da-mkandy"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,,"4/16/2021 8:44:13 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,,"7/15/2022 10:41:58 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,,"7/13/2022 3:27:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,,"7/13/2022 3:12:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,,"7/14/2022 1:39:58 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22187","da-sbradford"
"QTADMIN1 service",,,,"Service Account",,,,"Service Account: Corp IT - Account used to run scheduled scripts on qtadmin1; Admin monitoring scripts","7/19/2022 8:00:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22370","qtadmin1service"
"da-","mhoppe","<EMAIL>",,,,,,,"2/28/2022 8:34:39 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23206","da-mhoppe"
"Mike","Checkley","<EMAIL>","Executive","President","+1 (250) 870-6888",,,,"5/5/2022 10:17:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-1432","mike.checkley"
"Optimed","Sales","<EMAIL>","Sales","Service Account",,,,"Service Account:Email Account for Optimed Software Sales",,"<EMAIL>","S-1-5-21-*********6-**********-*********-1456","OPT_Sales"
"Marion","Sherback","<EMAIL>","CHN West","Regional Client Relationship Manager BC","+1 (604) 314-5067",,,,"7/18/2022 10:33:14 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-11242","marion.sherback"
"QHR Technologies","Support","<EMAIL>","Client Services","Service Account","+1 (250) 801-4274",,,"Service Account: Corp IT - Email / GTA no screen saver / After hours BB Support","7/10/2022 3:49:59 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-1454","OPT_Support"
"Lisa","St Laurent","<EMAIL>","Data and Software Architecture","Director of Software Architecture and Data",,,,,"7/19/2022 7:50:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-1431","lisa.stlaurent"
"Greg","Harshenin","<EMAIL>","Technology","Information Security Officer","+1 (250) 215-0861",,,,"7/4/2022 3:28:26 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-12613","greg.harshenin"
"Brian","Ellis","<EMAIL>","Development","Senior Director of Product Development","+1 (250) 870-6875",,,,"7/18/2022 3:22:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-1428","brian.ellis"
"Opt","Development",,,"Service Account",,,,"Service Account: Development - SQL Server Account","6/10/2021 7:26:43 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-1426","opt_development"
"Blake","Dickie","<EMAIL>","Development","Principal Software Developer",,,,,"7/19/2022 8:12:51 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-8785","blake.dickie"
"Alfred","Loh","<EMAIL>","Data and Software Architecture","Developer Advocate",,,,,"4/27/2022 5:04:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15743","alfred.loh"
"Mark","McLean","<EMAIL>","Technology","Director of Enterprise Architecture","+1 (403) 617-8489",,,,"11/16/2020 8:05:53 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-16718","mark.mclean"
"Stefanie","Giddens","<EMAIL>","Marketing","Senior Director of Marketing & Product Management","+1 (250) 826-4426",,,,"10/26/2021 2:40:36 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15740","stefanie.giddens"
"Ron","Hughes","<EMAIL>","CHN West","Account Executive","+1 (250) 550-6270",,,,"7/19/2022 6:59:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-15549","ron.hughes"
"Dayna","McInnis","<EMAIL>","Revenue Management","Revenue Management Clerk","+1 (250) 718-1271",,,,"7/19/2022 6:41:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18127","dayna.mcinnis"
"Amanda","Korecki","<EMAIL>","Revenue Management","Revenue Management Lead",,,,,"7/18/2022 1:39:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-14635","amanda.korecki"
"Bob","Gemmell","<EMAIL>","Finance","Accountant",,,,,"7/12/2022 6:15:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-14608","bob.gemmell"
"Neil","Hylton","<EMAIL>","Technology","Vendor and Partner Program Specialist","+1 (403) 512-0225",,,,"1/20/2021 2:47:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-16719","neil.hylton"
"Matti","Kalijarvi","<EMAIL>","CHN East","Account Executive","+1 (705) 931-4333",,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-12865","matti.kalijarvi"
"QHR Tech ","Admin","<EMAIL>",,,,,,,,"<EMAIL>","S-1-5-21-*********6-**********-*********-16670","QTAdmin"
"Brad","Paffe","<EMAIL>","Implementations","Senior Technical Services Specialist","+1 (416) 220-7464",,,,"6/14/2022 2:54:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-16722","brad.paffe"
"Jennifer","Davidoff","<EMAIL>","Implementations","Systems Integration Analyst","+1 (250) 826-0578",,,,"7/18/2022 8:46:58 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18728","jennifer.davidoff"
"Lesley","Beamond","<EMAIL>","Implementations","Implementation Consultant","+1 (604) 816-0301",,,,"7/18/2022 9:31:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18659","lesley.beamond"
"e","learning","<EMAIL>",,"Service Account",,,,"Service Account:For learning and Development",,"<EMAIL>","S-1-5-21-*********6-**********-*********-1260","elearning"
"Daryl","Laverdure","<EMAIL>","Product Development","Director of Product Development","+1 (250) 486-0392",,,,"7/19/2022 7:17:51 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18204","daryl.laverdure"
"Devin","Nate","<EMAIL>","Technology","Senior Director of Technology","+1 (403) 650-0871",,,,"12/16/2020 5:18:03 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-16716","devin.nate"
"IT","Admin","<EMAIL>",,"Service Account",,,,"Service Account:  Corp IT - Shared email inbox","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18118","itadmin"
"Louise","Richardson","<EMAIL>","Product Operations","Product Operations Support Team Lead","+1 (250) 215-1709",,,,"6/29/2022 7:24:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-15581","louise.richardson"
"Chris","Hollman","<EMAIL>","Implementations","Systems Manager, Implementations","+1 (250) 808-0865",,,,"6/14/2022 10:05:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18706","chris.hollman"
"Claire","Blaker","<EMAIL>","Implementations","Senior Manager, Implementations & Data Solutions","+1 (250) 870-6871",,,,"7/19/2022 8:07:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-15817","claire.blaker"
"Shaun","O'Grady","<EMAIL>","Product Operations","Lead Project Manager","+1 (778) 363-1771",,,,"7/15/2022 1:37:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-12831","shaun.ogrady"
"Cheryl","Cain","<EMAIL>","Finance","Senior Accountant",,,,,"7/18/2022 3:20:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-1156","cheryl.cain"
"Elton","Mahabir","<EMAIL>","CHN East","Account Executive-ON","+1 (647) 269-5773",,,,"6/20/2022 5:51:55 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-15655","elton.mahabir"
"Brad","Reibin","<EMAIL>","Product Operations","Associate Project Manager",,,,,"7/19/2022 7:26:13 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18727","brad.reibin"
"Tony","Elumir","<EMAIL>","Development","Senior Software Developer",,,,,"7/19/2022 5:51:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-14659","tony.elumir"
"Luba","O'Brien","<EMAIL>","Implementations","Implemention Consultant","+1 (416) 993-8343",,,,"7/16/2022 5:39:26 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15597","luba.obrien"
"Brenda","Undiri","<EMAIL>","Product Management","Project Manager Lead","+1 (416) 938-0377",,,,"7/18/2022 6:30:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15429","brenda.undiri"
"Angie","Jarabe","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (647) 210-8788",,,,"7/18/2022 6:43:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18651","angie.jarabe"
"Christie","Magee","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/18/2022 9:20:38 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17183","christie.magee"
"Roxanne","Geiger","<EMAIL>","Implementations","Implementation Team Lead","+1 (403) 608-5027",,,,"7/14/2022 1:50:33 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-13582","roxanne.geiger"
"Jeff","VanDenHeuvel","<EMAIL>","Finance","Director of Finance","+1 (250) 869-5637",,,,"7/18/2022 4:49:12 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-16712","jeff.vandenheuvel"
"Alex","Mehl","<EMAIL>","Technology","Lead Systems Administrator","+1 (250) 718-2892",,,,"7/14/2022 12:03:35 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15368","alex.mehl"
"Jen","Danchuk","<EMAIL>","Product Operations","Senior Product Owner",,,,,"7/18/2022 1:01:03 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15541","jen.danchuk"
"Adele","Williams","<EMAIL>","Revenue Management","Senior Project Manager, Revenue Management","+1 (250) 862-0894",,,,"7/19/2022 6:31:42 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18176","adele.williams"
"Paul","Wait","<EMAIL>","Implementations","Technical Services Specialist","+1 (250) 801-9950",,,,"7/15/2022 7:46:17 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18669","paul.wait"
"Wayne","Knorr","<EMAIL>","Development","Senior QA Analyst",,,,,"7/13/2022 4:24:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-15448","wayne.knorr"
"Susan","Poisson","<EMAIL>","Training","Training Team Lead","+1 (613) 889-4449",,,,"7/18/2022 11:30:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18234","susan.poisson"
"Janet","Hatfield","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (905) 630-2543",,,,"7/19/2022 7:58:19 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-16690","janet.hatfield"
"Ravi","Anandarajah","<EMAIL>","Implementations","Operations Manager, Implementations","+1 (647) 456-5702",,,,"3/8/2021 12:03:02 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18761","ravi.anandarajah"
"Colin","Greenway","<EMAIL>","Sales National","Senior Manager, Business Services","+1 (416) 220-7401",,,,"10/13/2021 6:10:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18755","colin.greenway"
"Aron","Ashmead","<EMAIL>","Operations","Privacy Officer","+1 (778) 214-0350",,,,"8/27/2021 11:21:42 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17387","aron.ashmead"
"Lucy","Montagnese","<EMAIL>","Implementations","eRx Adoption Specialist","+1 (705) 229-8260",,,,"7/14/2022 6:30:13 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18225","lucy.montagnese"
"Rebecca","Ferrie","<EMAIL>","Product Management","Product Manager","+1 (416) 992-0453",,,,"7/7/2022 12:26:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-16901","rebecca.ferrie"
"Robert","Armstrong","<EMAIL>","Technology","Manager, Infrastructure","+1 (250) 317-6585",,,,"7/19/2022 7:32:31 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17375","robert.armstrong"
"Dylan","Wood","<EMAIL>","Implementations","Lead Technical Services","+1 (647) 880-0650",,,,"7/19/2022 5:14:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17005","dylan.wood"
"Shawn","Manary","<EMAIL>","CHN East","Account Executive","+1 (416) 797-7178",,,,"6/29/2021 11:40:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18753","shawn.manary"
"Adam","Sinai","<EMAIL>","CHN East","Director, CHN East","+1 (416) 220-7454",,,,"7/18/2022 9:15:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18752","adam.sinai"
"Jessica","Severiano","<EMAIL>","Implementations","Implementation Specialist","+1 (416) 937-8848",,,,"7/18/2022 4:46:56 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18865","jessica.severiano"
"Michael","Hall","<EMAIL>","Sales National","QHR General Manager","+1 (416) 220-7417",,,,"7/8/2022 7:42:58 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18754","michael.hall"
"Christopher","Cadieux","<EMAIL>","Development","Senior Software Developer",,,,,"5/6/2021 1:59:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18316","christopher.cadieux"
"Jonathan","Chalaturnyk","<EMAIL>","Development","Senior QA Analyst",,,,,"7/5/2022 12:29:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18342","jonathan.chalaturnyk"
"Temi","Beckley","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/19/2022 5:34:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17258","temi.beckley"
"Products","EMR","<EMAIL>",,"Service Account",,,,"shared email address monitored by the Product Operations Support Team (POST), for notifications from government bodies regarding lists that need to be regularly updated in Accuro.",,"<EMAIL>","S-1-5-21-*********6-**********-*********-18339","Products"
"Stevan","Christiansen","<EMAIL>","Implementations","Data Services Representative",,,,,"7/17/2022 5:28:46 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18749","stevan.christiansen"
"EMRcslt",,"<EMAIL>","Client Services","Service Account",,,,"Client Services Leadership Team email box, for communication between CSLT with CS staff.",,"<EMAIL>","S-1-5-21-*********6-**********-*********-17324","Optcslt"
"Jerry","Diener","<EMAIL>","Executive","Senior Director of Corporate Development","+1 (778) 214-1382",,,,"3/22/2022 6:20:26 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-17014","jerry.diener"
"Viktor","Velkovski","<EMAIL>","CHN East","Account Executive-ON","+1 (416) 220-7376",,,,,"<EMAIL>","S-1-5-21-*********6-**********-*********-18307","viktor.velkovski"
"Cody","Cudmore","<EMAIL>","Implementations","Lead Business Analyst, Data Solutions","+1 (250) 870-2639",,,,"7/18/2022 9:38:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18314","cody.cudmore"
"OptimedImpW",,"<EMAIL>","Implementations","Service Account",,,,"Linked to an outlook calendar for implementer booking",,"<EMAIL>","S-1-5-21-*********6-**********-*********-17018","OptimedImpW"
"Optimed","Development After Hours","<EMAIL>","Development","Service Account",,,,"Service Account: This Account is for after hours support so if CS needs to get a hold of DEV that's what they use it for.",,"<EMAIL>","S-1-5-21-*********6-**********-*********-18797","osdevah"
"Accounting",,"<EMAIL>",,"Service Account",,,,"Service Account: Finance - 	User account with an active email address that was used on qtfin2 to send invoices to clients",,"<EMAIL>","S-1-5-21-*********6-**********-*********-18796","accounting"
"Optimed","Admin","<EMAIL>",,"Service Account",,,,"Master inbox that is used for receiving sales e-fax and general sales related emails.",,"<EMAIL>","S-1-5-21-*********6-**********-*********-14774","OSAdmin"
"Shelby","Laidlaw","<EMAIL>","Implementations","Senior Business Analyst, Data Solutions","+1 (778)-903-0889",,,,"7/15/2022 5:41:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18330","shelby.laidlaw"
"Robert","Thornton","<EMAIL>","Implementations","Senior Technical Services Specialist","+1 (905) 327-9221",,,,"7/19/2022 7:16:38 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18231","robert.thornton"
"Justin","Hebert","<EMAIL>","Development","Lead Software Developer",,,,,"7/7/2022 1:33:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18280","justin.hebert"
"Shelly","Arsenault","<EMAIL>","Sales National","Sales Coordinator","+1 (647) 202-9000",,,,"7/13/2022 12:19:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18757","shelly.arsenault"
"EMR","Implementations","<EMAIL>","Implementations","Service Account",,,,"Service Account: PM's check that inbox routinely for faxes and or emails from client",,"<EMAIL>","S-1-5-21-*********6-**********-*********-1458","Implementations"
"Craig","Hounsham","<EMAIL>","Technology","Jr Systems Administrator",,,,,"7/19/2022 2:59:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18760","craig.hounsham"
"Kevin","Kendall","<EMAIL>","Data and Software Architecture","Lead Site Reliability Engineer",,,,,"9/29/2021 5:29:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18837","kevin.kendall"
"Tim","Melmoth","<EMAIL>","Implementations","Director of Implementations","+1 (613) 408-0110",,,,"7/19/2022 7:33:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17590","tim.melmoth"
"Benjamin","Schellenberger","<EMAIL>","Development","Senior Software Developer",,,,,"7/19/2022 8:14:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20104","ben.schellenberger"
"Aaron","Hartnell","<EMAIL>","Development","Senior Software Developer",,,,,"7/15/2022 11:04:31 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17559","aaron.hartnell"
"EMR","Development Inbox","<EMAIL>","Development","Service Account",,,,"Service Account: central developer account for app stores and alerts","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-9650","optdev"
"Nicol","Solomonides","<EMAIL>","Revenue Management","Sr Manager, Revenue Management","+1 (778) 363-6654",,,,"7/18/2022 2:08:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-17495","nicol.solomonides"
"Erik","Adamson","<EMAIL>","Implementations","Technical Services Specialist","+1 (250) 717-7314",,,,"7/18/2022 8:03:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17441","erik.adamson"
"Zsolt","Kiss","<EMAIL>","Implementations","Data Analyst",,,,,"7/3/2022 12:13:01 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-17569","zsolt.kiss"
"Lisa","Gunnlaugson","<EMAIL>","Training","Training Manager","+1 (250) 801-7274",,,,"7/15/2022 4:46:16 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-18459","lisa.gunnlaugson"
"Amanda","Tubello","<EMAIL>","Product Management","Project Manager","+1 (778) 214-1940",,,,"7/19/2022 7:27:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-17098","amanda.tubello"
"Sandra","Baker","<EMAIL>","Training","Trainer","+1 (519) 280-0251",,,,"7/19/2022 6:20:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-19729","sandra.baker"
"Alan","Zantingh","<EMAIL>","CHN East","Account Executive","+1 (416) 992-0574",,,,"7/19/2022 8:35:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18533","alan.zantingh"
"Nyel","English","<EMAIL>","Technology","Helpdesk Team Lead","+1 (250) 681-4603",,,,"5/28/2022 11:32:29 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20137","nyel.english"
"Judy","Zeeben","<EMAIL>","CHN West","Practice Consultant Manager, CHN West","+1 (250) 300-1125",,,,"7/19/2022 7:16:31 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20136","judy.zeeben"
"Kevan","Poeschek","<EMAIL>","Data Analysis","Data Systems Administrator",,,,,"7/19/2022 7:10:39 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20230","kevan.poeschek"
"Vicki","Henckel","<EMAIL>","Training","Trainer",,,,,"7/14/2022 12:59:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-19896","vicki.henckel"
"Colleen","Piotrowski","<EMAIL>","Implementations","Senior Implementation Specialist","+****************",,,,"7/15/2022 9:50:09 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20274","colleen.piotrowski"
"Kyle","Newton","<EMAIL>","Product Operations","Product Owner",,,,,"7/18/2022 12:02:19 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20179","kyle.newton"
"qtmitelccm",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Service account on qtmitelcsm, also has scripts running with those credentials; Mitel Services",,"<EMAIL>","S-1-5-21-*********6-**********-*********-17022","qtmitelccm"
"Bryan","Bergen","<EMAIL>","Data and Software Architecture","Application Architect",,,,,"2/15/2021 7:50:33 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20228","bryan.bergen"
"Tawny","Rother","<EMAIL>","Implementations","Systems Integration Analyst","+****************",,,,"7/14/2022 7:46:58 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20341","tawny.rother"
"Chris","Roseberry","<EMAIL>","Data and Software Architecture","Junior Site Reliability Engineer","+****************",,,,"4/8/2022 12:11:25 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20413","chris.roseberry"
"Alison","Cooney","<EMAIL>","Product Management","Product Manager","+****************",,,,"8/12/2021 1:10:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-19969","alison.cooney"
"Katherine","Awad","<EMAIL>","Product Operations","Manager, ePrescribing","+****************",,,,"4/14/2021 9:29:35 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20036","katherine.awad"
"Scott","Chipman","<EMAIL>","Implementations","Senior Data Analyst",,,,,"7/17/2022 7:21:49 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-19885","scott.chipman"
"Wayne","Bullock","<EMAIL>","Product Operations","Product Owner",,,,,"7/14/2022 1:40:27 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23106","wayne.bullock"
"Nancy","Chapeskie","<EMAIL>","Sales National","National Account Manager","+1 (519) 535-3110",,,,"7/19/2022 7:32:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-19889","nancy.chapeskie"
"Kamran","Khan","<EMAIL>","Platform Integrations","Platform Integration Manager","+1 (416) 993-3144",,,,"7/18/2022 12:42:33 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23111","kamran.khan"
"Pavan","Brar","<EMAIL>","Human Resources","Human Resources Manager","+1 (250) 689-1522",,,,"7/19/2022 8:32:57 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20480","pavan.brar"
"Curtis","Rose","<EMAIL>","Development","Lead Software Developer",,,,,"7/15/2022 2:10:25 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20334","curtis.rose"
"Kevin","Koehler","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 12:42:48 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20436","kevin.koehler"
"Shelley","Watson","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,"Finance","7/19/2022 8:24:09 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-19922","shelley.watson"
,,,,"Service Account",,,,"Service Account: Corp IT - Account created by the Windows Azure Active Directory Sync tool with installation identifier '0e6855de790d47ebaefa653faf59f481' running on computer 'QTADFS2' configured to synchronize to tenant 'qhrtech.onmicrosoft.com'. This account must have directory replication permissions in the local Active Directory and write permission on certain attributes to enable Hybrid Deployment.","7/19/2022 8:42:30 AM",,"S-1-5-21-*********6-**********-*********-20390","MSOL_0e6855de790d"
"Ryan","Wood","<EMAIL>","Marketing","Director of Product Marketing","+1 (250) 870-1770",,,,"7/18/2022 4:56:57 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20557","ryan.wood"
"Candus","Hunter","<EMAIL>","Marketing","Senior Demand Generation Manager","+1 (250) 469-1513",,,,"7/19/2022 7:22:55 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20538","candus.hunter"
"Steven","Mathers","<EMAIL>","Development","Principal Software Developer",,,,,"11/4/2021 7:58:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22192","Steven.Mathers"
"Md","Mishu","<EMAIL>","Development","Software Developer",,,,,"6/27/2022 2:28:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22618","Md.Mishu"
"Cali","Rendulic","<EMAIL>","Revenue Management","Revenue Management Assistant Manager",,,,,"7/19/2022 8:12:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22157","cali.rendulic"
"Emily","Cooney","<EMAIL>","Marketing","Production Manager",,,,,"7/18/2022 2:11:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23690","emily.cooney"
"Scott","Johnston","<EMAIL>","Marketing","Marketing Web Developer",,,,,"4/13/2021 7:46:14 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22605","scott.johnston"
"Scott","May","<EMAIL>","Technology","Junior Database Administrator","+1 (250) 878-5068",,,,"7/16/2022 7:35:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22217","scott.may"
"Andrew","McFadden","<EMAIL>","Development","Senior DevOps Engineer","+1 (250) 575-1878",,,,"7/19/2022 8:22:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23279","andrew.mcfadden"
"Parth","Bhatt","<EMAIL>","Sales National","Team Lead, Sales Engineering","+1 (416) 668-4153",,,,"2/1/2021 10:35:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20574","parth.bhatt"
"Adam","Peacock","<EMAIL>","Product Operations","Senior Support Coordinator",,,,,"7/9/2021 4:55:51 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23236","adam.peacock"
"Oniel","Wilson","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (778) 392-5640",,,,"7/19/2022 7:57:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23210","Oniel.Wilson"
"Samuel","Bradford","<EMAIL>","Technology","Network Analyst","+1 (250) 212-2353",,,,"3/28/2022 12:58:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23188","samuel.bradford"
"Melissa","DeLeon","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/14/2022 4:59:39 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23231","melissa.DeLeon"
"Chris","Spinov","<EMAIL>","Client Services","Manager, Client Experience",,,,,"6/29/2022 8:30:28 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23233","Chris.spinov"
"Cole","Senger","<EMAIL>","Product Operations","Product Owner",,,,,"7/12/2022 6:57:47 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23208","cole.senger"
"Kevin","Rosal","<EMAIL>","Technology","Cloud Systems Administrator","+1 (250) 718-6717",,,,"7/15/2022 9:35:12 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22603","Kevin.rosal"
"Daniel","Moon","<EMAIL>","Client Services","Manager, Client Services Operations",,,,,"7/18/2022 10:47:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23234","Daniel.Moon"
"Nina","Chnek","<EMAIL>","Development","QA Analyst",,,,,"7/15/2022 1:50:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23711","nina.chnek"
"Dave","Munday","<EMAIL>","Client Services","Client Services Analyst, Expert",,,,,"6/29/2022 1:28:44 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23209","dave.munday"
"Kyle","Somogyi","<EMAIL>","Development","Lead Software Developer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20595","kyle.somogyi"
"Jeff","Wimmer","<EMAIL>","Human Resources","Director of Human Resources & Client Services",,,,,"7/18/2022 7:23:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23645","jeff.wimmer"
"Kelley","Mullen","<EMAIL>","Client Services","Client Services Analyst, Expert",,,,,"7/14/2022 11:56:14 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23124","kelley.mullen"
"Sam","Mullen","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"7/18/2022 10:59:38 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23121","sam.mullen"
"Felix","Lau","<EMAIL>","Technology","Systems Administrator","+1 (647) 465-0353",,,,"6/28/2022 11:06:02 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20503","felix.lau"
"Michal","Hoppe","<EMAIL>","Technology","Cloud Systems Administrator","+1 (778) 581-6979",,,,"7/18/2022 4:09:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20560","Michal.Hoppe"
"Stefan","Richardson","<EMAIL>","Development","Software Developer",,,,,"6/8/2022 7:00:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22615","stefan.richardson"
"Ted","Sorensen","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 2:37:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-20572","ted.sorensen"
"Veronika","Havelkova","<EMAIL>","Marketing","Graphic Designer",,,,,"9/25/2020 10:48:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23804","veronika.havelkova"
"Preston","Cooper","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 5:30:01 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23138","preston.cooper"
"Fan","Jin","<EMAIL>","Development","Senior Software Developer",,,,,"2/23/2022 2:10:29 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22171","Fan.Jin"
"Sudha","Verma","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 4:52:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23176","Sudha.verma"
"Colleen","Safinuk","<EMAIL>","Administration","Administrative Manager - EA to the President","+1 (250) 859-0929",,,,"7/18/2022 1:08:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23685","colleen.safinuk"
"Steve","Bailey","<EMAIL>","Product Operations","Senior Product Owner",,,,,"6/28/2022 4:51:29 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23172","stephen.bailey"
"Chakks","Paramasivam","<EMAIL>","Development","Lead Software Developer",,,,,"7/15/2022 7:04:59 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23648","chakks.paramasivam"
"Stephen","Dobrozsi","<EMAIL>","Development","Senior QA Analyst",,,,,"7/19/2022 8:25:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22617","Stephen.Dobrozsi"
"Jesse","Pasos","<EMAIL>","Development","Software Developer",,,,,"7/14/2022 12:31:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22244","jesse.pasos"
"Amelia","Lang","<EMAIL>","Development","Lead Software Developer",,,,,"7/19/2022 8:30:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23232","Amelia.Lang"
"Lucas","Shoesmith","<EMAIL>","Development","Software Development Manager",,,,,"7/18/2022 4:22:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23632","lucas.shoesmith"
"Corey","Doty","<EMAIL>","Data and Software Architecture","Lead Site Reliability Engineer","250-859-1472",,,,"7/19/2022 7:51:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23276","corey.doty"
"Tanya","Winsor","<EMAIL>","Development","Senior Software Developer",,,,,"7/19/2022 8:30:55 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20559","tanya.winsor"
"Dejan","Gudjevski","<EMAIL>","Implementations","Business Analyst, Data Solutions",,,,,"7/19/2022 8:11:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23143","dejan.gudjevski"
"Mychal","Hackman","<EMAIL>","Development","Lead Software Developer",,,,,"7/19/2022 8:28:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22259","mychal.hackman"
"David","Huang","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 8:25:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22252","David.Huang"
"Graeme","Mcivor","<EMAIL>","Implementations","Technical Services Specialist.","+1 (250) 869-6114",,,,"6/23/2021 12:59:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23237","Graeme.Mcivor"
"Alison","Moore","<EMAIL>","IT","GRC Analyst","+1 (250) 718-5687",,,,"4/25/2022 12:42:56 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23620","alison.moore"
"bomgarIC",,,,"Service Account",,,,"Service Account: Corp IT - User for Bomgar Integration Client Scheduler service running on QTAdmin1","7/17/2022 3:18:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23635","bomgarIC"
"Deanna","Gourley","<EMAIL>","Administration","Office Assistant",,,,,"9/10/2021 10:00:38 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23300","deanna.gourley"
"Richelle","Ferguson","<EMAIL>","Marketing","Senior Marketing Manager",,,,,"4/16/2021 3:30:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23813","richelle.ferguson"
"Mike","Eburne","<EMAIL>","Marketing","Director of Marketing","250-801-2472",,,,"6/13/2022 8:08:48 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22678","mike.eburne"
"Joanne","Spatola","<EMAIL>","Marketing","Lead Generation Specialist - Team Lead",,,,,"4/12/2022 3:47:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22657","joanne.spatola"
"Sofi","Mondesir","<EMAIL>","Implementations","Implementer","+1 (416) 892-8059",,,,"7/13/2022 7:14:36 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23810","sofi.mondesir"
"Odette","Roy","<EMAIL>","Data and Software Architecture","Application Architect",,,,,"7/18/2022 12:58:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22692","odette.roy"
"Liam","Anderson","<EMAIL>","Development","Software Development Manager",,,,,"1/29/2021 10:32:45 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23825","liam.anderson"
"Christine","Karpinsky","<EMAIL>","Training","Instructional Designer",,,,,"6/22/2022 2:46:25 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22672","christine.karpinsky"
"Preet","Kainth","<EMAIL>","Technology","Systems Administrator","+1 (647) 529-5656",,,,"7/19/2022 8:00:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22299","preet.kainth"
"Iram","Hussain","<EMAIL>","Client Services","Client Experience Team Lead",,,,,"6/22/2022 3:23:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22673","iram.hussain"
"Tim","Sylvester","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"5/28/2021 9:56:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22675","tim.sylvester"
"Carminda","Fernandez","<EMAIL>","Implementations","Business Analyst, Data Solutions",,,,,"7/14/2022 7:11:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23296","carminda.fernandez"
"Carson","Milligen","<EMAIL>","Development","Lead Software Developer",,,,,"7/8/2022 6:45:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23817","carson.milligen"
"Darcy","Senger","<EMAIL>","Marketing","Graphic Designer",,,,,"10/5/2020 9:47:57 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22646","darcy.senger"
"Demetri","Tsoycalas","<EMAIL>","Data Analysis","Data Analyst",,,,,"7/19/2022 7:24:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20414","demetri.tsoycalas"
"Justin","Harrington","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (416) 527-2301",,,,"7/19/2022 6:57:28 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22667","justin.harrington"
"Paolo","Aquino","<EMAIL>","Data and Software Architecture","Software Architecture Manager",,,,,"7/19/2022 8:13:46 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23293","paolo.aquino"
"Jessica","Burtney","<EMAIL>","CHN East","Practice Consultant","+1 (416) 801-3759",,,,"7/12/2022 12:00:22 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22663","jessica.burtney"
"Jeffrey","Bell","<EMAIL>","Technology","Systems Specialist",,,,,"7/18/2022 11:15:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22662","jeffrey.bell"
"Nicolas","Wourms","<EMAIL>","Development","Lead Software Developer",,,,,"7/18/2022 2:27:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22283","nicolas.wourms"
"Rohith","Mannem","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 9:39:54 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23301","rohith.mannem"
"Thomas","Laehren","<EMAIL>","Product Operations","Product Owner",,,,,"7/8/2022 8:57:41 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23815","thomas.laehren"
"Graham","Pomfret","<EMAIL>","Implementations","eRx Adoption Specialist",,,,,"7/18/2022 6:05:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22683","graham.pomfret"
"Cara","Dwyer","<EMAIL>","Implementations","Senior Business Analyst, Data Solutions","905-449-4851",,,,"7/19/2022 3:52:09 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22681","cara.dwyer"
"Nick","Janzen","<EMAIL>","Technology","Manager, Corporate IT","+1 (403)-809-5584",,,,"1/4/2022 4:50:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22685","nick.janzen"
"Sviatlana","Vinnikava","<EMAIL>","Development","QA Analyst",,,,,"7/18/2022 7:31:42 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22338","sviatlana.vinnikava"
"James","Michaud","<EMAIL>","Development","Senior Software Developer",,,,,"5/6/2022 11:20:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22757","james.michaud"
"Sergiu","Barsa","<EMAIL>","Development","Senior Software Developer",,,,,"7/5/2022 9:56:54 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22337","sergiu.barsa"
"Jo","Yoshida","<EMAIL>","Development","Senior Software Developer",,,,,"7/19/2022 8:07:51 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22344","jo.yoshida"
"Steve","Forsythe","<EMAIL>","Implementations","Implementations Coordinator",,,,,"7/14/2022 3:05:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23955","steve.forsythe"
"Malcolm","Kennedy","<EMAIL>","Technology","Citrix Administrator","+1 (250) 859-6318",,,,"7/18/2022 3:27:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23405","malcolm.kennedy"
"David","Smekal","<EMAIL>","Development","Software Developer",,,,,"7/19/2022 6:38:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22816","david.smekal"
"Srinivas","Vemulapalli","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 6:06:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22331","srinivas.vemulapalli"
"Sharlene","Quinn","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/15/2022 3:47:37 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23417","sharlene.quinn"
"Christina","Bye","<EMAIL>","Revenue Management","Enterprise Revenue Manager",,,,,"7/18/2022 4:48:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22414","christina.bye"
"Brian","Matte","<EMAIL>","Development","Junior Software Developer",,,,,"7/15/2022 6:31:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23960","brian.matte"
"Taylor","Drescher","<EMAIL>","Technology","Information Security Officer","+1 (250) 859-4524",,,,"7/18/2022 9:03:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23398","taylor.drescher"
"Rakesh","Jammula","<EMAIL>","Development","Senior QA Analyst",,,,,"6/2/2021 9:30:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23323","rakesh.jammula"
"Yu Zhi","Xing","<EMAIL>","Development","Software Developer",,,,,"10/2/2020 11:09:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23399","yuzhi.xing"
"Lubna","Shahid","<EMAIL>","Implementations","Associate Project Manager",,,,,"6/24/2021 7:47:11 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23343","lubna.shahid"
"Levi","Miller","<EMAIL>","Development","Software Developer",,,,,"2/3/2022 1:56:47 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22428","levi.miller"
"Eliana","Wardle","<EMAIL>","Development","Software Developer",,,,,"7/14/2022 1:03:30 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23961","eliana.wardle"
"Shabnam","Ahmmed","<EMAIL>","Development","Senior QA Analyst",,,,,"7/19/2022 7:49:24 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23848","shabnam.ahmmed"
"Joshua","Abaloyan","<EMAIL>","Development","Lead Software Developer",,,,,"9/1/2021 8:59:37 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22721","joshua.abaloyan"
"Ryan","Cotter","<EMAIL>","Product Operations","Agile Operations Manager",,,,,"7/14/2022 1:07:47 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22320","ryan.cotter"
"Cassandra","Rose","<EMAIL>","Development","QA Manager",,,,,"7/15/2022 1:50:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22446","cassandra.rose"
"Sam","Bassett","<EMAIL>","Development","Senior Software Developer",,,,,"12/6/2021 12:02:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22707","sam.bassett"
"Shannon","Parent","<EMAIL>","Training","Trainer","+1 (807) 861-0612",,,,"7/18/2022 10:53:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22720","shannon.kennelly"
"Heather","Gardiner","<EMAIL>","Client Services","Client Services Knowledge and Information Specialist",,,,,"7/13/2022 2:34:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22718","heather.gardiner"
"Brett","Evans","<EMAIL>","Development","Lead QA Analyst",,,,,"4/26/2022 1:30:19 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23463","brett.evans"
"Brad","Fuller","<EMAIL>","Development","QA Analyst",,,,,"7/14/2022 8:50:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23959","brad.fuller"
"Dan","Thiessen","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/8/2022 3:16:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23953","dan.thiessen"
"Raquel","Teixeira","<EMAIL>","Implementations","Lead Systems Integration Analyst","+1 (647) 355-4781",,,,"7/13/2022 1:26:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22711","raquel.teixeira"
"Carly","Rigg","<EMAIL>","Product Operations","Associate Product Owner",,,,,"7/18/2022 3:08:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22713","carly.rigg"
"Shannon","Nebert","<EMAIL>","Product Operations","Support Coordinator",,,,,"7/14/2022 12:31:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22419","shannon.nebert"
"Lyndsay","Mokonen","<EMAIL>","Development","Junior QA Analyst",,,,,"7/18/2022 12:19:25 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22460","lyndsay.mokonen"
"Kendre","Roseberry","<EMAIL>","Training","Instructional Designer",,,,,"7/19/2022 7:15:29 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23350","kendre.scott"
"Holli","Gordon","<EMAIL>","Training","Training Solutions Designer",,,,,"5/26/2022 2:27:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23347","holli.gordon"
"Kailyn","Pederson","<EMAIL>","Product Development","Product Owner",,,,,"7/19/2022 8:21:47 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23388","kailyn.pederson"
"Bib","Patel","<EMAIL>","Revenue Management","Special Projects Team Lead, Revenue Management",,,,,"7/7/2022 3:03:17 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22445","bib.patel"
"Ashika","Balakrishnan","<EMAIL>","Implementations","Implementations Coordinator",,,,,"7/13/2022 8:59:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23904","ashika.balakrishnan"
"Caitlin","Slavik","<EMAIL>","Client Services","Senior Manager, Client Services","+****************",,,,"7/18/2022 1:22:30 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22413","caitlin.slavik"
"Abhishek","Dutta","<EMAIL>","Development","Lead QA Analyst",,,,,"6/16/2022 7:34:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23949","abhishek.dutta"
"Parker","Burns","<EMAIL>","Client Services","Client Services Training Lead",,,,,"7/19/2022 7:19:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22719","parker.burns"
"Brittany","Koehler","<EMAIL>","Client Services","Client Services Team Lead",,,,,"9/30/2021 4:57:41 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22412","brittany.koehler"
"Divya","Chhabra","<EMAIL>","Implementations","Senior Systems Integration Analyst",,,,,"7/15/2022 11:53:57 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22710","divya.chhabra"
"Richard","Welsh","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/19/2022 8:27:37 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22348","richard.welsh"
"Katie","Light","<EMAIL>","IT","GRC Analyst","+1 (250) 826-3086",,,,"7/15/2022 4:16:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22450","katie.light"
"Anett","Kalmanczhey","<EMAIL>","Client Services","Client Services Team Lead, Senior",,,,,"7/18/2022 9:21:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22410","anett.kalmanczhey"
"Mallory","Conn","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"7/18/2022 1:26:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22452","mallory.conn"
"Reilly","Harper","<EMAIL>","Client Services","Client Services Team Lead","+1 (236) 457-5829",,,,"6/23/2022 1:31:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23391","reilly.harper"
"Paige","O'Hearn","<EMAIL>","CHN East","Practice Consultant",,,,,"7/19/2022 5:54:58 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23903","paige.ohearn"
"Sienna","Kohn","<EMAIL>","Product Operations","Senior Technical Writer",,,,,"7/15/2022 5:14:34 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23392","sienna.kohn"
"Ryan","Prevost","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/19/2022 7:09:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22456","ryan.prevost"
"Damian","Hamilton","<EMAIL>","Development","Junior Software Developer",,,,,"7/18/2022 4:35:10 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23464","damian.hamilton"
"Steve","Lewis","<EMAIL>","Development","Senior BI Developer",,,,,"7/14/2022 1:26:31 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22787","steve.lewis"
"Zohra","Charaniya","<EMAIL>","CHN East","Practice Consultant","+1 (416) 435-8524",,,,"9/24/2021 7:49:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22725","zohra.charaniya"
"Carlene","Williams","<EMAIL>","Implementations","Systems Integration Analyst","+1 (416) 574-4035",,,,"4/29/2022 4:53:50 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23458","carlene.williams"
"Christina","VandenBrink","<EMAIL>","Implementations","Associate Project Manager",,,,,"7/13/2022 11:31:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22415","christina.vandenbrin"
"Megan","Owens","<EMAIL>","Implementations","Implementation Consultant","+1 (403) 796-7460",,,,"5/12/2022 8:42:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22733","megan.owens"
"Danielle","Semple","<EMAIL>","CHN East","Practice Consultant","+1 (416) 992-3873",,,,"7/13/2022 11:58:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22342","danielle.semple"
"Courtney","Stokman","<EMAIL>","Human Resources","Human Resources Generalist","+1 (250) 681-3669",,,,"7/18/2022 5:17:42 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23448","courtney.stokman"
"Crystal","Benoit","<EMAIL>","IT","GRC Team Lead","+1 (306) 230-0853",,,,"6/16/2022 3:51:54 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23952","crystal.benoit"
"Mark","Coutts","<EMAIL>","Implementations","Data Analyst",,,,,"7/14/2022 12:50:17 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22758","mark.coutts"
"Karley","Davis","<EMAIL>","Client Services","Enterprise Relationship Analyst","+1 (250) 864-4295",,,,"7/19/2022 5:56:53 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23349","karley.davis"
"Richard","Millard","<EMAIL>","Development","Senior Software Developer",,,,,"7/8/2022 10:52:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23396","richard.millard"
"Jonathan","Chapman","<EMAIL>","Product Management","Project Manager","+1 (250) 469-2926",,,,"7/18/2022 2:09:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23402","jonathan.chapman"
"Divya","Manyala","<EMAIL>","Product Management","Product Manager",,,,,"3/16/2022 11:56:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23446","divya.manyala"
"Randy","Lewis","<EMAIL>","Quality Assurance","QA Analyst",,,,,"7/18/2022 3:44:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23390","randy.lewis"
"Punita","Gosar","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 10:33:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23465","punita.gosar"
"QHR backup","smtp","<EMAIL>",,"Service Account",,,,"Service Account: Tech Infrastructure - Backup notifications to C. Hounsham (SF Case ********)",,"<EMAIL>","S-1-5-21-*********6-**********-*********-23228","qhrbackupsmtp"
"QHR Technologies IT",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Contact for IT assistance",,"<EMAIL>","S-1-5-21-*********6-**********-*********-13309","IT"
"Sally","Nimmo","<EMAIL>","Development","Software Developer",,,,,"7/14/2022 3:01:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22430","sally.nimmo"
"David","Braaten","<EMAIL>","Development","Lead Software Developer",,,,".","7/18/2022 1:44:42 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23449","david.braaten"
"Greg","Harpell","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","General visibility into documentation related to both initiatives",,"<EMAIL>","S-1-5-21-*********6-**********-*********-23407","greg.harpell"
"Alan","McNaughton","<EMAIL>","Technology","Director of Technology Operations","+1 (403) 805-9002",,,,"6/22/2022 5:47:53 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22829","alan.mcnaughton"
"Dianne","Standring","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"7/18/2022 3:16:22 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23986","dianne.standring"
"KT","Nguyen","<EMAIL>","Development","Software Developer",,,,,"6/17/2022 12:58:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23987","kt.nguyen"
"Tricia","Nason","<EMAIL>","Implementations","Implementer","+1 (902) 240-0811",,,,"7/18/2022 8:01:26 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23497","tricia.nason"
"Sandeep","Singh","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 1:19:56 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23994","sandeep.singh"
"Jolanda","Kondrak","<EMAIL>","Marketing","Marketing Manager","+1 (250) 826-3866",,,,"7/19/2022 8:02:29 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23478","jolanda.kondrak"
"Chris","Bremmer","<EMAIL>","Data and Software Architecture","Database Architect",,,,,"6/14/2022 3:43:37 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23998","Chris.Bremmer"
"Gee Mary","Tan","<EMAIL>","Development","Senior QA Analyst",,,,,"7/13/2022 4:02:12 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23477","geemary.tan"
"Mark","Ramsden","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"7/7/2022 10:29:37 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23481","mark.ramsden"
"Kerry","Slater","<EMAIL>","Implementations","Project Manager",,,,,"7/13/2022 4:07:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22865","kerry.slater"
"Yuliya","Voytsekhivska","<EMAIL>","Development","Lead QA Analyst",,,,,"7/18/2022 9:47:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23483","yuliya.voytsekhivska"
"Carson","Judd","<EMAIL>","Development","Software Developer",,,,,"7/19/2022 8:07:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22847","carson.judd"
"Barrett","Sharpe","<EMAIL>","Development","Software Developer",,,,,"3/24/2022 8:37:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22864","barrett.sharpe"
"Tanya","Peixoto","<EMAIL>","Implementations","Implementer","+1 (416) 801-7445",,,,"7/18/2022 7:13:17 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24018","tanya.peixoto"
"Sara","Burgess","<EMAIL>","Technology","Knowledge and Information Lead","+1 (250) 870-7621",,,,"9/1/2021 3:39:16 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23515","sara.burgess"
"Stacey","Tovey","<EMAIL>","Implementations","Senior Data Analyst",,,,,"7/18/2022 9:35:31 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23823","stacey.tovey"
"Jocelyn","Smith","<EMAIL>","Product Management","Senior UX Researcher",,,,,"6/30/2022 10:49:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23517","jocelyn.smith"
"Nathan","Poehlke","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"7/18/2022 4:23:59 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24045","nathan.poehlke"
"Mohammad","Kandy","<EMAIL>","Technology","Cloud Systems Administrator",,,,,"1/26/2022 1:05:47 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23516","mohammad.kandy"
"Ryan","Kleiber","<EMAIL>","Development","Lead QA Analyst",,,,,"7/14/2022 11:12:29 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24011","ryan.kleiber"
"Megan","Bowker","<EMAIL>","Product Operations","Project Coordinator",,,,,"7/4/2022 1:17:41 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24044","megan.bowker"
"Harleen","Kohli","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"6/6/2022 4:31:59 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24043","harleen.kohli"
"Rebekka","Augustine","<EMAIL>","Marketing","Marketing Content Writer",,,,,"7/18/2022 4:18:31 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23513","rebekka.augustine"
"Lorenn","Floor","<EMAIL>","Product Operations","Associate Product Owner",,,,,"6/29/2022 8:04:03 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24031","lorenn.floor"
"Ryan","Yakiwchuk","<EMAIL>","Product Operations","Senior Product Owner",,,,,"9/14/2020 8:53:14 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23514","ryan.yakiwchuk"
"Hong","He","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 11:19:44 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23578","hong.he"
"Charisse","Abaloyan","<EMAIL>","Client Services","Client Experience Analyst, Senior",,,,,"7/18/2022 7:18:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24041","charisse.abaloyan"
"Liane","Blake","<EMAIL>","Revenue Management","Accounts Receivable & Collections Administrator",,,,,"7/19/2022 8:32:47 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22540","liane.blake"
"Tamika","Leslie","<EMAIL>","Product Operations","Product Owner",,,,,"7/14/2022 3:17:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23550","tamika.leslie"
"Phil","Campbell","<EMAIL>","Data and Software Architecture","Application Architect",,,,,"1/25/2022 3:01:49 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23579","phil.campbell"
"Alex","Shaw","<EMAIL>","Development","Software Developer",,,,,"7/12/2022 4:27:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23563","alex.shaw"
"Jake","Redekop","<EMAIL>","Product Development","Agile Operations Manager",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24055","jake.redekop"
"Jessica","Wright","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/18/2022 3:17:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23569","jessica.wright"
"Sami","Valkama","<EMAIL>","Product Operations","PMO Manager","+1 (250) 575-9376",,,,"7/18/2022 3:13:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23571","sami.valkama"
"Davena","Singh","<EMAIL>","Product Management","Senior Product Manager","+1 (416) 708-6649",,,,"7/19/2022 8:35:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24038","davena.singh"
"Spencer","Shupe","<EMAIL>","Development","Junior Software Developer",,,,,"11/25/2021 12:14:40 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23545","spencer.shupe"
"Becca","Hembling","<EMAIL>","Development","Software Developer",,,,,"6/27/2022 11:35:12 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23564","becca.hembling"
"Tawfiq","Menad","<EMAIL>","Development.","Software Developer",,,,,"1/16/2021 10:10:17 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23549","tawfiq.menad"
"Mingyuan","Yang","<EMAIL>","Development","QA Analyst",,,,,"7/19/2022 8:38:03 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23570","mingyuan.yang"
"Sheetal","Jathar","<EMAIL>","Development","Lead Software Developer",,,,,"7/19/2022 8:19:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24087","sheetal.jathar"
"Rene","Kabis","<EMAIL>","Development","Intermediate Software Developer",,,,,"7/18/2022 9:14:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22505","rene.kabis"
"Ashley","DeLaney","<EMAIL>","Training","Instructional Designer","+1 (403) 463-8346",,,,"5/4/2022 11:37:11 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24027","ashley.delaney"
"Rick","Poor","<EMAIL>","Client Services","Client Services Team Lead","+1 (250) 470-9636",,,,"7/15/2022 8:42:12 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24047","rick.poor"
"Benjamin","Luoma","<EMAIL>","Data Analysis","Lead Data Analyst",,,,,"5/18/2022 1:14:04 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22511","benjamin.luoma"
"OXIDIZEDSA",,,,"Service Account",,,,"Service Account: Networking backups and scripts","1/14/2022 3:10:12 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23534","OXIDIZEDSA"
"Stephane","Chan","<EMAIL>","Implementations","Data Analyst",,,,,"7/18/2022 10:03:19 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22507","stephane.chan"
"Jill","Sprinkling","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/19/2022 7:32:14 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24029","jill.sprinkling"
"Dave","Anderson","<EMAIL>","Implementations","Specialist, SQL",,,,,"7/19/2022 8:44:01 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22510","dave.anderson"
"Shubham","Malik","<EMAIL>","Operations","Privacy Analyst","+1 (250) 869-9189",,,,"2/8/2021 5:41:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22573","shubham.malik"
"Brandon","Unger","<EMAIL>","Development","Software Developer",,,,,"7/15/2022 1:36:30 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22565","brandon.unger"
"Peter","Laudenklos","<EMAIL>","Technology","Virtual Infrastructure Administrator","+1 (403) 829-8214",,,,"7/18/2022 6:53:56 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23593","Peter.Laudenklos"
"Chase","Jensen","<EMAIL>","Development","Lead Software Developer",,,,,"4/8/2022 9:32:46 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22661","chase.jensen"
"Victoria","Philips","<EMAIL>","Technology","Technical Writer","+1 (250) 870-1528",,,,"9/8/2021 8:02:24 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22563","victoria.philips"
"Ina","Kebet","<EMAIL>","Technology","Procurement Specialist",,,,,"7/19/2022 1:10:27 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22576","ina.kebet"
"Jonathan","Dunville","<EMAIL>","Product Management","Launch Operations Manager",,,,,"7/15/2022 10:07:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22580","jonathan.dunville"
"Rodney","Earl","<EMAIL>","Technology","Application Analyst, Financial Systems","250-826-0405",,,,"7/19/2022 8:02:27 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22917","rodney.earl"
"James","Koss","<EMAIL>","Development","Junior Software Developer",,,,,"7/18/2022 10:21:31 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24127","james.koss"
"Elise","Richardson","<EMAIL>","Client Services","Senior Enterprise Relationship Analyst",,,,,"7/19/2022 7:43:31 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24123","elise.richardson"
"Cintia","Schutt","<EMAIL>","Development","Junior Software Developer",,,,,"7/17/2022 5:13:54 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24108","cintia.schutt"
"Nishant","Vyas","<EMAIL>","Development","Junior Software Developer",,,,,"1/18/2021 9:00:23 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22559","nishant.vyas"
"Jordan","Wong","<EMAIL>","Client Services","Client Services Team Lead","+1 (250) 808-7173",,,,"9/8/2021 2:52:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22594","jordan.wong"
"Denis","Ivanov","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"7/18/2022 7:47:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24134","denis.ivanov"
"Vanessa","Stembridge","<EMAIL>","Training","Training Content Developer",,,,,"7/18/2022 12:30:29 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22585","vanessa.stembridge"
"Vrinda","Monga","<EMAIL>","Development","Senior QA Analyst",,,,,"7/18/2022 1:32:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22951","vrinda.monga"
"Aditya Kumar","Pothana","<EMAIL>","Development","QA Analyst",,,,,"4/5/2022 8:40:28 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24128","adityakumar.pothana"
"Taylor","Floor","<EMAIL>","Implementations","Trainer",,,,,"7/15/2022 8:45:28 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22587","taylor.floor"
"Tyler","Cooney","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"7/15/2022 3:44:22 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22943","tyler.cooney"
"Lindsay","Bronskill","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (403) 472-1772",,,,"7/14/2022 3:42:55 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24606","lindsay.bronskill"
"Chris","Heiss","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/6/2021 1:27:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24124","chris.heiss"
"Jeff","Brown","<EMAIL>","Infrastructure","Solution Architect",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22919","jeff.brown"
"Butch","Albrecht","<EMAIL>","Infrastructure","Sr. Manager Enterprise Architecture & Cloud Services",,,,,"6/7/2021 12:39:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22566","butch.albrecht"
"Ken","Royea","<EMAIL>","Implementations","Senior Business Analyst, Data Solutions",,,,,"7/14/2022 2:30:49 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22597","ken.royea"
"Liam","Shaw","<EMAIL>","Development","Junior Software Developer",,,,,"7/8/2022 4:35:04 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24112","liam.shaw"
"Andreas","Niemoeller","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 12:38:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-25106","andreas.niemoeller"
"Patrick","Nguyen","<EMAIL>",,"LCL - Senior Manager Internal Audit Services",,"Carly Innes","Carly Innes","LCL - IT Internal Audit Manager",,"<EMAIL>","S-1-5-21-*********6-**********-*********-24156","patrick.nguyen"
"Drew","Hawken","<EMAIL>","Data and Software Architecture","Lead Application Architect",,,,,"4/6/2022 9:32:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22989","drew.hawken"
"Andrey","Fedorov","<EMAIL>","Product Operations","Technical Writer",,,,,"7/14/2022 9:32:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24160","andrey.fedorov"
"Kirk","Calvin","<EMAIL>","Implementations","Systems Integration Analyst","2508596912",,,,"7/18/2022 1:37:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22988","kirk.calvin"
"Meera","Babu","<EMAIL>","Quality Assurance","QA Analyst",,,,,"7/18/2022 1:04:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24171","meera.babu"
"Melissa","Skowron","<EMAIL>","Product Operations","Project Coordinator, Operations","+1 (403) 389-6686",,,,"7/15/2022 6:25:36 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24172","melissa.skowron"
"Michael","Jacobs","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"7/19/2022 4:58:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24169","michael.jacobs"
"James","Daniell","<EMAIL>","Product Operations","Domain Owner",,,,,"3/18/2022 2:21:36 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-25129","james.daniell"
"Rowell","Selvano","<EMAIL>","Data Analysis","Data Analyst",,,,"Data Analysis","7/19/2022 2:31:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24627","rowell.selvano"
"Jane","Auyeung","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","ePrescribe Roadmap Review, Freedom Release Process, Document Sharing",,"<EMAIL>","S-1-5-21-*********6-**********-*********-25124","jane.auyeung"
"Splunk","Alerts",,,"Service Account",,,,"Service Account: Pprovide Alerts generated from Splunk service.",,"<EMAIL>","S-1-5-21-*********6-**********-*********-22911","splunk.alerts"
"GPeconnect",,,,"Service Account",,,,"Service Account: Finance - Dynamics service account used on qtfin3",,"<EMAIL>","S-1-5-21-*********6-**********-*********-23840","GPeconnect"
"Cecilia","McEachern","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"7/18/2022 1:06:56 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24675","cecilia.mceachern"
"Nirmol","Bajwa","<EMAIL>","Quality Assurance","Junior QA Analyst","+****************",,,,"7/19/2022 7:44:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24665","nirmol.bajwa"
"Ashley","Taron","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"7/12/2022 7:40:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24676","ashley.taron"
"Donovan","Rogall","<EMAIL>","Development","Lead Software Developer",,,,,"5/1/2021 10:44:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24677","donovan.rogall"
"Brad","Stel","<EMAIL>","Marketing","Marketing Coordinator",,,,,"5/21/2020 12:10:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23001","brad.stel"
"Matt","Prices","<EMAIL>",,,,,,"Matt from Price's Alarms for Bomgar access to QTKantech VM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-23361","matt.prices"
"Tyler","Cossentine","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 1:03:24 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22996","tyler.cossentine"
"Mimecast","LDAPS Sync",,,"Service Account",,,,"Service Account: Security - Secure LDAP account for Mimecast",,"<EMAIL>","S-1-5-21-*********6-**********-*********-19814","mimecastsync"
"Trevor","Trainee","<EMAIL>",,"Service Account",,,,"Service Account:  HR - Used for Training Video/Document","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24176","trevor.trainee"
"Paige","Morelli","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"7/13/2022 11:56:59 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24666","paige.morelli"
"Michelle","Czuczko","<EMAIL>","Client Services","Client Services Training Coordinator",,,,,"7/17/2022 7:02:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24668","michelle.czuczko"
"Chelsea","Stickney","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"7/19/2022 6:56:33 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20460","chelsea.stickney"
"Brandon","Chesley","<EMAIL>","Data and Software Architecture","Junior Site Reliability Engineer",,,,,"7/14/2022 1:21:43 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-22578","brandon.chesley"
"Steve","Logan","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","Freedom Release Process, Document Sharing",,"<EMAIL>","S-1-5-21-*********6-**********-*********-24708","steve.logan"
"Khaja","Imran","<EMAIL>","Quality Assurance","QA Analyst",,,,,"7/18/2022 1:26:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24712","khaja.imran"
"Peter","Zeng","<EMAIL>","Development","Junior Software Developer",,,,,"7/8/2022 4:47:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-25162","peter.zeng"
"George","Papadogoulas","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","Freedom Release Process, Document Sharing",,"<EMAIL>","S-1-5-21-*********6-**********-*********-24709","george.papadogoulas"
"Jenny","Manrique","<EMAIL>","Development","Junior Software Developer",,,,,"7/7/2022 11:29:49 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24721","jenny.manrique"
"Slava","Ravinsky","<EMAIL>","Development","Software Developer",,,,,"7/14/2022 3:54:36 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26130","slava.ravinsky"
"Mellissa","Senger","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"4/30/2022 7:57:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26139","mellissa.senger"
"Amanda","Easton","<EMAIL>","Product Operations","Support Coordinator",,,,,"7/18/2022 3:41:22 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26143","amanda.easton"
"Charity","Lebedoff","<EMAIL>","Administration","Office Assistant",,,,,"11/19/2021 12:42:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26148","Charity.Lebedoff"
"Chantal","Keizer","<EMAIL>","CHN East","Manager Practice Consultant, CHN East","+! (204) 872-4113",,,,"5/20/2022 12:31:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24745","Chantal.Keizer"
"Debra","Steiss","<EMAIL>","CHN Central","Practice Consultant","+1 (204) 872-4113",,,,"7/13/2022 1:18:59 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24746","Debra.Steiss"
"Erik","Holtom","<EMAIL>","Technology","Technical Writer",,,,,"6/25/2020 9:46:27 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26160","erik.holtom"
"econnect",,,,"Service Account",,,,"Service Account: Finance - for Dynamics running on qtfin3","7/15/2022 2:38:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23165","econnect"
"Charisa","Flach","<EMAIL>","Data and Software Architecture","Business Intelligence Analyst",,,,,"7/18/2022 12:17:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24754","charisa.flach"
"Leane","King","<EMAIL>","CHN Central","Account Executive",,,,,"7/19/2022 8:35:39 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24760","Leane.King"
"Srini","Venkatraman","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 8:22:42 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24768","srini.venkatraman"
"Greg","Gabelmann","<EMAIL>","Development","Software Development Manager",,,,,"7/14/2022 6:10:44 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24769","greg.gabelmann"
"Dynamics MR","Service",,,"Service Account",,,,"Service Account: add-on to GP that provides/creates financial reports","7/18/2022 2:40:41 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20096","mrsvc"
"JiraConfshare",,,,"Service Account",,,,"Service Account: Corp IT - Sync account between JIRA servers","7/18/2022 3:46:55 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22590","JiraConfshare"
"SQL-osqasql1",,,,"Service Account",,,,"Service Account: QA - SQL Service Account","3/24/2021 5:01:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-18681","sql-osqasql1"
"SQL-EMRDATASQL01",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2008R2
EMRDATASQL01\SQL2012
and EMRDATASQL01\SQL2017 MSSQL instances.","7/11/2022 1:02:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22632","SQL-EMRDATASQL01"
"SQL-OSDEVSQL2012",,,,"Service Account",,,,"Service Account: Development - SQL Service Account; used to run OSDEVSQL2012\OSDEVSQL2012
OSDEVSQL2012\MSSQL2014
and OSDEVSQL2012\MSSQL2016
MSSQL instances.","5/23/2022 1:03:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22629","SQL-OSDEVSQL2012"
"sftp","service",,,"Service Account",,,,"Service Account: Corp IT  - Used to mount shares on qtsftp1. Also used by newer Cerberus SFTP service (on QTSFTP1982 that never made it to production) ","4/8/2021 3:01:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-20262","sftp"
"Gloria","Alla","<EMAIL>","Implementations","Implementation Specialist",,,,,"2/8/2022 9:58:10 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26173","gloria.alla"
"KonicaMinolta",,"<EMAIL>",,"Service Account",,,,"Service Account: LDAP & Email (for scans)",,"<EMAIL>","S-1-5-21-*********6-**********-*********-24092","konicaminolta"
"Jerry","Chuang","<EMAIL>","Development","Junior Software Developer",,,,,"11/17/2021 8:55:51 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26176","jerry.chuang"
"Kace","User",,,"Service Account",,,,"Service Account: Corp IT - Read only user for LDAP imports (do NOT delete)","1/13/2021 3:18:25 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-16673","KACE_User"
"SQL-QTVersionOne",,,,"Service Account",,,,"Service Account: Development - SQL Server account for VersionOne. Legacy system - replaced by JIRA - still referred to.","2/19/2021 5:22:43 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-19680","sql-qtversionone"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Service account for VMWare","7/19/2022 7:49:09 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-25154","vcenter-skyline"
"Commvault","vCenter User - cvvcuser",,,"Service Account",,,,"Service Account: Commvault vCenter User","7/19/2022 8:00:07 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22544","cvvcuser"
"jenkins",,,,"Service Account",,,,"Service Account: Development/QA - QA Test Account","3/24/2021 2:21:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-19879","jenkins"
"VMware Reporting","Service Account",,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","7/19/2022 8:44:09 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23835","vmwarereport"
"Test","Passwriteback","<EMAIL>",,,,,,"For Password Writeback and MDM testing Azure","9/15/2020 8:59:22 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24700","Testpw"
"rhel-virt-who",,,,"Service Account",,,,"Service Account: RHEL Services Account","7/19/2022 8:01:05 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22598","rhel-virt-who"
"ActionTec",,,,"Service Account",,,,"Service Account: User required for wireless connection of ActionTec devices to ""Quadrant Network""",,"<EMAIL>","S-1-5-21-*********6-**********-*********-22489","actiontec"
"Francisco","Rubio","<EMAIL>","Product Operations","Product Owner",,,,,"7/18/2022 4:33:04 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26185","francisco.rubio"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","7/19/2022 8:44:31 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-23012","vcenter-ro"
"Thomas","Jaeger","<EMAIL>","Product Operations","Project Manager",,,,,"5/19/2021 4:48:23 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26189","thomas.jaeger"
"Mitel","Recording",,,"Service Account",,,,"Service Account: Corp IT - User account used on qtmitelscreen and qtmitelcall for accessing shares on those servers","7/15/2022 4:23:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-22799","mitelrecording"
"Ron","Protacio","<EMAIL>","Technology","Director, Cyber Security West Coast",,"SF: ********","SF: ********",,"8/5/2020 11:49:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26603","ron.protacio"
,,,,"Service Account",,,,"Service Account: AD account that it connects to its database with on infrasql where specific permissions are configured","7/19/2022 8:17:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26194","solarwindsdbservice"
"Tina","Steele","<EMAIL>","Implementations","Implementation Specialist","+****************",,,,"7/8/2021 1:19:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26605","tina.steele"
"Dharti","Narayan","<EMAIL>","Business Development","Practice Consultant","+****************",,,,"7/15/2022 9:27:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26608","dharti.narayan"
"Ellen","Doyle","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26196","ellen.doyle"
"Stephanie","Godin","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26197","Stephanie.Godin"
"Cindy","Bekkedam","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26198","Cindy.Bekkedam"
"Chantal","Hayes","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26199","Chantal.Hayes"
"Sophia","Khan","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26200","Sophia.Khan"
"Sophie","Koolen","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26201","Sophie.Koolen"
"Bianca","Santaromita","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26202","Bianca.Santaromita"
"Alison","Caron","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26203","alison.caron"
"Eric","Bauld","<EMAIL>","Development","Senior Software Developer",,,,,"3/31/2022 9:23:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26204","eric.bauld"
"Karanveer","Khanna","<EMAIL>","Development","Software Developer",,,,,"7/15/2022 9:07:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24793","karanveer.khanna"
"KantechEmail","Weekly Report",,,"Service Account",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26612","kantechemail"
"Padmalatha","Ragunathan","<EMAIL>",,"Microsoft Consultant",,"SF:********","SF:********",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-24796","Padmalatha.Ragunatha"
"Mandy","Mann","<EMAIL>","Training","Trainer",,,,,"7/19/2022 5:39:09 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24798","mandy.mann"
"Manjit","Purewal","<EMAIL>",,"LCL- Senior Manager Internal Audit Services",,"SF:********","SF:********",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26207","Manjit.Purewal"
"Stephanie","Wright","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"7/13/2022 11:35:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26211","stephanie.wright"
"Nadia","Hussain","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"7/15/2022 4:34:57 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26213","nadia.hussain"
"Rachel","Klein","<EMAIL>","Marketing","Product Designer",,,,,"2/19/2021 2:16:32 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26215","rachel.klein"
"Brooke","Laing","<EMAIL>","Client Services","Client Services Analyst",,,,,"8/24/2021 2:50:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24804","brooke.laing"
"Jaspreet","Sangha","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/20/2021 2:07:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24805","jaspreet.sangha"
"Ashley","Farrell","<EMAIL>","Learning & Development","Learning & Development Specialist",,,,,"5/19/2022 6:50:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24809","ashley.farrell"
"Tona","Mutimura","<EMAIL>",,"LCL - Audit Services",,"Carly.Innes","Carly.Innes",,,"<EMAIL>","S-1-5-21-*********6-**********-*********-24811","Tona.Mutimura"
"Jarrid","Pond","<EMAIL>","Technology","IT Helpdesk Analyst","+1 (778) 392-7070",,,"+1 (778) 738 3327","7/19/2022 7:57:05 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26620","jarrid.pond"
"Mark","Devries","<EMAIL>","Development","Senior Software Developer",,,,,"7/15/2022 3:21:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26629","Mark.Devries"
"James","Greenwood","<EMAIL>",,,,"Colleen Safinuk","Colleen Safinuk",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26630","james.greenwood"
"CS","Training",,"CS","Service Account",,,,"Client Services, used for booking training for employees.","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26631","cs.training"
"Pritpal","Garcha","<EMAIL>","Development","Junior Software Developer",,,,,"2/16/2022 1:11:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26235","Pritpal.Garcha"
"Intune","NDES",,,"Service Account",,,,"Service Account : Corp IT-NDES to enroll certificates from Certificate Authority to AADJ Devices","6/30/2022 9:35:24 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24813","IntuneNDES"
"David","Rivard","<EMAIL>","Development","Junior Software Developer",,,,,"7/19/2022 7:35:48 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26239","david.rivard"
"Jordan","Pinske","<EMAIL>","Technology","IT Helpdesk Analyst","+1 (250) 317-6996",,,,"7/19/2022 8:41:19 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26245","jordan.pinske"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,,"6/2/2020 11:42:55 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24838","da-balbrecht"
"Janelle","Prejet","<EMAIL>","CHN Central","Practice Consultant","+! (204) 292-1286",,,,"7/18/2022 5:56:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24839","janelle.prejet"
"Lemuel","Caldito","<EMAIL>","Development","Software Developer",,,,,"7/13/2022 12:42:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24840","lemuel.caldito"
"Fred","Xiao","<EMAIL>","Technology","Risk Analyst IAM, Cyber Security West Coast",,"Arnold Nzailu","Arnold Nzailu",,"7/18/2022 1:42:42 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24842","Fred.Xiao"
"Lauri","McCormack","<EMAIL>","Client Services","Client Services Auditor",,"Caitlin Slavik","Caitlin Slavik","Client Services Auditor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24844","Lauri.McCormack"
"Akhila","Guttikonda","<EMAIL>","Development","QA Analyst",,,,,"7/13/2022 12:59:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24849","akhila.guttikonda"
"Kelsey","Hess","<EMAIL>","Implementations","Implementer","+1 (519) 641-9245",,,,"7/15/2022 4:59:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24855","kelsey.hess"
"ca","secscan",,,"Service Account",,,,"Service Account: Security - This AD Account was created in response to salesforce ticket ******** to access QHR servers and report back to the CIS platform dashboard. This account ensure servers are configured Securely.","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26674","ca_secscan"
"Carrie","Ng","<EMAIL>","Product Management","Senior Director, Strategy & Planning",,"Shelley Hughes","Shelley Hughes","LCL employee, temporary access","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24859","Carrie.Ng"
"John","Wilson","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"7/8/2022 9:01:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26276","john.wilson"
"Parker","Steadman","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"7/15/2022 2:06:48 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26279","parker.steadman"
"Lisa","Helin","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"7/15/2022 2:59:03 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26281","lisa.helin"
"Lora","Henriksen","<EMAIL>","Administration","Senior Administrative Coordinator","+1 (250) 878-3343",,,,"7/19/2022 8:31:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24870","lora.henriksen"
"Melissa","Shu","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/8/2022 8:51:04 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24875","melissa.shu"
"Debbie","Davies","<EMAIL>","Implementations","Implementation Coordinator",,,,,"7/13/2022 11:23:32 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24876","debbie.davies"
"OME","User",,,"Service Account",,,,"Service Account: Corp IT - Windows account to access SMB Share on Dell-DRM","8/13/2020 3:28:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26682","omeuser"
"Stephanie","Koopmans","<EMAIL>","Client Services","Client Experience Analyst",,,,,"7/6/2022 6:31:42 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24879","stephanie.koopmans"
"Jade","Davies","<EMAIL>","Client Services","Client Experience Analyst",,,,,"7/8/2022 2:33:17 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24881","jade.davies"
"svc","Flexapp",,,,,,,"Service Account:Do not change - service account for Flexapp",,"<EMAIL>","S-1-5-21-*********6-**********-*********-24884","svc-flexapp"
"SVC","Jenkins",,,,,,,"DO not change - Jenkins slave account for Flexapp","7/19/2022 3:48:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24885","svc-jenkins"
"Saurabh","Moghe","<EMAIL>","Development","Software Developer",,,,,"7/4/2022 12:51:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26292","saurabh.moghe"
"Helder","Necker","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 3:31:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26294","helder.necker"
"Simon","Hamilton","<EMAIL>","Product Operations","Product Owner",,,,,"7/18/2022 8:05:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26296","simon.hamilton"
"Renchie","Abraham",,"Technology","Sr. Cyber Security Specialist, West Coast",,"Ron protacio","Ron protacio",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24892","Renchie.Abraham"
"Olivia","Floyd","<EMAIL>","Product Operations","Associate Project Manager",,,,,"7/19/2022 7:56:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24893","olivia.floyd"
"SQL-EMRDATASQL02",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2019 MSSQL instance.","6/20/2022 1:30:10 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24900","SQL-EMRDATASQL02"
"Justin","Germain",,"Technology","Systems Administrator",,,,,"7/19/2022 7:59:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26693","justin.germain"
"Niloo","Vakili","<EMAIL>","Client Services","Client Services Team Lead, Senior","+1 (250) 899-2919",,,,"7/14/2022 7:51:02 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26694","niloo.vakili"
"Laya","Taheri","<EMAIL>","Technology","Senior Specialist, Security",,"Carly Innes","Carly Innes",,"7/14/2022 5:27:54 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26695","Laya.Taheri"
"Blaine","Bradley","<EMAIL>","Administration","Facilities Manager","+1 (250)-212-2531",,,,"7/12/2022 3:53:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26696","blaine.bradley"
"Shannon","Burns","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"7/19/2022 8:44:16 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26698","shannon.burns"
"Nathan","Taylor","<EMAIL>","Client Services","Client Services Analyst, Specialist",,,,,"7/19/2022 4:56:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26700","nathan.taylor"
"Jordan","Levesque","<EMAIL>","Client Services","Client Services Team Lead","250-718-5762",,,,"7/16/2022 8:01:55 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26701","jordan.levesque"
"Selene","Vera","<EMAIL>","Data and Software Architecture","Cloud Cost Analyst",,,,,"4/22/2022 7:16:29 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24917","selene.vera"
"Muhammad","Ali","<EMAIL>","Technology","Network Analyst","+1 (403) 891-4275",,,,"7/19/2022 7:33:43 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26307","muhammad.ali"
"Lawrence","Lee","<EMAIL>","Data Analysis","Lead Data Analyst",,,,,"7/18/2022 7:54:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26309","lawrence.lee"
"Jennifer","Roseberry","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"7/19/2022 7:43:10 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26311","jennifer.roseberry"
"Etevaldo","Memoria","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"8/30/2021 2:14:34 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26711","etevaldo.memoria"
"Bradley","MacDonald","<EMAIL>","Data Analysis","TimeAcct Contractor",,"Nicola Austin","Nicola Austin","TimeAcct Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26718","Bradley.MacDonald"
"Anna","Tam","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"7/18/2022 9:40:50 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-24923","anna.tam"
"Guinevere","Ashby","<EMAIL>","Training","Training Solutions Lead","+1 (587) 834-7524",,,,"7/18/2022 2:25:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-24925","guinevere.ashby"
"Benie","Tan","<EMAIL>","Quality Assurance","QA Analyst",,,,,"7/19/2022 7:23:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26721","benie.tan"
"Sajid","Syed","<EMAIL>","Product Operations","Scrum Master",,,,,"7/19/2022 8:14:03 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26722","sajid.syed"
"Rob","Lintott","<EMAIL>","Development","Software Developer",,,,,"6/30/2022 12:21:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26723","rob.lintott"
"Julie","Allen","<EMAIL>","Implementations","Implementation Specialist","+1 (705) 677-8367",,,,"7/18/2022 5:09:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26724","julie.allen"
"CommVault","Backup Admin",,,,,,,,"7/19/2022 2:53:26 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26725","svc-commvault"
"Mahlet","Negussie","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/3/2021 11:26:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26727","mahlet.negussie"
"Jackie","Lin","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 5:58:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26728","jackie.lin"
"Dean","McGregor","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/15/2021 3:30:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26730","dean.mcgregor"
"Urvashi","Gupta","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 7:44:12 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26733","urvashi.gupta"
"Myles","Kirkhammer","<EMAIL>","Client Services","Client Services Operations Analyst",,,,,"7/4/2022 6:48:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26736","myles.kirkhammer"
"OXIDIZEDSABASH",,,,"Service Account",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26745","OXIDIZEDSABASH"
"Michelle","Pereira","<EMAIL>","Sales National","Inside Sales Representative","+1 (905) 317-6175",,,,"7/18/2022 6:08:19 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26322","Michelle.Pereira"
"Chris","Schuh",,"IT","New Rocket (ServiceNow) Vendor",,"Sami Valkama","Sami Valkama","New Rocket (ServiceNow) Vendor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26748","chris.schuh"
"Jennifer","McDougall","<EMAIL>","CHN West","Practice Consultant","+1 (403) 463-6794",,,,"7/19/2022 8:32:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26325","jennifer.mcdougall"
"Kelly","Hanson","<EMAIL>","CHN East","Practice Consultant",,,,,"7/19/2022 8:20:19 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26328","kelly.hanson"
"Service-Now","Blob-Reader",,,"Service Account",,,,"Service Account: Corp IT - Used for Azure blob storage and Service-Now Prod impersonation connection",,"<EMAIL>","S-1-5-21-*********6-**********-*********-26331","servicenow.blob"
"Natasha","Lakhani","<EMAIL>","Development","Legal Compliance and Contracts Manager",,,,,"7/18/2022 2:26:40 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26335","natasha.lakhani"
"Paul","Profeta","<EMAIL>","CHN Central","Contractor",,"Audrey Blatz","Audrey Blatz","Contractor - Confluence access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26337","Paul.Profeta"
"Darshini","Trivedi","<EMAIL>","Client Services","Client Services Analyst",,,,,"8/20/2021 11:09:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26340","Darshini.Trivedi"
"Tessa","Tjepkema","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 8:14:46 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26341","tessa.tjepkema"
"Teagan","King","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/21/2021 9:46:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26342","teagan.king"
"Patricia","Camara","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"10/27/2021 8:19:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26344","patricia.camara"
"Jen","Currier","<EMAIL>","Client Services","Client Services Analyst",,,,,"5/21/2021 1:42:31 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26347","jen.currier"
"Jenna","Slonski","<EMAIL>","Client Services","Client Services Analyst, Specialist",,,,,"7/15/2022 4:38:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26348","jenna.slonski"
"Tuba","Tanveer","<EMAIL>","Client Services","Client Services Analyst",,,,,"2/4/2022 10:35:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26353","Tuba.Tanveer"
"Claudia","Escanhola","<EMAIL>","Client Services","Client Services Analyst",,,,,"5/28/2022 11:36:32 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26354","Claudia.Escanhola"
"Derek","Riggs","<EMAIL>","Implementations","Trainer",,,,,"7/14/2022 12:47:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26355","Derek.Riggs"
"Aukse","Braziunaite","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"7/13/2022 8:26:11 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26357","Aukse.Braziunaite"
"Carla","Mendoza","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/30/2022 1:26:45 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26358","Carla.Mendoza"
"Chetna","Singh","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26359","chetna.singh"
"Inna","Danilevich","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26360","inna.danilevich"
"Parfait","Kongo","<EMAIL>","Technology","Sr. Specialist, Security",,"Ron protacio","Ron protacio","Sr. Specialist, Security - EXTERNAL","10/28/2021 11:57:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26361","Parfait.Kongo"
"Anthony","Cesario","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26364","Anthony.Cesario"
"Cameron","Sekulin","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26365","Cameron.Sekulin"
"Hyejoong","Kim","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26366","Hyejoong.Kim"
"Kanika","Vig","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 8:25:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26370","kanika.vig"
"Elara","David","<EMAIL>","Client Services","Client Services Analyst",,,,,"2/2/2022 2:25:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26757","elara.david"
"Jack","Fu",,"Client Services","Client Services Analyst",,,,,"5/4/2022 8:32:50 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26759","Jack.Fu"
"Deepa","Sugur",,"Client Services","Client Services Analyst",,,,,"10/12/2021 2:26:28 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26761","Deepa.Sugur"
"Colleen","Corrigan","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/16/2022 8:41:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26762","Colleen.corrigan"
"Brenda","Kaweesi","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/21/2021 4:51:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26764","Brenda.Kaweesi"
"Ami","Goswami","<EMAIL>","Client Services","Client Services Analyst",,,,,"3/2/2022 6:10:05 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26765","Ami.Goswami"
"James","Calder","<EMAIL>","Marketing","Design Manager","+1 (250) 470-8846",,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26766","james.calder"
"Naaz","Mughal","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"7/18/2022 11:49:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26376","naaz.mughal"
"MDI","sensor",,,,,,,"Service Account: Security - Microsoft Defender for Identity Sensor for QTDC01/02 and QTADFS2","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26767","svc-mdisensor"
"Jarrid","Pond",,,,,,,,"7/18/2022 6:50:12 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26768","da-jpond"
"Nicholas","Braidwood","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 12:48:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26771","nicholas.braidwood"
"Abdur","Rafi","<EMAIL>","Data Analysis","Data Analyst",,,,,"6/19/2022 11:38:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26772","abdur.rafi"
"Jordan","Pinske",,,,,,,,"6/9/2022 9:30:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26776","da-jpinske"
"Rachel","Herzog","<EMAIL>","Marketing","Lead Generation Specialist",,,,,"7/19/2022 8:01:33 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26377","rachel.herzog"
"Simon","Cohen","<EMAIL>","Sales National","Sales Engineer",,,,,"12/8/2021 8:05:12 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26378","simon.cohen"
"Lidia","Ghobrial-Zaki",,"CHN Central","Contractor",,"Audrey Blatz","Audrey Blatz","MD Practice Solutions External Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26379","lidia.GhobrialZaki"
"Uday","Bhaskar","<EMAIL>","Development","Software Developer",,,,,"5/20/2021 9:31:04 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26777","uday.bhaskar"
"Tayo","Aruleba","<EMAIL>","Technology","Application Analyst, Jira","2505564182",,,,"7/19/2022 8:14:17 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26380","Tayo.Aruleba"
"Tayo","Aruleba",,,,,,,,"5/26/2022 5:25:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26381","DA-TAruleba"
"Dean","Malig","<EMAIL>","Technology","Systems Administrator",,,,,"7/19/2022 6:40:47 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26382","dean.malig"
"Lyndsey","Wong","<EMAIL>","Development","Junior Software Developer",,,,,"5/19/2022 4:20:57 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26384","lyndsey.wong"
"Gerardo","Marcos","<EMAIL>","Development","Junior Software Developer",,,,,"9/9/2021 8:35:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26385","gerardo.marcos"
"Jane","Ekegbu","<EMAIL>","Development","BI Developer",,,,,"2/9/2022 1:22:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26386","jane.ekegbu"
"Jeff","Fleming","<EMAIL>","Technology","IT Helpdesk Analyst","+1 (250) 681-2391",,,,"7/18/2022 5:25:26 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26387","jeff.fleming"
"Dilcia","Torres","<EMAIL>","Technology","Senior Systems Specialist","4039684700",,,,"7/5/2022 3:56:38 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26388","dilcia.torres"
"David","Markwell",,"Product Management","Senior Director, Strategy & Planning",,"Angela Tam","Angela Tam",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26389","David.Markwell"
"Jeff","Fleming",,,,,,,,"7/15/2022 3:10:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26391","da-jfleming"
"Domain Admin","Dilcia Torres","<EMAIL>",,,,,,,"7/4/2022 3:39:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26392","da-dtorres"
"CAT","Read-Only","<EMAIL>","IT","Deloitte Contractor",,"Lemuel Caldito","Lemuel Caldito","Test account for Central Admin Tools","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26779","cat.readonly"
"Eden","Ritchie","<EMAIL>","Product Operations","Project Coordinator",,,,,"7/19/2022 7:42:19 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26781","Eden.Ritchie"
"Bre","Wilson","<EMAIL>","Client Services","Client Services Team Lead","416 320 0463",,,,"7/13/2022 3:04:44 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26782","Bre.Wilson"
"Harry","Shams","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 6:35:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26784","Harry.Shams"
"Komal","Kaur","<EMAIL>","Client Services","Client Services Analyst",,,,,"9/7/2021 2:35:12 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26393","Komal.Kaur"
"Nawshin","Tabassum","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 11:58:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26395","Nawshin.Tabassum"
"Pierce","Cowan","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"7/7/2022 5:07:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26396","Pierce.Cowan"
"Eunice","Ndung'u","<EMAIL>","Development","RPA Developer",,,,,"7/18/2022 7:01:48 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26399","eunice.ndungu"
"Omar","Elhalabi","<EMAIL>","Business Development","Practice Consultant",,,,"External - Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26400","omarelhalabi"
"Allan","Holbrook","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 4:38:05 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26792","allan.holbrook"
"Test","Phoneforward",,,,,,,"Service Account: Corp IT - Testing Teams Phone Forwarding","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26793","test.phoneforward"
"Asama","Leduc","<EMAIL>","Client Services","Client Services Analyst, Specialist",,,,,"7/18/2022 2:28:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26794","asama.leduc"
"Cassandra","McAvoy","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 4:28:28 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26795","cassandra.mcavoy"
"Chandra","DeLaney","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/6/2022 1:14:01 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26796","chandra.delaney"
"Gabriela","Parente","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/20/2022 2:12:24 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26797","gabriela.parente"
"Sam","Kaushal","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/21/2021 11:17:44 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26798","sam.kaushal"
"Himali","Lalit","<EMAIL>","Client Services","Client Services Analyst, Specialist",,,,,"7/13/2022 11:36:58 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26799","himali.lalit"
"Joel","Burns","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/3/2022 11:50:45 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26800","joel.burns"
"Natalie","Wilson","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/19/2022 7:45:17 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26801","natalie.wilson"
"Cody","Kinzett","<EMAIL>","Development","Software Developer",,,,,"7/19/2022 7:47:47 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26808","cody.kinzett"
"Iné","Fourie","<EMAIL>","CHN Central","Practice Consultant",,,,,"7/19/2022 8:15:49 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26810","ine.fourie"
"Josh","Henderson","<EMAIL>","Development","Software Developer",,,,,"6/15/2022 7:17:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26811","josh.henderson"
"Avi","van Haren","<EMAIL>","Product Management","Director of Product Management",,,,,"7/19/2022 7:20:07 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26813","avi.vanharen"
"Srija","Yarlagadda","<EMAIL>","Product Operations","Product Owner",,,,,"7/18/2022 1:16:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26814","srija.yarlagadda"
"Daniel","Mason","<EMAIL>","Development","Software Developer",,,,,"6/30/2022 2:22:52 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26403","daniel.mason"
"Lisa","Laurent",,,,,,,,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26404","pa-llaurent"
"Adnan","Ashfaq","<EMAIL>","Development","Junior Software Developer",,,,,"7/15/2022 2:05:40 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26405","adnan.ashfaq"
"Caleb","Penman","<EMAIL>","Development","Junior Software Developer",,,,,"6/21/2022 1:08:09 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26815","caleb.penman"
"Gaku","Jinyama","<EMAIL>","Development","Junior Software Developer",,,,,"9/27/2021 9:27:27 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26406","gaku.jinyama"
"Domain Admin","Fred Xiao",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26411","da-fxiao"
"Sarah","Ryder","<EMAIL>","Administration","Administrative Coordinator",,,,,"7/18/2022 3:54:23 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26822","sarah.ryder"
"Timi","Ade-Malomo","<EMAIL>","Product Operations","Project Manager",,,,,"7/18/2022 10:22:17 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26412","timi.amalomo"
"Janani","Kulanthaiswamy","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"12/18/2021 12:59:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26413","janani.kulanthaiswam"
"Arpita","Brar","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/14/2021 8:21:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26825","arpita.brar"
"Jo","Jraige","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/12/2021 11:22:06 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26829","jo.jraige"
"Samantha","Silverthorne","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/7/2021 9:16:34 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26831","samantha.silverthorn"
"Diego","Silva","<EMAIL>","Development","Senior Software Developer",,,,,"7/18/2022 10:31:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26414","diego.silva"
"Ashley","Quigley","<EMAIL>","Implementations","Implementation Specialist",,,,,"12/27/2021 12:05:24 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26834","ashley.quigley"
"Praveen","Kumar Theegala","<EMAIL>","Development","Junior Software Developer",,,,,"7/18/2022 4:10:40 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26835","praveen.ktheegala"
"Robert","Bro","<EMAIL>","Development","Software Developer",,,,,"7/13/2022 5:13:24 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26836","robert.bro"
"Chris","Haggard","<EMAIL>","IT","New Rocket (ServiceNow) Vendor",,"Sami Valkama","Sami Valkama","Onetrust Vendor",,"<EMAIL>","S-1-5-21-*********6-**********-*********-26415","chris.haggard"
"Srikanth","Reddy Surukanti","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"7/15/2022 10:38:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26837","srikanth.rsurukanti"
"Paul","Farry","<EMAIL>","Technology","System Administrator",,,,,"7/19/2022 8:16:07 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26838","paul.farry"
"Keith","Borgmann","<EMAIL>","Development","Senior Software Developer",,,,,"7/19/2022 6:31:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26839","keith.borgmann"
"Splunk","Sync",,,,,,,"Service Account: Security - Used for Splunk Syncing","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26840","svc_splunksync"
"Pavan","Mantripragada","<EMAIL>","Data and Software Architecture","Senior Site Reliability Engineer",,,,,"3/26/2022 6:48:55 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26418","pavan.mantripragada"
"John","Hetherington","<EMAIL>",,,,"Alan Mcnaughton","Alan Mcnaughton","Lumina Contractor, limited confluence access",,"<EMAIL>","S-1-5-21-*********6-**********-*********-26419","john.hetherington"
"Thu","Pystynen","<EMAIL>",,,,"Alan Mcnaughton","Alan Mcnaughton","Lumina Contractor - Limited confluence access","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26420","thu.pystynen"
"Liam","Mowatt","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26841","liam.mowatt"
"Loanne","Power","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26842","loanne.power"
"Samantha","Dykeman","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 2:58:53 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26845","samantha.dykeman"
"Anish","Kumar","<EMAIL>","Development","Software Developer Intern",,,,,"7/14/2022 2:24:43 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26421","anish.kumar"
"Zuhra","Bakhshi","<EMAIL>","Development","Junior Software Developer",,,,,"7/18/2022 8:24:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26423","zuhra.bakhshi"
"Stacy","Roemer","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 8:08:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26426","stacy.roemer"
"Mark","Petersen-Dixon","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26427","mark.petersen-dixon"
"Antonio","Cienfuegos","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26847","antonio.cienfuegos"
"Matt","Ball","<EMAIL>","Implementations","TimeAcct Contractor",,"Stephan Luies","Stephan Luies","TimeAcct Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26430","matt.ball"
"Paul","Farry",,,,,,,,"7/18/2022 9:50:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26431","da-pfarry"
"Jolanta","Gronowski","jolanta,<EMAIL>","CHN Central","Director, CHN Central",,,,,"7/8/2022 6:58:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26849","jolanta.gronowski"
"Kathryn","Roseberry","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"7/17/2022 4:36:41 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26850","kathryn.roseberry"
"Vuk","Varicak","<EMAIL>","Data Analysis","Data Analyst",,,,,"7/8/2022 8:14:49 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26434","vuk.varicak"
"Jenny","Tieu",,"Marketing","Senior Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26435","jenny.tieu"
"Mehdi","Noroozi","<EMAIL>","Data and Software Architecture","Site Reliability Engineer","403-402-2294",,,,"6/27/2022 3:23:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26436","mehdi.noroozi"
"Bansari","Purohit","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/7/2022 10:57:41 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26855","Bansari.Purohit"
"Tom","Williams","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"6/24/2022 11:45:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26856","tom.williams"
"Elijah","Lewis","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/14/2022 5:11:51 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26857","Elijah.Lewis"
"Jacinta","Kennedy","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/17/2022 6:08:13 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26858","Jacinta.Kennedy"
"Pascal","Swalwell","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/6/2022 10:25:49 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26860","Pascal.Swalwell"
"Naia","Maird","<EMAIL>","Client Services","Client Services Analyst",,,,,"4/1/2022 5:06:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26861","Naia.Maird"
"Vincent","Tolley","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/14/2022 7:53:52 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26863","Vincent.Tolley"
"Christopher","Lee","<EMAIL>","Data and Software Architecture","Business Intelligence Analyst",,,,,"7/18/2022 5:13:53 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26866","christopher.lee"
"Chrisaine","Brown-Humphrey","<EMAIL>","Technology","IT Helpdesk Analyst",,,,,"7/18/2022 11:39:47 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26869","chrisaine.bhumphrey"
"Namrata","Jain","<EMAIL>","Product Management","Associate Product Manager",,,,,"7/18/2022 11:14:57 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26870","namrata.jain"
"Tiffany","Smith","<EMAIL>","Implementations","Implementation Specialist",,,,,"7/18/2022 2:25:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26872","tiffany.smith"
"Chrisaine","Brown-Humphrey",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26873","da-cbrown-humphrey"
"Michael","MacCarthy","<EMAIL>","Development","Junior Software Developer",,,,,"7/13/2022 12:59:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26874","michael.maccarthy"
"Aakash","Siddhanti","<EMAIL>","Product Management","Product Manager",,,,,"6/23/2022 2:28:57 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26875","aakash.siddhanti"
"Yara","Bagh","<EMAIL>","Marketing","Product Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26876","yara.bagh"
"Eszter","Karpati","<EMAIL>","Business Development","Practice Consultant",,,,,"5/20/2022 3:11:06 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26877","eszter.karpati"
"Anthony","Yip","<EMAIL>","Product Operations","Associate Product Owner",,,,,"6/29/2022 1:39:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26878","anthony.yip"
"Vanessa","Marchelletta","<EMAIL>","Product Operations","Project Coordinator",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26879","vanessa.marchelletta"
"Connor","Jones","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/2/2022 8:56:13 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26438","connor.jones"
"Fartune","Ahmed","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 2:27:22 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26439","fartune.ahmed"
"Anna-Kay","Dwyer","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/14/2022 1:14:14 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26443","annakay.dwyer"
"Natalie","Brandon","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/14/2022 4:35:39 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26445","natalie.brandon"
"Shawn","Mohan","<EMAIL>","Client Services","Client Services Analyst",,,,,"4/13/2022 1:43:15 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26447","shawn.mohan"
"svc-sqlbackups",,,,,,,,,"7/19/2022 2:41:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26449","svc-sqlbackups"
"Ana","Macedo","<EMAIL>","Marketing","Product Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26450","ana.macedo"
"Sudeep","Mool","<EMAIL>","Technology","Database Admin Team Lead","403-879-7603",,,,"7/15/2022 8:26:30 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26880","sudeep.mool"
"Patrick","Badine","<EMAIL>","Data Insights","Junior Application Architect",,,,,"7/19/2022 7:56:35 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26881","patrick.badine"
"Andrea","Johnston","<EMAIL>","Technology","Knowledge and Information Lead",,,,,"7/18/2022 12:54:57 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26882","andrea.johnston"
"Yetunde","Osanyin","<EMAIL>","Product Operations","Project Manager",,"10","10","..","7/19/2022 7:59:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26883","yetunde.osanyin"
"Hana","Ghazi","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26885","hana.ghazi"
"svc-vmbackups",,,,,,,,,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26454","svc-vmbackups"
"Swetha","Mandadi","<EMAIL>","Technology","Senior Application Analyst, Salesforce","250-859-3083",,,,"6/11/2022 10:49:20 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26455","swetha.mandadi"
"Block","Box",,,,,,,"Service Account: Corp IT - User for BlockBox Audit, to be delete once audit is complete.","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26887","BlockBoxService"
"svc-mstrueup",,,,,,,,,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26456","svc-mstrueup"
"Satya","Chandran","<EMAIL>","Development","Process Improvement Manager",,,,,"7/15/2022 1:51:35 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26457","satya.chandran"
"Ronald","Lai","<EMAIL>","IT","Compliance Specialist",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26459","ronald.lai"
"Graham","McFie","<EMAIL>","Marketing","Senior Product Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26460","graham.mcfie"
"Kurt","Armbruster","<EMAIL>","Development","Junior Software Developer",,,,,"7/18/2022 4:30:12 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26461","kurt.armbruster"
"Monica","Dial","<EMAIL>","Training","Trainer",,,,,"7/19/2022 8:01:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26463","monica.dial"
"Benji","Tanner","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/8/2022 6:05:56 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26464","benji.tanner"
"Caleb","Marcel","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/6/2022 6:22:32 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26465","caleb.marcel"
"Cassie","Olivares","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/19/2022 8:15:05 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26466","cassie.olivares"
"Chris","Tugle","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/5/2022 4:57:41 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26467","chris.tugle"
"Jordan","DeLaney","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 4:08:57 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26468","jordan.delaney"
"Kiran","Gill","<EMAIL>","Client Services","Client Services Analyst",,,,,"5/21/2022 10:08:24 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26469","kiran.gill"
"Nirav","Chaudhary","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/30/2022 3:05:13 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26471","nirav.chaudhary"
"Devang","Vansh","<EMAIL>","IT","MetaQuirk Contractor",,"Arnold Nzailu","Arnold Nzailu","External user for MetaQuirk (Contractor)","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26888","devang.vansh"
"Matt","Haig","<EMAIL>","IT","MetaQuirk Contractor",,"Arnold Nzailu","Arnold Nzailu","External user for MetaQuirk (Contractor)","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26889","matt.haig"
"Scott","Barr","<EMAIL>","IT","MetaQuirk Contractor",,"Taylor Dresher","Taylor Dresher","External user for MetaQuirk (Contractor). Zscaler only,","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26890","scott.barr"
"Domain Admin - Sudeep","Mool",,,,,,,,"7/5/2022 11:19:02 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26892","da-smool"
"Nicholas","Brown","<EMAIL>","Development","Junior Software Developer",,,,,"7/18/2022 1:59:56 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26473","nicholas.brown"
"Ashley","Robertson","<EMAIL>","Development","Junior Software Developer",,,,,"7/14/2022 1:07:01 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26475","ashley.robertson"
"Kent","Ellens","<EMAIL>","Infrastructure","Manager of Cloud Services",,,,".","7/14/2022 5:04:04 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26476","kent.ellens"
"Rajin","Ramjit","<EMAIL>","IT","Sr. Manager - Technology Risk Management",,"Ron Protacio","Ron Protacio","LCL Employee - Needs Confluence, Jira and Sack","7/18/2022 3:06:51 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26893","rajin.ramjit"
"Rushik","Panchal",,"IT","Sr. Cyber Security Specialist",,"Ron Protacio","Ron Protacio","LCL Employee - Needs Confluence, Jira and Slack","7/14/2022 3:05:21 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26477","rushik.panchal"
"Sam","McGrath","<EMAIL>","CHN East","Practice Consultant",,,,,"7/19/2022 8:13:27 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26478","sam.mcgrath"
"Tully","Johnson","<EMAIL>","Development","Software Developer",,,,,"7/14/2022 4:11:47 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26479","tully.johnson"
"Domain Admin -","Kent Ellis",,,,,,,,,"<EMAIL>","S-1-5-21-*********6-**********-*********-26480","da-kellens"
"Domain Admin","Justin Germain",,,,,,,,"5/8/2022 10:55:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26481","da-jgermain"
"Domain Admin -","Muhammad Ali","<EMAIL>",,,,,,,"5/25/2022 1:16:54 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26483","da-mali"
"Amanda","Harris","<EMAIL>","CHN Central","Practice Consultant",,,,,"7/11/2022 10:30:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26894","amanda.harris"
"Naomi","Brown","<EMAIL>","Implementations","Implementations Specialist",,,,,"7/18/2022 9:45:10 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26484","naomi.brown"
"Temi","Solanke","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 10:31:22 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26895","temi.solanke"
"Simona","Cernanska","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 2:54:10 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26896","simona.cernanska"
"Shikha","Batham","<EMAIL>","Product Management","Technical Product Manager",,,,,"7/19/2022 8:13:15 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26485","shikha.batham"
"Sina","Sereshki","<EMAIL>","Product Management","Technical Product Manager",,,,,"7/19/2022 8:19:20 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26486","sina.sereshki"
"Daniel","Bragg","<EMAIL>","Development","Application Architect",,,,,"7/19/2022 7:46:18 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26897","daniel.bragg"
"Andre","Bertram","<EMAIL>","Development","Junior Software Developer",,,,,"7/19/2022 8:04:38 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26898","andre.bertram"
"Jackson","Ukpong","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/7/2022 12:10:36 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26487","jackson.ukpong"
"Jon","Auger","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/12/2022 2:02:07 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26899","jon.auger"
"Nadine","Dunning","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/7/2022 2:47:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26900","nadine.dunning"
"Shantavia","Allerdyce","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/18/2022 7:16:01 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26901","shantavia.allerdyce"
"Ashleigh","Wilson","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/19/2022 6:50:25 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26489","ashleigh.wilson"
"Kennedy","Huizer","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/19/2022 7:44:21 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26902","kennedy.huizer"
"Lety","Mitroi","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/8/2022 11:46:40 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26903","lety.mitroi"
"Navneet","Malli","<EMAIL>","Human Resources","HR Administrative Assistant",,,,,"7/19/2022 8:36:08 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26490","navneet.malli"
"Viet","Nguyen","<EMAIL>","Development","Software Developer",,,,,"7/18/2022 1:00:44 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26905","viet.nguyen"
"Jody","Kramer","<EMAIL>","CHN West","Practice Consultant Manager, CHN West",,,,,"7/18/2022 1:35:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26906","jody.kramer"
"Christine","Downing","<EMAIL>","Product Operations","Lead Project Manager",,,,,"7/13/2022 10:23:00 AM","<EMAIL>","S-1-5-21-*********6-**********-*********-26907","christine.downing"
"Andy","Chang","<EMAIL>","Development","Junior Software Developer",,,,,"7/14/2022 5:02:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26491","andy.chang"
"Daniel","Adamic",,"IT","MetaQuirk Contractor",,"Taylor Drescher","Taylor Drescher","External user for MetaQuirk (Contractor). Zscaler only,","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26493","daniel.adamic"
"Nithya","Kumar","<EMAIL>","Development","QA Analyst",,,,,"7/14/2022 5:23:19 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26908","nithya.kumar"
"Stephen","Gu","<EMAIL>","Implementations","Data Analyst",,,,,"7/15/2022 12:31:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26909","stephen.gu"
"Bereket","Mogos","<EMAIL>","Implementations","Data Analyst",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26910","bereket.mogos"
"Andrew","Steed","<EMAIL>","Development","Software Development Manager",,,,,"7/18/2022 5:17:08 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26911","andrew.steed"
"Shweta","Patel",,"Development","Junior Software Developer",,,,,"7/18/2022 4:24:46 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26912","shweta.patel"
"Jill","Veitch","<EMAIL>","Marketing","Lead Generation Specialist",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26913","jill.veitch"
"Gaurav","Sharma","<EMAIL>","Marketing","Lead Generation Specialist",,,,,"7/18/2022 4:55:11 PM","<EMAIL>","S-1-5-21-*********6-**********-*********-26914","gaurav.sharma"
