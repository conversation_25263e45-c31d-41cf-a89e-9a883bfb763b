﻿
##################################################################################################################
# Please Configure the following variables....
$smtpServer="mail.qhrtech.com"
$expireindays = 10
$from = "PASSWORD EXPIRATION <<EMAIL>>"
###################################################################################################################

#Get Users From AD who are enabled
Import-Module ActiveDirectory
$users = get-aduser -filter * -properties Name, samaccountname, PasswordNeverExpires, PasswordExpired, PasswordLastSet, Em<PERSON><PERSON>dd<PERSON> |where {$_.EmailAddress -ne $null -and $_.Enabled -eq "True" -and $_.PasswordNeverExpires -eq $false -and $_.passwordexpired -eq $false -and  $_.samaccountname -notmatch "da-" -and $_.samaccountname -notmatch "su-" }

foreach ($user in $users)
{
  $Name = (Get-ADUser $user | foreach { $_.Name})
  $emailaddress = $user.emailaddress
  $passwordSetDate = (get-aduser $user -properties * | foreach { $_.PasswordLastSet })
  $PasswordPol = (Get-AduserResultantPasswordPolicy $user)
  #$image1 = "c:\scripts\JamfConnectStartMenu.jpg"
  #$attachment = Get-item C:\scripts\JamfConnectStartMenu.jpg
  
  # Check for Fine Grained Password
  if (($PasswordPol) -ne $null)
  {
    $maxPasswordAge = ($PasswordPol).MaxPasswordAge
  }
  
  else
  {
    $maxPasswordAge = (Get-ADDefaultDomainPasswordPolicy).MaxPasswordAge
  }
  
  
  $expireson = $passwordsetdate + $maxPasswordAge
  $today = (get-date)
  $daystoexpire = (New-TimeSpan -Start $today -End $Expireson).Days
  $subject="Your password will expire in $daystoExpire days"
  $body = "<html><body>
  Dear $name,<br><br>
  Your QHR Technologies Domain Password will expire in $daystoExpire days. Please follow the steps below to update your password:<br><br>
  <strong><u>Windows Device</u></strong><br>
  Passwords can be updated on Windows devices by following either of the processes below:<br>
  <i>Process 1:</i><br>
  <ul><li>Press the Ctrl + Alt + Delete keys on your Keyboard at the same time, chose <strong>Change password</strong>, and follow the prompts.</li></ul>
  <i>Process 2:</i><br>
  <ul><li>Go to this web-link:
  <a href=""https://portal.microsoftonline.com/ChangePassword.aspx"" style=""color:#05B6C6"">Change Password</a></ul>
  <strong><u>macOS Device</u></strong><br>
  Passwords can be updated on Windows devices by following either of the processes below:
  <ul>1. Open Jamf Connect from the Status Menu.</ul><br>
  <ul>2. Select <strong>Change Password</strong></ul><br>
  For a step-by-step guide, see:
  <a href=""https://confluence.qhrtech.com/pages/viewpage.action?pageId=401410736"" style=""color:#05B6C6"">ACT/PW- Mac- Password Change Process</a><br><br>
  <strong>NOTE:-</strong> We recommend you update your password before the expiry date.<br><br>
  If you are having problems, please contact QHR IT by email or phone:<br><br>
  <ul><li><strong>Email:</strong> You can submit a ticket to Helpdesk <NAME_EMAIL>. Please be sure to include as much detail as possible and a short, but descriptive subject line.</ul></li>
  <ul><li><strong>Call:</strong> You can call HelpDesk at ************ or ************** (toll free) or on a mitel phone ext 3100. Our working hours are 5:00 - 17:00 Pacific Time.</ul></li><br>
  Thank you,<br>
  <strong>QHR TECH IT</strong>
  </body></html>"

  if ($daystoexpire -le $expireindays -and $daystoexpire -gt 0 -and [bool]($daystoexpire%2))
  {
    Send-Mailmessage -smtpServer $smtpServer -from $from -to $emailaddress -subject $subject -body $body -bodyasHTML -priority High
    #Write "$name - $daystoExpire"
    #sleep 3 
  }
  elseif ($daystoexpire -eq 0) { 
    Send-Mailmessage -smtpServer $smtpServer -from $from -to $emailaddress -Bcc <EMAIL> -subject $subject -body $body -bodyasHTML -priority High -whatif
   #Write "$name - $daystoExpire"
  }
}