##################################################################################################################
# Please Configure the following variables....
$smtpServer="mail.qhrtech.com"
$expireindays = 10
$from = "PASSWORD EXPIRATION <<EMAIL>>"
###################################################################################################################

#Get Users From AD who are enabled
Import-Module ActiveDirectory
$users = get-aduser -filter * -properties Name, samaccountname, PasswordNeverExpires, PasswordExpired, PasswordLastSet, Em<PERSON><PERSON>dd<PERSON> |where {$_.EmailAddress -ne $null -and $_.Enabled -eq "True" -and $_.PasswordNeverExpires -eq $false -and $_.passwordexpired -eq $false -and  $_.samaccountname -notmatch "da-" -and $_.samaccountname -notmatch "su-" }

foreach ($user in $users)
{
  $Name = (Get-ADUser $user | foreach { $_.Name})
  $emailaddress = $user.emailaddress
  $passwordSetDate = (get-aduser $user -properties * | foreach { $_.PasswordLastSet })
  $PasswordPol = (Get-AduserResultantPasswordPolicy $user)
  # Check for Fine Grained Password
  if (($PasswordPol) -ne $null)
  {
    $maxPasswordAge = ($PasswordPol).MaxPasswordAge
  }
  
  else
  {
    $maxPasswordAge = (Get-ADDefaultDomainPasswordPolicy).MaxPasswordAge
  }
  
  
  $expireson = $passwordsetdate + $maxPasswordAge
  $today = (get-date)
  $daystoexpire = (New-TimeSpan -Start $today -End $Expireson).Days
  $subject="Your password will expire in $daystoExpire days"
  $body ="<html><body>
  <p style=""font-family: Arial; color:#3E474A"">Dear $name,<br><br>
  Your QHR Technologies Domain Password will expire in $daystoexpire days.<br><br>
  To change your password, please visit:<br>
  <a href=""https://password.qhrtech.com"" style=""color:#05B6C6"">https://password.qhrtech.com</a><br><br>
  If you are having problems, please contact QHR IT.<br><br>
  Thanks, <br>
  <b>QHR TECH IT<br>
  Direct:</b> 855.550.5004 x 3100<br>
  <b style=""color:#05B6C6"">QHRtechnologies.com</b></htmltag>
  </p></body></html>"
  
  if ($daystoexpire -le $expireindays -and $daystoexpire -gt 0 -and [bool]($daystoexpire%2))
  {
    Send-Mailmessage -smtpServer $smtpServer -from $from -to $emailaddress -subject $subject -body $body -bodyasHTML -priority High
    #Write "$name - $daystoExpire"
    #sleep 3 
  }
  elseif ($daystoexpire -eq 0) { 
    Send-Mailmessage -smtpServer $smtpServer -from $from -to $emailaddress -Bcc <EMAIL> -subject $subject -body $body -bodyasHTML -priority High -whatif
   #Write "$name - $daystoExpire"
  }
}