﻿$userlist = (Get-Content C:\Scripts\username-change-list.txt).ToLower()

$LiveCred = Get-Credential "<EMAIL>"
Connect-MsolService -Credential $livecred -ErrorAction Stop


foreach ($newusername in $userlist) {
$oldsam = $newusername.Split('.')
$oldusername = $oldsam[0].Substring(0,1) + $oldsam[1]


$userfull = get-aduser $oldusername -Properties *
$oldsip = $userfull.proxyAddresses | ? {$_ -match "SIP"}
$oldupn = $userfull.userprincipalname
$newupn = $newusername + '@QHRtech.com'


Write "`nChanging username $oldusername to $newusername"
Write "Changing UPN from $oldupn to $newupn"
Write "Changing SIP address from $oldsip to sip:$newupn"


$lastchance = Read-Host "`n*** PROCEEDING CANNOT BE REVERSED ***`nContinue??? (type: yes)"
if ($lastchance -ne "yes") {
    Write "`n`nAborted!!!"
    break
} else {

# *** CAUTION *** START CHANGING THINGS!!!! *** CAUTION ***

# Update AD Username and UPN
Set-ADUser $oldusername -SamAccountName $newusername -UserPrincipalName $newupn

# Update Office365 Username
Set-MsolUserPrincipalName -newuserprincipalname $newupn -userprincipalname $oldupn

# Remove OLD SIP address and add new one
Set-ADUser $newusername -Replace @{"msRTCSIP-PrimaryUserAddress"="sip:$($newupn)"}
Set-ADUser $newusername -Remove @{"Proxyaddresses"=$($oldsip)}
Set-ADUser $newusername -Add @{"ProxyAddresses"="sip:$($newupn)"}
Set-ADUser $user -Remove @{"MsExchShadowProxyAddresses"=$($oldsip)} 
Set-ADUser $user -Add @{"MsExchShadowProxyAddresses"="sip:$($newupn)"}

     }

}
Write "`n`n`nChanges Complete!  Force ADFS and update SharePoint username(s) on qtspwfe1!`n`n`n"
Pause