# Prompt for the new user's province/region
$NewUserArea = "All Staff - "
$NewUserProvince = Read-Host -Prompt 'Which province/region is the user from? (BC, AB, MB, NS, ON, SK)'

# Add a question, is the user internal or external
$NewUserInternalOrExternal = Read-Host -Prompt 'Is the user internal or external? (internal/external)'
if ($NewUserInternalOrExternal -contains "external") {
    $NewUserArea += "Externals"
} else {
    $NewUserArea += $NewUserProvince

    # Ask location-specific questions based on the province
    switch ($NewUserProvince) {
        "BC" {
            # Add a question, is the user located in Vancouver, Kelowna, or other
            $NewUserLocation = Read-Host -Prompt 'Is the user located in Vancouver, Kelowna, or other? (Vancouver/Kelowna/other)'
            if ($NewUserLocation -contains "Vancouver") {
                $NewUserArea += " Vancouver BC"
            } elseif ($NewUserLocation -contains "Kelowna") {
                # Add a question, is the new user working in the office? Is the user working on floor 2 or floor 4?
                $NewUserWorkingInOffice = Read-Host -Prompt 'Is the new user working in the office? (y/n)'
                if ($NewUserWorkingInOffice -contains "y") {
                    $NewUserFloor = Read-Host -Prompt 'Is the user working on floor 2 or floor 4? (2/4)'
                    if ($NewUserFloor -contains "2") {
                        $NewUserArea += " Kelowna LM5-200"
                    } elseif ($NewUserFloor -contains "4") {
                        $NewUserArea += " Kelowna LM5-400"
                    }
                } else {
                    $NewUserArea += " Kelowna BC"
                }
            }
        }
        "ON" {
            # Add a question, is the user from the Greater Toronto Area or Ottawa?
            $NewUserFromGTAorOttawa = Read-Host -Prompt 'Is the user from the Greater Toronto Area or Ottawa? (GTA/Ottawa/other)'
            if ($NewUserFromGTAorOttawa -contains "GTA") {
                $NewUserArea += " Toronto GTA ON"
            } elseif ($NewUserFromGTAorOttawa -contains "Ottawa") {
                $NewUserArea += " Ottawa ON"
            }
        }
    }
}

# Add the user to the appropriate mail group
Write-Verbose "Adding $NewUserDisplayName to the mail group:  $NewUserArea"
Add-DistributionGroupMember -Identity $NewUserArea -Member $NewUser

# Prompt to continue replicating or stop
$strQuit = Read-Host "`n Stop replicating groups for new users? (y/n)"
Until ($strQuit -contains "y")

# Disconnect from Exchange Online
Write-Verbose "Disconnecting from Exchange Online..."
Disconnect-AzureAD
Disconnect-ExchangeOnline
