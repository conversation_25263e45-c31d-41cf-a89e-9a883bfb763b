﻿#install-PackageProvider -Name NuGet -MinimumVersion 2.8.5.201 -Force
#install-module  MicrosoftTeams -RequiredVersion '2.5.1'
Import-Module MicrosoftTeams  #use da-account
Connect-MicrosoftTeams 

#Lines 7+8 change the phone number, line 9 confirms changes, line 10 disconnects connection. Line 4 connects connection. Place the number on the end of line 7
Set-CsPhoneNumberAssignment -Identity "<EMAIL>" -EnterpriseVoiceEnabled $true -PhoneNumber +*********** -PhoneNumberType DirectRouting
Grant-CsOnlineVoiceRoutingPolicy -Identity <EMAIL> -PolicyName "ThinkTel"
Get-CsOnlineUser -identity "<EMAIL>" | fl *Voice*, *PSTN*, *lineuri*
Disconnect-MicrosoftTeams


