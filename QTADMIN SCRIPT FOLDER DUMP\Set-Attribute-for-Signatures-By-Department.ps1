﻿Import-Module act*
$userlist = @()

#Select a Signature Template
$selection = read-host "Select template number to apply signature attribute to group members. `n (1) Name Title CS Toll Free `n (2) Name Title Mobile `n (3) Name Title Direct Line `n (4) Name Title Direct Line Mobile `n (5) Name Title Direct Line Mobile Stock `n"

$departmentselection = read-host "Enter Department name"
$department = Get-ADUser -LDAPFilter "(Department=$departmentselection)"

foreach ($departmentuser in $department)
    {
    #$user.GivenName + "." + $user.Surname
    $username = $departmentuser.UserPrincipalName
    $username = $username.Replace("@QHRtech.com", "")
    $userlist += ,$username
    }
    $userlist

$yesorno = read-host "Would you like the above list to change to template ${selection}? y/n"

Switch ($yesorno)
    {
    "y" {
        Foreach ($user in $userlist)
            {
            SET-ADUSER $user –replace @{extensionAttribute1=”$selection”}
            }
        }
    "n" {
        Break
        }
    }


