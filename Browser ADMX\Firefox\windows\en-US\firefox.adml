<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="7.1" schemaVersion="1.0">
  <displayName/>
  <description/>
  <resources>
    <stringTable>
      <string id="SUPPORTED_WINXPSP2">Microsoft Windows XP SP2 or later</string>
      <string id="UNSUPPORTED">No longer supported.</string>
      <string id="SUPPORTED_FF60">Firefox 60 or later, Firefox 60 ESR or later</string>
      <string id="SUPPORTED_FF62">Firefox 62 or later, Firefox 60.2 ESR or later</string>
      <string id="SUPPORTED_FF63">Firefox 63 or later</string>
      <string id="SUPPORTED_FF64">Firefox 64 or later, Firefox 60.4 ESR or later</string>
      <string id="SUPPORTED_FF66">Firefox 66 or later, Firefox 60.6 ESR or later</string>
      <string id="SUPPORTED_FF67">Firefox 67 or later, Firefox 60.7 ESR or later</string>
      <string id="SUPPORTED_FF68">Firefox 68 or later, Firefox 68 ESR or later</string>
      <string id="SUPPORTED_FF6801">Firefox 68.0.1 or later, Firefox 68.0.1 ESR or later</string>
      <string id="SUPPORTED_FF60ESR">Firefox 60 ESR or later</string>
      <string id="SUPPORTED_FF68ESR">Firefox 68.5 ESR or later</string>
      <string id="SUPPORTED_FF69">Firefox 69 or later, Firefox 68.1 ESR or later</string>
      <string id="SUPPORTED_FF70">Firefox 70 or later, Firefox 68.2 ESR or later</string>
      <string id="SUPPORTED_FF71">Firefox 71 or later, Firefox 68.3 ESR or later</string>
      <string id="SUPPORTED_FF72">Firefox 72 or later, Firefox 68.4 ESR or later</string>
      <string id="SUPPORTED_FF73">Firefox 73 or later, Firefox 68.5 ESR or later</string>
      <string id="SUPPORTED_FF74">Firefox 74 or later, Firefox 68.6 ESR or later</string>
      <string id="SUPPORTED_FF75">Firefox 75 or later, Firefox 68.7 ESR or later</string>
      <string id="SUPPORTED_FF75_ONLY">Firefox 75 or later</string>
      <string id="SUPPORTED_FF76">Firefox 76 or later, Firefox 68.8 ESR or later</string>
      <string id="SUPPORTED_FF76_ONLY">Firefox 76 or later</string>
      <string id="SUPPORTED_FF77">Firefox 77 or later, Firefox 68.9 ESR or later</string>
      <string id="SUPPORTED_FF77_ONLY">Firefox 77 or later</string>
      <string id="SUPPORTED_FF78">Firefox 78 or later</string>
      <string id="SUPPORTED_FF79">Firefox 79 or later, Firefox 78.1 ESR or later</string>
      <string id="SUPPORTED_FF80">Firefox 80 or later, Firefox 78.2 ESR or later</string>
      <string id="SUPPORTED_FF81">Firefox 81 or later, Firefox 78.3 ESR or later</string>
      <string id="SUPPORTED_FF82">Firefox 82 or later, Firefox 78.4 ESR or later</string>
      <string id="SUPPORTED_FF83">Firefox 83 or later, Firefox 78.5 ESR or later</string>
      <string id="SUPPORTED_FF84">Firefox 84 or later, Firefox 78.6 ESR or later</string>
      <string id="SUPPORTED_FF85">Firefox 85 or later, Firefox 78.7 ESR or later</string>
      <string id="SUPPORTED_FF86">Firefox 86 or later, Firefox 78.8 ESR or later</string>
      <string id="SUPPORTED_FF88">Firefox 88 or later, Firefox 78.10 ESR or later</string>
      <string id="SUPPORTED_FF89">Firefox 89 or later, Firefox 78.11 ESR or later</string>
      <string id="SUPPORTED_FF90">Firefox 90 or later, Firefox 78.12 ESR or later</string>
      <string id="SUPPORTED_FF91">Firefox 91 or later</string>
      <string id="SUPPORTED_FF95">Firefox 95 or later, Firefox 91.4 ESR or later</string>
      <string id="SUPPORTED_FF96">Firefox 96 or later, Firefox 91.5 ESR or later</string>
      <string id="SUPPORTED_FF96_ONLY">Firefox 96 or later</string>
      <string id="SUPPORTED_FF97">Firefox 97 or later, Firefox 91.6 ESR or later</string>
      <string id="SUPPORTED_FF98_ONLY">Firefox 98 or later</string>
      <string id="SUPPORTED_FF99">Firefox 99 or later, Firefox 91.8 ESR or later</string>
      <string id="SUPPORTED_FF100">Firefox 100 or later, Firefox 91.9 ESR or later</string>
      <string id="SUPPORTED_FF101">Firefox 101 or later, Firefox 91.10 ESR or later</string>
      <string id="SUPPORTED_FF102">Firefox 102 or later</string>
      <string id="SUPPORTED_FF104">Firefox 104 or later, Firefox 102.2 ESR or later</string>
      <string id="SUPPORTED_FF105">Firefox 105 or later, Firefox 102.3 ESR or later</string>
      <string id="SUPPORTED_FF106">Firefox 106 or later, Firefox 102.4 ESR or later</string>
      <string id="SUPPORTED_FF107">Firefox 107 or later, Firefox 102.5 ESR or later</string>
      <string id="SUPPORTED_FF108">Firefox 108 or later, Firefox 102.6 ESR or later</string>
      <string id="SUPPORTED_FF109">Firefox 109 or later, Firefox 102.7 ESR or later</string>
      <string id="SUPPORTED_FF110">Firefox 110 or later, Firefox 102.8 ESR or later</string>
      <string id="SUPPORTED_FF112_ONLY">Firefox 112 or later</string>
      <string id="SUPPORTED_FF113_ONLY">Firefox 113 or later</string>
      <string id="SUPPORTED_FF114">Firefox 114 or later, Firefox 102.12 ESR or later</string>
      <string id="SUPPORTED_FF118">Firefox 118 or later, Firefox 115.3 ESR or later</string>
      <string id="SUPPORTED_FF120">Firefox 120 or later, Firefox 115.5 ESR or later</string>
      <string id="SUPPORTED_FF121">Firefox 121 or later, Firefox 115.6 ESR or later</string>
      <string id="SUPPORTED_FF122">Firefox 122 or later, Firefox 115.7 ESR or later</string>
      <string id="SUPPORTED_FF123">Firefox 123 or later, Firefox 115.8 ESR or later</string>
      <string id="SUPPORTED_FF124">Firefox 124 or later, Firefox 115.9 ESR or later</string>
      <string id="SUPPORTED_FF124_ONLY">Firefox 124 or later</string>
      <string id="SUPPORTED_FF125">Firefox 125 or later, Firefox 115.10 ESR or later</string>
      <string id="SUPPORTED_FF126_ONLY">Firefox 126 or later</string>
      <string id="SUPPORTED_FF127_ONLY">Firefox 127 or later</string>
      <string id="SUPPORTED_FF128">Firefox 128 or later</string>
      <string id="SUPPORTED_FF129">Firefox 129 or later, Firefox 128.1 ESR or later</string>
      <string id="SUPPORTED_FF130">Firefox 130 or later, Firefox 128.2 ESR or later</string>
      <string id="SUPPORTED_FF130_ONLY">Firefox 130 or later</string>
      <string id="SUPPORTED_FF131">Firefox 131 or later, Firefox 128.3 ESR or later</string>
      <string id="SUPPORTED_FF137_ONLY">Firefox 137 or later</string>
      <string id="SUPPORTED_FF138_ONLY">Firefox 138 or later</string>
      <string id="SUPPORTED_FF138">Firefox 138, Firefox 128.10 ESR</string>
      <string id="firefox">Firefox</string>
      <string id="Permissions_group">Permissions</string>
      <string id="Camera_group">Camera</string>
      <string id="Microphone_group">Microphone</string>
      <string id="Location_group">Location</string>
      <string id="Notifications_group">Notifications</string>
      <string id="Autoplay_group">Autoplay</string>
      <string id="VirtualReality_group">Virtual Reality</string>
      <string id="Authentication_group">Authentication</string>
      <string id="Bookmarks_group">Bookmarks</string>
      <string id="Certificates_group">Certificates</string>
      <string id="Popups_group">Popups</string>
      <string id="Cookies_group">Cookies</string>
      <string id="Addons_group">Addons</string>
      <string id="Extensions_group">Extensions</string>
      <string id="Flash_group">Flash</string>
      <string id="Homepage_group">Home page</string>
      <string id="Search_group">Search</string>
      <string id="Preferences_group">Preferences (Deprecated)</string>
      <string id="UserMessaging_group">User Messaging</string>
      <string id="DisabledCiphers_group">Disabled Ciphers</string>
      <string id="EncryptedMediaExtensions_group">Encrypted Media Extensions</string>
      <string id="PDFjs_group">PDF.js</string>
      <string id="PictureInPicture_group">Picture-in-Picture</string>
      <string id="ProxySettings_group">Proxy Settings</string>
      <string id="SecurityDevices_group">Security Devices</string>
      <string id="FirefoxSuggest_group">Firefox Suggest (US only)</string>
      <string id="ContentAnalysis_group">Content Analysis (DLP)</string>
      <string id="InterceptionPoints_group">Interception Points</string>
      <string id="InterceptionPoints_Clipboard_group">Clipboard</string>
      <string id="InterceptionPoints_DragAndDrop_group">Drag And Drop</string>
      <string id="Allow">Allowed Sites</string>
      <string id="AllowSession">Allowed Sites (Session Only)</string>
      <string id="Block">Blocked Sites</string>
      <string id="AppAutoUpdate">Application Autoupdate</string>
      <string id="AppAutoUpdate_Explain">If this policy is enabled, Firefox is automatically updated without user approval.

If this policy is disabled, Firefox updates are downloaded but the user can choose when to install the update.

If this policy is not configured, the user can choose whether not Firefox is automatically updated.</string>
      <string id="AppUpdateURL">Custom Update URL</string>
      <string id="AppUpdateURL_Explain">If this policy is enabled, you can set a URL to an update server other than the default. This could be helpful if you run your own update server on your network.

If this policy is disabled or not configured, the default update URL is used.</string>
      <string id="Authentication_SPNEGO">SPNEGO</string>
      <string id="Authentication_SPNEGO_Explain">If this policy is enabled, the specified websites are permitted to engage in SPNEGO authentication with the browser. Entries in the list are formatted as mydomain.com or https://myotherdomain.com.

If this policy is disabled or not configured, no websites are permitted to engage in SPNEGO authentication with the browser.

For more information, see https://developer.mozilla.org/en-US/docs/Mozilla/Integrated_authentication.</string>
      <string id="Authentication_Delegated">Delegated</string>
      <string id="Authentication_Delegated_Explain">If this policy is enabled, the browser may delegate user authorization to the server for the specified websites. Entries in the list are formatted as mydomain.com or https://myotherdomain.com.

If this policy is disabled or not configured, the browser will not delegate user authorization to the server for any websites.

For more information, see https://developer.mozilla.org/en-US/docs/Mozilla/Integrated_authentication.</string>
      <string id="Authentication_NTLM">NTLM</string>
      <string id="Authentication_NTLM_Explain">If this policy is enabled, the specified websites are trusted to use NTLM authentification. Entries in the list are formatted as mydomain.com or https://myotherdomain.com.

If this policy is disabled or not configured, no websites are trusted to use NTLM authentification.

For more information, see https://developer.mozilla.org/en-US/docs/Mozilla/Integrated_authentication.</string>
      <string id="Authentication_AllowNonFQDN">Allow Non FQDN</string>
      <string id="Authentication_AllowNonFQDN_Explain">If this policy is enabled (and the checkboxes are checked), you can always allow SPNEGO or NTLM on non FQDNs (fully qualified domain names).

If this policy is disabled or not configured, NTLM and SPNEGO are not enabled on non FQDNs.</string>
      <string id="Authentication_AllowProxies">Allow Proxies</string>
      <string id="Authentication_AllowProxies_Explain">If this policy is disabled, SPNEGO and NTLM will not authenticate with proxy servers.

If this policy is enabled (and the checkboxes are checked) or not configured, NTLM and SPNEGO will always authenticate with proxies.</string>
      <string id="Authentication_Locked">Do not allow authentication preferences to be changed</string>
      <string id="Authentication_Locked_Explain">If this policy is disabled, authentication preferences can be changed by the user.

If this policy is enabled or not configured, authentication preferences cannot be changed by the user.</string>
      <string id="Authentication_PrivateBrowsing">Allow authentication in private browsing</string>
      <string id="Authentication_PrivateBrowsing_Explain">If this policy is enabled, integrated authentication is used in private browsing.

If this policy is disabled or not configured, integrated authentication is not used in private browsing.</string>
      <string id="BlockAboutAddons">Block Add-ons Manager</string>
      <string id="BlockAboutAddons_Explain">If this policy is enabled, the user cannot access the Add-ons Manager or about:addons.

If this policy is disabled or not configured, the user can access the Add-ons Manager and about:addons.</string>
      <string id="BlockAboutConfig">Block about:config</string>
      <string id="BlockAboutConfig_Explain">If this policy is enabled, the user cannot access about:config.

If this policy is disabled or not configured, the user can access about:config.</string>
      <string id="BlockAboutProfiles">Block about:profiles</string>
      <string id="BlockAboutProfiles_Explain">If this policy is enabled, the user cannot access about:profiles.

If this policy is disabled or not configured, the user can access about:profiles.</string>
      <string id="BlockAboutSupport">Block Troubleshooting Information</string>
      <string id="BlockAboutSupport_Explain">If this policy is enabled, the user cannot access Troubleshooting Information or about:support.

If this policy is disabled or not configured, the user can access Troubleshooting Information and about:support.</string>
      <string id="DisableSetDesktopBackground">Disable Set Desktop Background</string>
      <string id="DisableSetDesktopBackground_Explain">If this policy is enabled, the user cannot set an image as their desktop background.

If this policy is disabled or not configured, users can set images as their desktop background.</string>
      <string id="CaptivePortal">Captive Portal</string>
      <string id="CaptivePortal_Explain">If this policy is disabled, captive portal support is disabled.

If this policy is enabled or not configured, captive portal support is enabled.</string>
      <string id="Certificates_ImportEnterpriseRoots">Import Enterprise Roots</string>
      <string id="Certificates_ImportEnterpriseRoots_Explain">If this policy is enabled, Firefox will read certificates from the Windows certificate store.

If this policy is disabled or not configured, Firefox will not read certificates from the Windows certificate store.</string>
      <string id="Certificates_Install">Install Certificates</string>
      <string id="Certificates_Install_Explain">If this policy is enabled, Firefox will install the listed certificates into Firefox. It will look in %USERPROFILE%\AppData\Local\Mozilla\Certificates and %USERPROFILE%\AppData\Roaming\Mozilla\Certificates.

If this policy is disabled or not configured, Firefox will not install additional certificates.</string>
      <string id="DefaultDownloadDirectory">Default Download Directory</string>
      <string id="DefaultDownloadDirectory_Explain">If this policy is enabled, you can set the default directory for downloads. ${home} can be used for the native home path.

If this policy is disabled or not configured, the default Firefox download directory is used.</string>
      <string id="DownloadDirectory">Download Directory</string>
      <string id="DownloadDirectory_Explain">If this policy is enabled, you can set and lock the directory for downloads. ${home} can be used for the native home path.

If this policy is disabled or not configured, the default Firefox download directory is used and the user can change it.</string>
      <string id="DNSOverHTTPS_group">DNS Over HTTPS</string>
      <string id="DNSOverHTTPS_Enabled">Enabled</string>
      <string id="DNSOverHTTPS_Enabled_Explain">If this policy is disabled, DNS over HTTPS is disabled.

If this policy is enabled or not configured, DNS Over HTTPS is enabled.</string>
      <string id="DNSOverHTTPS_ProviderURL">Provider URL</string>
      <string id="DNSOverHTTPS_ProviderURL_Explain">If this policy is enabled, the URL specified is used as the provider URL.

If this policy is disabled or not configured, the default provider is used.</string>

      <string id="DNSOverHTTPS_Locked">Locked</string>
      <string id="DNSOverHTTPS_Locked_Explain">If this policy is enabled, DNS over HTTPS settings cannot be changed by the user.

If this policy is disabled or not configured, DNS over HTTPS settings can be changed by the user.</string>
      <string id="DNSOverHTTPS_ExcludedDomains">Excluded Domains</string>
      <string id="DNSOverHTTPS_ExcludedDomains_Explain">If this policy is enabled, the specified domains are excluded from DNS over HTTPS.

If this policy is disabled or not configured, no domains are excluded from DNS over HTTPS.</string>
      <string id="DNSOverHTTPS">Configure DNS Over HTTPS (Moved)</string>
      <string id="DNSOverHTTPS_Explain">If this policy is enabled, the default configuration for DNS over HTTPS can be changed.

If this policy is disabled or not configured, DNS Over HTTPS uses the default Firefox configuration.</string>
      <string id="DNSOverHTTPS_Fallback">Fallback</string>
      <string id="DNSOverHTTPS_Fallback_Explain">If this policy is disabled, Firefox will not fallback to your default DNS resolver if there is a problem with the secure DNS provider.

If this policy is enabled or not configured, Firefox will use your default DNS resolver if there is a problem with the secure DNS provider.</string>
      <string id="DisableMasterPasswordCreation">Disable Master Password Creation</string>
      <string id="DisableMasterPasswordCreation_Explain">If this policy is enabled, users cannot create a master password.

If this policy is disabled or not configured, users can create a master password.</string>
      <string id="DisableAppUpdate">Disable Update</string>
      <string id="DisableAppUpdate_Explain">If this policy is enabled, the browser does not receive updates.

If this policy is disabled or not configured, the browser receives updates.</string>
      <string id="DisableBuiltinPDFViewer">Disable Built-in PDF Viewer (PDF.js)</string>
      <string id="DisableBuiltinPDFViewer_Explain">If this policy is enabled, PDF files are not viewed within Firefox.

If this policy is disabled or not configured, PDF files are viewed within Firefox.</string>
      <string id="DisableDefaultBrowserAgent">Disable the default browser agent</string>
      <string id="DisableDefaultBrowserAgent_Explain">If this policy is enabled, the default browser agent is disabled.

If this policy is disabled or not configured, the default browser agent is enabled.

For more information about the default browser agent, see https://firefox-source-docs.mozilla.org/toolkit/mozapps/defaultagent/default-browser-agent/index.html</string>
      <string id="DisableDeveloperTools">Disable Developer Tools</string>
      <string id="DisableDeveloperTools_Explain">If this policy is enabled, web developer tools are not available within Firefox.

If this policy is disabled or not configured, web developer tools are available within Firefox.</string>
      <string id="DisableFeedbackCommands">Disable Feedback Commands</string>
      <string id="DisableFeedbackCommands_Explain">If this policy is enabled, the "Submit Feedback" and "Report Deceptive Site" menuitems are not available from the help menu.

If this policy is disabled or not configured, the "Submit Feedback" and "Report Deceptive Site" menuitems are available from the help menu.</string>
      <string id="DisableFirefoxAccounts">Disable Firefox Accounts</string>
      <string id="DisableFirefoxAccounts_Explain">If this policy is enabled, Firefox Accounts is disabled which includes disabling Sync.

If this policy is disabled or not configured, Firefox Accounts and Sync are available.</string>
      <string id="DisableFirefoxScreenshots">Disable Firefox Screenshots</string>
      <string id="DisableFirefoxScreenshots_Explain">If this policy is enabled, Firefox Screenshots is not available.

If this policy is disabled or not configured, Firefox Screenshots is available.</string>
      <string id="DisableFirefoxStudies">Disable Firefox Studies</string>
      <string id="DisableFirefoxStudies_Explain">If this policy is enabled, Firefox will never run SHIELD studies or do Heartbeat surveys.

If this policy is disabled or not configured, the user can choose to enable SHIELD studies or Heartbeat surveys.

For more information, see https://support.mozilla.org/en-US/kb/shield and https://wiki.mozilla.org/Firefox/Shield/Heartbeat</string>
      <string id="DisableForgetButton">Disable Forget Button</string>
      <string id="DisableForgetButton_Explain">If this policy is enabled, the "Forget" button is not available.

If this policy is disabled or not configured, the "Forget" button is available.</string>
      <string id="DisableFormHistory">Disable Form History</string>
      <string id="DisableFormHistory_Explain">If this policy is enabled, Firefox will not remember form or search history.

If this policy is disabled or not configured, Firefox will remember form and search history.</string>
      <string id="DisablePasswordReveal">Do not allow passwords to be revealed in saved logins</string>
      <string id="DisablePasswordReveal_Explain">If this policy is enabled, users cannot show passwords in saved logins.

If this policy is disabled or not configured, users can show passwords in saved logins.</string>
      <string id="DisablePocket">Disable Pocket (Deprecated)</string>
      <string id="DisablePocket_Explain">If this policy is enabled, Pocket is not available.

If this policy is disabled or not configured, Pocket is available.</string>
      <string id="DisablePrivateBrowsing">Disable Private Browsing</string>
      <string id="DisablePrivateBrowsing_Explain">If this policy is enabled, private browsing is not allowed.

If this policy is disabled or not configured, private browsing is allowed.</string>
      <string id="DisableProfileImport">Disable Profile Import</string>
      <string id="DisableProfileImport_Explain">If this policy is enabled, the "Import data from another browser" option is not available in the bookmarks window.

If this policy is disabled or not configured, the "Import data from another browser" option is available.</string>
      <string id="DisableProfileRefresh">Disable Profile Refresh</string>
      <string id="DisableProfileRefresh_Explain">If this policy is enabled, the "Refresh Firefox" button is not available on the about:support page or on support.mozilla.org.

If this policy is disabled or not configured, the "Refresh Firefox" button is available.</string>
      <string id="DisableSafeMode">Disable Safe Mode</string>
      <string id="DisableSafeMode_Explain">If this policy is enabled, the user cannot restart the browser into safe mode.

If this policy is disabled or not configured, safe mode is allowed.</string>
      <string id="DisableSecurityBypass_InvalidCertificate">Prevent overriding certificate errors</string>
      <string id="DisableSecurityBypass_InvalidCertificate_Explain">If this policy is enabled, the "Add Exception" button is not available when a certificate is invalid. This prevents the user from overriding the certificate error.

If this policy is disabled or not configured, certificate errors can be overridden.</string>
      <string id="DisableSecurityBypass_SafeBrowsing">Prevent overriding safe browsing errors</string>
      <string id="DisableSecurityBypass_SafeBrowsing_Explain">If this policy is enabled, a user cannot bypass the warning and visit a harmful site.

If this policy is disabled or not configured, a user can choose to visit a harmful site.</string>
      <string id="DisableSystemAddonUpdate">Disable System Addon Updates</string>
      <string id="DisableSystemAddonUpdate_Explain">If this policy is enabled, new system add-ons will not be installed and existing system add-ons will not be updated.

If this policy is disabled or not configured, system add-ons are installed and updated.</string>
      <string id="DisableTelemetry">Disable Telemetry</string>
      <string id="DisableTelemetry_Explain">If this policy is enabled, telemetry is not uploaded.

If this policy is disabled or not configured, telemetry is collected and uploaded.

Mozilla recommends that you do not disable telemetry. Information collected through telemetry helps us build a better product for businesses like yours.</string>
      <string id="DisplayBookmarksToolbar">Display Bookmarks Toolbar (Deprecated)</string>
      <string id="DisplayBookmarksToolbar_Explain">If this policy is enabled, the bookmarks toolbar is displayed by default. The user can still hide it.

If this policy is disabled or not configured, the bookmarks toolbar is not displayed by default.</string>
      <string id="DisplayBookmarksToolbar_Enum">Display Bookmarks Toolbar</string>
      <string id="DisplayBookmarksToolbar_Enum_Explain">If this policy is enabled, the default bokmarks toolbar display can be configured.

If this policy is disabled or not configured, the bookmarks toolbar is displayed on the new tab page by default.</string>
      <string id="DisplayBookmarksToolbar_Always">Always</string>
      <string id="DisplayBookmarksToolbar_Never">Never</string>
      <string id="DisplayBookmarksToolbar_NewTab">New Tab</string>
      <string id="DisplayMenuBar">Display Menu Bar (Deprecated)</string>
      <string id="DisplayMenuBar_Explain">If this policy is enabled, the menu bar is displayed by default. The user can still hide it.

If this policy is disabled or not configured, the menu bar is not displayed by default.</string>
      <string id="DisplayMenuBar_Enum">Display Menu Bar</string>
      <string id="DisplayMenuBar_Enum_Explain">If this policy is enabled, you can choose whether or not the menu bar is displayed and whether or not the user can show and hide the menu bar.

If this policy is disabled or not configured, the menu bar is not displayed by default.</string>
      <string id="DisplayMenuBar_Always">Always</string>
      <string id="DisplayMenuBar_Never">Never</string>
      <string id="DisplayMenuBar_Default_On">On by default</string>
      <string id="DisplayMenuBar_Default_Off">Off by default</string>
      <string id="DontCheckDefaultBrowser">Don't Check Default Browser</string>
      <string id="DontCheckDefaultBrowser_Explain">If this policy is enabled, Firefox does not check to see if it is the default browser at startup.

If this policy is disabled or not configured, Firefox checks to see if it is the default browser at startup.</string>
      <string id="Extensions_Install">Extensions to Install</string>
      <string id="Extensions_Install_Explain">If this policy is enabled, you can specify a list of extension URLs or paths that will be installed when Firefox is started.
Anytime this list is changed, the extensions will be reinstalled.

If this policy is disabled or not configured, no extensions are installed.</string>
      <string id="Extensions_Uninstall">Extensions to Uninstall</string>
      <string id="Extensions_Uninstall_Explain">If this policy is enabled, you can specify a list of extension IDs that will be uninstalled.
Anytime this list is changed, the extensions will be uninstalled.

If this policy is disabled or not configured, no extensions are uninstalled.</string>
      <string id="Extensions_Locked">Prevent extensions from being disabled or removed</string>
      <string id="Extensions_Locked_Explain">If this policy is enabled, you can specify a list of extension IDs that the user will be unable to uninstall or disable.

If this policy is disabled or not configured, no extensions are locked</string>
      <string id="ExtensionUpdate">Extension Update</string>
      <string id="ExtensionUpdate_Explain">If this policy is disabled, extensions will not be updated automatically.

If this policy is enabled or not configured, extensions will be updated automatically.</string>
      <string id="ExtensionSettings">Extension Management</string>
      <string id="ExtensionSettings_Explain">If this policy is enabled, you can use JSON to describe the extension management policy.

If this policy is disabled or not configured, extensions will not be managed.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#extensionsettings.</string>
      <string id="ExtensionSettingsOneLine">Extension Management (JSON on one line)</string>
      <string id="HardwareAcceleration">Hardware Acceleration</string>
      <string id="HardwareAcceleration_Explain">If this policy is disabled, hardware acceleration is disabled and cannot be enabled.

If this policy is enabled or not configured, hardware acceleration is enabled.</string>
      <string id="LegacyProfiles">Legacy Profiles</string>
      <string id="LegacyProfiles_Explain">If this policy is enabled, Firefox will not try to create different profiles for installations of Firefox in different directories. This is the equivalent of the MOZ_LEGACY_PROFILES environment variable.

If this policy is disabled or not configured, Firefox will create a new profile for each unique installation of Firefox.</string>
      <string id="LegacySameSiteCookieBehaviorEnabled">Revert to legacy SameSite behavior</string>
      <string id="LegacySameSiteCookieBehaviorEnabled_Explain">If this policy is enabled, Firefox will revert to the legacy behavior of SameSite. This means that cookies that don't explicitly specify a SameSite attribute are treated as if they were SameSite=None.

If this policy is disabled or not configured, Firefox will enforce SameSite=lax.</string>
      <string id="LegacySameSiteCookieBehaviorEnabledForDomainList">Revert to legacy SameSite behavior on specific domains</string>
      <string id="LegacySameSiteCookieBehaviorEnabledForDomainList_Explain">If this policy is enabled, Firefox will revert to the legacy behavior of SameSite for the specified domains. This means that cookies that don't explicitly specify a SameSite attribute are treated as if they were SameSite=None.

If this policy is disabled or not configured, Firefox will enforce SameSite=lax for all domains.</string>
      <string id="LocalFileLinks">Local File Links</string>
      <string id="LocalFileLinks_Explain">If this policy is enabled, you can specify origins where linking to local files is allowed.

If this policy is disabled or not configured, websites can't link to local files.</string>
      <string id="NetworkPrediction">Network Prediction</string>
      <string id="NetworkPrediction_Explain">If this policy is disabled, network prediction (DNS prefetching) will be disabled.

If this policy is enabled or not configured, network prediction (DNS prefetching) will be enabled.</string>
      <string id="NewTabPage">New Tab Page</string>
      <string id="NewTabPage_Explain">If this policy is disabled, the new tab page will be blank.

If this policy is enabled or not configured, the new tab page will be the default.</string>
      <string id="OfferToSaveLogins">Offer to save logins</string>
      <string id="OfferToSaveLogins_Explain">If this policy is enabled or not configured, Firefox will offer to save website logins and passwords.

If this policy is disabled, Firefox will not offer to save website logins and passwords.</string>
      <string id="OfferToSaveLoginsDefault">Offer to save logins (default)</string>
      <string id="OfferToSaveLoginsDefault_Explain">If this policy is enabled or not configured, Firefox will offer to save website logins and passwords.

If this policy is disabled, Firefox will not offer to save website logins and passwords.

In either case, the user will be able to change the value (it is not locked).</string>
      <string id="PopupBlocking_Allow_Explain">If this policy is enabled, pop-up windows are always allowed for the origins indicated. If a top level domain is specified (http://example.org), pop-up windows are allowed for all subdomains as well.

If this policy is disabled or not configured, the default pop-up policy is followed.</string>
      <string id="PopupBlocking_Default">Block pop-ups from websites</string>
      <string id="PopupBlocking_Default_Explain">If this policy is disabled, pop-up windows are allowed from websites by default.

If this policy is not configured or enabled, popups are not allowed from websites.</string>
      <string id="PopupBlocking_Locked">Do not allow preferences to be changed</string>
      <string id="PopupBlocking_Locked_Explain">If this policy is enabled, pop-up preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their pop-up preferences.</string>
      <string id="InstallAddonsPermission_Allow_Explain">If this policy is enabled, add-ons are always allowed for the origins indicated unless add-on install is disabled. If a top level domain is specified (http://example.org), add-ons are allowed for all subdomains as well.

If this policy is disabled or not configured, the default add-on policy is followed.</string>
      <string id="InstallAddonsPermission_Default">Allow add-on installs from websites</string>
      <string id="InstallAddonsPermission_Default_Explain">If this policy is disabled, add-ons cannot be installed.

If this policy is not configured or enabled, add-ons can be installed.</string>
      <string id="Cookies_Allow_Explain">If this policy is enabled, cookies are always allowed for the origins indicated. If a top level domain is specified (http://example.org), cookies are allowed for all subdomains as well.

If this policy is disabled or not configured, the default cookie policy is followed.</string>
      <string id="Cookies_AllowSession_Explain">If this policy is enabled, cookies are allowed for the origins indicated, but removed at the end of the session. If a top level domain is specified (http://example.org), cookies are allowed for all subdomains as well.

If this policy is disabled or not configured, the default cookie policy is followed.</string>
      <string id="Cookies_Block_Explain">If this policy is enabled, cookies are blocked for the origins indicated. If a top level domain is specified (http://example.org), cookies are blocked from all subdomains as well.

If this policy is disabled or not configured, cookies are not blocked by default.</string>
      <string id="Cookies_Default">Accept cookies from websites (Deprecated)</string>
      <string id="Cookies_Default_Explain">If this policy is disabled, cookies are not accepted from websites by default.

If this policy is not configured or enabled, cookies are accepted from websites.</string>
      <string id="Cookies_AcceptThirdParty">Accept third-party cookies (Deprecated)</string>
      <string id="Cookies_AcceptThirdParty_Explain">If this policy is enabled and cookies are allowed, you can set when to accept third-party cookies.

This setting is ignored if this policy is disabled or not configured or if cookies are not allowed.</string>
      <string id="Cookies_AcceptThirdParty_All">Always</string>
      <string id="Cookies_AcceptThirdParty_None">Never</string>
      <string id="Cookies_AcceptThirdParty_FromVisited">From visited</string>
      <string id="Cookies_ExpireAtSessionEnd">Keep cookies until Firefox is closed</string>
      <string id="Cookies_ExpireAtSessionEnd_Explain">If this policy is enabled and cookies are allowed, they will expire when Firefox is closed.

This setting is ignored if this policy is disabled or not configured or if cookies are not allowed.</string>
      <string id="Cookies_RejectTracker">Reject trackers (Deprecated)</string>
      <string id="Cookies_RejectTracker_Explain">If this policy is enabled and cookies are allowed, Firefox will reject tracker cookies by default.

This setting is ignored if this policy is disabled or not configured or if cookies are not allowed.</string>
      <string id="Cookies_Locked">Do not allow preferences to be changed</string>
      <string id="Cookies_Locked_Explain">If this policy is enabled, cookie preferences cannot be changed by the user.</string>
      <string id="Cookies_Behavior">Cookie Behavior</string>
      <string id="Cookies_Behavior_Explain">If this policy is enabled, you can configure cookie behavior.

If this policy is not configured or disabled, cookies are rejected for known trackers.</string>
      <string id="Cookies_BehaviorPrivateBrowsing">Cookie Behavior in private browsing</string>
      <string id="Cookies_BehaviorPrivateBrowsing_Explain">If this policy is enabled, you can configure cookie behavior in private browsing.

If this policy is not configured or disabled, in private browsing, cookies are rejected for known trackers and third-party cookies are partitioned.</string>
      <string id="Cookies_Behavior_Accept">Accept all cookies</string>
      <string id="Cookies_Behavior_RejectForeign">Reject third party cookies</string>
      <string id="Cookies_Behavior_Reject">Reject all cookies</string>
      <string id="Cookies_Behavior_LimitForeign">Reject third party cookies for sites you haven't visited</string>
      <string id="Cookies_Behavior_RejectTracker">Reject cookies for known trackers</string>
      <string id="Cookies_Behavior_RejectTrackerAndPartitionForeign">Reject cookies for known trackers and partition third-party cookies (Total Cookie Protection)</string>
      <string id="Camera_Allow_Explain">If this policy is enabled, access to the camera is always allowed for the origins indicated.

If this policy is disabled or not configured, the default camera policy is followed.</string>
      <string id="Camera_Block_Explain">If this policy is enabled, access to the camera is blocked for the origins indicated.

If this policy is disabled or not configured, access to the camera is not blocked by default.</string>
      <string id="Camera_BlockNewRequests">Block new requests asking to access the camera</string>
      <string id="Camera_BlockNewRequests_Explain">If this policy is enabled, sites that are not in the Allow policy will not be allowed to ask permission to access the camera.

If this policy is disabled or not configured, any site that is not in the Block policy can ask permission to access the camera.</string>
      <string id="Camera_Locked">Do not allow preferences to be changed</string>
      <string id="Camera_Locked_Explain">If this policy is enabled, camera preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their camera preferences.</string>
      <string id="Microphone_Allow_Explain">If this policy is enabled, access to the microphone is always allowed for the origins indicated.

If this policy is disabled or not configured, the default microphone policy is followed.</string>
      <string id="Microphone_Block_Explain">If this policy is enabled, access to the microphone is blocked for the origins indicated.

If this policy is disabled or not configured, access to the microphone is not blocked by default.</string>
      <string id="Microphone_BlockNewRequests">Block new requests asking to access the microphone</string>
      <string id="Microphone_BlockNewRequests_Explain">If this policy is enabled, sites that are not in the Allow policy will not be allowed to ask permission to access the microphone.

If this policy is disabled or not configured, any site that is not in the Block policy can ask permission to access the microphone.</string>
      <string id="Microphone_Locked">Do not allow preferences to be changed</string>
      <string id="Microphone_Locked_Explain">If this policy is enabled, microphone preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their camera preferences.</string>
      <string id="Location_Allow_Explain">If this policy is enabled, access to location is always allowed for the origins indicated.

If this policy is disabled or not configured, the default location policy is followed.</string>
      <string id="Location_Block_Explain">If this policy is enabled, access to location is blocked for the origins indicated.

If this policy is disabled or not configured, access to location is not blocked by default.</string>
      <string id="Location_BlockNewRequests">Block new requests asking to access location</string>
      <string id="Location_BlockNewRequests_Explain">If this policy is enabled, sites that are not in the Allow policy will not be allowed to ask permission to access location.

If this policy is disabled or not configured, any site that is not in the Block policy can ask permission to access location.</string>
      <string id="Location_Locked">Do not allow preferences to be changed</string>
      <string id="Location_Locked_Explain">If this policy is enabled, location preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change location preferences.</string>
      <string id="Notifications_Allow_Explain">If this policy is enabled, notifications can always be sent for the origins indicated.

If this policy is disabled or not configured, the default notification policy is followed.</string>
      <string id="Notifications_Block_Explain">If this policy is enabled, notifications are always blocked for the origins indicated.

If this policy is disabled or not configured, notifications are not blocked by default.</string>
      <string id="Notifications_BlockNewRequests">Block new requests asking to send notifications</string>
      <string id="Notifications_BlockNewRequests_Explain">If this policy is enabled, sites that are not in the Allow policy will not be allowed to ask permission to send notifications.

If this policy is disabled or not configured, any site that is not in the Block policy can ask permission to send notifications.</string>
      <string id="Notifications_Locked">Do not allow preferences to be changed</string>
      <string id="Notifications_Locked_Explain">If this policy is enabled, notification preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their notification preferences.</string>
      <string id="Autoplay_Allow_Explain">If this policy is enabled, autoplay is always enabled for the origins indicated.

If this policy is disabled or not configured, the default autoplay policy is followed.</string>
      <string id="Autoplay_Block_Explain">If this policy is enabled, autoplay is always blocked for the origins indicated.

If this policy is disabled or not configured, the default autoplay policy is followed.</string>
      <string id="Autoplay_Default">Default autoplay level</string>
      <string id="Autoplay_Default_Explain">If this policy is enabled, you can choose the default autoplay level.

If this policy is disabled or not configured, audio is blocked by default.

Note: Blocking audio and video does not work on the ESR.</string>
      <string id="Autoplay_Locked">Do not allow preferences to be changed</string>
      <string id="Autoplay_Locked_Explain">If this policy is enabled, autoplay preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change autoplay preferences.</string>
      <string id="AllowAudioVideo">Allow Audio and Video</string>
      <string id="BlockAudio">Block Audio</string>
      <string id="BlockAudioVideo">Block Audio and Video</string>
      <string id="VirtualReality_Allow_Explain">If this policy is enabled, access to virtual reality devices is always allowed for the origins indicated.

If this policy is disabled or not configured, the default virtual reality policy is followed.</string>
      <string id="VirtualReality_Block_Explain">If this policy is enabled, access to virtual reality devices is blocked for the origins indicated.

If this policy is disabled or not configured, access to virtual reality devices is not blocked by default.</string>
      <string id="VirtualReality_BlockNewRequests">Block new requests asking to access virtual reality devices.</string>
      <string id="VirtualReality_BlockNewRequests_Explain">If this policy is enabled, sites that are not in the Allow policy will not be allowed to ask permission to access virtual reality devices.

If this policy is disabled or not configured, any site that is not in the Block policy can ask permission to virtual reality devices.</string>
      <string id="VirtualReality_Locked">Do not allow preferences to be changed</string>
      <string id="VirtualReality_Locked_Explain">If this policy is enabled, virtual reality preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their virtual reality preferences.</string>
      <string id="FirefoxHome">Customize Firefox Home</string>
      <string id="FirefoxHome_Explain">If this policy is enabled, you can choose the sections displayed on Firefox Home and prevent the user from changing them.

If this policy is disabled or not configured, the default sections are displayed and the user can change them.</string>
      <string id="FlashPlugin_Allow_Explain">If this policy is enabled, Flash is activated by default for the origins indicated unless Flash is completely disabled. If a top level domain is specified (http://example.org), Flash is allowed for all subdomains as well.

If this policy is disabled or not configured, the default Flash policy is followed.</string>
      <string id="FlashPlugin_Block_Explain">If this policy is enabled, Flash is blocked for the origins indicated. If a top level domain is specified (http://example.org), Flash is blocked from all subdomains as well.

If this policy is disabled or not configured, the default Flash policy is followed.</string>
      <string id="FlashPlugin_Default">Activate Flash on websites</string>
      <string id="FlashPlugin_Default_Explain">If this policy is enabled, Flash is always activated on websites.

If this policy is disabled, Flash is never activated on websites, even if they are in the specified in the Allow list.

If this policy is not configured, Flash is click to play.</string>
      <string id="FlashPlugin_Locked">Do not allow preferences to be changed</string>
      <string id="FlashPlugin_Locked_Explain">If this policy is enabled, Flash preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their Flash preferences.</string>
      <string id="OverrideFirstRunPage">Override the first run page</string>
      <string id="OverrideFirstRunPage_Explain">If this policy is enabled, you can specify a URL to be used as the first run page. If you leave the URL blank, no first run page will be shown.

Starting with Firefox 83, Firefox ESR 78.5, you can also specify multiple URLS separated by a vertical bar (|).

If this policy is disabled or not configured, the first run page is displayed.</string>
      <string id="OverridePostUpdatePage">Override the upgrade page</string>
      <string id="OverridePostUpdatePage_Explain">If this policy is enabled, you can specify a URL to be displayed after Firefox is updated. If you leave the URL blank, no upgrade page will be shown.

If this policy is disabled or not configured, the upgrade is displayed.</string>
      <string id="SanitizeOnShutdown">Clear data when browser is closed (Moved)</string>
      <string id="SanitizeOnShutdown_Explain">If this policy is enabled, you can choose data to be cleared when Firefox is closed.

If this policy is disabled or not configured, data is not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_group">Clear data when browser is closed</string>

      <string id="SanitizeOnShutdown_Cache">Cache</string>
      <string id="SanitizeOnShutdown_Cache_Explain">If the policy is enabled, the cache is cleared when the browser is closed.

If this policy is disabled or not configured, the cache is not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_Cookies">Cookies</string>
      <string id="SanitizeOnShutdown_Cookies_Explain">If the policy is enabled, cookies are cleared when the browser is closed.

If this policy is disabled or not configured, cookies are not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_Downloads">Download History (Deprecated)</string>
      <string id="SanitizeOnShutdown_Downloads_Explain">If the policy is enabled, download history is cleared when the browser is closed.

If this policy is disabled or not configured, download history is not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_FormData">Form &amp; Search History (Deprecated)</string>
      <string id="SanitizeOnShutdown_FormData_Explain">If the policy is enabled, form data is cleared when the browser is closed.

If this policy is disabled or not configured, form data is not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_History">History</string>
      <string id="SanitizeOnShutdown_History_Explain">If the policy is enabled, browsing history, download history, search history, and form data are cleared when the browser is closed.

If this policy is disabled or not configured, browsing history, download history, search history, and form data are not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_Sessions">Active Logins</string>
      <string id="SanitizeOnShutdown_Sessions_Explain">If the policy is enabled, sessions cleared when the browser is closed.

If this policy is disabled or not configured, sessions not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_SiteSettings">Site Preferences</string>
      <string id="SanitizeOnShutdown_SiteSettings_Explain">If the policy is enabled, site preferences are cleared when the browser is closed.

If this policy is disabled or not configured, site preferences are not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_OfflineApps">Offline Website Data (Deprecated)</string>
      <string id="SanitizeOnShutdown_OfflineApps_Explain">If the policy is enabled, offline application storage is cleared when the browser is closed.

If this policy is disabled or not configured, offline application storage is not cleared when the browser is closed.</string>
      <string id="SanitizeOnShutdown_Locked">Locked</string>
      <string id="SanitizeOnShutdown_Locked_Explain">If this policy is disabled, all shutdown preferences can be changed by the user.

If this policy is enabled, any shutdown preferences explicitly set via policy cannot be changed by the user.

If this policy is not configured, no shutdown preferences can be changed by the user (previous behavior).</string>
      <string id="WebsiteFilter_Block">Blocked websites</string>
      <string id="WebsiteFilter_Block_Explain">If this policy is enabled, you can specify match patterns that indicate sites to be blocked. The match patterns are documented at https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns. There is a 1000 entry limit.

If this policy is disabled or not configured, no websites are blocked.</string>
      <string id="WebsiteFilter_Exceptions">Exceptions to blocked websites</string>
      <string id="WebsiteFilter_Exceptions_Explain">If this policy is enabled, and the website filter is enabled, you can specify match patterns for sites you do not want to block. The match patterns are documented at https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns. There is a 1000 entry limit.

If this policy is disabled or not configured, there are no exceptions to the website filter.</string>
      <string id="WebsiteFilterOneLine">Website Filter (JSON on one line)</string>
      <string id="WebsiteFilter">Website Filter (JSON)</string>
      <string id="WebsiteFilter_Explain">If this policy is enabled, you can specify blocked sites and exceptions via JSON.

If this policy is disabled or not configured, websites are not filtered.</string>
      <string id="Bookmark01">Bookmark 01</string>
      <string id="Bookmark02">Bookmark 02</string>
      <string id="Bookmark03">Bookmark 03</string>
      <string id="Bookmark04">Bookmark 04</string>
      <string id="Bookmark05">Bookmark 05</string>
      <string id="Bookmark06">Bookmark 06</string>
      <string id="Bookmark07">Bookmark 07</string>
      <string id="Bookmark08">Bookmark 08</string>
      <string id="Bookmark09">Bookmark 09</string>
      <string id="Bookmark10">Bookmark 10</string>
      <string id="Bookmark11">Bookmark 11</string>
      <string id="Bookmark12">Bookmark 12</string>
      <string id="Bookmark13">Bookmark 13</string>
      <string id="Bookmark14">Bookmark 14</string>
      <string id="Bookmark15">Bookmark 15</string>
      <string id="Bookmark16">Bookmark 16</string>
      <string id="Bookmark17">Bookmark 17</string>
      <string id="Bookmark18">Bookmark 18</string>
      <string id="Bookmark19">Bookmark 19</string>
      <string id="Bookmark20">Bookmark 20</string>
      <string id="Bookmark21">Bookmark 21</string>
      <string id="Bookmark22">Bookmark 22</string>
      <string id="Bookmark23">Bookmark 23</string>
      <string id="Bookmark24">Bookmark 24</string>
      <string id="Bookmark25">Bookmark 25</string>
      <string id="Bookmark26">Bookmark 26</string>
      <string id="Bookmark27">Bookmark 27</string>
      <string id="Bookmark28">Bookmark 28</string>
      <string id="Bookmark29">Bookmark 29</string>
      <string id="Bookmark30">Bookmark 30</string>
      <string id="Bookmark31">Bookmark 31</string>
      <string id="Bookmark32">Bookmark 32</string>
      <string id="Bookmark33">Bookmark 33</string>
      <string id="Bookmark34">Bookmark 34</string>
      <string id="Bookmark35">Bookmark 35</string>
      <string id="Bookmark36">Bookmark 36</string>
      <string id="Bookmark37">Bookmark 37</string>
      <string id="Bookmark38">Bookmark 38</string>
      <string id="Bookmark39">Bookmark 39</string>
      <string id="Bookmark40">Bookmark 40</string>
      <string id="Bookmark41">Bookmark 41</string>
      <string id="Bookmark42">Bookmark 42</string>
      <string id="Bookmark43">Bookmark 43</string>
      <string id="Bookmark44">Bookmark 44</string>
      <string id="Bookmark45">Bookmark 45</string>
      <string id="Bookmark46">Bookmark 46</string>
      <string id="Bookmark47">Bookmark 47</string>
      <string id="Bookmark48">Bookmark 48</string>
      <string id="Bookmark49">Bookmark 49</string>
      <string id="Bookmark50">Bookmark 50</string>
      <string id="Bookmark_Explain">If this policy is enabled, you can configure a bookmark be added to Firefox. Due to a bug, you must select the location. Note that you must specify the bookmarks in order.

If this policy is disabled or not configured, a new bookmark is not added.</string>
      <string id="BookmarkPlacementToolbar">Toolbar</string>
      <string id="BookmarkPlacementMenu">Menu</string>
      <string id="NoDefaultBookmarks">No Default Bookmarks</string>
      <string id="NoDefaultBookmarks_Explain">If this policy is enabled, the default bookmarks and Smart Bookmarks (Most Visited, Recent Tags) are not created.

If this policy is disabled or not configured, default bookmarks and Smart Bookmarks (Most Visited, Recent Tags) are created.

Note: this policy is only effective if used before the first run of the profile.</string>
      <string id="HomepageURL">URL for Home page</string>
      <string id="HomepageURL_Explain">If this policy is enabled, you can set a default home page. You can also lock the home page.

If this policy is disabled or not configured, the user can set and change the home page.</string>
      <string id="HomepageAdditional">Additional Homepages</string>
      <string id="HomepageAdditional_Explain">If this policy is enabled, you can have additional home pages. They are opened in multiple tabs.

If this policy is disabled or not configured, there is only one home page.</string>
      <string id="HomepageStartPage">Start Page</string>
      <string id="HomepageStartPage_Explain">If this policy is enabled, you can change what is displayed when Firefox starts. It can be the homepage, the previous session, or a blank page.

If this policy is disabled or not configured, the start page defaults to the previous session.</string>
      <string id="None">None</string>
      <string id="Homepage">Homepage</string>
      <string id="PreviousSession">Previous Session</string>
      <string id="HomepageLocked">Homepage (Locked)</string>
      <string id="Homepage_ShowHomeButton">Show Home button on toolbar</string>
      <string id="Homepage_ShowHomeButton_Explain">If this policy is enabled, the home button will appear on the toolbar by default.

If this policy is disabled, the home button will not appear on the toolbar by default.

If this policy is not configured, Firefox will determine whether or not the home button appears on the toolbar by default.</string>
      <string id="PasswordManagerEnabled">Password Manager</string>
      <string id="PasswordManagerEnabled_Explain">If this policy is disabled, the password manager is not available via preferences.

If this policy is enabled or not configured, the password manager is available via preferences.</string>
      <string id="PasswordManagerExceptions">Password Manager Exceptions</string>
      <string id="PasswordManagerExceptions_Explain">If this policy is enabled, you can specify sites where Firefox won't offer to save passwords.

If this policy is disabled or not configured, Firefox will offer to save passwords on all sites.</string>
      <string id="PromptForDownloadLocation">Prompt for download location</string>
      <string id="PromptForDownloadLocation_Explain">If this policy is disabled, the user is not prompted for a download location.

If this policy is enabled, the user is always prompted for a download location.

If this policy is not configured, the user is prompted for a download location, but can change the default.</string>
      <string id="Proxy">Proxy Settings (Moved)</string>
      <string id="Proxy_Explain">If this policy is enabled, you can configure and lock network settings.

Select the connection type and then fill in the appropriate sections. Due to a bug, you must select a value for the SOCKS proxy version.

If this policy is disabled or not configured, the default network settings are used and user can change them.</string>
      <string id="SOCKSVersion4">SOCKS v4</string>
      <string id="SOCKSVersion5">SOCKS v5</string>
      <string id="AutoConfigURL">Automatic proxy configuration URL</string>
      <string id="AutoConfigURL_Explain">These should only be set if you selected autoConfig</string>
      <string id="Passthrough">Proxy bypass URLs</string>
      <string id="Passthrough_Explain">These should only be set if you selected manual proxy</string>
      <string id="Connection">Connection Type</string>
      <string id="NoProxy">No Proxy</string>
      <string id="SystemProxy">Use system proxy settings</string>
      <string id="ManualProxy">Manual proxy configuration</string>
      <string id="AutoDetectProxy">Auto-detect proxy settings</string>
      <string id="AutoConfigProxy">Automatic proxy configuration</string>
      <string id="TrackingProtection">Tracking Protection (Moved)</string>
      <string id="TrackingProtection_Explain">If this policy is not configured, tracking protection is not enabled by default in the browser but it is enabled by default in private browsing and the user can change it.

If this policy is disabled, tracking protection is disabled and locked in both the browser and private browsing.

If this policy is enabled, private browsing is enabled by default in both the browser and private browsing and you can choose whether or not to prevent the user from changing it.</string>
      <string id="TrackingProtection_group">Tracking Protection</string>
      <string id="TrackingProtection_Value">Enabled</string>
      <string id="TrackingProtection_Value_Explain">If this policy is enabled, tracking protection is enabled.

If this policy is disabled, tracking protection is disabled and cannot be changed by the user.

If this policy is not configured, standard tracking protection is used and the user can change it.</string>
      <string id="TrackingProtection_Cryptomining">Cryptomining</string>
      <string id="TrackingProtection_Cryptomining_Explain">If this policy is enabled, scripts that use cryptomining are blocked.

If this policy is disabled or not configured, scripts that use cryptomining are not blocked.</string>
      <string id="TrackingProtection_Fingerprinting">Fingerprinting</string>
      <string id="TrackingProtection_Fingerprinting_Explain">If this policy is enabled, scripts that use fingerprinting are blocked.

If this policy is disabled or not configured, scripts that use fingerprinting are not blocked.</string>
      <string id="TrackingProtection_Exceptions">Exceptions</string>
      <string id="TrackingProtection_Exceptions_Explain">If this policy is enabled, you can specify origins where tracking protection is not enabled.

If this policy is disabled or not configured, tracking protection is enabled for all websites.</string>
      <string id="TrackingProtection_Locked">Do not allow tracking protection preferences to be changed</string>
      <string id="TrackingProtection_Locked_Explain">If this policy is enabled, tracking protection preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change tracking protection preferences.</string>
      <string id="TrackingProtection_EmailTracking">Email Tracking</string>
      <string id="TrackingProtection_EmailTracking_Explain">If this policy is enabled, hidden email tracking pixels and scripts on websites are blocked.

If this policy is disabled or not configured, hidden email tracking pixels and scripts on websites are not blocked.</string>
      <string id="RequestedLocales">Requested locale</string>
      <string id="RequestedLocalesString">Requested locale (string)</string>
      <string id="RequestedLocales_Explain">If this policy is enabled, you can specify a list of requested locales for the application in order of preference. It will cause the corresponding language pack to become active.

If this policy is disabled or not configured, the application will use the default locale.</string>
      <string id="SecurityDevices">Security Devices</string>
      <string id="SecurityDevices_Explain">If this policy is enabled, you can specify a list of PKCS #11 modules to be installed. Modules are specified as a name and a fully qualified path.

If this policy is disabled or not configured, no additional PKCS #11 modules will be installed.</string>
      <string id="SecurityDevices_Add">Add</string>
      <string id="SecurityDevices_Delete">Delete</string>
      <string id="SecurityDevices_Delete_Explain">If this policy is enabled, you can specify the names of PKCS #11 modules to be deleted.

If this policy is disabled or not configured, no PKCS #11 modules will be deleted.</string>
      <string id="SearchBar">Search bar location</string>
      <string id="SearchBar_Explain">If this policy is enabled, you can set whether the search bar is separate from the URL bar.

If this policy is disabled or not configured, new users get a unified search bar, users upgrading from Firefox 56 and below get a separate search bar.</string>
      <string id="SearchEngines_1">Search Engine One</string>
      <string id="SearchEngines_2">Search Engine Two</string>
      <string id="SearchEngines_3">Search Engine Three</string>
      <string id="SearchEngines_4">Search Engine Four</string>
      <string id="SearchEngines_5">Search Engine Five</string>
      <string id="SearchEngines_Explain">If this policy is enabled, you can configure a search engine to be added to Firefox. Use {searchTerms} to indicate where the search term is placed. Due to a bug, you must select the method (usually GET). Note that you must specify the search engines in order.

If this policy is disabled or not configured, a new search engine is not added.</string>
      <string id="SearchBar_Unified">Unified</string>
      <string id="SearchBar_Separate">Separate</string>
      <string id="SearchEngine_Method_GET">GET</string>
      <string id="SearchEngine_Method_POST">POST</string>
      <string id="SearchEngines_Default">Default Search Engine</string>
      <string id="SearchEngines_Default_Explain">If this policy is enabled, you can set the name of a search engine to be used as the default.

If this policy is disabled or not configured, the Firefox default engine is used.</string>
      <string id="SearchEngines_PreventInstalls">Prevent Search Engine Installs</string>
      <string id="SearchEngines_PreventInstalls_Explain">If this policy is enabled, the user cannot install search engines from web page.

If this policy is disabled or not configured, search engines can be installed from web pages.</string>
      <string id="SearchEngines_Remove">Remove Search Engines</string>
      <string id="SearchEngines_Remove_Explain">If this policy is enabled, you can specify the names of engines to be removed or hidden.

If this policy is disabled or not configured, search engines will not be removed or hidden.</string>
      <string id="SearchSuggestEnabled">Search Suggestions</string>
      <string id="SearchSuggestEnabled_Explain">If this policy is disabled, search suggestions will be disabled.

If this policy is enabled, search suggestions will be enabled.

If this policy is not configured, search suggestions will be enabled, but the user can turn them off.</string>
      <string id="SSLVersionMin">Minimum SSL version enabled</string>
      <string id="SSLVersionMin_Explain">If this policy is enabled, Firefox will not use SSL/TLS versions less than the value specified.

If this policy is disabled or not configured, Firefox defaults to a minimum of TLS 1.2.</string>
      <string id="SSLVersionMax">Maximum SSL version enabled</string>
      <string id="SSLVersionMax_Explain">If this policy is enabled, Firefox will not use SSL/TLS versions greater than the value specified.

If this policy is disabled or not configured, Firefox defaults to a maximum of TLS 1.3.</string>
      <string id="TLS1">TLS 1.0</string>
      <string id="TLS1_1">TLS 1.1</string>
      <string id="TLS1_2">TLS 1.2</string>
      <string id="TLS1_3">TLS 1.3</string>
      <string id="SupportMenu">Support Menu</string>
      <string id="SupportMenu_Explain">If this policy is enabled, a new menuitem is added to the help menu with support information.

If this policy is disabled or not configured, no menuitem is added.</string>
      <string id="UserMessaging_WhatsNew">What's New (Deprecated)</string>
      <string id="UserMessaging_WhatsNew_Explain">If this policy is disabled, the What's new icon and menuitem will not be displayed.

If this policy is enabled or not configured, the What's New icon and menuitem will be displayed.</string>
      <string id="UserMessaging_ExtensionRecommendations">Extension Recommendations</string>
      <string id="UserMessaging_ExtensionRecommendations_Explain">If this policy is disabled, extensions will not be recommended as the user visits websites.

If this policy is enabled or not configured, extensions will be recommended as the user visits websites.</string>
      <string id="UserMessaging_FeatureRecommendations">Feature Recommendations</string>
      <string id="UserMessaging_FeatureRecommendations_Explain">If this policy is disabled, Firefox features will not be recommended as the user uses Firefox.

If this policy is enabled or not configured, Firefox features will be recommended as the user uses Firefox.</string>
      <string id="UserMessaging_UrlbarInterventions">Urlbar Interventions</string>
      <string id="UserMessaging_UrlbarInterventions_Explain">If this policy is disabled, actions will not be recommended based on what the user types in the URL bar.

If this policy is enabled or not configured, actions will be recommended based on what the user types in the URL bar.</string>
      <string id="UserMessaging_SkipOnboarding">Skip Onboarding</string>
      <string id="UserMessaging_SkipOnboarding_Explain">If this policy is enabled, onboarding messages will not be shown on the new tab page.

If this policy is disabled or not configured, onboarding messages will be shown on the new tab page.</string>
      <string id="UserMessaging_MoreFromMozilla">More from Mozilla</string>
      <string id="UserMessaging_MoreFromMozilla_Explain">If this policy is disabled, the More from Mozilla section will not be shown in preferences.

If this policy is enabled or not configured, the More from Mozilla section will be shown in preferences.</string>
      <string id="UserMessaging_FirefoxLabs">Firefox Labs</string>
      <string id="UserMessaging_FirefoxLabs_Explain">If this policy is disabled, the Firefox Labs section will not be shown in preferences.

If this policy is enabled or not configured, the Firefox Labs section will be shown in preferences.</string>
      <string id="UserMessaging_Locked">Do not allow user messaging preferences to be changed</string>
      <string id="UserMessaging_Locked_Explain">If this policy is disabled, user messaging preferences can be changed by the user.

If this policy is enabled or not configured, user messaging preferences cannot be changed by the user.</string>
      <string id="DisabledCiphers_TLS_DHE_RSA_WITH_AES_128_CBC_SHA">TLS_DHE_RSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_DHE_RSA_WITH_AES_256_CBC_SHA">TLS_DHE_RSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA">TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA">TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256">TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256">TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_128_CBC_SHA">TLS_RSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_256_CBC_SHA">TLS_RSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_3DES_EDE_CBC_SHA">TLS_RSA_WITH_3DES_EDE_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_128_GCM_SHA256">TLS_RSA_WITH_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_256_GCM_SHA384">TLS_RSA_WITH_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA">TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA">TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384">TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256">TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384">TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256">TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256</string>
      <string id="DisabledCiphers_TLS_CHACHA20_POLY1305_SHA256">TLS_CHACHA20_POLY1305_SHA256</string>
      <string id="DisabledCiphers_TLS_AES_128_GCM_SHA256">TLS_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_AES_256_GCM_SHA384">TLS_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_Explain">If this policy is enabled, the corresponding cipher is disabled.

If this policy is disabled, the corresponding cipher is enabled.

If this policy is not configured, the corresponding cipher is enabled or disabled based on the default in Firefox.</string>
      <string id="EncryptedMediaExtensions_Enabled">Enable Encrypted Media Extensions</string>
      <string id="EncryptedMediaExtensions_Enabled_Explain">If this policy is disabled, encrypted media extensions (like Widevine) are not downloaded by Firefox unless the user consents to installing them.

If this policy is enabled or not configured, encrypted media extensions (like Widevine) are downloaded automatically and used by Firefox.</string>
      <string id="EncryptedMediaExtensions_Locked">Lock Encrypted Media Extensions</string>
      <string id="EncryptedMediaExtensions_Locked_Explain">If this policy is enabled and EncryptedMediaExtensions are disabled, Firefox will not download encrypted media extensions (like Widevine) or ask the user to install them.

If this policy is not disabled or not configured, it has no effect.</string>
      <string id="PDFjs_Enabled">Enable PDF.js</string>
      <string id="PDFjs_Enabled_Explain">If this policy is disabled, the built-in PDF viewer is not used.

If this policy is enabled or not configured, the built-in PDF viewer is used.</string>
      <string id="PDFjs_EnablePermissions">Enable Permissions</string>
      <string id="PDFjs_EnablePermissions_Explain">If this policy is enabled, the built-in PDF viewer will honor document permissions like preventing the copying of text.

If this policy is not disabled or not configured, document permissions are ignored.</string>
      <string id="PictureInPicture_Enabled">Enabled</string>
      <string id="PictureInPicture_Enabled_Explain">If this policy is disabled, the Picture-in-Picture toggle does not appear on videos.

If this policy is enabled or not configured, the Picture-in-Picture toggle is available on videos.</string>
      <string id="PictureInPicture_Locked">Locked</string>
      <string id="PictureInPicture_Locked_Explain">If this policy is enabled, Picture-in-Picture settings cannot be changed by the user.

If this policy is disabled or not configured,Picture-in-Picture settings can be changed by the user.</string>
      <string id="PrimaryPassword">Primary (Master) Password</string>
      <string id="PrimaryPassword_Explain">If this policy is enabled, a primary password is required.

If this policy is disabled, users cannot create a primary password.

If this policy is not configured, users can choose to create a primary password.</string>
      <string id="HandlersOneLine">Handlers (JSON on one line)</string>
      <string id="Handlers">Handlers</string>
      <string id="Handlers_Explain">If this policy is enabled, you can use JSON to configure default application handlers.

If this policy is disabled or not configured, Firefox defaults are used.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#handlers.</string>
      <string id="PreferencesOneLine">Preferences (JSON on one line)</string>
      <string id="Preferences">Preferences</string>
      <string id="Preferences_Explain">Note: In order to use this policy, you must clear all settings in the old Preferences (Deprecated) section.

If this policy is enabled, you can use JSON to configure preferences.

If this policy is disabled or not configured, preferences are not modified.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#preferences.</string>
      <string id="BookmarksOneLine">Bookmarks (JSON on one line)</string>
      <string id="Bookmarks">Bookmarks (JSON)</string>
      <string id="Bookmarks_Explain">If this policy is enabled, you can use JSON to configure bookmarks, including [] to clear all bookmarks.

If this policy is disabled or not configured, the individual bookmark policies are used.

If this policy is enabled along with individual bookmarks, those bookmarks will not be added.

This policy has no effect on Managed Bookmarks.

For detailed information on the JSON, see https://github.com/mozilla/policy-templates/blob/master/README.md#bookmarks.</string>
      <string id="ManagedBookmarksOneLine">Managed Bookmarks (JSON on one line)</string>
      <string id="ManagedBookmarks">Managed Bookmarks</string>
      <string id="ManagedBookmarks_Explain">If this policy is enabled, you can use JSON to configure managed bookmarks.

If this policy is disabled or not configured, managed bookmarks are not added.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#managedbookmarks.</string>
      <string id="AllowedDomainsForApps">Define domains allowed to access Google Workspace</string>
      <string id="AllowedDomainsForApps_Explain">If this policy is enabled, users can only access Google Workspace for the specified domains (separated by a comma). To allow access to Gmail, you can add consumer_accounts.

If this policy is disabled or not configured, users can access any account on Google Workspace as well as Gmail.</string>
      <string id="BackgroundAppUpdate">Background updater</string>
      <string id="BackgroundAppUpdate_Explain">If this policy disabled, the application will not try to install updates when the application is not running.

If this policy is enabled or not configured, application updates may be installed (without user approval) in the background, even when the application is not running. The operating system might still require approval.</string>
      <string id="AutoLaunchProtocolsFromOriginsOneLine">Auto Launch Protocols From Origins (JSON on one line)</string>
      <string id="AutoLaunchProtocolsFromOrigins">Auto Launch Protocols From Origins</string>
      <string id="AutoLaunchProtocolsFromOrigins_Explain">If this policy is enabled, you can define a list of external protocols that can be used from listed origins without prompting the user.

If this policy is disabled or not configured, any site that invokes an external protocol will ask the user for permission.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#autolaunchprotocolsfromorigins.</string>
      <string id="WindowsSSO">Windows SSO</string>
      <string id="WindowsSSO_Explain">If this policy is enabled, Firefox will use credentials stored in Windows to sign in to Microsoft, work, and school accounts.

If this policy is disabled or not configured, credentials must be entered manually.</string>
      <string id="UseSystemPrintDialog">Use System Print Dialog</string>
      <string id="UseSystemPrintDialog_Explain">If this policy is enabled, Firefox will use the system print dialog instead of showing print preview before printing.

If this policy is disabled or not configured, Firefox will show print preview before printing.</string>
      <string id="ExemptDomainFileTypePairsFromFileTypeDownloadWarningsOneLine">Disable warnings based on file extension for specific file types on domains (JSON on one line)</string>
      <string id="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings">Disable warnings based on file extension for specific file types on domains</string>
      <string id="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings_Explain">If this policy is enabled, you can define a list of domains and file type extensions that will be exempt from executable warnings.

If this policy is disabled or not configured, warnings are shown for all executable file types.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#exemptdomainfiletypepairsfromfiletypedownloadwarnings.</string>
      <string id="StartDownloadsInTempDirectory">Start Downloads in Temporary Directory</string>
      <string id="StartDownloadsInTempDirectory_Explain">If this policy is enabled, Firefox will start downloads in a temporary directory and automatically deleted when you close the browser.

If this policy is disabled or not configured, Firefox will to the download folder and will not be automatically deleted when you close the browser.</string>
      <string id="GoToIntranetSiteForSingleWordEntryInAddressBar">Force direct intranet site navigation on single word entries in the address bar</string>
      <string id="GoToIntranetSiteForSingleWordEntryInAddressBar_Explain">If this policy is enabled, typing single word entries in the address bar will attempt to navigate to intranet sites first, falling back to search if the DNS request fails.

If this policy is disabled or not configured, typing single word entries in the address bar will search.</string>
      <string id="AppUpdatePin">Pin updates to a specific version</string>
      <string id="AppUpdatePin_Explain">If this policy is enabled, you can specify a Firefox version as xx. or xx.xx. and Firefox will not be updated beyond that major or minor version.

If this policy is disabled or not configured, Firefox will update normally.</string>
      <string id="Proxy_Locked">Do not allow proxy settings to be changed</string>
      <string id="Proxy_Locked_Explain">If this policy is enabled, proxy settings cannot be changed by the user.

If this policy is disabled or not configured, the user can change their proxy settings.</string>
      <string id="Proxy_ConnectionType">Connection Type</string>
      <string id="Proxy_ConnectionType_Explain">If this policy is enabled, you can set the connection type.

If this policy is disabled or not configured, Firefox defaults to using the system proxy.</string>
      <string id="Proxy_HTTPProxy">HTTP Proxy</string>
      <string id="Proxy_HTTPProxy_Explain">If this policy is enabled, you can set the HTTP Proxy used when manual proxy configuration is specified.

If this policy is disabled or not configured, Firefox does not use an HTTP Proxy.</string>
      <string id="Proxy_UseHTTPProxyForAllProtocols">Use HTTP proxy for HTTPS</string>
      <string id="Proxy_UseHTTPProxyForAllProtocols_Explain">If this policy is enabled, the HTTP Proxy is used for HTTPS when manual proxy configuration is specified.

If this policy is disabled or not configured, Firefox does not use an HTTPS Proxy unless specified.</string>
      <string id="Proxy_SSLProxy">HTTPS Proxy</string>
      <string id="Proxy_SSLProxy_Explain">If this policy is enabled, you can set the HTTPS Proxy used when manual proxy configuration is specified.

If this policy is disabled or not configured, Firefox does not use an HTTPS Proxy.</string>
      <string id="Proxy_SOCKSProxy">SOCKS Host</string>
      <string id="Proxy_SOCKSProxy_Explain">If this policy is enabled, you can set the SOCKS Host and version used when manual proxy configuration is specified.

If this policy is disabled or not configured, Firefox does not use a SOCKS Host.</string>
      <string id="Proxy_AutoConfigURL">Automatic proxy configuration URL</string>
      <string id="Proxy_AutoConfigURL_Explain">If this policy is enabled, you can set the automatic proxy configuration URL.

If this policy is disabled or not configured, Firefox does not use an automatic proxy configuration URL.</string>
      <string id="Proxy_Passthrough">Proxy Passthrough</string>
      <string id="Proxy_Passthrough_Explain">If this policy is enabled, the proxy settings are bypassed for the locations specified.

If this policy is disabled or not configured, Firefox does not bypass the proxy.</string>
      <string id="Proxy_AutoLogin">Do not prompt for authentication if password is saved</string>
      <string id="Proxy_AutoLogin_Explain">If this policy is enabled, Firefox will not prompt for proxy authentication when a password is saved.

If this policy is disabled or not configured, Firefox will always prompt for proxy authentication.</string>
      <string id="Proxy_UseProxyForDNS">Proxy DNS when using SOCKS</string>
      <string id="Proxy_UseProxyForDNS_Explain">If this policy is enabled, DNS is proxied when using SOCKS.

If this policy is disabled, DNS is not proxied when using SOCKS.

If this policy not configured, DNS is not proxied when using SOCKS v4, but proxied when using SOCKS v5.</string>
      <string id="DisableThirdPartyModuleBlocking">Disable Third Party Module Blocking</string>
      <string id="DisableThirdPartyModuleBlocking_Explain">If this policy is enabled, users are not allowed to block third-party modules from the about:third-party page.

If this policy is disabled or not configured, users users are allowed to block third-party modules from the about:third-party page.</string>
      <string id="ContainersOneLine">Containers (JSON on one line)</string>
      <string id="Containers">Containers</string>
      <string id="Containers_Explain">If this policy is enabled, you can use JSON to configure the default containers.

If this policy is disabled or not configured, the built-in defaults are used.

For detailed information on creating the policy, see https://github.com/mozilla/policy-templates/blob/master/README.md#containers.</string>
      <string id="FirefoxSuggest_WebSuggestions">Suggestions from the web</string>
      <string id="FirefoxSuggest_WebSuggestions_Explain">If this policy is enabled, you will get suggestions from Firefox related to your search.

If this policy is disabled, you will not get these suggestions.

If this policy is not configured, , you will get suggestions from Firefox related to your search.</string>
      <string id="FirefoxSuggest_SponsoredSuggestions">Suggestions from sponsors</string>
      <string id="FirefoxSuggest_SponsoredSuggestions_Explain">If this policy is enabled, you will support the development of Firefox with occasional sponsored suggestions.

If this policy is disabled, you will not get these suggestions.

If this policy is not configured, you will get occasional sponsored suggestions.</string>
      <string id="FirefoxSuggest_ImproveSuggest">Improve the Firefox Suggest experience</string>
      <string id="FirefoxSuggest_ImproveSuggest_Explain">If this policy is enabled, you will help create a richer search experience by allowing Mozilla to process your search queries.

If this policy is disabled or not configured, you do not allow Mozilla to process your search queries.</string>
      <string id="FirefoxSuggest_Locked">Do not allow preferences to be changed</string>
      <string id="FirefoxSuggest_Locked_Explain">If this policy is enabled, Firefox Suggest preferences cannot be changed by the user.

If this policy is disabled or not configured, the user can change their Firefox Suggest preferences.</string>
      <string id="PrintingEnabled">Printing</string>
      <string id="PrintingEnabled_Explain">If this policy is disabled, printing is disabled.

If this policy is enabled or not configured, printing is enabled.</string>
      <string id="ManualAppUpdateOnly">Manual Update Only</string>
      <string id="ManualAppUpdateOnly_Explain">If this policy is enabled, users will not be prompted to install updates and Firefox will not check for updates in the background. The user must manually check and install updates from the About dialog.

If this policy is disabled or not configured, the browser receives updates.

This policy is not recommended for most users.</string>
      <string id="AllowFileSelectionDialogs">Allow File Selection Dialogs</string>
      <string id="AllowFileSelectionDialogs_Explain">If this policy is disabled, users will not be able to open file selection dialogs. In most cases, Firefox will act as if the user clicked the cancel button.

If this policy is enabled or not configured, users can open file selection dialogs.</string>
      <string id="AutofillAddressEnabled">Enable autofill for addresses</string>
      <string id="AutofillAddressEnabled_Explain">If this policy is disabled, addresses will not be autofilled for Firefox versions and regions that support it.

If this policy is enabled or not configured, addresses will be autofilled for Firefox versions and regions that support it.</string>
      <string id="AutofillCreditCardEnabled">Enable autofill for payment methods</string>
      <string id="AutofillCreditCardEnabled_Explain">If this policy is disabled, payment methods will not be autofilled for Firefox versions and regions that support it.

If this policy is enabled or not configured, payment methods will be autofilled for Firefox versions and regions that support it.</string>
      <string id="TranslateEnabled">Enable webpage translation</string>
      <string id="TranslateEnabled_Explain">If this policy is disabled, web page translation will not be available.

If this policy is enabled or not configured, web page translation will be available.

Note: Web page translation is done completely on the client, so there is no data or privacy risk.</string>
      <string id="DisableEncryptedClientHello">Disable Encrypted Client Hello</string>
      <string id="DisableEncryptedClientHello_Explain">If this policy is enabled, the TLS feature Encrypted Client Hello (ECH) will be disabled.

If this policy is disabled or not configured, the TLS feature Encrypted Client Hello (ECH) will be enabled.</string>
      <string id="PostQuantumKeyAgreementEnabled">Enable post-quantum key agreement</string>
      <string id="PostQuantumKeyAgreementEnabled_Explain">If this policy is enabled, post-quantum key agreement for TLS will be enabled.

If this policy is disabled or not configured, post-quantum key agreement for TLS will be disabled.</string>
      <string id="HttpsOnlyMode">HTTPS-Only Mode</string>
      <string id="HttpsOnlyMode_Explain">If this policy is enabled, you can set the default behavior for HTTPS-Only Mode.

If this policy is disabled or not configured, HTTPS-Only Mode is not enabled.</string>
      <string id="HttpsOnlyMode_Allowed">Off by default</string>
      <string id="HttpsOnlyMode_Disallowed">Off and locked</string>
      <string id="HttpsOnlyMode_Enabled">On by default</string>
      <string id="HttpsOnlyMode_ForceEnabled">On and locked</string>
      <string id="HttpAllowlist">HTTP Allowlist</string>
      <string id="HttpAllowlist_Explain">If this policy is enabled, you can specify a list origins that will not be upgraded to HTTPS.

If this policy is disabled or not configured, all origins are upgraded to HTTPS if HTTPS-Only Mode is enabled.</string>
      <string id="PrivateBrowsingModeAvailability">Private Browsing Mode Availability</string>
      <string id="PrivateBrowsingModeAvailability_Explain">If this policy is enabled, you can set the availability of Private Browsing Mode.

If this policy is disabled or not configured, Private Browsing Mode is available.</string>
      <string id="PrivateBrowsingModeAvailability_0">Allow Private Browsing Mode</string>
      <string id="PrivateBrowsingModeAvailability_1">Disable Private Browsing Mode</string>
      <string id="PrivateBrowsingModeAvailability_2">Force Private Browsing Mode</string>
      <string id="ContentAnalysis_AgentName">Agent Name</string>
      <string id="ContentAnalysis_AgentName_Explain">If this policy is enabled, you can specify the name of the DLP agent, used in dialogs and notifications about DLP operations.

If this policy is disabled or not configured, the agent name "A DLP Agent" is used.</string>
      <string id="ContentAnalysis_AgentTimeout">Agent Timeout</string>
      <string id="ContentAnalysis_AgentTimeout_Explain">If this policy is enabled, you can specify the timeout in number of seconds after a DLP request is sent to the agent. After this timeout, the request will be denied unless 'Default Result' is set to 1 or 2.

If this policy is disabled or not configured, the timeout is 30 seconds.</string>
      <string id="ContentAnalysis_AllowUrlRegexList">Allow Url Regex List</string>
      <string id="ContentAnalysis_AllowUrlRegexList_Explain">If this policy is enabled, you can specify a space-separated list of regular expressions that indicates URLs for which DLP operations will always be allowed without consulting the agent. The default is "^about:(?!blank|srcdoc).*", meaning that any pages that start with "about:" will be exempt from DLP except for "about:blank" and "about:srcdoc", as these can be controlled by web content.

If this policy is disabled or not configured, the DLP agent will always be consulted.</string>
      <string id="ContentAnalysis_BypassForSameTabOperations">Bypass For Same Tab Operations</string>
      <string id="ContentAnalysis_BypassForSameTabOperations_Explain">If this policy is enabled, Firefox will automatically allow DLP requests whose data comes from the same tab and frame - for example, if data is copied to the clipboard and then pasted on the same page.

If this policy is disabled or not configured, Firefox  Firefox will not pass DLP requests whose data comes from the same tab and frame to the DLP agent as normal.</string>
      <string id="ContentAnalysis_ClientSignature">Client Signature</string>
      <string id="ContentAnalysis_ClientSignature_Explain">If this policy is enabled, you can set the required signature of the DLP agent connected to the pipe. If this is a non-empty string and the DLP agent does not have a signature with a Subject Name that exactly matches this value, Firefox will not connect to the pipe.

If this policy is disabled or not configured, the signature will not be verified.</string>
      <string id="ContentAnalysis_DefaultResult">Default Result</string>
      <string id="ContentAnalysis_DefaultResult_Explain">If this policy is enabled, you can indicate the desired behavior for DLP requests if there is a problem connecting to the DLP agent.

If this policy is disabled or not configured, the DLP request will be denied if there is a problem connecting to the agent.</string>
      <string id="ContentAnalysis_DefaultResult_0">Deny the request</string>
      <string id="ContentAnalysis_DefaultResult_1">Warn the user and allow them to choose whether to allow or deny</string>
      <string id="ContentAnalysis_DefaultResult_2">Allow the request</string>
      <string id="ContentAnalysis_DenyUrlRegexList">Deny Url Regex List</string>
      <string id="ContentAnalysis_DenyUrlRegexList_Explain">If this policy is enabled, you can specify a space-separated list of regular expressions that indicates URLs for which DLP operations will always be denied without consulting the agent.

If this policy is disabled or not configured, the DLP agent will always be consulted.</string>
      <string id="ContentAnalysis_Enabled">Enabled</string>
      <string id="ContentAnalysis_Enabled_Explain">If this policy is enabled, Firefox will use DLP.

If this policy is disabled or not configured, Firefox will not use DLP.

Note: If this policy is enabled and no DLP agent is running, all DLP requests will be denied unless Default Result is set to 1 or 2.</string>
      <string id="ContentAnalysis_IsPerUser">Is Per User</string>
      <string id="ContentAnalysis_IsPerUser_Explain">If this policy is disabled, the pipe the DLP agent creates is per-system.

If this policy is enabled or not configured, the pipe the DLP agent creates is per-user.</string>
      <string id="ContentAnalysis_PipePathName">Pipe Path Name</string>
      <string id="ContentAnalysis_PipePathName_Explain">If this policy is enabled, you can change the name of the pipe for the DLP agent.

If this policy is disabled or not configured, the default pipe name of 'path_user' is used.</string>
      <string id="ContentAnalysis_ShowBlockedResult">Show Blocked Result</string>
      <string id="ContentAnalysis_ShowBlockedResult_Explain">If this policy is disabled, Firefox will not show a notification when a DLP request is denied.

If this policy is enabled or not configured, Firefox will show a notification when a DLP request is denied.</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard">Enabled</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard_Explain">If this policy is disabled, clipboard operations will not use DLP.

If this policy is enabled or not configured, clipboard operations will use DLP.</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly">Plain Text Only</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly_Explain">If this policy is disabled, all formats will be analyzed on the clipboard, which some DLP agents may not expect.

If this policy is enabled or not configured, only the text/plain format will be analyzed on the clipboard.</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop">Enabled</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop_Explain">If this policy is disabled, drag and drop operations will not use DLP.

If this policy is enabled or not configured, drag and drop operations will use DLP.</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly">Plain Text Only</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly_Explain">If this policy is disabled, all formats will be analyzed in what is being dropped, which some DLP agents may not expect.

If this policy is enabled or not configured, only the text/plain format will be analyzed in what is being dropped.</string>
      <string id="ContentAnalysis_InterceptionPoints_FileUpload">File Upload</string>
      <string id="ContentAnalysis_InterceptionPoints_FileUpload_Explain">If this policy is disabled, file upload operations will not use DLP.

If this policy is enabled or not configured, file upload operations will use DLP.</string>
      <string id="ContentAnalysis_InterceptionPoints_Print">Print</string>
      <string id="ContentAnalysis_InterceptionPoints_Print_Explain">If this policy is disabled, print operations will not use DLP.

If this policy is enabled or not configured, print operations will use DLP.</string>
      <string id="ContentAnalysis_TimeoutResult">Timeout Result</string>
      <string id="ContentAnalysis_TimeoutResult_Explain">If this policy is enabled, you can indicate the desired behavior for DLP requests if the DLP agent does not respond to a request in less than AgentTimeout seconds. 

If this policy is disabled or not configured, the request will be denied.</string>
      <string id="SkipTermsOfUse">Skip Terms of Use</string>
      <string id="SkipTermsOfUse_Explain">If this policy is enabled, the Firefox Terms of Use (https://www.mozilla.org/about/legal/terms/firefox/) and Privacy Notice (https://www.mozilla.org/privacy/firefox/) do not display upon startup.

You represent that you accept and have the authority to accept the Terms of Use on behalf of all individuals to whom you provide access to this browser.

If this policy is disabled or not configured, the Firefox Terms of Use and Privacy Notice do display upon startup.</string>
      <string id="Preferences_Boolean_Explain">If this policy is enabled, the preference is locked to true. If this policy is disabled, the preference is locked to false.

For a description of the preference, see:

https://github.com/mozilla/policy-templates/blob/master/README.md#preferences</string>
      <string id="Preferences_String_Explain">If this policy is enabled, the preference is locked to the string entered. If this policy is disabled, it has no effect.

For a description of the preference, see:

https://github.com/mozilla/policy-templates/blob/master/README.md#preferences</string>
      <string id="Preferences_Enum_Explain">If this policy is enabled, the preference is locked to the value selected. If this policy is disabled, it has no effect.

For a description of the preference, see:

https://github.com/mozilla/policy-templates/blob/master/README.md#preferences.</string>
      <string id="Preferences_Unsupported_Explain">This preference is no longer support on Windows. We are investigating creating a policy.</string>
      <string id="Preferences_accessibility_force_disabled_auto">Auto (0)</string>
      <string id="Preferences_accessibility_force_disabled_off">Always Off (1)</string>
      <string id="Preferences_security_default_personal_cert_Ask_Every_Time">Ask Every Time</string>
      <string id="Preferences_security_default_personal_cert_Select_Automatically">Select Automatically</string>
      <string id="accessibility_force_disabled">accessibility.force_disabled</string>
      <string id="app_update_auto">app.update.auto (Deprecated)</string>
      <string id="browser_bookmarks_autoExportHTML">browser.bookmarks.autoExportHTML</string>
      <string id="browser_bookmarks_file">browser.bookmarks.file</string>
      <string id="browser_bookmarks_restore_default_bookmarks">browser.bookmarks.restore_default_bookmarks</string>
      <string id="browser_cache_disk_enable">browser.cache.disk.enable</string>
      <string id="browser_fixup_dns_first_for_single_words">browser.fixup.dns_first_for_single_words</string>
      <string id="browser_places_importBookmarksHTML">browser.places.importBookmarksHTML</string>
      <string id="browser_safebrowsing_phishing_enabled">browser.safebrowsing.phishing.enabled</string>
      <string id="browser_safebrowsing_malware_enabled">browser.safebrowsing.malware.enabled</string>
      <string id="browser_search_update">browser.search.update</string>
      <string id="browser_tabs_warnOnClose">browser.tabs.warnOnClose</string>
      <string id="browser_cache_disk_parent_directory">browser.cache.disk.parent_directory</string>
      <string id="browser_slowStartup_notificationDisabled">browser.slowStartup.notificationDisabled</string>
      <string id="browser_taskbar_previews_enable">browser.taskbar.previews.enable</string>
      <string id="browser_urlbar_suggest_bookmark">browser.urlbar.suggest.bookmark</string>
      <string id="browser_urlbar_suggest_history">browser.urlbar.suggest.history</string>
      <string id="browser_urlbar_suggest_openpage">browser.urlbar.suggest.openpage</string>
      <string id="datareporting_policy_dataSubmissionPolicyBypassNotification">datareporting.policy.dataSubmissionPolicyBypassNotification</string>
      <string id="dom_allow_scripts_to_close_windows">dom.allow_scripts_to_close_windows</string>
      <string id="dom_disable_window_flip">dom.disable_window_flip</string>
      <string id="dom_disable_window_move_resize">dom.disable_window_move_resize</string>
      <string id="dom_event_contextmenu_enabled">dom.event.contextmenu.enabled</string>
      <string id="dom_keyboardevent_keypress_hack_dispatch_non_printable_keys_addl">dom.keyboardevent.keypress.hack.dispatch_non_printable_keys.addl</string>
      <string id="dom_keyboardevent_keypress_hack_use_legacy_keycode_and_charcode_addl">dom.keyboardevent.keypress.hack.use_legacy_keycode_and_charcode.addl</string>
      <string id="dom_xmldocument_load_enabled">dom.xmldocument.load.enabled</string>
      <string id="dom_xmldocument_async_enabled">dom.xmldocument.async.enabled</string>
      <string id="extensions_blocklist_enabled">extensions.blocklist.enabled</string>
      <string id="geo_enabled">geo.enabled</string>
      <string id="extensions_getAddons_showPane">extensions.getAddons.showPane</string>
      <string id="intl_accept_languages">intl.accept_languages</string>
      <string id="media_eme_enabled">media.eme.enabled (Deprecated)</string>
      <string id="media_gmp-gmpopenh264_enabled">media.gmp-gmpopenh264.enabled</string>
      <string id="media_gmp-widevinecdm_enabled">media.gmp-widevinecdm.enabled</string>
      <string id="network_dns_disableIPv6">network.dns.disableIPv6</string>
      <string id="network_IDN_show_punycode">network.IDN_show_punycode</string>
      <string id="places_history_enabled">places.history.enabled</string>
      <string id="print_save_print_settings">print.save_print_settings</string>
      <string id="security_default_personal_cert">security.default_personal_cert</string>
      <string id="security_ssl_errorReporting_enabled">security.ssl.errorReporting.enabled</string>
      <string id="security_mixed_content_block_active_content">security.mixed_content.block_active_content</string>
      <string id="ui_key_menuAccessKeyFocuses">ui.key.menuAccessKeyFocuses</string>
      <string id="browser_newtabpage_activity-stream_default_sites">browser.newtabpage.activity-stream.default.sites</string>
      <string id="extensions_htmlaboutaddons_recommendations_enabled">extensions.htmlaboutaddons.recommendations.enabled</string>
      <string id="media_peerconnection_enabled">media.peerconnection.enabled</string>
      <string id="media_peerconnection_ice_obfuscate_host_addresses_whitelist">media.peerconnection.ice.obfuscate_host_addresses.whitelist (Deprecated)</string>
      <string id="media_peerconnection_ice_obfuscate_host_addresses_blocklist">media.peerconnection.ice.obfuscate_host_addresses.blocklist</string>
      <string id="security_osclientcerts_autoload">security.osclientcerts.autoload</string>
      <string id="security_tls_hello_downgrade_check">security.tls.hello_downgrade_check</string>
      <string id="widget_content_gtk-theme-override">widget.content.gtk-theme-override</string>
    </stringTable>
    <presentationTable>
      <presentation id="AppUpdateURL">
        <textBox refId="AppUpdateURL">
          <label>URL:</label>
        </textBox>
      </presentation>
      <presentation id="Authentication">
        <listBox refId="Authentication"/>
      </presentation>
      <presentation id="Authentication_AllowNonFQDN">
        <checkBox refId="Authentication_AllowNonFQDN_NTLM">Always allow NTLM on non FQDNs</checkBox>
        <checkBox refId="Authentication_AllowNonFQDN_SPNEGO">Always allow SPNEGO on non FQDNs</checkBox>
      </presentation>
      <presentation id="Authentication_AllowProxies">
        <checkBox refId="Authentication_AllowProxies_NTLM">Allow NTLM to automatically authenticate with proxy servers</checkBox>
        <checkBox refId="Authentication_AllowProxies_SPNEGO">Allow SPNEGO to automatically authenticate with proxy servers</checkBox>
      </presentation>
      <presentation id="Certificates_Install">
        <listBox refId="Certificates_Install"/>
      </presentation>
      <presentation id="RequestedLocales">
        <listBox refId="RequestedLocales"/>
      </presentation>
      <presentation id="SecurityDevices">
        <listBox refId="SecurityDevices"/>
      </presentation>
      <presentation id="Extensions">
        <listBox refId="Extensions"/>
      </presentation>
      <presentation id="WebsiteFilter">
        <listBox refId="WebsiteFilter"/>
      </presentation>
      <presentation id="Permissions"><listBox refId="Permissions"/></presentation>
      <presentation id="PopupsAllow"><listBox refId="PopupsAllowDesc">Allow popups for websites</listBox></presentation>
      <presentation id="Cookies_AcceptThirdParty">
        <dropdownList refId="Cookies_AcceptThirdParty"/>
      </presentation>
      <presentation id="Cookies_Behavior">
        <dropdownList refId="Cookies_Behavior"/>
      </presentation>
      <presentation id="Cookies_BehaviorPrivateBrowsing">
        <dropdownList refId="Cookies_BehaviorPrivateBrowsing"/>
      </presentation>
      <presentation id="SearchBar">
        <dropdownList refId="SearchBar"/>
      </presentation>
      <presentation id="TrackingProtection">
        <checkBox refId="TrackingProtectionLocked">Don't allow tracking protection preferences to be changed.</checkBox>
        <checkBox refId="Cryptomining">Block cryptomining scripts.</checkBox>
        <checkBox refId="Fingerprinting">Block fingerprinting scripts.</checkBox>
        <text>Exceptions:</text>
        <listBox refId="TrackingProtection_Exceptions"/>
      </presentation>
      <presentation id="TrackingProtection_Exceptions">
        <listBox refId="TrackingProtection_Exceptions"/>
      </presentation>
      <presentation id="OverridePage">
        <textBox refId="OverridePage">
          <label>URL:</label>
        </textBox>
      </presentation>
      <presentation id="HomepageURL">
        <text>URL:</text>
        <textBox refId="HomepageURL">
          <label/>
        </textBox>
        <checkBox refId="HomepageLocked">Don't allow the homepage to be changed.</checkBox>
      </presentation>
      <presentation id="HomepageAdditional">
        <listBox refId="HomepageAdditional">Additional homepages</listBox>
      </presentation>
      <presentation id="StartPage">
        <dropdownList refId="StartPage"/>
      </presentation>
      <presentation id="Bookmark">
        <text>Title:</text>
        <textBox refId="BookmarkTitle">
          <label/>
        </textBox>
        <text>URL:</text>
        <textBox refId="BookmarkURL">
          <label/>
        </textBox>
        <text>Favicon URL:</text>
        <textBox refId="BookmarkFavicon">
          <label/>
        </textBox>
        <text>Placement:</text>
        <dropdownList refId="BookmarkPlacement"/>
        <text>Folder name:</text>
        <textBox refId="BookmarkFolder">
          <label/>
        </textBox>
      </presentation>
      <presentation id="SearchEngine">
        <textBox refId="SearchEngine_Name">
          <label>Name:</label>
        </textBox>
        <textBox refId="SearchEngine_URLTemplate">
          <label>URL Template:</label>
        </textBox>
        <text>Method:</text>
        <dropdownList refId="SearchEngine_Method"/>
        <textBox refId="SearchEngine_IconURL">
          <label>Icon URL:</label>
        </textBox>
        <textBox refId="SearchEngine_Alias">
          <label>Alias:</label>
        </textBox>
        <textBox refId="SearchEngine_Description">
          <label>Description:</label>
        </textBox>
        <textBox refId="SearchEngine_SuggestURLTemplate">
          <label>Suggest URL Template:</label>
        </textBox>
        <textBox refId="SearchEngine_PostData">
          <label>POST data:</label>
        </textBox>
        <textBox refId="SearchEngine_Encoding">
          <label>Encoding:</label>
        </textBox>
      </presentation>
      <presentation id="SearchEngines_Default">
        <textBox refId="SearchEngines_Default">
          <label/>
        </textBox>
      </presentation>
      <presentation id="SearchEngines_Remove">
        <listBox refId="SearchEngines_Remove"/>
      </presentation>
      <presentation id="Proxy">
        <checkBox refId="ProxyLocked">Don't allow proxy settings to be changed.</checkBox>
        <text>Connection Type:</text>
        <dropdownList refId="ConnectionType"/>
        <text>HTTP Proxy:</text>
        <textBox refId="HTTPProxy">
          <label/>
        </textBox>
        <checkBox refId="UseHTTPProxyForAllProtocols">Use this proxy server for all protocols.</checkBox>
        <text>SSL Proxy:</text>
        <textBox refId="SSLProxy">
          <label/>
        </textBox>
        <text>FTP Proxy:</text>
        <textBox refId="FTPProxy">
          <label/>
        </textBox>
        <text>SOCKS Host:</text>
        <textBox refId="SOCKSProxy">
          <label/>
        </textBox>
        <text>SOCKS Version:</text>
        <dropdownList refId="SOCKSVersion"/>
        <text>No proxy for</text>
        <textBox refId="Passthrough">
          <label/>
        </textBox>
        <text>Example: .mozilla.org, .net.nz, ***********/24</text>
        <text>Automatic proxy configuration URL:</text>
        <textBox refId="AutoConfigURL">
          <label/>
        </textBox>
        <checkBox refId="AutoLogin">Do not prompt for authentication if password is saved.</checkBox>
        <checkBox refId="UseProxyForDNS">Proxy DNS when using SOCKS v5.</checkBox>
      </presentation>
      <presentation id="DNSOverHTTPS">
        <text>Provider URL:</text>
        <textBox refId="ProviderURL">
          <label/>
        </textBox>
        <checkBox refId="DNSOverHTTPSEnabled">Enable DNS over HTTPS.</checkBox>
        <checkBox refId="DNSOverHTTPSLocked">Don't allow DNS over HTTPS preferences to be changed.</checkBox>
      </presentation>
      <presentation id="SSLVersionMin">
        <dropdownList refId="SSLVersion" defaultItem="2"/>
      </presentation>
      <presentation id="SSLVersionMax">
        <dropdownList refId="SSLVersion" defaultItem="3"/>
      </presentation>
      <presentation id="SupportMenu">
        <text>Title:</text>
        <textBox refId="SupportMenuTitle">
          <label/>
        </textBox>
        <text>URL:</text>
        <textBox refId="SupportMenuURL">
          <label/>
        </textBox>
        <text>Access key:</text>
        <textBox refId="SupportMenuAccessKey">
          <label/>
        </textBox>
      </presentation>
      <presentation id="Preferences_String">
        <textBox refId="Preferences_String">
          <label/>
        </textBox>
      </presentation>
      <presentation id="Preferences_accessibility_force_disabled">
        <dropdownList refId="Preferences_accessibility_force_disabled"/>
      </presentation>
      <presentation id="Preferences_security_default_personal_cert">
        <dropdownList refId="Preferences_security_default_personal_cert"/>
      </presentation>
      <presentation id="LegacySameSiteCookieBehaviorEnabledForDomainList">
        <listBox refId="LegacySameSiteCookieBehaviorEnabledForDomainList"/>
      </presentation>
      <presentation id="LocalFileLinks">
        <listBox refId="LocalFileLinks"/>
      </presentation>
      <presentation id="SanitizeOnShutdown">
        <checkBox refId="SanitizeOnShutdown_Cache">Cache</checkBox>
        <checkBox refId="SanitizeOnShutdown_Cookies">Cookies</checkBox>
        <checkBox refId="SanitizeOnShutdown_Downloads">Download History</checkBox>
        <checkBox refId="SanitizeOnShutdown_FormData">Form &amp; Search History</checkBox>
        <checkBox refId="SanitizeOnShutdown_History">Browsing History</checkBox>
        <checkBox refId="SanitizeOnShutdown_Sessions">Active Logins</checkBox>
        <checkBox refId="SanitizeOnShutdown_SiteSettings">Site Preferences</checkBox>
        <checkBox refId="SanitizeOnShutdown_OfflineApps">Offline Website Data</checkBox>
      </presentation>
      <presentation id="FirefoxHome">
        <checkBox refId="FirefoxHome_Search">Search</checkBox>
        <checkBox refId="FirefoxHome_TopSites">Top Sites</checkBox>
        <checkBox refId="FirefoxHome_SponsoredTopSites">Sponsored Top Sites</checkBox>
        <checkBox refId="FirefoxHome_Highlights">Download History</checkBox>
        <checkBox refId="FirefoxHome_Pocket">Recommended by Pocket</checkBox>
        <checkBox refId="FirefoxHome_SponsoredPocket">Sponsored Pocket Stories</checkBox>
        <checkBox refId="FirefoxHome_Snippets">Snippets</checkBox>
        <checkBox refId="FirefoxHome_Locked">Don't allow settings to be changed</checkBox>
      </presentation>
      <presentation id="ExtensionSettings">
        <multiTextBox refId="ExtensionSettings"/>
      </presentation>
      <presentation id="Handlers">
        <multiTextBox refId="Handlers"/>
      </presentation>
      <presentation id="DisplayMenuBar">
        <dropdownList refId="DisplayMenuBar"/>
      </presentation>
      <presentation id="DisplayBookmarksToolbar">
        <dropdownList refId="DisplayBookmarksToolbar"/>
      </presentation>
      <presentation id="String">
        <textBox refId="String">
          <label/>
        </textBox>
      </presentation>
      <presentation id="List">
        <listBox refId="List"/>
      </presentation>
      <presentation id="Autoplay_Default">
        <dropdownList refId="Autoplay_Default"/>
      </presentation>
      <presentation id="JSON">
        <multiTextBox refId="JSON"/>
      </presentation>
      <presentation id="JSONOneLine">
        <textBox refId="JSONOneLine">
          <label/>
        </textBox>
      </presentation>
      <presentation id="Proxy_ConnectionType">
        <dropdownList refId="Proxy_ConnectionType"/>
      </presentation>
      <presentation id="Proxy_HTTPProxy">
        <textBox refId="Proxy_HTTPProxy">
          <label>Host including port:</label>
        </textBox>
      </presentation>
      <presentation id="Proxy_SSLProxy">
        <textBox refId="Proxy_SSLProxy">
          <label>Host including port:</label>
        </textBox>
      </presentation>
      <presentation id="Proxy_SOCKSProxy">
        <text>Host including port:</text>
        <textBox refId="Proxy_SOCKSProxy">
          <label/>
        </textBox>
        <text>SOCKS Version:</text>
        <dropdownList refId="Proxy_SOCKSVersion"/>
      </presentation>
      <presentation id="Proxy_AutoConfigURL">
        <textBox refId="Proxy_AutoConfigURL">
          <label>URL:</label>
        </textBox>
      </presentation>
      <presentation id="Proxy_Passthrough">
        <text>No proxy for</text>
        <textBox refId="Proxy_Passthrough">
          <label/>
        </textBox>
        <text>Example: .mozilla.org, .net.nz, ***********/24</text>
        <text>Connections to localhost, 127.0.0.1/8, and ::1 are never proxied.</text>
      </presentation>
      <presentation id="HttpsOnlyMode">
        <dropdownList refId="HttpsOnlyMode"/>
      </presentation>
      <presentation id="PrivateBrowsingModeAvailability">
        <dropdownList refId="PrivateBrowsingModeAvailability"/>
      </presentation>
      <presentation id="ContentAnalysis_DefaultResult">
        <dropdownList refId="ContentAnalysis_DefaultResult"/>
      </presentation>
      <presentation id="Number">
        <decimalTextBox refId="Number"/>
      </presentation>
      <presentation id="ContentAnalysis_TimeoutResult">
        <dropdownList refId="ContentAnalysis_TimeoutResult"/>
      </presentation>
    </presentationTable>
  </resources>
</policyDefinitionResources>
