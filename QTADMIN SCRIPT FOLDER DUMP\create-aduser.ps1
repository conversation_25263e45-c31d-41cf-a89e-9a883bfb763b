﻿$userfile = get-content -path 'c:\users\<USER>\desktop\Names2.txt'

Foreach ($username in $userfile) {
	#$name = $username.Split(".")
    #$firstname = $name[0].substring(0,1).toupper()+$name[0].substring(1).tolower()
    #$lastname = $name[1].substring(0,1).toupper()+$name[1].substring(1).tolower()
    #New-ADUser -samaccountname $username -userprincipalname $<EMAIL> -DisplayName "$firstname $lastname" -Name "$firstname $lastname" -GivenName $firstname -Surname $lastname -Path "OU=_DISABLED_,OU=QHR Technologies Inc,DC=QuadrantHR,DC=com" -Enabled $false
    Set-ADAccountPassword -Identity $username  -NewPassword (ConvertTo-SecureString -asplaintext "ABCD989i32u89u8KHKSKHDF!" -Force)
    Set-ADUser -Identity $username -Enabled $true
}