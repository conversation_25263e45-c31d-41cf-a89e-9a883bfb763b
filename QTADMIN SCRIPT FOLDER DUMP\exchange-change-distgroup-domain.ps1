﻿# This script will replace the default domain for all distribution groups

# Connect to exchange on-prem
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://qtexch1.quadranthr.com/PowerShell/ -Authentication Kerberos -Credential $UserCredential
Import-PSSession $Session -AllowClobber

$olddomain = "@qhrtechnologies.com"
$newdomain = "@QHRtech.com"

$GetDists = Get-DistributionGroup  | ? {$_.primarysmtpaddress -match $olddomain -and $_.emailaddresses -notmatch $newdomain}

# Set default email address to $newdomain
foreach ($mbx in $GetDists) {
  $oldemail = $mbx.PrimarySmtpAddress.ToString()
  $newemail =  $oldemail -ireplace $olddomain,$newdomain
  Write " Changing $oldemail to $newemail"
    
  Set-DistributionGroup $mbx.Identity -EmailAddressPolicyEnabled $false -PrimarySmtpAddress $newemail
}

