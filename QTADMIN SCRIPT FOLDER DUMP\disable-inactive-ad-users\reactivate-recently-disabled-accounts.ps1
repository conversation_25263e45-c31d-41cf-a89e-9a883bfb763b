﻿# This script is used to reactivate recently disabled AD accounts from the script "disable-inactive-accounts.ps1"


###### Set these paths
$csvPath = "C:\Scripts\disable-inactive-ad-users\recently-disabled-accounts.csv" #Import users that were disabled last time disable-inactive-accounts.ps1 was run
$log = "C:\Scripts\disable-inactive-ad-users\logs\$(get-date -f yyyy-MM-dd)-reactivate-recently-disabled-accounts.log" # Log Path 


"$(get-date -f g) - Starting reactive-recently-disabled-accounts.ps1" | out-file $log -Append


#import CSV of disabled users to enable again
try {
    "$(get-date -f g) - Importing users from $csvpath" | out-file $log -Append
    $users = import-csv $csvpath
    $numberOfUsersToExclude = $users.count
    "$(get-date -f g) - Successful import of CSV users, $numberOfUsersToExclude users were imported" | out-file $log -Append
}
catch{
    "ERROR: can't read csv file in $csvpath. Stopping Script"
    "$(get-date -f g) - ERROR: can't read csv file in $csvpath. Stopping Script" | out-file $log -Append
    "" | out-file $log -Append
    break
}


#enable all imported user accounts and log them
try {
    foreach($member in $users) {
        $memberSamName = $member.samAccountName
        "$memberSamName" | out-file $log -Append
        "$memberSamName"
    }
}
catch {
    "$(get-date -f g) Error: Can't disable accounts. Might be permissions issue. Stopping Script" | out-file $log -Append
    "$(get-date -f g) Error: Can't disable accounts. Might be permissions issue. Stopping Script" 

}

"$(get-date -f g) - Finished reactive-recently-disabled-accounts.ps1" | out-file $log -Append