﻿Import-Module ActiveDirectory
 
# All of the properties you'd like to pull from Get-ADUser
$properties=@(
    'givenName',
    'sn',
    'displayName',
    'sAMAccountType',
    'Created',
    'objectGUID',
    'department',
    'title',
    'enabled',
    'description',
    'userPrincipalName',
    'lastLogon',
    'sAMAccountName'
    )
 
 
# All of the expressions you want with all of the filtering .etc you'd like done to them
$expressions=@(
    @{Expression={$_.userPrincipalName};Label="userPrincipalName"},
        @{Expression={$_.sAMAccountName};Label="Logon Name"},
    @{Expression={$_.displayName};Label="displayName"},
      @{Expression={$_.title};Label="Title"},
        @{Expression={$_.objectGUID};Label="id"},
    @{Expression={$_.sAMAccountType};Label="Account Type"},
   @{Expression={$_.department};Label="Department"},
   @{Expression={$_.enabled};Label="accountEnabled"},
    @{Expression={$_.description};Label="Description"},
    @{Expression={$_.Created};Label="Created"},
    @{Expression={[DateTime]::FromFileTime([Int64]::Parse($_.lastLogon))};Label="Last Log-on Date"}


    )
 
$path_to_file = "C:\scripts\admin-and-service-user-report_$((Get-Date).ToString('yyyy-MM-dd_hh-mm-ss')).csv"

 
$groups = "Enterprise Admins","Domain Admins", "Schema Admins"
$results = foreach ($group in $groups) {
Get-ADGroupMember $group | Get-aduser -Properties $properties | select $expressions
} 
$results += Get-ADGroupMember $group | Get-aduser -filter {Title -eq "Service Account" -And enabled -eq "TRUE"}  -Properties $properties | select $expressions
 $results += Get-ADGroupMember $group |Get-aduser -filter {Name -like "*Domain*" -And enabled -eq "TRUE"} -Properties $properties | select $expressions
$results += Get-ADGroupMember $group | Get-aduser -filter {sAMAccountName -like '*test*' -And enabled -eq "TRUE"}  -Properties $properties | select $expressions
$results += Get-ADGroupMember $group | Get-aduser -filter {sAMAccountName -like '*svc*' -And enabled -eq "TRUE"}  -Properties $properties | select $expressions


$results
$results | Export-CSV $path_to_file -notypeinformation -Encoding UTF8