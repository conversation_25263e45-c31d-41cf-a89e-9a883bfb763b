﻿# Import necessary modules
Import-Module ActiveDirectory
Import-Module ExchangeOnlineManagement
Import-Module AzureAD


# Function to Revoke Exchange Online Sessions
function Revoke-ExchangeSessions {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    # Revoke Exchange Online sessions
    Get-Mailbox -Identity $exitedUser | Remove-MailboxPermission -User $exitedUser -AccessRights FullAccess -Confirm:$false
}

# Function to Disable User Account in AD
function Disable-ADUser {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    Disable-ADAccount -Identity $exitedUser
    Write-Host "Disabled AD account for '$exitedUser'"
}

# Function to Disable Mailbox
function Disable-Mailbox {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    Disable-Mailbox -Identity $exitedUser -Confirm:$false
    Write-Host "Disabled mailbox for '$exitedUser'"
}

# Function to Update Group Membership
function Update-GroupMembership {
    param (
        [Parameter(Mandatory=$true)]
        [string]$exitedUser
    )
    $noAccessGroup = "NoAccess"
    $qHREmployeeGroup = "QHEmployee"
    
    # Remove user from all local AD groups
    $userGroups = Get-ADUser -Identity $exitedUser -Properties MemberOf | Select-Object -ExpandProperty MemberOf
    foreach ($group in $userGroups) {
        Remove-ADGroupMember -Identity $group -Members $exitedUser -Confirm:$false
        Write-Host "Removed '$exitedUser' from '$group'"
    }

    # Add user to 'NoAccess' group
    Add-ADGroupMember -Identity $noAccessGroup -Members $exitedUser
    Write-Host "Added '$exitedUser' to '$noAccessGroup'"
    
    # Add user to 'QHEmployee' distribution list
    Add-DistributionGroupMember -Identity $qHREmployeeGroup -Member $exitedUser
    Write-Host "Added '$exitedUser' to '$qHREmployeeGroup'"
}

# Function to Hide Email Address from Exchange Address Book
function Hide-EmailFromAddressBook {
    param (
        [Parameter(Mandatory=$true)]
        [string]$UserName
    )
    Set-Mailbox -Identity $exitedUser -HiddenFromAddressListsEnabled $true
    Write-Host "Hidden '$exitedUser' from the Exchange Address Book"
}

# Function to Move User and Computer to a Specific OU
function Move-UserAndComputer {
    param (
        [Parameter(Mandatory=$true)]
        [string]$UserName,
        [Parameter(Mandatory=$true)]
        [string]$TargetOU
    )
    $user = Get-ADUser -Identity $exitedUser
    Move-ADObject -Identity $user.DistinguishedName -TargetPath $TargetOU
    Write-Host "Moved '$exitedUser' to '$TargetOU'"
    
    $computer = Get-ADComputer -Filter { Name -eq $exitedUser }
    if ($computer) {
        Move-ADObject -Identity $computer.DistinguishedName -TargetPath $TargetOU
        Write-Host "Moved computer '$UserName' to '$TargetOU'"
    } else {
        Write-Host "No computer account found for '$exitedUser'"
    }
}

# Function to Remove User from Azure AD Groups
function Remove-UserFromAzureADGroups {
    param (
        [Parameter(Mandatory=$true)]
        [string]$UserName
    )
    $user = Get-AzureADUser -UserPrincipalName $exitedUser
    if ($user) {
        $groups = Get-AzureADUserMembership -ObjectId $user.ObjectId
        foreach ($group in $groups) {
            Remove-AzureADGroupMember -ObjectId $group.ObjectId -MemberId $user.ObjectId
            Write-Host "Removed '$exitedUser' from Azure AD group '$($group.DisplayName)'"
        }
    } else {
        Write-Host "No Azure AD user found with UserPrincipalName '$exitedUser'"
    }
}

# Main Script Execution
# Prompt for the username
#DO NOT CHANGER VARIABLE NAME!
$userName = Read-Host -Prompt "Enter the username"

Connect-ExchangeOnline -UserPrincipalName $userName

# Prompt for the username to disable and remove attributes
$exitedUser = Read-Host -Prompt "Enter the username to be exited"

# Revoke Exchange sessions
Revoke-ExchangeSessions -UserName $exitedUser

# Disable user account in AD
Disable-ADUser -UserName $exitedUser

# Disable mailbox
Disable-Mailbox -UserName $exitedUser

# Hide email address from Exchange Address Book
Hide-EmailFromAddressBook -UserName $exitedUser

# Move user and computer to the 'Graveyard/Disabled Users' OU
$targetOU = "OU=Disabled Users,OU=Graveyard,DC=yourdomain,DC=com"
Move-UserAndComputer -UserName $exitedUser -TargetOU $targetOU

# Update group memberships
Update-GroupMembership -UserName $exitedUser

# Remove user from Azure AD groups
Remove-UserFromAzureADGroups -UserName $exitedUser

# Disconnect from Exchange Online
Disconnect-ExchangeOnline -Confirm:$false

Write-Host "User account '$exitedUser' has been processed for termination."
