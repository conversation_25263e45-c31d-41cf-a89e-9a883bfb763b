﻿#Install-Module AzureAD

#Connect-AzureAD

#get-azureadgroup -searchstring 'MFA'
#ObjectId                             DisplayName             Description              
#--------                             -----------             -----------              
#2f4d11fa-566c-47ab-8641-63edb31d6dff MFA - Testing All Users Testing MFA for All Users

#Get-AzureAdGroupMember -All $True -objectid 2f4d11fa-566c-47ab-8641-63edb31d6dff | Export-csv -path C:\scripts\MFAUsers.csv
Get-AzureADUser -All $True | Export-csv -path C:\scripts\AllUsers.csv