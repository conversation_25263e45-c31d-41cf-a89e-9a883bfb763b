﻿Import-Module ActiveDirectory
 
# All of the properties you'd like to pull from Get-ADUser
$properties=@(
    'DisplayName',
    'title',
    'department',
    'title'
    'Manager'
    )
 
 
# All of the expressions you want with all of the filtering .etc you'd like done to them
$expressions=@(
    @{Expression={$_.DisplayName};Label="Name"},
    @{Expression={$_.title};Label="Role"},
    @{Expression={$_.department};Label="Department"}
    @{Expression={$_.title};Label="Title"}
    @{n="manager"; e={$_.manager.split(",")[0].replace("CN=", "")}}
    )
 
$path_to_file = "C:\scripts\user-report_$((Get-Date).ToString('yyyy-MM-dd_hh-mm-ss')).csv"
 
Get-ADUser -Filter 'enabled -eq $true' -Properties $properties | select $expressions | Export-CSV $path_to_file -notypeinformation -Encoding UTF8