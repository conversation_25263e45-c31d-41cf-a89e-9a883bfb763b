﻿#install-PackageProvider -Name NuGet -MinimumVersion ********* -Force
#install-module  MicrosoftTeams -RequiredVersion '2.3.1'
Import-Module MicrosoftTeams  -RequiredVersion '2.3.1'
Connect-MicrosoftTeams #-TenantId 2ff1bbf0-c017-4130-bfc5-f9b6b0347029
#Lines 7+8 change the phone number, line 9 confirms changes, line 10 disconnects connection. Line 4 connects connection
#Set-CsUser -identity <EMAIL> -EnterpriseVoiceEnabled $true -HostedVoicemail $true -OnPremlineURI tel:+***********
#Grant-CsOnlineVoiceRoutingPolicy -Identity <EMAIL> -PolicyName "ThinkTel"
Get-CsOnlineUser -identity "<EMAIL>" | fl *Voice*, *PSTN*, *lineuri*
Disconnect-MicrosoftTeams