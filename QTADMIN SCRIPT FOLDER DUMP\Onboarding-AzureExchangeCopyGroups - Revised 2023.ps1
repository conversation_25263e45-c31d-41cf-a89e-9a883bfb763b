# Check if Active Directory module is available, if not, install it
$adModule = Get-Module -Name ActiveDirectory -ListAvailable
if ($adModule -eq $null) {
    Install-WindowsFeature RSAT-AD-PowerShell
    Import-Module ActiveDirectory
}

# Import required modules
Import-Module AzureAD
Import-Module ExchangeOnlineManagement

# Connect to Azure AD and Exchange Online
Connect-AzureAD
Connect-ExchangeOnline

# Loop for user replication
do {
    # Prompt for the template user's email address
    $TemplateUser = Read-Host -Prompt 'Who will be used as the template user? (firstname.lastname)'
    $TemplateUser += '@qhrtech.com'

    # Prompt for the new user's email address
    $NewUser = Read-Host -Prompt 'What is the name of the new user? (firstname.lastname)'
    $NewUser += '@qhrtech.com'

    # Get new user's Object ID and Display Name
    $NewUserObject = Get-AzureADUser -ObjectId $NewUser
    $NewUserObjectId = $NewUserObject.ObjectId
    $NewUserDisplayName = $NewUserObject.DisplayName

    # Get template user's group memberships
    $templateUserGroups = Get-AzureADUserMembership -ObjectId $TemplateUser 

    foreach ($group in $templateUserGroups) { 
        try {
            Add-AzureADGroupMember -ObjectId $group.ObjectId -RefObjectId $NewUserObjectId
        }
        catch {
            if ($_ -like "*Cannot Update a mail-enabled security groups and or distribution list*") {
                $distGroupName = $group.DisplayName
                $distGroupMembers = Get-DistributionGroupMember -Identity $distGroupName

                if ($distGroupMembers.Name -contains $NewUserDisplayName) {
                    continue
                }
                else {
                    if ($group.DisplayName -notlike "*All Staff*") {
                        Write-Host
                        Write-Host "Adding $NewUserDisplayName to the mail group: $distGroupName"
                        Add-DistributionGroupMember -Identity $distGroupName -Member $NewUser
                    }
                }
            }
            else {
                continue
            }
        }
    }

    # Prompt for the new user's province/region
    $NewUserArea = "All Staff - "
    $NewUserArea += Read-Host -Prompt 'Which province/region is the user from? (BC, AB, MB, NS, ON, SK)'

    if ($NewUserArea -contains "All Staff - BC") {
        $NewUserKelownaOrRemote = Read-Host -Prompt "Does the new onboard live in Kelowna? (y/n)"
        if ($NewUserKelownaOrRemote -contains "y") {
            Write-Host "Adding $NewUserDisplayName to the mail group: All Staff - BC Kelowna"
            Add-DistributionGroupMember -Identity "All Staff - Kelowna BC" -Member $NewUser
        }
        else {
            Write-Host "Adding $NewUserDisplayName to the mail group: All Staff - BC"
            Add-DistributionGroupMember -Identity "All Staff - BC" -Member $NewUser
        }
    }
    else {
        Write-Host "Adding $NewUserDisplayName to the mail group:  $NewUserArea"
        Add-DistributionGroupMember -Identity $NewUserArea -Member $NewUser
    }

    # Prompt to continue replicating or stop
    $strQuit = Read-Host "`n Stop replicating groups for new users? (y/n)"
}
Until ($strQuit -contains "y")

# Disconnect from Exchange Online
Disconnect-ExchangeOnline
