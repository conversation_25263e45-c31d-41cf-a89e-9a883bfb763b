<#
.SYNOPSIS
Sets comprehensive Place Attributes and ensures Room List membership for specified rooms in LM5, Kelowna. (v3 - Simplified Add Member Step)

.DESCRIPTION
This script performs a complete configuration for rooms in LM5 (Kelowna, BC) to appear correctly in Outlook Room Finder:
1. Sets Place attributes (Commented out assuming previous success).
2. Checks for the existence of the Room List '<EMAIL>'.
3. Creates the Room List '<EMAIL>' if it does not exist.
4. Adds each specified room mailbox as a member to the Room List '<EMAIL>' (Simplified error handling).

.NOTES
Run this script after connecting to Exchange Online PowerShell.
Ensure you have the necessary permissions for Set-Place, Get-DistributionGroup, New-DistributionGroup, Add-DistributionGroupMember.
Changes can take up to 24 hours (or longer) to fully sync and appear in Outlook.
If Step 3 still fails on '-Member', investigate your PowerShell module/session environment.
#>

#Requires -Modules ExchangeOnlineManagement

# --- Configuration ---
$buildingName = "LM5"
$cityName = "Kelowna"
$stateName = "BC"
$countryCode = "CA"
$targetFloor = 2 # All rooms are on Floor 2
$roomListName = "<EMAIL>" # Specific Room List Name/Email

# Define Room Emails and their specific capacities
$roomConfigurations = @{
    "<EMAIL>"           = @{ Capacity = 4 }
    "<EMAIL>"       = @{ Capacity = 16 }
    "<EMAIL>"           = @{ Capacity = 6 }
    "<EMAIL>" = @{ Capacity = 4 }
    "<EMAIL>"           = @{ Capacity = 6 }
    "<EMAIL>"                = @{ Capacity = 6 }
    "<EMAIL>"         = @{ Capacity = 6 }
    "<EMAIL>"            = @{ Capacity = 4 }
    "<EMAIL>"             = @{ Capacity = 6 }
    "<EMAIL>"        = @{ Capacity = 1 }
    "<EMAIL>"        = @{ Capacity = 1 }
    "<EMAIL>"        = @{ Capacity = 1 }
    "<EMAIL>"              = @{ Capacity = 8 }
    "<EMAIL>"            = @{ Capacity = 4 }
    "<EMAIL>"               = @{ Capacity = 6 }
}
# --- End Configuration ---

# --- Step 1: Set Place Attributes ---
Write-Host "Step 1: Applying comprehensive Place Attribute Updates..." -ForegroundColor Yellow
Write-Host " (Building: '$buildingName', City: '$cityName', State: '$stateName', Country: '$countryCode', Floor: '$targetFloor')"
Write-Host "-------------------------------------------------"
Write-Host "INFO: Assuming Place Attributes were set correctly in the previous run. Skipping Step 1."
# (Place attribute setting loop commented out)
Write-Host ""

# --- Step 2: Ensure Room List Exists (Revised Error Handling) ---
Write-Host "Step 2: Checking/Creating Room List '$roomListName'..." -ForegroundColor Yellow
Write-Host "-------------------------------------------------"
$roomListExists = $false
$roomListObject = $null
try {
    $roomListObject = Get-DistributionGroup -Identity $roomListName -ErrorAction SilentlyContinue
    if ($null -ne $roomListObject) {
        Write-Host "Room List '$roomListName' already exists." -ForegroundColor Green
        $roomListExists = $true
    } else {
        Write-Host "Room List '$roomListName' not found. Attempting to create..."
        try {
            New-DistributionGroup -Name $roomListName -PrimarySmtpAddress $roomListName -RoomList -ErrorAction Stop
            Write-Host "SUCCESS: Created Room List '$roomListName'." -ForegroundColor Green
            $roomListExists = $true
        } catch {
            Write-Host "ERROR: Failed to create Room List '$roomListName`: $($_.Exception.Message)" -ForegroundColor Red
            $roomListExists = $false
        }
    }
} catch {
    Write-Host "ERROR checking for Room List '$roomListName`: $($_.Exception.Message)" -ForegroundColor Red
    $roomListExists = $false
}
Write-Host "Finished Step 2."
Write-Host ""


# --- Step 3: Add Rooms to Room List (Simplified) ---
Write-Host "Step 3: Adding Rooms to Room List '$roomListName'..." -ForegroundColor Yellow
Write-Host "-------------------------------------------------"

if (-not $roomListExists) {
    Write-Host "Cannot add rooms because Room List '$roomListName' does not exist or could not be created. Please check errors in Step 2." -ForegroundColor Red
} elseif ($roomConfigurations.Count -eq 0) {
    Write-Host "No room configurations defined to add to the list." -ForegroundColor Red
} else {
    foreach ($email in $roomConfigurations.Keys) {
        Write-Host "Attempting to add member: $email to list: $roomListName"
        try {
            # Directly attempt to add the member
            Add-DistributionGroupMember -Identity $roomListName -Member $email -ErrorAction Stop
            Write-Host "  SUCCESS: Added $email to Room List '$roomListName'." -ForegroundColor Green
        } catch {
             # Catch any error during the add operation
            Write-Host "  ERROR adding $email to Room List '$roomListName`: $($_.Exception.Message)" -ForegroundColor Red
            # Check if the error message specifically mentions the '-Member' parameter again
            if ($_.Exception.Message -like "*parameter name 'Member'*") {
                 Write-Host "  INVESTIGATE: The '-Member' parameter is still not recognized. Check your ExchangeOnlineManagement module version or try reconnecting your session." -ForegroundColor Magenta
            }
        }
         Write-Host "---"
    }
}
Write-Host "Finished Step 3."
Write-Host ""

# --- Final Reminder ---
Write-Host "Script finished." -ForegroundColor Yellow
Write-Host "IMPORTANT REMINDER:" -ForegroundColor Magenta
Write-Host "Changes to Place attributes and Room List membership can take up to 24 HOURS (sometimes longer) to fully synchronize and appear correctly in Outlook Room Finder / Places Finder." -ForegroundColor Magenta
Write-Host "Please be patient and check OWA/New Outlook periodically, potentially not expecting full results until Monday." -ForegroundColor Magenta