﻿$Groups = Get-DistributionGroup
$Groups | ForEach-Object {
$group = $_.Name
Get-DistributionGroupMember $group | ForEach-Object {
      New-Object -TypeName PSObject -Property @{
       ManagedBy = $_.ManagedBy 
       Group = $group
       Member = $_.Name
       EmailAddress = $_.PrimarySMTPAddress
       RecipientType= $_.RecipientType
      
}}} | Select-Object Group,Member,EmailAddress,RecipientType,ManagedBy |

Export-CSV "d:\Distribution_GroupMembers.csv" -NoTypeInformation -Encoding UTF8