#The attribute telephoneNumber is also occupied by the cellphone# for some users
#Add exception to include changes to telephoneNumber attribute to users in select OUs (CS, Support)
$userList = Import-csv "C:\Scripts\userTeamsExtensionList.csv"
#The Exception list was retrieved from the Company Cell Phones confluence page https://confluence.qhrtech.com/display/DCO/Company+Cell+Phones
$userExceptionList = Import-csv "C:\Scripts\userTeamsExceptionList.csv"
foreach($user in $userList){
    $userMatch = $userExceptionList | where-object {$_.Email -eq $user.SipAddress}
    $userCellCheck = Get-ADUser $user.SipAddress -Properties mobile
    #Runs for users that are excepted from the telephone attribute change since they already have a cell number
    if ($userMatch -and $userCellCheck.mobile -eq $EMPTY)  {
        Set-ADUser -Identity $user.SipAddress -replace @{extensionAttribute9=$user.LineURI}
        Write-Host $user.DisplayName
        #$user.DisplayName | out-file -Path "C:\Scripts\Excepted-Users.txt" -Append
    }
    #Runs for users not in the exception list
    else{
        Write-Host $user.SipAddress
        #$user.DisplayName | out-file -Path "C:\Scripts\Non-Excepted-Users.txt" -Append
        Set-ADUser -Identity $user.SipAddress -replace @{extensionAttribute9=$user.LineURI}
        Set-ADUser -Identity $user.SipAddress -replace @{telephoneNumber=$user.LineURI} 
    }
}