﻿"SAMAccountName","Display<PERSON>ame","Email<PERSON>ddress","<PERSON>","Department"
"hans.fuhrmann",,,,
"brooks.ovie","<PERSON>","<EMAIL>",,
"anthony.yip","<PERSON>","<EMAIL>",,
"angelo.jumarang","<PERSON>",,,
"<PERSON>","<PERSON>","<EMAIL>",,
"kelly.fitzsimons",,,,
"scott.refvik","<PERSON>","<EMAIL>",,
"renee.kunka","<PERSON>",,,
"kelly.hanson","<PERSON>","<EMAIL>",,
"srikanth.rsurukanti","<PERSON><PERSON><PERSON>",,,
"ravi.anandarajah",,,,
"pa-vphilips","<PERSON><PERSON>",,,
"sergii.kurinnyi","<PERSON><PERSON><PERSON>","<EMAIL>",,
"SQL-EMRDATASQL01","SQL-EMRDATASQL01",,"Service Account",
"lawrence.lee","<PERSON> <PERSON>","<EMAIL>",,
"mike.fernandez","Mike Fernandez","<EMAIL>",,
"bob.gemmell","<PERSON> Gemmell","<EMAIL>",,
"farhan.ahmad","Farhan Ahmad",,,
"Oniel.Wilson","Oniel <PERSON>","<EMAIL>",,
"chris.adamson","Chris Adamson",,,
"nina.chnek","Nina Chnek",,,
"jeff.wimmer","Jeff Wimmer","<EMAIL>",,
"test2.user","test2 user",,"Senior Client Services Analyst","Client Services"
"natasha.lakhani","Natasha Lakhani","<EMAIL>",,
"shubham.malik","Shubham Malik","<EMAIL>",,
"da-ajumarang","Domain Admin - Angelo Jumarang","<EMAIL>",,
"test3.user","test3 user",,"Client Services Analyst","Client Services"
"shabnam.ahmmed","Shabnam Ahmmed","<EMAIL>",,
"alfred.loh","Alfred Loh","<EMAIL>",,
"deanna.gourley","Deanna Gourley",,,
"charisse.abaloyan","Charisse Abaloyan",,,
"da-sratti","Domain Admin - Suren Ratti",,,
"sam.bassett","Sam Bassett","<EMAIL>",,
"ina.kebet","Ina Kebet","<EMAIL>",,
"caroline.grant",,,,
"Sudha.verma","Sudha Verma","<EMAIL>",,
"brad.fuller","Brad Fuller","<EMAIL>",,
"svc-mstrueup","svc-mstrueup",,,
"carly.rigg","Carly Rigg","<EMAIL>",,
"keshav.ganesan","Keshav Ganesan",,,
"chakks.paramasivam","Chakks Paramasivam","<EMAIL>",,
"muthu.elangovan","Muthu Elangovan",,,
"zana.petric","Zana Petric",,,
"sara.burgess","Sara Burgess",,,
"preeya.narayan","Preeya Narayan","<EMAIL>",,
"holli.gordon","Holli Gordon","<EMAIL>",,
"kyle.mcdade","Kyle McDade","<EMAIL>",,
"omeuser","OME User",,"Service Account",
"stacey.tovey","Stacey Tovey","<EMAIL>",,
"benji.tanner",,,,
"sami.valkama","Sami Valkama",,,
"sanjana.ekbote","Sanjana Ekbote","<EMAIL>","Zuora Vendor","Project Management"
"davena.singh","Davena Singh",,,
"ritchie.wang","Ritchie Wang",,,
"cat.readonly","CAT Read-Only","<EMAIL>","Deloitte Contractor","IT"
"infracalendar","Infrastructure Calendar","<EMAIL>",,
"ryan.kleiber","Ryan Kleiber","<EMAIL>",,
"guinevere.ashby","Guinevere Ashby",,,
"svc-flexapp","SVC Flexapp",,,
"svc-jenkins","SVC Jenkins",,,
"da-anzailu","Domain Admin - Arnold Nzailu","<EMAIL>",,
"ryan.prevost","Ryan Prevost","<EMAIL>",,
"carlene.williams","Carlene Williams","<EMAIL>",,
"iram.hussain","Iram Hussain",,,
"david.rivard","David Rivard","<EMAIL>",,
"da-rpanchal",,,,
"stanley.uzoma","Stanley Uzoma","<EMAIL>",,
"grant.willison","Grant Willison","<EMAIL>",,
"mo.al","Mo Al","<EMAIL>",,
"darcy.senger","Darcy Senger","<EMAIL>",,
"rajin.ramjit","Rajin Ramjit",,,
"anders.pettersson","Anders Pettersson",,,
"kanika.vig","Kanika Vig",,,
"megan.angeles","Megan Angeles","<EMAIL>",,
"Stephanie.Smith","Stephanie Smith","<EMAIL>",,
"karley.davis","Karley Davis",,,
"paolo.aquino","Paolo Aquino",,,
"rushik.panchal","Rushik Panchal",,,
"divya.bhat","Divya Bhat",,,
"Colleen.corrigan","Colleen Corrigan",,,
"zuhra.bakhshi","Zuhra Bakhshi",,,
"Arnold.Nzailu","Arnold Nzailu","<EMAIL>",,
"Brenda.Kaweesi","Brenda Kaweesi",,,
"da-ksmith","Domain Admin - Kevin Smith",,,
"Leane.King",,,,
"james.calder","James Calder","<EMAIL>",,
"da-suzoma","Domain Admin - Stanley Uzoma",,,
"sql-qtversionone","SQL-QTVersionOne",,"Service Account",
"lijin.jacob","Lijin Jacob","<EMAIL>",,
"colleen.piotrowski","Colleen Piotrowski",,,
"shaun.ogrady","Shaun O'Grady",,,
"test.phoneforward","Test Phoneforward",,,
"$DUPLICATE-1f4","Administrator","<EMAIL>","Service Account",
"kyle.newton","Kyle Newton","<EMAIL>",,
"da-jpond","Domain Admin - Jarrid Pond",,,
"himali.lalit","Himali Lalit",,,
"jerry.diener","Jerry Diener",,,
"joel.burns","Joel Burns",,,
"raspreet.sidhu","Raspreet Sidhu",,,
"manoj.kumar","Manoj Kumar",,"Zuora Vendor","Project Management"
"sunil.joshi","Sunil Joshi",,"Zuora Vendor","Project Management"
"rohith.mannem","Rohith Mannem","<EMAIL>",,
"jay.layco",,,,
"sina.sereshki","Sina Sereshki",,,
"alex.pilipenko","Alex Pilipenko","<EMAIL>",,
"alishia.olinyk","Alishia Olinyk",,,
"olivia.floyd","Olivia Floyd","<EMAIL>",,
"nadia.hussain",,,,
"jolanda.kondrak","Jolanda Kondrak","<EMAIL>",,
"HR","Human Resources","<EMAIL>",,"Human Resources"
"avi.vanharen","Avi van Haren","<EMAIL>",,
"kathryn.roseberry","Kathryn Roseberry",,,
"srija.yarlagadda","Srija Yarlagadda",,,
"Testpw","Test Passwriteback","<EMAIL>",,
"adnan.ashfaq","Adnan Ashfaq",,,
"lisa.sahibram","Lisa Sahibram","Lisa <EMAIL>",,
"gbemisola.omojola","Gbemisola Omojola",,,
"jay.armand","Jay Armand","<EMAIL>",,
"aymen.akhter","Aymen Akhter","<EMAIL>",,
"ayla.szabo","Ayla Szabo","<EMAIL>",,
"femi.aloba","Femi Aloba",,,
"Michelle.Pereira","Michelle Pereira","<EMAIL>",,
"gaurav.sharma","Gaurav Sharma",,,
"ashley.cubillos","Ashley Cubillos",,,
"alex.chow","Alex Chow","<EMAIL>",,
"da-pkainth","Domain Admin - Preet Kainth","<EMAIL>",,
"dataanalyst","EMR Data Analyst","<EMAIL>",,"Implementations"
"test1.user","Test1 user","<EMAIL>","Client Services Analyst","Client Services"
"raymund.abejuela","Raymund Lorenzo Ricarte IV Abejuela","<EMAIL>","LCL CCC Employee","Technology"
"ca_secscan","ca_secscan",,"Service Account",
"amy.wiggett","Amy Wiggett",,,
"niloo.vakili","Niloo Vakili",,,
"stefanie.giddens","Stefanie Giddens",,,
"mireile.kon","Mireile Kon",,,
"james.koss","James Koss","<EMAIL>",,
"james.falodun","James Falodun",,,
"erik.holtom",,,,
"kiran.kaur","Kiran Kaur","<EMAIL>",,
"ron.hughes",,,,
"janani.kulanthaiswam","Janani Kulanthaiswamy","<EMAIL>",,
"cj.edwards","Clinton Edwards","<EMAIL>",,
"tyler.genberg","Tyler Genberg",,,
"arpita.brar","Misty Brar","<EMAIL>",,
"lauren.romano","Lauren Romano","<EMAIL>",,
"chris.macpherson","Chris MacPherson","<EMAIL>",,
"suren.ratti","Suren Ratti","<EMAIL>",,
"DA-KKaur","Domain Admin - Kiran Kaur",,,
"aymen.naanaa","Aymen Naanaa"," <EMAIL>",,
"amrita.abhyankar","Amrita Abhyankar","<EMAIL>",,
"snowden.longacre","Snowden Longacre",,,
"blaine.bradley","Blaine Bradley"," ",,
"nithya.alle","Nithya Alle",,,
"Mark.Devries",,,,
"janet.hatfield","Janet Hatfield",,,
"harrison.johnson","Harrison Johnson","<EMAIL>",,
"selene.vera","Selene Vera","<EMAIL>",,
