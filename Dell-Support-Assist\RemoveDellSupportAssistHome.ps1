# RemoveDellSupportAssist.ps1
# Date: 2024-05-29
# Author: <PERSON>
# Description: This script removes Dell SupportAssist and related components.

# User-editable variables
$logDirectory = "C:\Temp"
$logFile = "$logDirectory\RemoveDellSupportAssist.log"
$retries = 3
$retryInterval = 5 # seconds

# Ensure log directory exists
if (-not (Test-Path -Path $logDirectory)) {
    New-Item -ItemType Directory -Path $logDirectory -Force
}

# Function to log messages with timestamp
function Write-Log {
    param (
        [string]$message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "$timestamp - $message"
    #Write-Output $logMessage
    Add-Content -Path $logFile -Value $logMessage
}

# Check for administrative privileges
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Log "Script is not running with administrative privileges. Exiting."
    Exit 1
}

# Check if the machine is a Dell
$manufacturer = (Get-WmiObject -Query "SELECT Manufacturer FROM Win32_ComputerSystem").Manufacturer
if ($manufacturer -notlike "*Dell*") {
    Write-Log "This script is designed to run on Dell machines only. Exiting."
    Exit 1
}

# Function to retry a command
function Retry-Command {
    param (
        [scriptblock]$Command,
        [int]$retries,
        [int]$interval
    )
    for ($i = 0; $i -lt $retries; $i++) {
        try {
            & $Command
            return $true
        } catch {
            Write-Log "Command failed. Attempt $($i + 1) of $retries."
            Start-Sleep -Seconds $interval
        }
    }
    Write-Log "Command failed after $retries attempts."
    return $false
}

# Function to check if a program is installed
function Is-ProgramInstalled {
    param (
        [string]$guid
    )
    $key = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\${guid}"
    if (Test-Path $key) {
        return $true
    } else {
        return $false
    }
}

# Function to remove a program
function Remove-Program {
    param (
        [string]$uninstallString
    )
    $command = { cmd.exe /c $uninstallString }
    Retry-Command -Command $command -retries $retries -interval $retryInterval
}

# Uninstall Dell SupportAssist and related components
$programsToUninstall = @(
    @{Path = 'C:\ProgramData\Package Cache\{ec40a028-983b-4213-af2c-77ed6f6fe1d5}\DellUpdateSupportAssistPlugin.exe /uninstall /quiet'; Guid = '{ec40a028-983b-4213-af2c-77ed6f6fe1d5}'},
    @{Path = 'MsiExec.exe /qn /norestart /X{E98E94E2-12D1-48E5-AC69-2C312F466136}'; Guid = '{E98E94E2-12D1-48E5-AC69-2C312F466136}'},
    @{Path = 'MsiExec.exe /qn /norestart /X{806422F1-FC4E-4D7C-8855-05748AEFC031}'; Guid = '{806422F1-FC4E-4D7C-8855-05748AEFC031}'},
    @{Path = 'MsiExec.exe /X{0309AC01-330F-494C-B27D-58E297E4674F} /qn REBOOT=REALLYSUPRESS'; Guid = '{0309AC01-330F-494C-B27D-58E297E4674F}'},
    @{Path = 'MsiExec.exe /X{122666A9-2995-4E47-A75E-6423A827B7AF} /qn REBOOT=REALLYSUPRESS'; Guid = '{122666A9-2995-4E47-A75E-6423A827B7AF}'},
    @{Path = 'MsiExec.exe /X{18EF001B-B005-46CB-917B-112BA69ED85E} /qn REBOOT=REALLYSUPRESS'; Guid = '{18EF001B-B005-46CB-917B-112BA69ED85E}'},
    @{Path = 'MsiExec.exe /X{1AE53ECE-2255-4191-998B-07741E5EFCDA} /qn REBOOT=REALLYSUPRESS'; Guid = '{1AE53ECE-2255-4191-998B-07741E5EFCDA}'},
    @{Path = 'MsiExec.exe /X{33E712C1-2183-421C-9BC8-C902DB9C596C} /qn REBOOT=REALLYSUPRESS'; Guid = '{33E712C1-2183-421C-9BC8-C902DB9C596C}'},
    @{Path = 'MsiExec.exe /X{45FD01F4-B11B-4A58-B465-1D600B5CDF64} /qn REBOOT=REALLYSUPRESS'; Guid = '{45FD01F4-B11B-4A58-B465-1D600B5CDF64}'},
    @{Path = 'MsiExec.exe /X{4CB4741A-20C1-454E-8276-993D06A76D67} /qn REBOOT=REALLYSUPRESS'; Guid = '{4CB4741A-20C1-454E-8276-993D06A76D67}'},
    @{Path = 'MsiExec.exe /X{50EF2C72-95EC-4206-AAC3-9E84004A6140} /qn REBOOT=REALLYSUPRESS'; Guid = '{50EF2C72-95EC-4206-AAC3-9E84004A6140}'},
    @{Path = 'MsiExec.exe /X{5A18ABE3-52D1-4CA5-9169-25EC7E789582} /qn REBOOT=REALLYSUPRESS'; Guid = '{5A18ABE3-52D1-4CA5-9169-25EC7E789582}'},
    @{Path = 'MsiExec.exe /X{8D7B279C-A661-465C-9658-F62FBD6A6B91} /qn REBOOT=REALLYSUPRESS'; Guid = '{8D7B279C-A661-465C-9658-F62FBD6A6B91}'},
    @{Path = 'MsiExec.exe /X{9074E264-F615-4DDE-969E-1FDBCFEC3FB5} /qn REBOOT=REALLYSUPRESS'; Guid = '{9074E264-F615-4DDE-969E-1FDBCFEC3FB5}'},
    @{Path = 'MsiExec.exe /X{90881C8E-6C4F-4662-9923-85AFCA058C44} /qn REBOOT=REALLYSUPRESS'; Guid = '{90881C8E-6C4F-4662-9923-85AFCA058C44}'},
    @{Path = 'MsiExec.exe /X{9DD6B149-CEBC-4910-B11A-242393EDF6D3} /qn REBOOT=REALLYSUPRESS'; Guid = '{9DD6B149-CEBC-4910-B11A-242393EDF6D3}'},
    @{Path = 'MsiExec.exe /X{D793D5B1-A985-4443-90F4-E55A13CFF117} /qn REBOOT=REALLYSUPRESS'; Guid = '{D793D5B1-A985-4443-90F4-E55A13CFF117}'},
    @{Path = 'MsiExec.exe /X{E98E94E2-12D1-48E5-AC69-2C312F466136} /qn REBOOT=REALLYSUPRESS'; Guid = '{E98E94E2-12D1-48E5-AC69-2C312F466136}'},
    @{Path = 'MsiExec.exe /X{806422F1-FC4E-4D7C-8855-05748AEFC031} /qn REBOOT=REALLYSUPRESS'; Guid = '{806422F1-FC4E-4D7C-8855-05748AEFC031}'},
    @{Path = 'MsiExec.exe /X{27130E51-9555-408B-8134-7BFF54EDE27B} /qn REBOOT=REALLYSUPRESS'; Guid = '{27130E51-9555-408B-8134-7BFF54EDE27B}'},
    @{Path = 'MsiExec.exe /X{3ED468C2-2235-4747-90AD-A7A34F0FE70A} /qn REBOOT=REALLYSUPRESS'; Guid = '{3ED468C2-2235-4747-90AD-A7A34F0FE70A}'},
    @{Path = 'MsiExec.exe /X{398E49A0-84CA-43B5-A926-42EF68619E91} /qn REBOOT=REALLYSUPRESS'; Guid = '{398E49A0-84CA-43B5-A926-42EF68619E91}'},
    @{Path = 'MsiExec.exe /X{39BF0E71-7A16-4A80-BBCE-FBDD2D1CC2D5} /qn REBOOT=REALLYSUPRESS'; Guid = '{39BF0E71-7A16-4A80-BBCE-FBDD2D1CC2D5}'},
    @{Path = 'MsiExec.exe /X{B8B9B89F-A73B-4119-A6EA-3CA373EA1AB7} /qn REBOOT=REALLYSUPRESS'; Guid = '{B8B9B89F-A73B-4119-A6EA-3CA373EA1AB7}'}
)

foreach ($program in $programsToUninstall) {
    if (Is-ProgramInstalled -guid $program.Guid) {
        Write-Log "Attempting to uninstall: $($program.Path)"
        Remove-Program -uninstallString $program.Path
    } else {
        Write-Log "Program with GUID $($program.Guid) is not installed. Skipping."
    }
}

# Stop and remove SupportAssist services and directories
Write-Log "Stopping and removing SupportAssist services."

# Stop SupportAssistAgent.exe if running
if (Get-Process -Name SupportAssistAgent -ErrorAction SilentlyContinue) {
    Retry-Command -Command { taskkill /im SupportAssistAgent.exe /f /t } -retries $retries -interval $retryInterval
} else {
    Write-Log "SupportAssistAgent.exe process not found. Skipping taskkill."
}

if (Get-Process -Name SupportAssistAgent -ErrorAction SilentlyContinue) {
    Retry-Command -Command { taskkill /im SupportAssist.exe /f /t } -retries $retries -interval $retryInterval
} else {
    Write-Log "SupportAssist.exe process not found. Skipping taskkill."
}

# Stop SupportAssistAgent service if it exists
if (Get-Service -Name SupportAssistAgent -ErrorAction SilentlyContinue) {
    Retry-Command -Command { net stop SupportAssistAgent } -retries $retries -interval $retryInterval
    Retry-Command -Command { sc delete SupportAssistAgent } -retries $retries -interval $retryInterval
} else {
    Write-Log "SupportAssistAgent service not found. Skipping service stop and delete."
}

# Remove directories
if (Test-Path "C:\Program Files\Dell\SupportAssist") {
    Retry-Command -Command { rd "C:\Program Files\Dell\SupportAssist" /s /q } -retries $retries -interval $retryInterval
} else {
    Write-Log "Directory C:\Program Files\Dell\SupportAssist not found. Skipping removal."
}

if (Test-Path "C:\Program Files\Dell\SupportAssistAgent") {
    Retry-Command -Command { rd "C:\Program Files\Dell\SupportAssistAgent" /s /q } -retries $retries -interval $retryInterval
} else {
    Write-Log "Directory C:\Program Files\Dell\SupportAssistAgent not found. Skipping removal."
}

if (Test-Path "C:\Program Files (x86)\Dell\SupportAssist") {
    Retry-Command -Command { rd "C:\Program Files (x86)\Dell\SupportAssist" /s /q } -retries $retries -interval $retryInterval
} else {
    Write-Log "Directory C:\Program Files (x86)\Dell\SupportAssist not found. Skipping removal."
}

# Remove registry entries and shortcuts
Write-Log "Removing registry entries and shortcuts."
if (Test-Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PC-Doctor for Windows") {
    Retry-Command -Command { reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PC-Doctor for Windows" /f } -retries $retries -interval $retryInterval
} else {
    Write-Log "Registry key HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PC-Doctor for Windows not found. Skipping removal."
}

if (Test-Path "C:\Users\<USER>\Desktop\SupportAssist.lnk") {
    Retry-Command -Command { del "C:\Users\<USER>\Desktop\SupportAssist.lnk" /f /q } -retries $retries -interval $retryInterval
} else {
    Write-Log "Shortcut C:\Users\<USER>\Desktop\SupportAssist.lnk not found. Skipping removal."
}

# Report summary
Write-Log "Dell SupportAssist removal script completed."
#Write-host Uninstalled
exit 0