<#
.SYNOPSIS
    Updates Active Directory user attributes (<PERSON><PERSON><PERSON><PERSON><PERSON>, Department, Manager) based on an input CSV file.
    The script generates a detailed audit log of all changes, grouped by department, and notes any UPN/DisplayName mismatches.

.DESCRIPTION
    This script is designed to be run on a Windows Server with the Active Directory module installed.
    It iterates through a list of users provided in a CSV file. For each user, it performs the following actions:
    1. Groups users by the department listed in the CSV file.
    2. For each department, it processes the users within that group.
    3. Finds the user in Active Directory based on their full name from the CSV. It correctly handles names with special characters like apostrophes.
    4. Compares the user's current DisplayName, Department, and Manager in AD with the values in the CSV, safely handling cases where an AD attribute might not be set.
    5. If any of these attributes are different, it updates them.
    6. Performs an audit check to see if the user's UPN follows the standard '<EMAIL>' format based on their current DisplayName. If not, it logs a special note for manual review.
    7. All actions, comparisons, and changes are logged to a daily log file in C:\temp\helpdesk\ADlogs\, with the output grouped by department for readability.

    SAFETY:
    This script NO LONGER modifies the UserPrincipalName (UPN). It includes a -WhatIf parameter which is HIGHLY
    recommended for the first run to see a report of intended changes without modifying AD objects.

.PARAMETER CsvPath
    The full path to the input CSV file. The file MUST be in CSV format (.csv). The CSV must contain the following columns:
    - 'Employee Name'
    - 'Home Department Description'
    - 'Reports to'

.EXAMPLE
    # First, save your .xlsx file as a .csv file in Excel.
    # DRY RUN - Shows what changes would be made without executing them.
    .\Update-ADUsers.ps1 -CsvPath "C:\data\Direct Reports Report.csv" -WhatIf

.EXAMPLE
    # LIVE RUN - Executes the changes in Active Directory.
    .\Update-ADUsers.ps1 -CsvPath "C:\data\Direct Reports Report.csv"

.AUTHOR
    Slader Sheppard
#>
[CmdletBinding(SupportsShouldProcess = $true)]
param(
    [Parameter(Mandatory = $true, HelpMessage = "Please provide the full path to the input CSV file.")]
    [string]$CsvPath
)

#Requires -Module ActiveDirectory

# --- Script Setup ---
# Define the log directory and ensure it exists
$logDirectory = "C:\temp\helpdesk\ADlogs"
if (-not (Test-Path -Path $logDirectory)) {
    try {
        Write-Host "Log directory not found. Creating directory: $logDirectory"
        New-Item -ItemType Directory -Path $logDirectory -Force -ErrorAction Stop | Out-Null
    }
    catch {
        Write-Host "FATAL ERROR: Could not create log directory at '$logDirectory'. Please check permissions or create it manually. Error: $_"
        exit
    }
}

# Create a date-stamped log file name. All logs for a given day will append to the same file.
$dateStamp = Get-Date -Format "yyyy-MM-dd"
$logFile = Join-Path -Path $logDirectory -ChildPath "ADuserUpdates$dateStamp.log"

# --- Logging Function ---
# A helper function to write consistent log messages to the console and the log file.
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO" # e.g., INFO, CHANGE, WARNING, AUDIT, ERROR
    )
    $logEntry = "[{0}] - {1} - {2}" -f (Get-Date -Format "yyyy-MM-dd HH:mm:ss"), $Level.ToUpper(), $Message
    Write-Host $logEntry
    Add-Content -Path $logFile -Value $logEntry
}

# --- Main Script Execution ---

# --- VALIDATION: Check for correct file type ---
if ($CsvPath -notlike '*.csv') {
    Write-Log -Level "FATAL" -Message "Invalid file type. The provided file must be a CSV file (.csv). Please open your XLSX file in Excel and use 'Save As' to create a 'CSV (Comma delimited)' file."
    return
}

# Check if the CSV file exists before proceeding
if (-not (Test-Path $CsvPath)) {
    Write-Log -Level "ERROR" -Message "Input CSV file not found at path: $CsvPath"
    return # Exit the script
}

Write-Log "----------------------------------------------------"
Write-Log "Starting Active Directory User Update Script."
Write-Log "Input CSV File: $CsvPath"
Write-Log "Log File: $logFile"
if ($PSCmdlet.ShouldProcess("Active Directory", "Update Users")) {
    Write-Log "Mode: LIVE RUN. Changes will be applied to Active Directory."
}
else {
    Write-Log "Mode: DRY RUN (-WhatIf). No changes will be made."
}
Write-Log "----------------------------------------------------"

try {
    # Import the data from the CSV file
    $usersToProcess = Import-Csv -Path $CsvPath
    Write-Log "Successfully imported $($usersToProcess.Count) rows from the CSV file."
}
catch {
    Write-Log -Level "ERROR" -Message "Failed to import CSV file. Error: $_"
    return # Exit the script
}

# --- Group users by department for organized logging ---
$groupedUsers = $usersToProcess | Group-Object -Property 'Home Department Description'

# Loop through each department group
foreach ($departmentGroup in $groupedUsers) {
    $departmentName = $departmentGroup.Name
    Write-Log "===================================================="
    Write-Log "Processing Department: '$departmentName'"
    Write-Log "===================================================="

    # Loop through each user record within the current department group
    foreach ($csvUser in $departmentGroup.Group) {
        
        # Trim whitespace from CSV values to prevent mismatches
        $employeeName = $csvUser.'Employee Name'.Trim()

        # --- VALIDATION: Skip row if Employee Name is blank ---
        if ([string]::IsNullOrWhiteSpace($employeeName)) {
            Write-Log -Level "WARNING" -Message "Skipping a row because its 'Employee Name' column is blank or empty."
            continue # Move to the next user in the CSV
        }

        try {
            Write-Log "--- Processing User: '$employeeName' ---"

            # FIX: Escape single quotes in names (e.g., O'Brien) for the AD filter.
            $escapedEmployeeName = $employeeName.Replace("'", "''")

            # Find the user in Active Directory by their name
            # We request the properties we need to check to make the query more efficient.
            $adUser = Get-ADUser -Filter "Name -eq '$escapedEmployeeName'" -Properties Department, Manager, UserPrincipalName, DisplayName
            
            if (-not $adUser) {
                Write-Log -Level "WARNING" -Message "User '$employeeName' not found in Active Directory. Skipping."
                continue # Move to the next user in the CSV
            }

            # --- 1. AUDIT CHECK: Compare DisplayName to UPN ---
            # This check flags users whose UPN might be based on a previous name (e.g., maiden name).
            # FIX: Safely handle potentially null attributes by casting to a string before calling methods.
            $adDisplayName = ([string]$adUser.DisplayName).Trim()
            $nameParts = $adDisplayName.Split(' ')
            if ($nameParts.Count -ge 2) {
                $expectedUpn = "$($nameParts[0]).$($nameParts[-1])@qhrtech.com".ToLower()
                $currentUpn = ([string]$adUser.UserPrincipalName).ToLower()

                if ($currentUpn -ne $expectedUpn) {
                    $logMessage = "UPN may not match current DisplayName. DisplayName: '$adDisplayName', UPN: '$($adUser.UserPrincipalName)'. Manual review suggested."
                    Write-Log -Level "AUDIT" -Message $logMessage
                }
            }

            # --- 2. DisplayName Update ---
            $csvDisplayName = $employeeName # Already trimmed from above
            if ($adDisplayName -ne $csvDisplayName) {
                $logMessage = "DisplayName: '$($adUser.DisplayName)' -> '$csvDisplayName'"
                Write-Log -Level "CHANGE" -Message $logMessage
                if ($PSCmdlet.ShouldProcess($adUser.DistinguishedName, "Set DisplayName to '$csvDisplayName'")) {
                    Set-ADUser -Identity $adUser -DisplayName $csvDisplayName
                }
            } else {
                Write-Log "DisplayName is already correct: '$($adUser.DisplayName)'."
            }


            # --- 3. Department Update ---
            $csvDepartment = $csvUser.'Home Department Description'.Trim()
            # FIX: Safely handle potentially null attributes by casting to a string before calling methods.
            if (([string]$adUser.Department).Trim() -ne $csvDepartment) {
                $logMessage = "Department: '$($adUser.Department)' -> '$csvDepartment'"
                Write-Log -Level "CHANGE" -Message $logMessage
                if ($PSCmdlet.ShouldProcess($adUser.DistinguishedName, "Set Department to '$csvDepartment'")) {
                    Set-ADUser -Identity $adUser -Department $csvDepartment
                }
            } else {
                Write-Log "Department is already correct: '$($adUser.Department)'."
            }

            # --- 4. Manager Update ---
            $csvManagerName = $csvUser.'Reports to'.Trim()
            if (-not ([string]::IsNullOrWhiteSpace($csvManagerName))) {
                
                # FIX: Escape single quotes in manager names for the AD filter.
                $escapedManagerName = $csvManagerName.Replace("'", "''")
                
                # Find the manager object in AD to get their identity
                $adManager = Get-ADUser -Filter "Name -eq '$escapedManagerName'"
                
                if ($adManager) {
                    # Get the DistinguishedName of the current manager for comparison
                    $currentManagerDN = $adUser.Manager
                    
                    if ($currentManagerDN -ne $adManager.DistinguishedName) {
                        # To make the log readable, we'll get the current manager's name
                        $currentManagerName = "Not Set"
                        if($currentManagerDN) {
                           $currentManagerName = (Get-ADUser $currentManagerDN).Name
                        }
                        $logMessage = "Manager: '$currentManagerName' -> '$($adManager.Name)'"
                        Write-Log -Level "CHANGE" -Message $logMessage
                        if ($PSCmdlet.ShouldProcess($adUser.DistinguishedName, "Set Manager to $($adManager.Name)")) {
                            Set-ADUser -Identity $adUser -Manager $adManager
                        }
                    } else {
                        Write-Log "Manager is already correct: '$($adManager.Name)'."
                    }
                } else {
                    Write-Log -Level "WARNING" -Message "Manager '$csvManagerName' not found in Active Directory. Cannot update manager for '$employeeName'."
                }
            } else {
                 Write-Log "No manager listed in CSV for '$employeeName'. Skipping manager check."
            }

        }
        catch {
            # Catch any errors that occur for a specific user and log them
            Write-Log -Level "ERROR" -Message "An unexpected error occurred while processing '$employeeName'. Error: $_"
        }
        finally {
            Write-Log "--- Finished Processing User: '$employeeName' ---`n"
        }
    }
}

Write-Log "----------------------------------------------------"
Write-Log "Script execution finished."
Write-Log "Please review the log file for details: $logFile"
Write-Log "----------------------------------------------------"

