﻿clear
$sharedmailbox = Read-Host "1. Enter shared mailbox to remove (eg. john.doe) from manager's Outlook"
$sharedmailbox = Get-ADUser $sharedmailbox 

$sharedmailbox | select Name, UserPrincipalName
##write-host $sharedmailbox.UserPrincipalName
$confirm = Read-Host "`nIs this correct??? (y/n)"

if ($confirm -eq "y")
  {
  $managermailbox = Read-Host "2. Enter manager mailbox to remove previous shared mailbox (eg. john.doe)"
  $managermailbox = Get-ADUser $managermailbox 

  $managermailbox | select Name, UserPrincipalName
  ##write-host $managermailbox.UserPrincipalName
  $confirm = Read-Host "`nIs this correct??? (y/n)"

  if ($confirm -eq "y")
      {
      ## Connect to Office 365 (http://technet.microsoft.com/library/jj151815.aspx#BKMK_connect):
      $LiveCred = Get-Credential <EMAIL>
      $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
      Import-PSSession $Session -AllowClobber
      Connect-MsolService -Credential $LiveCred

      Write-Host "`n`nRemoving Shared Mailbox..." -ForegroundColor Green
      ## Add-MailboxPermission -Identity <EMAIL> -User <EMAIL> -AccessRights deleteitem -InheritanceType All -Automapping $false
      Add-MailboxPermission -Identity $sharedmailbox.UserPrincipalName -User $managermailbox.UserPrincipalName -AccessRights deleteitem -InheritanceType All -Automapping $false
      Write-Host "Complete!!!" -ForegroundColor Green

      Exit-PSSession

    Pause
    }
  else
    {break}
  }