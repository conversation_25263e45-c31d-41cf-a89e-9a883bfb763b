<#
.SYNOPSIS
(LIVE MODE - FILTERED) Connects via Delegated permissions to rename Intune devices matching a filter, per corporate convention (C-DP-FLast), excluding certain prefixes/keywords. Corrected suffix logic (starts at 2). Makes actual changes.

.DESCRIPTION
*** THIS SCRIPT MAKES ACTUAL CHANGES TO DEVICE NAMES IN INTUNE ***
*** IT PROCESSES DEVICES MATCHING THE $DeviceFilter VARIABLE ***

Renames corporate Windows devices matching filter, excluding names starting with CPC, EMR, QHRCIAB, QHR or containing TEST/TESTING.
Corrected Suffix Logic: If ideal name exists, starts checking duplicates from suffix 2.
1. Defines logging function.
2. Sets up Department Map.
3. Connects to <PERSON>raph (interactive login).
4. Gets ALL device names for duplicate checks.
5. Gets target devices matching filter.
6. For each target device:
    a. Checks exclusion rules; skips if matched.
    b. Checks Intune Primary User / Entra Registered User; skips if none found.
    c. Gets user details.
    d. Determines Department Code (user/manager fallback); skips if invalid.
    e. Constructs ideal name ('C-{DPT}-{FirstInitial}{LastName}'), truncated to 15 chars.
    f. Skips if current name matches ideal.
    g. Checks for duplicates (ideal name first, then suffix starting from 2); appends suffixes with dynamic truncation if needed.
    h. If unique name differs from current, EXECUTES the rename action in Intune.
    i. Updates internal lookup on success.
    j. Logs all actions, successes, warnings, errors.

Requires necessary Graph SDK modules & user permissions/consent. TEST THOROUGHLY (e.g., with Simulation script first, then narrow filter).

.EXAMPLE
# 1. Review/Adjust $DeviceFilter to target intended devices.
# 2. Run the script: .\IntuneNameCompliance.ps1
Runs the script for filtered devices. User logs in. Changes ARE made. Check console/log file.

.NOTES
Author: Slader Sheppard / AI Assistant
Date: 2025-04-07 (Kelowna, BC, Canada) - FILTERED LIVE MODE
Version: 4.1-LiveFiltered (Corrected Suffix Logic)
Requires PowerShell 5.1+. User needs Intune/Entra permissions.
Naming: 'C-{DPT}-{FirstInitial}{LastNameTruncated}[Suffix]'. Department Map keys must match Entra ID.
VERIFY $DeviceFilter and test with simulation first. Changes made are real.
#>

#region Configuration and Setup
# ===================================================================================
# Consider using C:\Temp or another user-writable path if running non-admin
$LogFilePath = "C:\Temp\DeviceRenameLogs\DeviceRenameLog_$(Get-Date -Format 'yyyyMMdd_HHmmss')_LiveFiltered.log" # Changed Log Name
Function Write-Log { Param([Parameter(Mandatory)][string]$Message, [Parameter(Mandatory)][ValidateSet("INFO","WARN","ERROR","ACTION","SUCCESS","DEBUG")][string]$Level) ; $ts = Get-Date -Format "yyyy-MM-dd HH:mm:ss" ; $le = "$ts [$($Level.PadRight(7))] $Message" ; $c = switch ($Level) {"INFO"{"White"} "WARN"{"Yellow"} "ERROR"{"Red"} "ACTION"{"Cyan"} "SUCCESS"{"Green"} "DEBUG"{"Gray"} default{"White"}} ; Write-Host $le -ForegroundColor $c ; try { Add-Content -Path $LogFilePath -Value $le -EA Stop } catch { Write-Warning "Failed log write: $($_.Exception.Message)" } }
# User's updated Department Map
$DepartmentMap = @{ "Development"="DE";"Quality Assurance"="QA";"IT"="TE";"Administration"="AD"; "Revenue Management"="AC"; "Implementations"="IM"; "Training"="TR"; "Data and Software Architecture"="IM"; "Data Insights and Integrations"="IM"; "Client Services"="CS"; "Finance"="FI"; "HR"="HR"; "Technology"="TE"; "Marketing"="MA"; "Development (Product development)"="DE"; "Sales National"="CH"; "Product Management"="PM"; "Product Development (PD Quality Assurance)"="QA"; "Product Operations"="PO"; "Platform Integration"="DE"; "CHN Central"="CH"; "CHN East"="CH"; "CHN West"="CH"; "GRC"="TE" } # ADJUST KEYS!
$DevicePrefix = "C"; $Separator = "-"; $MaxNameLength = 15
# Using Device Filter as per user's script
$DeviceFilter = "operatingSystem eq 'Windows' and managedDeviceOwnerType eq 'company'"
$RequiredScopes = @("User.Read.All", "DeviceManagementManagedDevices.ReadWrite.All", "Device.Read.All")
$IgnorePrefixes = @('CPC', 'EMR', 'QHRCIAB', 'QHR') # Uppercase
$IgnoreKeywords = @('TEST', 'TESTING') # Uppercase
#endregion

#region Main Script Logic
# ===================================================================================
Write-Log -Level "ACTION" -Message "Starting Intune Device Rename Script (v4.1 - Corrected Suffix - FILTERED LIVE MODE)... Log: $LogFilePath"
Write-Log -Level "WARN" -Message "*** LIVE MODE ACTIVE - PROCESSING DEVICES MATCHING FILTER: $DeviceFilter ***"
Write-Log -Level "WARN" -Message "*** CHANGES WILL BE MADE TO MATCHING DEVICES (excluding ignored names) ***"

# --- Step 1: Authenticate ---
Write-Log -Level "ACTION" -Message "Connecting to Graph..."
# (Same connection logic)
try { $ctx = Get-MgContext -EA SilentlyContinue; $miss = $RequiredScopes | Where-Object { $null -eq $ctx -or $ctx.Scopes -notcontains $_ }; if ($null -eq $ctx -or $miss) { if ($null -ne $ctx) { Write-Log -Level "INFO" -Message "Reconnecting for scopes..."; Disconnect-MgGraph -EA SilentlyContinue }; Write-Log -Level "WARN" -Message "Connect prompt may appear."; Connect-MgGraph -Scopes $RequiredScopes -EA Stop -NoWelcome; $ctx = Get-MgContext -EA SilentlyContinue ; if ($null -eq $ctx) { Throw "Connect failed." }; $miss = $RequiredScopes | Where-Object { $ctx.Scopes -notcontains $_ }; if ($miss) { Throw "Missing scopes: $($miss -join ', ')" } } else { Write-Log -Level "INFO" -Message "Already connected." }; Write-Log -Level "SUCCESS" -Message "Graph connected (User: $($ctx.Account), Tenant: $($ctx.TenantId)).";} catch { Write-Log -Level "ERROR" -Message "Fatal connect/scope error: $($_.Exception.Message)"; exit 1 }

# --- Step 2: Get ALL Device Names ---
Write-Log -Level "ACTION" -Message "Getting all device names..." ; $AllDeviceNamesLookup = @{}; try { $AllDevs = Get-MgDeviceManagementManagedDevice -Property "deviceName" -All -EA Stop; foreach ($d in $AllDevs) { if (-not [string]::IsNullOrEmpty($d.DeviceName)) { $AllDeviceNamesLookup[$d.DeviceName.ToUpper()] = $true } }; Write-Log -Level "SUCCESS" -Message "Got $($AllDeviceNamesLookup.Count) unique names for duplicate check." } catch { Write-Log -Level "ERROR" -Message "Fatal error getting all names: $($_.Exception.Message)"; Write-Log -Level "INFO" -Message "Disconnecting..."; Disconnect-MgGraph; exit 1 }

# --- Step 3: Get Target Devices (Using Filter) ---
Write-Log -Level "ACTION" -Message "Getting target devices (filter: '$($DeviceFilter)')..."; try { $TargetDevices = Get-MgDeviceManagementManagedDevice -Filter $DeviceFilter -Property "id,deviceName,userPrincipalName,azureADDeviceId" -All -EA Stop; Write-Log -Level "SUCCESS" -Message "Found $($TargetDevices.Count) target devices." } catch { Write-Log -Level "ERROR" -Message "Error getting targets: $($_.Exception.Message)"; Write-Log -Level "INFO" -Message "Disconnecting..."; Disconnect-MgGraph; exit 1 }
if ($null -eq $TargetDevices -or $TargetDevices.Count -eq 0) { Write-Log -Level "WARN" -Message "No targets found. Exiting."; Write-Log -Level "INFO" -Message "Disconnecting..."; Disconnect-MgGraph; exit 0 }

# --- Step 4: Process Target Devices ---
Write-Log -Level "ACTION" -Message "Processing $($TargetDevices.Count) target devices (Live Mode)..." ; $renameCounter = 0
foreach ($Device in $TargetDevices) {
    $DeviceLogPrefix = "--> Device '$($Device.DeviceName)' (IntuneID: $($Device.id)):" ; Write-Log -Level "INFO" -Message "`n$DeviceLogPrefix Processing started."
    $UserPrincipalName = $null ; $UserDataSource = "None"

    # --- 4a: EXCLUSION CHECK ---
    $CurrentDeviceNameUpper = $Device.DeviceName.ToUpper()
    $SkipDevice = $false ; $SkipReason = ""
    foreach ($prefix in $IgnorePrefixes) { if ($CurrentDeviceNameUpper.StartsWith($prefix)) { $SkipDevice = $true; $SkipReason = "Name starts with ignored prefix '$prefix'"; break } }
    if (-not $SkipDevice) { foreach ($keyword in $IgnoreKeywords) { if ($CurrentDeviceNameUpper.Contains($keyword)) { $SkipDevice = $true; $SkipReason = "Name contains ignored keyword '$keyword'"; break } } }
    if ($SkipDevice) { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Skipping - $SkipReason."; continue }

    # --- 4b: Determine User Principal Name ---
    # (Same logic as simulation)
    if (-not [string]::IsNullOrEmpty($Device.UserPrincipalName)) { $UserPrincipalName = $Device.UserPrincipalName; $UserDataSource = "Intune Primary User" ; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Found $UserDataSource{}: '$($UserPrincipalName)'."
    } else { Write-Log -Level "INFO" -Message "$DeviceLogPrefix Intune Primary User missing. Checking Entra Registered User..."; if (-not [string]::IsNullOrEmpty($Device.azureADDeviceId)) { $EntraDeviceId = $Device.azureADDeviceId; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Found Entra ID: '$EntraDeviceId'. Querying..."; try { $EntraDevice = Get-MgDevice -DeviceId $EntraDeviceId -ExpandProperty "registeredUsers(`$select=id,userPrincipalName)" -EA Stop ; if ($null -ne $EntraDevice.RegisteredUsers -and $EntraDevice.RegisteredUsers.Count -gt 0) { $RegisteredUserUPN = $EntraDevice.RegisteredUsers[0].UserPrincipalName; if (-not [string]::IsNullOrEmpty($RegisteredUserUPN)) { $UserPrincipalName = $RegisteredUserUPN; $UserDataSource = "Entra Registered User"; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Found $UserDataSource{}: '$($UserPrincipalName)'." } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Entra Registered User object has empty UPN." } } else { Write-Log -Level "INFO" -Message "$DeviceLogPrefix No registered user found for Entra device." } } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Failed get Entra device/user for ID '$EntraDeviceId': $($_.Exception.Message)" } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Entra ID missing. Cannot check registered user." } }
    if ([string]::IsNullOrEmpty($UserPrincipalName)) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Could not determine user. Skipping."; continue }

    # --- 4c: Get User Details ---
    # (Same logic as simulation)
    $User = $null ; try { Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Getting details for '$UserPrincipalName' ($UserDataSource)..." ; $User = Get-MgUser -UserId $UserPrincipalName -Property "id,displayName,department,manager" -EA Stop ; if ($null -eq $User) { Throw "User null." } } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Failed get user details for '$UserPrincipalName'. Skipping. Error: $($_.Exception.Message)"; continue }

    # --- 4d: Determine Department Code ---
    # (Same logic as simulation)
    $DepartmentCode = $null ; $UserDepartment = $User.Department
    if (-not [string]::IsNullOrEmpty($UserDepartment)) { Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix User Dept: '$UserDepartment'. Checking map..."; if ($DepartmentMap.ContainsKey($UserDepartment)) { $DepartmentCode = $DepartmentMap[$UserDepartment] ; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Using Dept Code '$DepartmentCode' from User ('$UserDepartment')." } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix User dept '$UserDepartment' not in map." } } else { Write-Log -Level "INFO" -Message "$DeviceLogPrefix User dept empty. Check manager..." }
    if ($null -eq $DepartmentCode) { if ($null -ne $User.Manager -and $User.Manager -is [Microsoft.Open.MSGraph.Model.DirectoryObject] -and -not [string]::IsNullOrEmpty($User.Manager.Id)) { $ManagerId = $User.Manager.Id; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Mgr ID: $ManagerId. Getting details..." ; $Manager = $null ; try { $Manager = Get-MgUser -UserId $ManagerId -Property "id,department,displayName" -EA Stop ; if ($null -ne $Manager) { $ManagerDepartment = $Manager.Department; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Mgr '$($Manager.DisplayName)' Dept: '$ManagerDepartment'. Checking map..." ; if (-not [string]::IsNullOrEmpty($ManagerDepartment)) { if ($DepartmentMap.ContainsKey($ManagerDepartment)) { $DepartmentCode = $DepartmentMap[$ManagerDepartment]; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Using Dept Code '$DepartmentCode' from Mgr ('$ManagerDepartment')." } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Mgr dept '$ManagerDepartment' not in map." } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Mgr '$($Manager.DisplayName)' has empty dept." } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Mgr object (ID: $ManagerId) null." } } catch { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Failed get mgr (ID: $ManagerId): $($_.Exception.Message)" } } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix User has no manager." } }
    if ($null -eq $DepartmentCode) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix No valid Dept Code found. Skipping."; continue }

    # --- 4e: Construct UsernamePart ---
    # (Same logic as simulation)
    $DisplayName = $User.DisplayName; if ([string]::IsNullOrEmpty($DisplayName)) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix User '$UserPrincipalName' empty DisplayName. Skipping."; continue } ; $NameParts = $DisplayName -split '\s+'; if ($NameParts.Count -lt 2) { Write-Log -Level "WARN" -Message "$DeviceLogPrefix DisplayName '$DisplayName' may not be 'First Last'."; $FirstInitial = $NameParts[0].Substring(0, 1); $LastName = $NameParts[0] } else { $FirstInitial = $NameParts[0].Substring(0, 1); $LastName = $NameParts[-1] } ; $UsernamePartRaw = "$($FirstInitial)$($LastName)".Replace(" ",""); Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Raw UsernamePart: '$UsernamePartRaw'."

    # --- 4f: Construct Ideal Name & Initial Truncation ---
    # (Same logic as simulation)
    $FixedPrefix = "$($DevicePrefix)$($Separator)$($DepartmentCode)$($Separator)"; $MaxUserPartLength = $MaxNameLength - $FixedPrefix.Length; if ($MaxUserPartLength -lt 1) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Prefix '$FixedPrefix' too long. Skipping."; continue } ; $UsernamePart = if ($UsernamePartRaw.Length -gt $MaxUserPartLength) { $UsernamePartRaw.Substring(0, $MaxUserPartLength) } else { $UsernamePartRaw } ; $IdealName = "$($FixedPrefix)$($UsernamePart)"; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Ideal name (initial): '$IdealName'."

    # --- 4g: Check if Current Name Matches Ideal ---
    # (Same logic as simulation)
    if ($Device.DeviceName -eq $IdealName) { Write-Log -Level "INFO" -Message "$DeviceLogPrefix Current name matches ideal. No action needed."; continue }

    # --- 4h: Duplicate Check & Suffixing (Corrected Logic) ---
    # (Same logic as simulation)
    $FinalName = $null ; $IdealNameUpper = $IdealName.ToUpper()
    if (-not $AllDeviceNamesLookup.ContainsKey($IdealNameUpper)) { $FinalName = $IdealName ; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Ideal name '$FinalName' is unique."
    } else { Write-Log -Level "WARN" -Message "$DeviceLogPrefix Ideal name '$IdealName' exists. Checking suffixes starting from 2..." ; $Suffix = 2
        while ($true) { $SuffixStr = $Suffix.ToString() ; $MaxLengthBeforeSuffix = $MaxNameLength - $SuffixStr.Length; $MaxUserPartLengthWithSuffix = $MaxLengthBeforeSuffix - $FixedPrefix.Length ; if ($MaxUserPartLengthWithSuffix -lt 1) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Cannot make unique name for ID $($Device.Id). Suffix '$SuffixStr' leaves no room. Skipping."; $FinalName = $null; break } ; $UsernamePartForSuffix = if ($UsernamePartRaw.Length -gt $MaxUserPartLengthWithSuffix) { $UsernamePartRaw.Substring(0, $MaxUserPartLengthWithSuffix) } else { $UsernamePartRaw } ; $CandidateName = "$($FixedPrefix)$($UsernamePartForSuffix)$($SuffixStr)"; Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Trying suffix candidate: '$CandidateName'." ; if (-not $AllDeviceNamesLookup.ContainsKey($CandidateName.ToUpper())) { $FinalName = $CandidateName ; Write-Log -Level "INFO" -Message "$DeviceLogPrefix Found unique name with suffix: '$FinalName'."; break } ; $Suffix++ ; if ($Suffix -ge 1000) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Max suffix attempts. Skipping."; $FinalName = $null; break } }
    }

    # --- 4i: Perform Rename if Necessary (LIVE ACTION) ---
    if ($null -ne $FinalName -and $Device.DeviceName -ne $FinalName) {
        Write-Log -Level "ACTION" -Message "$DeviceLogPrefix *** LIVE MODE *** Attempting to rename from '$($Device.DeviceName)' to '$FinalName'..."
        try {
            $Body = @{ deviceName = $FinalName } | ConvertTo-Json -Depth 4
            $RenameUri = "https://graph.microsoft.com/v1.0/deviceManagement/managedDevices/$($Device.Id)/setDeviceName"

            # ---!!! LIVE RENAME COMMAND !!!---
            Invoke-MgGraphRequest -Method POST -Uri $RenameUri -Body $Body -ContentType "application/json" -ErrorAction Stop

            Write-Log -Level "SUCCESS" -Message "$DeviceLogPrefix Rename command sent successfully. Change applies after check-in/reboot."
            $renameCounter++ # Increment counter for actual actions

            # Update lookup table immediately
            if ($AllDeviceNamesLookup.ContainsKey($Device.DeviceName.ToUpper())) { $AllDeviceNamesLookup.Remove($Device.DeviceName.ToUpper()) }
            $AllDeviceNamesLookup[$FinalName.ToUpper()] = $true
            Write-Log -Level "DEBUG" -Message "$DeviceLogPrefix Updated internal name lookup."

        } catch {
             Write-Log -Level "ERROR" -Message "$DeviceLogPrefix FAILED to send rename command for name '$FinalName': $($_.Exception.Message)"
             if ($_.Exception.Response) { $ER = $_.Exception.Response.GetResponseStream(); $SR = New-Object System.IO.StreamReader($ER); $EB = $SR.ReadToEnd() | ConvertFrom-Json -EA SilentlyContinue; Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Error Response: $($EB | Out-String)" }
        } # End Catch for Rename Action
    } elseif ($null -ne $FinalName -and $Device.DeviceName -eq $FinalName) { Write-Log -Level "INFO" -Message "$DeviceLogPrefix Device already has final name '$FinalName'."
    } elseif ($null -eq $FinalName) { Write-Log -Level "ERROR" -Message "$DeviceLogPrefix Rename skipped: Could not determine unique final name." }

} # End Foreach Device Loop

# --- Step 5: Disconnect ---
Write-Log -Level "ACTION" -Message "`nScript processing complete (Filtered Live Mode). Sent $renameCounter rename commands for processed devices."
Write-Log -Level "INFO" -Message "Disconnecting from Microsoft Graph..." ; Disconnect-MgGraph ; Write-Log -Level "INFO" -Message "Disconnected."
#endregion