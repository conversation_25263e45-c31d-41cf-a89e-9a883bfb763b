﻿
$editmode = $true


$category = Read-Host "Enter CATEGORY to Update"
$designation = Read-Host "Enter DESIGNATION in $category to Update"
$newCT = Read-Host "Enter CONTENT TYPE to set Docs to"

#Load necessary module to connect to SPOService
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client") | Out-Null
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client.Runtime") | Out-Null

$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)

Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred

$WebUrl = "https://qhrtech.sharepoint.com/ca/ISO/"

#Connect to SharePoint Online service
Write-Host "Logging into SharePoint online service." -ForegroundColor Green

$Context = New-Object Microsoft.SharePoint.Client.ClientContext($WebUrl)
$Context.Credentials = New-Object Microsoft.SharePoint.Client.SharePointOnlineCredentials($creduser, $encrypted)


#Get the Necessary List
Write-Host "Getting the required list." -ForegroundColor Green
$List = $Context.Web.Lists.GetByTitle("Documents")

$ContentTypes = $list.ContentTypes

$Query = [Microsoft.SharePoint.Client.CamlQuery]::CreateAllItemsQuery(1500); 
$Items = $List.GetItems($Query);

$Context.Load($ContentTypes)
$Context.Load($Items);
$context.Load($List)
$Context.ExecuteQuery();

foreach ($item in $ContentTypes) {
#Write "$($item.Id): $($item.Name)"
if ($item.name -eq $newCT) {
    $newCTID = $item.Id.StringValue
    }
}

if ($newCTID -eq $null) { 
   Write-Host "Content Type not found, quitting." -ForegroundColor Red
   break
}

#Edit existing list items
foreach($item in $Items)
{

### Uncomment next line to change by category AND designation ###
    if(($item["Categories0"] -eq $category) -and ($item["Designation"] -eq $designation) -and ($item["Archived"] -eq $false) -and ($item["ContentTypeId"] -ne $newCTID))
    
### Uncomment next line to change by category ONLY ###
###    if(($item["Categories0"] -eq $category) -and ($item["Archived"] -eq $false))
    
    {
      Write-Host "Updating Record #$($item.Id)..." -ForegroundColor Green
      $comment = "Set Content Type to $newCT"
      if ($editmode -eq $true) {
        $item.file.CheckOut()
        $Context.ExecuteQuery()


        $item["ContentTypeId"] = $newCTID
       
        $item.Update()
        $Context.ExecuteQuery()
        
        #$CIConfirm = Read-Host "Check in? (y/n)"
        #if ($CIConfirm = "y") {
          $item.file.CheckIn($comment,[Microsoft.SharePoint.Client.CheckinType]::MinorCheckIn)
          $Context.ExecuteQuery()
        #}  
      }
      ### Break here for testing
      #break
    }
    
}

Write-Host "`n`nYour changes have now been made." -ForegroundColor Green