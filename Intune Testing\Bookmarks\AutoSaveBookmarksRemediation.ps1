<#
.SYNOPSIS
    Backs up bookmarks from Google Chrome, Microsoft Edge, and Mozilla Firefox
    to a structured folder in OneDrive or local user Documents. (Remediation Script)

.DESCRIPTION
    This script exports bookmarks from installed browsers into a common backup location.
    - For Google Chrome and Microsoft Edge:
        - Detects all user profiles (Default, Profile 1, etc.).
        - Exports bookmarks from each profile to a NETSCAPE-compatible HTML file.
    - For Mozilla Firefox:
        - Detects all user profiles from both standard and MSIX (Microsoft Store) installation paths.
        - Copies key raw data files: latest .jsonlz4 backup, places.sqlite (live bookmarks/history), 
          and favicons.sqlite from each profile.
        - A README_Firefox_Backup.txt file is created in the Firefox backup directory.

    Backup Location Priority:
    1. $env:OneDrive\Documents\BrowserBookmarkBackups
    2. $env:OneDriveCommercial\Documents\BrowserBookmarkBackups
    3. $env:USERPROFILE\Documents\BrowserBookmarkBackups

    Intune MDM Compliance:
    - No console output. Exits 0 on success, 1 on failure.

.NOTES
    Author: Enhanced and formatted by <PERSON><PERSON>
    Date: June 3, 2025 
#>

#------------------------------------------------------------------------------
# Global Log File Setup
#------------------------------------------------------------------------------
$globalLogPath = $null
try {
    $globalLogPath = Join-Path -Path $env:TEMP -ChildPath "Global_BookmarkBackup_Remediation_Log_$(Get-Date -Format 'yyyyMMddHHmmssfff').txt"
}
catch {
    # If we can't even define a log path in TEMP, something is very wrong. Script will likely fail later.
}

Function Write-GlobalLog {
    param([string]$Message)
    if (-not ([string]::IsNullOrEmpty($globalLogPath))) {
        try {
            Add-Content -Path $globalLogPath -Value "$(Get-Date -Format 'o') - $Message" -ErrorAction SilentlyContinue
        }
        catch {
            # Silently continue if logging fails, to not disrupt main script flow.
        }
    }
}

#------------------------------------------------------------------------------
# Function Definitions
#------------------------------------------------------------------------------

Function Get-BrowserBackupBaseDirectory {
    <#
.SYNOPSIS
    Determines and prepares the root backup directory.
.RETURNS
    [string] The path to the base backup directory, or $null on failure.
#>
    Param()
    Write-GlobalLog "Get-BrowserBackupBaseDirectory: Starting."
    $backupRootFolderName = "BrowserBookmarkBackups"
    $potentialDocPaths = [System.Collections.Generic.List[string]]::new()

    if ($env:OneDrive) { Write-GlobalLog "Get-BrowserBackupBaseDirectory: Checking OneDrive personal path."; $potentialDocPaths.Add((Join-Path -Path $env:OneDrive -ChildPath "Documents")) }
    if ($env:OneDriveCommercial) { Write-GlobalLog "Get-BrowserBackupBaseDirectory: Checking OneDrive commercial path."; $potentialDocPaths.Add((Join-Path -Path $env:OneDriveCommercial -ChildPath "Documents")) }
    Write-GlobalLog "Get-BrowserBackupBaseDirectory: Checking UserProfile Documents path."
    $potentialDocPaths.Add((Join-Path -Path $env:USERPROFILE -ChildPath "Documents"))

    foreach ($docPath in ($potentialDocPaths | Select-Object -Unique)) {
        Write-GlobalLog "Get-BrowserBackupBaseDirectory: Evaluating potential Documents path: $docPath"
        if (-not (Test-Path $docPath)) { Write-GlobalLog "Get-BrowserBackupBaseDirectory: Path $docPath does not exist. Skipping."; continue }
        
        $targetBaseDir = Join-Path -Path $docPath -ChildPath $backupRootFolderName
        Write-GlobalLog "Get-BrowserBackupBaseDirectory: Target base backup dir candidate: $targetBaseDir"
        try {
            if (-not (Test-Path $targetBaseDir)) {
                Write-GlobalLog "Get-BrowserBackupBaseDirectory: Creating directory $targetBaseDir"
                New-Item -ItemType Directory -Path $targetBaseDir -Force -ErrorAction Stop | Out-Null
                Write-GlobalLog "Get-BrowserBackupBaseDirectory: Successfully created $targetBaseDir"
            }
            else {
                Write-GlobalLog "Get-BrowserBackupBaseDirectory: Directory $targetBaseDir already exists."
            }
            Write-GlobalLog "Get-BrowserBackupBaseDirectory: Using base backup directory: $targetBaseDir"
            return $targetBaseDir
        }
        catch {
            Write-GlobalLog "Get-BrowserBackupBaseDirectory: FAILED to create/use $targetBaseDir. Error: $($_.Exception.Message)"
            continue
        }
    }
    Write-GlobalLog "Get-BrowserBackupBaseDirectory: All attempts to find/create a base backup directory failed."
    return $null
}

Function Export-JsonBookmarksToHtml {
    <#
.SYNOPSIS
    Converts a browser's JSON bookmarks file to HTML.
#>
    Param(
        [Parameter(Mandatory = $true)][string]$InputJsonPath,
        [Parameter(Mandatory = $true)][string]$OutputHtmlPath
    )
    Write-GlobalLog "Export-JsonBookmarksToHtml: Starting for Input: $InputJsonPath, Output: $OutputHtmlPath"
    $htmlHeader = @'
<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
'@
    # ^^^ CRITICAL: Closing '@' MUST BE ALONE AT THE START OF ITS LINE.

    try {
        $htmlHeader | Out-File -FilePath $OutputHtmlPath -Force -Encoding utf8
        Write-GlobalLog "Export-JsonBookmarksToHtml: Successfully wrote HTML header to $OutputHtmlPath"
    }
    catch {
        Write-GlobalLog "Export-JsonBookmarksToHtml: FATAL - Failed to write HTML header to $OutputHtmlPath. Error: $($_.Exception.Message)"
        throw # Re-throw to be caught by main script's catch for this function call
    }

    try {
        # Main processing try block
        Function Get-RecursiveBookmarkData {
            [CmdletBinding()]
            Param([Parameter(Mandatory = $true)]$BookmarkNode, [Parameter(Mandatory = $true)][string]$CurrentOutputHtmlPath)
            Process {
                if ($null -ne $BookmarkNode -and ($null -ne $BookmarkNode.PSObject.Properties['children']) -and ($null -ne $BookmarkNode.children)) {
                    foreach ($childItem in $BookmarkNode.children) {
                        $dateAddedEpochSeconds = 0
                        if ($null -ne $childItem.PSObject.Properties['date_added']) {
                            $dateStr = [string]$childItem.date_added
                            if ($dateStr -ne "0" -and -not([string]::IsNullOrEmpty($dateStr))) {
                                try { $dateAddedEpochSeconds = [math]::Round([double]$dateStr / 1000000) } catch {}
                            }
                        }
                        $itemName = "[No Name]"
                        if ($null -ne $childItem.PSObject.Properties['name']) {
                            $rawName = [string]$childItem.name
                            if (-not([string]::IsNullOrWhiteSpace($rawName))) {
                                $encodedName = [System.Net.WebUtility]::HtmlEncode($rawName)
                                $itemName = if (-not([string]::IsNullOrWhiteSpace($encodedName))) { $encodedName } else { "[Encoded Empty Name]" }
                            }
                            else { $itemName = "[Effectively No Name]" }
                        }
                        if ($null -ne $childItem.PSObject.Properties['type']) {
                            $itemType = [string]$childItem.type
                            if ($itemType -eq 'folder') {
                                "      <DT><H3 FOLDED ADD_DATE=""$($dateAddedEpochSeconds)"">$($itemName)</H3>" | Out-File -FilePath $CurrentOutputHtmlPath -Append -Force -Encoding utf8
                                "        <DL><p>" | Out-File -FilePath $CurrentOutputHtmlPath -Append -Force -Encoding utf8
                                Get-RecursiveBookmarkData -BookmarkNode $childItem -CurrentOutputHtmlPath $CurrentOutputHtmlPath
                                "        </DL><p>" | Out-File -FilePath $CurrentOutputHtmlPath -Append -Force -Encoding utf8
                            }
                            elseif ($itemType -eq 'url') {
                                $itemUrl = "#"
                                if ($null -ne $childItem.PSObject.Properties['url']) {
                                    $itemUrlCandidate = [string]$childItem.url
                                    if (-not([string]::IsNullOrEmpty($itemUrlCandidate))) { $itemUrl = [System.Net.WebUtility]::HtmlEncode($itemUrlCandidate) }
                                }
                                "      <DT><A HREF=""$($itemUrl)"" ADD_DATE=""$($dateAddedEpochSeconds)"">$($itemName)</A>" | Out-File -FilePath $CurrentOutputHtmlPath -Append -Force -Encoding utf8
                            }
                        }
                    } 
                } 
            } 
        }

        Write-GlobalLog "Export-JsonBookmarksToHtml: Reading JSON from $InputJsonPath"
        $bookmarkJsonContent = Get-Content -Path $InputJsonPath -Encoding UTF8 -Raw | ConvertFrom-Json
        if ($null -eq $bookmarkJsonContent -or ($null -eq $bookmarkJsonContent.PSObject.Properties['roots'])) { Write-GlobalLog "Export-JsonBookmarksToHtml: ERROR - Failed to parse JSON or 'roots' missing."; throw "Diagnostic: Failed to parse JSON or 'roots' missing from '$InputJsonPath'." }
        if ($null -eq $bookmarkJsonContent.roots) { Write-GlobalLog "Export-JsonBookmarksToHtml: ERROR - JSON 'roots' object is null."; throw "Diagnostic: JSON 'roots' object is null in '$InputJsonPath'." }
        
        $bookmarkRootKeys = $bookmarkJsonContent.roots.PSObject.Properties | Select-Object -ExpandProperty Name
        if ($null -eq $bookmarkRootKeys) { $bookmarkRootKeys = @() }
        Write-GlobalLog "Export-JsonBookmarksToHtml: Found $($bookmarkRootKeys.Count) root keys."

        ForEach ($rootKey in $bookmarkRootKeys) {
            Write-GlobalLog "Export-JsonBookmarksToHtml: Processing root key: $rootKey"
            $rootNode = $bookmarkJsonContent.roots.$rootKey
            if ($null -ne $rootNode -and ($null -ne $rootNode.PSObject.Properties['name']) -and ($null -ne $rootNode.PSObject.Properties['type']) -and ([string]$rootNode.type -eq 'folder')) {
                $rootDateAddedEpochSeconds = 0
                if ($null -ne $rootNode.PSObject.Properties['date_added']) {
                    $dateStr = [string]$rootNode.date_added
                    if ($dateStr -ne "0" -and -not([string]::IsNullOrEmpty($dateStr))) { try { $rootDateAddedEpochSeconds = [math]::Round([double]$dateStr / 1000000) } catch {} }
                }
                $rootName = "[No Root Name]"
                if ($null -ne $rootNode.PSObject.Properties['name']) {
                    $rawRootName = [string]$rootNode.name
                    if (-not([string]::IsNullOrWhiteSpace($rawRootName))) {
                        $encodedRootName = [System.Net.WebUtility]::HtmlEncode($rawRootName)
                        $rootName = if (-not([string]::IsNullOrWhiteSpace($encodedRootName))) { $encodedRootName } else { "[Encoded Empty Root Name]" }
                    }
                    else { $rootName = "[Effectively No Root Name]" }
                }
                Write-GlobalLog "Export-JsonBookmarksToHtml: Writing root folder: $rootName"
                "    <DT><H3 FOLDED ADD_DATE=""$($rootDateAddedEpochSeconds)"">$($rootName)</H3>" | Out-File -FilePath $OutputHtmlPath -Append -Force -Encoding utf8
                "      <DL><p>" | Out-File -FilePath $OutputHtmlPath -Append -Force -Encoding utf8
                Get-RecursiveBookmarkData -BookmarkNode $rootNode -CurrentOutputHtmlPath $OutputHtmlPath
                "      </DL><p>" | Out-File -FilePath $OutputHtmlPath -Append -Force -Encoding utf8
            }
            else {
                Write-GlobalLog "Export-JsonBookmarksToHtml: Skipped root key $rootKey (not a valid folder node or missing properties)."
            }
        }
        Write-GlobalLog "Export-JsonBookmarksToHtml: Appending final DL tag."
        '</DL><p>' | Out-File -FilePath $OutputHtmlPath -Append -Force -Encoding utf8
        Write-GlobalLog "Export-JsonBookmarksToHtml: Successfully completed for $InputJsonPath"
    }
    catch {
        # Catches errors within the main processing block of Export-JsonBookmarksToHtml
        Write-GlobalLog "Export-JsonBookmarksToHtml: ERROR during main processing for $InputJsonPath. Error: $($_.Exception.Message)"
        # Create detailed error log in TEMP
        $detailedErrorLogPath = $null
        try {
            $timestampForFile = Get-Date -Format 'yyyyMMdd_HHmmss_fff'
            $safeInputNamePart = ([System.IO.Path]::GetFileNameWithoutExtension($InputJsonPath) -replace '[^a-zA-Z0-9]', '_') | Select-Object -First 30
            $detailedErrorLogFileName = "BookmarkScript_Error_ExportFunc_${safeInputNamePart}_${timestampForFile}.log"
            $detailedErrorLogPath = Join-Path -Path ([System.IO.Path]::GetTempPath()) -ChildPath $detailedErrorLogFileName
            $errorDetails = @"
Timestamp: $(Get-Date -Format ISO8601)
Error in Export-JsonBookmarksToHtml for:
Input JSON Path: $InputJsonPath
Output HTML Path: $OutputHtmlPath
Exception Type: $($_.Exception.GetType().FullName)
Exception Message: $($_.Exception.Message)
ScriptStackTrace: $($_.ScriptStackTrace)
TargetObject: $($_.TargetObject | Out-String -Width 120)
Exception Full: $($_.Exception | Out-String -Width 120)
"@
            # ^^ Ensure closing "@ is on its own line, at the beginning for this here-string too.
            Set-Content -Path $detailedErrorLogPath -Value $errorDetails -Encoding UTF8 -Force
            Write-GlobalLog "Export-JsonBookmarksToHtml: Detailed error for this failure written to $detailedErrorLogPath"
        }
        catch {
            Write-GlobalLog "Export-JsonBookmarksToHtml: CRITICAL - FAILED to write detailed error to $detailedErrorLogPath during error handling. Inner error: $($_.Exception.Message)"
        }
        throw $_ # Re-throw original error to be caught by the caller's try/catch in the main script body
    }
}

#------------------------------------------------------------------------------
# Main Script Execution Logic
#------------------------------------------------------------------------------
try {
    Write-GlobalLog "Remediation script execution started."
    $baseBackupDirectory = Get-BrowserBackupBaseDirectory
    if ([string]::IsNullOrEmpty($baseBackupDirectory)) { 
        Write-GlobalLog "FATAL: Base backup directory could not be determined or created. Script will exit 1."
        exit 1 
    }
    Write-GlobalLog "Base backup directory set to: $baseBackupDirectory"

    $overallSuccess = $true

    $browsersToProcess = @(
        @{ Name = "GoogleChrome"; PathType = "UserDataPath"; PathValue = (Join-Path -Path $env:LOCALAPPDATA -ChildPath "Google\Chrome\User Data"); BackupType = "JsonBookmarks" },
        @{ Name = "MicrosoftEdge"; PathType = "UserDataPath"; PathValue = (Join-Path -Path $env:LOCALAPPDATA -ChildPath "Microsoft\Edge\User Data"); BackupType = "JsonBookmarks" },
        @{ Name = "MozillaFirefox"; PathType = "ProfilesRoots"; # PathValue determined dynamically
            BackupType = "FirefoxRawFilesBackup" 
        } 
    )

    foreach ($browser in $browsersToProcess) {
        Write-GlobalLog "------------------------------------------------------------------"
        Write-GlobalLog "Processing browser: $($browser.Name)"
        $browserSpecificBackupDir = Join-Path -Path $baseBackupDirectory -ChildPath $browser.Name
        
        $pathsToSearchForProfiles = [System.Collections.Generic.List[string]]::new()

        if ($browser.BackupType -eq "FirefoxRawFilesBackup") {
            Write-GlobalLog "Firefox: Detecting profile paths."
            $msixPackagesBasePath = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Packages"
            if (Test-Path $msixPackagesBasePath) {
                Write-GlobalLog "Firefox: MSIX Packages base path found: $msixPackagesBasePath"
                $firefoxMsixPackageDir = Get-ChildItem -Path $msixPackagesBasePath -Directory -Filter "Mozilla.Firefox*" -ErrorAction SilentlyContinue | Select-Object -First 1
                if ($firefoxMsixPackageDir) {
                    Write-GlobalLog "Firefox: Found MSIX package dir: $($firefoxMsixPackageDir.FullName)"
                    $msixProfilesPath = Join-Path -Path $firefoxMsixPackageDir.FullName -ChildPath "LocalCache\Roaming\Mozilla\Firefox\Profiles"
                    if (Test-Path $msixProfilesPath) { 
                        Write-GlobalLog "Firefox: Found MSIX profiles path: $msixProfilesPath"
                        $pathsToSearchForProfiles.Add($msixProfilesPath)
                    }
                    else { Write-GlobalLog "Firefox: MSIX profiles path NOT found: $msixProfilesPath" }
                }
                else { Write-GlobalLog "Firefox: No Mozilla.Firefox* package directory found under $msixPackagesBasePath" }
            }
            else { Write-GlobalLog "Firefox: MSIX Packages base path NOT found: $msixPackagesBasePath" }

            $standardProfilesPath = Join-Path -Path $env:APPDATA -ChildPath "Mozilla\Firefox\Profiles"
            Write-GlobalLog "Firefox: Checking standard profiles path: $standardProfilesPath"
            if (Test-Path $standardProfilesPath) {
                Write-GlobalLog "Firefox: Found standard profiles path: $standardProfilesPath"
                if (-not ($pathsToSearchForProfiles.Contains($standardProfilesPath))) { $pathsToSearchForProfiles.Add($standardProfilesPath) }
            }
            else { Write-GlobalLog "Firefox: Standard profiles path NOT found: $standardProfilesPath" }
            
            if ($pathsToSearchForProfiles.Count -eq 0) { Write-GlobalLog "Firefox: No profile roots found. Skipping Firefox."; continue }
            Write-GlobalLog "Firefox: Profile root paths to check: $($pathsToSearchForProfiles -join '; ')"
        }
        else {
            # For JsonBookmarks (Chrome/Edge)
            $primaryChromeEdgeUserDataPath = $browser.PathValue
            Write-GlobalLog "$($browser.Name): Checking UserDataPath: $primaryChromeEdgeUserDataPath"
            if (-not (Test-Path $primaryChromeEdgeUserDataPath)) { Write-GlobalLog "$($browser.Name): UserDataPath NOT found. Skipping."; continue }
            $pathsToSearchForProfiles.Add($primaryChromeEdgeUserDataPath) 
        }

        $browserBackupDirCreatedOrExisted = $false
        Write-GlobalLog "Attempting to create/confirm browser backup dir: $browserSpecificBackupDir"
        try {
            if (-not (Test-Path $browserSpecificBackupDir)) {
                New-Item -ItemType Directory -Path $browserSpecificBackupDir -Force -ErrorAction Stop | Out-Null
                Write-GlobalLog "Created browser backup dir: $browserSpecificBackupDir"
            }
            if (Test-Path $browserSpecificBackupDir) {
                $browserBackupDirCreatedOrExisted = $true
                Write-GlobalLog "Confirmed browser backup dir exists: $browserSpecificBackupDir"
            }
            else { $overallSuccess = $false; Write-GlobalLog "ERROR: Failed to confirm browser backup dir after creation attempt: $browserSpecificBackupDir" }
        }
        catch { $overallSuccess = $false; Write-GlobalLog "EXCEPTION creating browser backup dir $browserSpecificBackupDir : $($_.Exception.Message)" }

        if (-not $browserBackupDirCreatedOrExisted) { Write-GlobalLog "Skipping browser $($browser.Name) as its backup directory was not created/confirmed."; continue }
        
        if ($browser.BackupType -eq "JsonBookmarks") {
            Write-GlobalLog "$($browser.Name): Processing JSON bookmarks from profiles in $($pathsToSearchForProfiles[0])"
            Get-ChildItem -Path $pathsToSearchForProfiles[0] -Directory -ErrorAction SilentlyContinue | Where-Object { $_.Name -eq "Default" -or $_.Name -like "Profile *" } | ForEach-Object {
                $profileDir = $_
                Write-GlobalLog "$($browser.Name): Found profile dir: $($profileDir.FullName)"
                $bookmarksJsonFile = Join-Path -Path $profileDir.FullName -ChildPath "Bookmarks"
                if (Test-Path $bookmarksJsonFile) {
                    Write-GlobalLog "$($browser.Name): Bookmarks JSON file found: $bookmarksJsonFile"
                    $outputHtmlFileName = "$($profileDir.Name)_Bookmarks_$($env:COMPUTERNAME).html"
                    $outputHtmlFilePath = Join-Path -Path $browserSpecificBackupDir -ChildPath $outputHtmlFileName
                    try {
                        Export-JsonBookmarksToHtml -InputJsonPath $bookmarksJsonFile -OutputHtmlPath $outputHtmlFilePath
                        Write-GlobalLog "$($browser.Name): Successfully exported bookmarks for profile $($profileDir.Name) to $outputHtmlFilePath"
                    }
                    catch { $script:overallSuccess = $false; Write-GlobalLog "$($browser.Name): FAILED to export for profile $($profileDir.Name). Error: $($_.Exception.Message)" }
                }
                else { Write-GlobalLog "$($browser.Name): Bookmarks JSON file NOT found for profile $($profileDir.Name) at $bookmarksJsonFile" }
            }
        }
        elseif ($browser.BackupType -eq "FirefoxRawFilesBackup") {
            Write-GlobalLog "Firefox: Processing raw file backup."
            $readmeFilePath = Join-Path -Path $browserSpecificBackupDir -ChildPath "README_Firefox_Backup.txt"
            if (-not (Test-Path $readmeFilePath)) {
                try {
                    Write-GlobalLog "Firefox: Creating README file at $readmeFilePath"
                    $readmeContent = @'
Firefox Bookmark Backup Information
===================================
This folder contains raw backup files for Mozilla Firefox profiles. An HTML export is not provided by this script.
Files copied per profile:
1. *_bookmarks_jsonlz4_*.jsonlz4: Compressed bookmark backups from the 'bookmarkbackups' folder. Use Firefox's Restore feature.
2. *_places.sqlite: Live bookmarks, history, and other data. Replace in a profile (Firefox closed).
3. *_favicons.sqlite: Bookmark favicons.
Refer to Mozilla support for details on using these files.
'@
                    # ^^ Ensure closing '@ is on its own line, at the beginning.
                    Set-Content -Path $readmeFilePath -Value $readmeContent -Encoding UTF8 -Force -ErrorAction SilentlyContinue
                }
                catch { Write-GlobalLog "Firefox: Non-critical error creating README file. Error: $($_.Exception.Message)" }
            }

            foreach ($profileRootPath in ($pathsToSearchForProfiles | Select-Object -Unique)) {
                Write-GlobalLog "Firefox: Searching for profiles in root: $profileRootPath"
                Get-ChildItem -Path $profileRootPath -Directory -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*.default*" -or $_.Name -like "*.release*" } | ForEach-Object {
                    $profileDir = $_; $profileNameForFile = $profileDir.Name
                    Write-GlobalLog "Firefox: Processing profile: $($profileDir.FullName)"
                    
                    $backupItems = @(
                        @{ Name = "JSONLZ4 Backup"; SourceSubPath = "bookmarkbackups"; Filter = "*.jsonlz4"; SortProperty = "LastWriteTime"; DestNamePattern = "$($profileNameForFile)_bookmarks_jsonlz4_{0}.jsonlz4"; IsLatestOnly = $true };
                        @{ Name = "Places DB"; SourceSubPath = ""; FileName = "places.sqlite"; DestNamePattern = "$($profileNameForFile)_places.sqlite"; IsLatestOnly = $false };
                        @{ Name = "Favicons DB"; SourceSubPath = ""; FileName = "favicons.sqlite"; DestNamePattern = "$($profileNameForFile)_favicons.sqlite"; IsLatestOnly = $false }
                    )

                    foreach ($itemToBackup in $backupItems) {
                        Write-GlobalLog "Firefox: Attempting to back up $($itemToBackup.Name) for profile $profileNameForFile"
                        $sourceContainerPath = Join-Path -Path $profileDir.FullName -ChildPath $itemToBackup.SourceSubPath
                        if ($itemToBackup.SourceSubPath -ne "" -and (-not (Test-Path $sourceContainerPath))) { Write-GlobalLog "Firefox: Source container path not found for $($itemToBackup.Name): $sourceContainerPath"; continue }

                        if ($itemToBackup.IsLatestOnly) {
                            $sourceFile = Get-ChildItem -Path $sourceContainerPath -Filter $itemToBackup.Filter -File -ErrorAction SilentlyContinue | Sort-Object $itemToBackup.SortProperty -Descending | Select-Object -First 1
                            if ($sourceFile) {
                                $timestamp = $sourceFile.LastWriteTime.ToString('yyyyMMddHHmmss')
                                $destFileName = $itemToBackup.DestNamePattern -replace "\{0\}", $timestamp 
                                $destinationPath = Join-Path -Path $browserSpecificBackupDir -ChildPath $destFileName
                                Write-GlobalLog "Firefox: Copying $($sourceFile.FullName) to $destinationPath"
                                try { Copy-Item -Path $sourceFile.FullName -Destination $destinationPath -Force -ErrorAction Stop } 
                                catch { $script:overallSuccess = $false; Write-GlobalLog "Firefox: FAILED to copy $($itemToBackup.Name) from $($sourceFile.FullName). Error: $($_.Exception.Message)" }
                            }
                            else { Write-GlobalLog "Firefox: No source file found for $($itemToBackup.Name) with filter $($itemToBackup.Filter) in $sourceContainerPath" }
                        }
                        else { 
                            $actualSourcePath = if ([string]::IsNullOrEmpty($itemToBackup.SourceSubPath)) { $profileDir.FullName } else { $sourceContainerPath }
                            $sourceFile = Join-Path -Path $actualSourcePath -ChildPath $itemToBackup.FileName 
                            if (Test-Path $sourceFile) {
                                $destinationPath = Join-Path -Path $browserSpecificBackupDir -ChildPath $itemToBackup.DestNamePattern
                                Write-GlobalLog "Firefox: Copying $sourceFile to $destinationPath"
                                try { Copy-Item -Path $sourceFile -Destination $destinationPath -Force -ErrorAction Stop } 
                                catch { $script:overallSuccess = $false; Write-GlobalLog "Firefox: FAILED to copy $($itemToBackup.Name) from $sourceFile. Error: $($_.Exception.Message)" }
                            }
                            else { Write-GlobalLog "Firefox: Source file NOT found for $($itemToBackup.Name): $sourceFile" }
                        }
                    } 
                } 
            } 
        } 
        Write-GlobalLog "Finished processing for browser: $($browser.Name)"
    } 

    Write-GlobalLog "Remediation script finished. Overall success: $overallSuccess. Exiting."
    if ($overallSuccess) { exit 0 } else { exit 1 }
}
catch {
    Write-GlobalLog "CRITICAL SCRIPT ERROR (Main Catch Block): Error: $($_.Exception.Message) -- StackTrace: $($_.ScriptStackTrace)"
    exit 1
}
