#TYPE Selected.System.Management.Automation.PSCustomObject
"Name","Alias","PrimarySmtpAddress"
"MSX Services","MSXServices","<EMAIL>"
"QHRNetAdmin","QHRNetAdmin","<EMAIL>"
"O Perator","operator","<EMAIL>"
"Optimed Sales","emrsales","<EMAIL>"
"Quarantine","quarantine","<EMAIL>"
"Ricoh","ricoh","<EMAIL>"
"e meeting","emeeting","<EMAIL>"
"e learning","elearning","<EMAIL>"
"QHR Hosted","QHR_Hosted","<EMAIL>"
"Web Demo","webdemo","<EMAIL>"
"WebMaster","WebMaster","<EMAIL>"
"SQL-OptDevKel01","SQL-OptDevKel01","<EMAIL>"
"SQL-OptDevKel02","SQL-OptDevKel02","<EMAIL>"
"SQL-OptDevKel03","SQL-OptDevKel03","<EMAIL>"
"SQL-qhrsql03SQL2K5","SQL-qhrsql03SQL2K5","<EMAIL>"
"SQL-QHRSQL03","SQL-QHRSQL03","<EMAIL>"
"SRV-OptOnline","SRV-OptOnline","<EMAIL>"
"Staff Meeting","staffmeeting","<EMAIL>"
"Live Meeting","livemeeting","<EMAIL>"
"Clinicare","clinicare","<EMAIL>"
"Server Farm","SharePointServiceEmail","<EMAIL>"
"Toshiba","Toshiba","<EMAIL>"
"voicemail","voicemail","<EMAIL>"
"SQL-OCS","sql-ocs","<EMAIL>"
"QT StaffMeetings","qtstaffmeetings","<EMAIL>"
"FepSQLAdmin","FepSQLAdmin","<EMAIL>"
"qtmiteloaisys","qtmiteloaisys","<EMAIL>"
"qtmitelvmail","qtmitelvmail","<EMAIL>"
"Tops","tops","<EMAIL>"
"qtialerts","qtialerts","<EMAIL>"
"test support","test.support","<EMAIL>"
"noreply support","noreply.support","<EMAIL>"
"Exchange Server Failures","exch_serverfailures","<EMAIL>"
"Test OnPrem","testonprem","<EMAIL>"
"QHR Toronto Office","18king","<EMAIL>"
"SEP","sep","<EMAIL>"
