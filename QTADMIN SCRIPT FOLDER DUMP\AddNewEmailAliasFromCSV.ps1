﻿Import-Module activedirectory

#Update path to CSV list
$path = "C:\temp\AllUsersAccoutName.csv"

#Name of new Alias you wish to add
$smtp13 = ".<EMAIL>"
$Smtp ='smtp:'
$SMTPPrimary ='SMTP:'

#This loop loops through each line of the CSV, takes the username and looks up the AD User, afterwards it will apply the Alias.
Import-Csv $path | Foreach-Object { 

    foreach ($property in $_.PSObject.Properties)
    {
        $Username = $property.value
        $user = Get-ADUser $Username
        $fname=$($user.givenname)
        $lname=$($user.surname)
        #You may need to update the formatting of this string.
        $proxyaddresses13=$Smtp + $fname + '.' +$lname + $smtp13
        Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses13}
    } 

}

pause



