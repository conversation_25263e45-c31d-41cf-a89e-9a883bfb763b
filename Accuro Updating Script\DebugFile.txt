The chosen JVM: m_jvmPath=C:\Program Files (x86)\Accuro - AB\jvm_run\jre\bin\client\jvm.dll,m_javaBundle=,m_binJvmDir=,m_comparableVersion=,m_javaHomePath=,m_version=,m_jrePath=,m_acceptable=true
The properties chosen: m_fullPathAndNameOfExe=C:\Program Files (x86)\Accuro - AB\UpdateAccuro.exe,m_selfHomePath=C:\Program Files (x86)\Accuro - AB,m_customJavaHomePath=C:\Program Files (x86)\Accuro - AB\jvm_run,m_customBinJvmDir=,m_mainClass=com.optimedsoftware.updater.client.app.UpdateMain,m_customJvmPath=,m_minJavaVersion=,m_maxJavaVersion=,m_trapConsoleCtrl=,m_jarsInClasspath=;C:\Program Files (x86)\Accuro - AB\Updater\AccuroCommunication.jar;C:\Program Files (x86)\Accuro - AB\Updater\antlr.jar;C:\Program Files (x86)\Accuro - AB\Updater\asm-attrs.jar;C:\Program Files (x86)\Accuro - AB\Updater\asm.jar;C:\Program Files (x86)\Accuro - AB\Updater\bcprov.jar;C:\Program Files (x86)\Accuro - AB\Updater\cglib.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-cli.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-codec.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-collections.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-httpclient.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-io.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-lang.jar;C:\Program Files (x86)\Accuro - AB\Updater\commons-logging.jar;C:\Program Files (x86)\Accuro - AB\Updater\dom4j.jar;C:\Program Files (x86)\Accuro - AB\Updater\ehcache.jar;C:\Program Files (x86)\Accuro - AB\Updater\ejb3-persistence.jar;C:\Program Files (x86)\Accuro - AB\Updater\fast-md5.jar;C:\Program Files (x86)\Accuro - AB\Updater\hibernate-annotations.jar;C:\Program Files (x86)\Accuro - AB\Updater\hibernate-commons-annotations.jar;C:\Program Files (x86)\Accuro - AB\Updater\hibernate.jar;C:\Program Files (x86)\Accuro - AB\Updater\HikariCP-java6.jar;C:\Program Files (x86)\Accuro - AB\Updater\javassist.jar;C:\Program Files (x86)\Accuro - AB\Updater\jta.jar;C:\Program Files (x86)\Accuro - AB\Updater\jtds.jar;C:\Program Files (x86)\Accuro - AB\Updater\kxml2.jar;C:\Program Files (x86)\Accuro - AB\Updater\log4j.jar;C:\Program Files (x86)\Accuro - AB\Updater\OSCEncryption.jar;C:\Program Files (x86)\Accuro - AB\Updater\proxool.jar;C:\Program Files (x86)\Accuro - AB\Updater\slf4j-api.jar;C:\Program Files (x86)\Accuro - AB\Updater\slf4j-log4j12.jar;C:\Program Files (x86)\Accuro - AB\Updater\swing-layout.jar;C:\Program Files (x86)\Accuro - AB\Updater\swing-worker.jar;C:\Program Files (x86)\Accuro - AB\Updater\UpdateLibrary.jar;C:\Program Files (x86)\Accuro - AB\Updater\UpdatesDataLibrary.jar,m_libraryPath=,m_debugFile=DebugFile.txt,m_callerDir=C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\Accuro Updating Script,m_javaSystemProperties[0]=-Xms64m,m_javaSystemProperties[1]=-Xmx256m,m_commandLineArguments[0]=-url,m_commandLineArguments[1]=http://services.optimedsoftware.com/Updates/updates/,m_commandLineArguments[2]=-downloadurl,m_commandLineArguments[3]=https://updateemr-files.optimedsoftware.com/Updates/updates/,m_commandLineArguments[4]=-product,m_commandLineArguments[5]=Accuro,m_commandLineArguments[6]=-checkupdater,m_commandLineArguments[7]=-checkaccuro,m_commandLineArguments[8]=-basedir,m_commandLineArguments[9]=C:\Program Files (x86)\Accuro - AB
The chosen JVM: m_jvmPath=C:\Program Files (x86)\Accuro - BC\jvm_run\jre\bin\client\jvm.dll,m_javaBundle=,m_binJvmDir=,m_comparableVersion=,m_javaHomePath=,m_version=,m_jrePath=,m_acceptable=true
The properties chosen: m_fullPathAndNameOfExe=C:\Program Files (x86)\Accuro - BC\UpdateAccuro.exe,m_selfHomePath=C:\Program Files (x86)\Accuro - BC,m_customJavaHomePath=C:\Program Files (x86)\Accuro - BC\jvm_run,m_customBinJvmDir=,m_mainClass=com.optimedsoftware.updater.client.app.UpdateMain,m_customJvmPath=,m_minJavaVersion=,m_maxJavaVersion=,m_trapConsoleCtrl=,m_jarsInClasspath=;C:\Program Files (x86)\Accuro - BC\Updater\AccuroCommunication.jar;C:\Program Files (x86)\Accuro - BC\Updater\antlr.jar;C:\Program Files (x86)\Accuro - BC\Updater\asm-attrs.jar;C:\Program Files (x86)\Accuro - BC\Updater\asm.jar;C:\Program Files (x86)\Accuro - BC\Updater\bcprov.jar;C:\Program Files (x86)\Accuro - BC\Updater\cglib.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-cli.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-codec.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-collections.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-httpclient.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-io.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-lang.jar;C:\Program Files (x86)\Accuro - BC\Updater\commons-logging.jar;C:\Program Files (x86)\Accuro - BC\Updater\dom4j.jar;C:\Program Files (x86)\Accuro - BC\Updater\ehcache.jar;C:\Program Files (x86)\Accuro - BC\Updater\ejb3-persistence.jar;C:\Program Files (x86)\Accuro - BC\Updater\fast-md5.jar;C:\Program Files (x86)\Accuro - BC\Updater\hibernate-annotations.jar;C:\Program Files (x86)\Accuro - BC\Updater\hibernate-commons-annotations.jar;C:\Program Files (x86)\Accuro - BC\Updater\hibernate.jar;C:\Program Files (x86)\Accuro - BC\Updater\HikariCP-java6.jar;C:\Program Files (x86)\Accuro - BC\Updater\javassist.jar;C:\Program Files (x86)\Accuro - BC\Updater\jta.jar;C:\Program Files (x86)\Accuro - BC\Updater\jtds.jar;C:\Program Files (x86)\Accuro - BC\Updater\kxml2.jar;C:\Program Files (x86)\Accuro - BC\Updater\log4j.jar;C:\Program Files (x86)\Accuro - BC\Updater\OSCEncryption.jar;C:\Program Files (x86)\Accuro - BC\Updater\proxool.jar;C:\Program Files (x86)\Accuro - BC\Updater\slf4j-api.jar;C:\Program Files (x86)\Accuro - BC\Updater\slf4j-log4j12.jar;C:\Program Files (x86)\Accuro - BC\Updater\swing-layout.jar;C:\Program Files (x86)\Accuro - BC\Updater\swing-worker.jar;C:\Program Files (x86)\Accuro - BC\Updater\UpdateLibrary.jar;C:\Program Files (x86)\Accuro - BC\Updater\UpdatesDataLibrary.jar,m_libraryPath=,m_debugFile=DebugFile.txt,m_callerDir=C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\Accuro Updating Script,m_javaSystemProperties[0]=-Xms64m,m_javaSystemProperties[1]=-Xmx256m,m_commandLineArguments[0]=-url,m_commandLineArguments[1]=http://services.optimedsoftware.com/Updates/updates/,m_commandLineArguments[2]=-downloadurl,m_commandLineArguments[3]=https://updateemr-files.optimedsoftware.com/Updates/updates/,m_commandLineArguments[4]=-product,m_commandLineArguments[5]=Accuro,m_commandLineArguments[6]=-checkupdater,m_commandLineArguments[7]=-checkaccuro,m_commandLineArguments[8]=-basedir,m_commandLineArguments[9]=C:\Program Files (x86)\Accuro - BC
The chosen JVM: m_jvmPath=C:\Program Files (x86)\Accuro - MB\jvm_run\jre\bin\client\jvm.dll,m_javaBundle=,m_binJvmDir=,m_comparableVersion=,m_javaHomePath=,m_version=,m_jrePath=,m_acceptable=true
The properties chosen: m_fullPathAndNameOfExe=C:\Program Files (x86)\Accuro - MB\UpdateAccuro.exe,m_selfHomePath=C:\Program Files (x86)\Accuro - MB,m_customJavaHomePath=C:\Program Files (x86)\Accuro - MB\jvm_run,m_customBinJvmDir=,m_mainClass=com.optimedsoftware.updater.client.app.UpdateMain,m_customJvmPath=,m_minJavaVersion=,m_maxJavaVersion=,m_trapConsoleCtrl=,m_jarsInClasspath=;C:\Program Files (x86)\Accuro - MB\Updater\AccuroCommunication.jar;C:\Program Files (x86)\Accuro - MB\Updater\antlr.jar;C:\Program Files (x86)\Accuro - MB\Updater\asm-attrs.jar;C:\Program Files (x86)\Accuro - MB\Updater\asm.jar;C:\Program Files (x86)\Accuro - MB\Updater\bcprov.jar;C:\Program Files (x86)\Accuro - MB\Updater\cglib.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-cli.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-codec.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-collections.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-httpclient.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-io.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-lang.jar;C:\Program Files (x86)\Accuro - MB\Updater\commons-logging.jar;C:\Program Files (x86)\Accuro - MB\Updater\dom4j.jar;C:\Program Files (x86)\Accuro - MB\Updater\ehcache.jar;C:\Program Files (x86)\Accuro - MB\Updater\ejb3-persistence.jar;C:\Program Files (x86)\Accuro - MB\Updater\fast-md5.jar;C:\Program Files (x86)\Accuro - MB\Updater\hibernate-annotations.jar;C:\Program Files (x86)\Accuro - MB\Updater\hibernate-commons-annotations.jar;C:\Program Files (x86)\Accuro - MB\Updater\hibernate.jar;C:\Program Files (x86)\Accuro - MB\Updater\HikariCP-java6.jar;C:\Program Files (x86)\Accuro - MB\Updater\javassist.jar;C:\Program Files (x86)\Accuro - MB\Updater\jta.jar;C:\Program Files (x86)\Accuro - MB\Updater\jtds.jar;C:\Program Files (x86)\Accuro - MB\Updater\kxml2.jar;C:\Program Files (x86)\Accuro - MB\Updater\log4j.jar;C:\Program Files (x86)\Accuro - MB\Updater\OSCEncryption.jar;C:\Program Files (x86)\Accuro - MB\Updater\proxool.jar;C:\Program Files (x86)\Accuro - MB\Updater\slf4j-api.jar;C:\Program Files (x86)\Accuro - MB\Updater\slf4j-log4j12.jar;C:\Program Files (x86)\Accuro - MB\Updater\swing-layout.jar;C:\Program Files (x86)\Accuro - MB\Updater\swing-worker.jar;C:\Program Files (x86)\Accuro - MB\Updater\UpdateLibrary.jar;C:\Program Files (x86)\Accuro - MB\Updater\UpdatesDataLibrary.jar,m_libraryPath=,m_debugFile=DebugFile.txt,m_callerDir=C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\Accuro Updating Script,m_javaSystemProperties[0]=-Xms64m,m_javaSystemProperties[1]=-Xmx256m,m_commandLineArguments[0]=-url,m_commandLineArguments[1]=http://services.optimedsoftware.com/Updates/updates/,m_commandLineArguments[2]=-downloadurl,m_commandLineArguments[3]=https://updateemr-files.optimedsoftware.com/Updates/updates/,m_commandLineArguments[4]=-product,m_commandLineArguments[5]=Accuro,m_commandLineArguments[6]=-checkupdater,m_commandLineArguments[7]=-checkaccuro,m_commandLineArguments[8]=-basedir,m_commandLineArguments[9]=C:\Program Files (x86)\Accuro - MB
The chosen JVM: m_jvmPath=C:\Program Files (x86)\Accuro - NS\jvm_run\jre\bin\client\jvm.dll,m_javaBundle=,m_binJvmDir=,m_comparableVersion=,m_javaHomePath=,m_version=,m_jrePath=,m_acceptable=true
The properties chosen: m_fullPathAndNameOfExe=C:\Program Files (x86)\Accuro - NS\UpdateAccuro.exe,m_selfHomePath=C:\Program Files (x86)\Accuro - NS,m_customJavaHomePath=C:\Program Files (x86)\Accuro - NS\jvm_run,m_customBinJvmDir=,m_mainClass=com.optimedsoftware.updater.client.app.UpdateMain,m_customJvmPath=,m_minJavaVersion=,m_maxJavaVersion=,m_trapConsoleCtrl=,m_jarsInClasspath=;C:\Program Files (x86)\Accuro - NS\Updater\AccuroCommunication.jar;C:\Program Files (x86)\Accuro - NS\Updater\antlr.jar;C:\Program Files (x86)\Accuro - NS\Updater\asm-attrs.jar;C:\Program Files (x86)\Accuro - NS\Updater\asm.jar;C:\Program Files (x86)\Accuro - NS\Updater\bcprov.jar;C:\Program Files (x86)\Accuro - NS\Updater\cglib.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-cli.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-codec.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-collections.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-httpclient.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-io.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-lang.jar;C:\Program Files (x86)\Accuro - NS\Updater\commons-logging.jar;C:\Program Files (x86)\Accuro - NS\Updater\dom4j.jar;C:\Program Files (x86)\Accuro - NS\Updater\ehcache.jar;C:\Program Files (x86)\Accuro - NS\Updater\ejb3-persistence.jar;C:\Program Files (x86)\Accuro - NS\Updater\fast-md5.jar;C:\Program Files (x86)\Accuro - NS\Updater\hibernate-annotations.jar;C:\Program Files (x86)\Accuro - NS\Updater\hibernate-commons-annotations.jar;C:\Program Files (x86)\Accuro - NS\Updater\hibernate.jar;C:\Program Files (x86)\Accuro - NS\Updater\HikariCP-java6.jar;C:\Program Files (x86)\Accuro - NS\Updater\javassist.jar;C:\Program Files (x86)\Accuro - NS\Updater\jta.jar;C:\Program Files (x86)\Accuro - NS\Updater\jtds.jar;C:\Program Files (x86)\Accuro - NS\Updater\kxml2.jar;C:\Program Files (x86)\Accuro - NS\Updater\log4j.jar;C:\Program Files (x86)\Accuro - NS\Updater\OSCEncryption.jar;C:\Program Files (x86)\Accuro - NS\Updater\proxool.jar;C:\Program Files (x86)\Accuro - NS\Updater\slf4j-api.jar;C:\Program Files (x86)\Accuro - NS\Updater\slf4j-log4j12.jar;C:\Program Files (x86)\Accuro - NS\Updater\swing-layout.jar;C:\Program Files (x86)\Accuro - NS\Updater\swing-worker.jar;C:\Program Files (x86)\Accuro - NS\Updater\UpdateLibrary.jar;C:\Program Files (x86)\Accuro - NS\Updater\UpdatesDataLibrary.jar,m_libraryPath=,m_debugFile=DebugFile.txt,m_callerDir=C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\Accuro Updating Script,m_javaSystemProperties[0]=-Xms64m,m_javaSystemProperties[1]=-Xmx256m,m_commandLineArguments[0]=-url,m_commandLineArguments[1]=http://services.optimedsoftware.com/Updates/updates/,m_commandLineArguments[2]=-downloadurl,m_commandLineArguments[3]=https://updateemr-files.optimedsoftware.com/Updates/updates/,m_commandLineArguments[4]=-product,m_commandLineArguments[5]=Accuro,m_commandLineArguments[6]=-checkupdater,m_commandLineArguments[7]=-checkaccuro,m_commandLineArguments[8]=-basedir,m_commandLineArguments[9]=C:\Program Files (x86)\Accuro - NS
The chosen JVM: m_jvmPath=C:\Program Files (x86)\Accuro - ON\jvm_run\jre\bin\client\jvm.dll,m_javaBundle=,m_binJvmDir=,m_comparableVersion=,m_javaHomePath=,m_version=,m_jrePath=,m_acceptable=true
The properties chosen: m_fullPathAndNameOfExe=C:\Program Files (x86)\Accuro - ON\UpdateAccuro.exe,m_selfHomePath=C:\Program Files (x86)\Accuro - ON,m_customJavaHomePath=C:\Program Files (x86)\Accuro - ON\jvm_run,m_customBinJvmDir=,m_mainClass=com.optimedsoftware.updater.client.app.UpdateMain,m_customJvmPath=,m_minJavaVersion=,m_maxJavaVersion=,m_trapConsoleCtrl=,m_jarsInClasspath=;C:\Program Files (x86)\Accuro - ON\Updater\AccuroCommunication.jar;C:\Program Files (x86)\Accuro - ON\Updater\antlr.jar;C:\Program Files (x86)\Accuro - ON\Updater\asm-attrs.jar;C:\Program Files (x86)\Accuro - ON\Updater\asm.jar;C:\Program Files (x86)\Accuro - ON\Updater\bcprov.jar;C:\Program Files (x86)\Accuro - ON\Updater\cglib.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-cli.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-codec.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-collections.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-httpclient.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-io.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-lang.jar;C:\Program Files (x86)\Accuro - ON\Updater\commons-logging.jar;C:\Program Files (x86)\Accuro - ON\Updater\dom4j.jar;C:\Program Files (x86)\Accuro - ON\Updater\ehcache.jar;C:\Program Files (x86)\Accuro - ON\Updater\ejb3-persistence.jar;C:\Program Files (x86)\Accuro - ON\Updater\fast-md5.jar;C:\Program Files (x86)\Accuro - ON\Updater\hibernate-annotations.jar;C:\Program Files (x86)\Accuro - ON\Updater\hibernate-commons-annotations.jar;C:\Program Files (x86)\Accuro - ON\Updater\hibernate.jar;C:\Program Files (x86)\Accuro - ON\Updater\HikariCP-java6.jar;C:\Program Files (x86)\Accuro - ON\Updater\javassist.jar;C:\Program Files (x86)\Accuro - ON\Updater\jta.jar;C:\Program Files (x86)\Accuro - ON\Updater\jtds.jar;C:\Program Files (x86)\Accuro - ON\Updater\kxml2.jar;C:\Program Files (x86)\Accuro - ON\Updater\log4j.jar;C:\Program Files (x86)\Accuro - ON\Updater\OSCEncryption.jar;C:\Program Files (x86)\Accuro - ON\Updater\proxool.jar;C:\Program Files (x86)\Accuro - ON\Updater\slf4j-api.jar;C:\Program Files (x86)\Accuro - ON\Updater\slf4j-log4j12.jar;C:\Program Files (x86)\Accuro - ON\Updater\swing-layout.jar;C:\Program Files (x86)\Accuro - ON\Updater\swing-worker.jar;C:\Program Files (x86)\Accuro - ON\Updater\UpdateLibrary.jar;C:\Program Files (x86)\Accuro - ON\Updater\UpdatesDataLibrary.jar,m_libraryPath=,m_debugFile=DebugFile.txt,m_callerDir=C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\Accuro Updating Script,m_javaSystemProperties[0]=-Xms64m,m_javaSystemProperties[1]=-Xmx256m,m_commandLineArguments[0]=-url,m_commandLineArguments[1]=http://services.optimedsoftware.com/Updates/updates/,m_commandLineArguments[2]=-downloadurl,m_commandLineArguments[3]=https://updateemr-files.optimedsoftware.com/Updates/updates/,m_commandLineArguments[4]=-product,m_commandLineArguments[5]=Accuro,m_commandLineArguments[6]=-checkupdater,m_commandLineArguments[7]=-checkaccuro,m_commandLineArguments[8]=-basedir,m_commandLineArguments[9]=C:\Program Files (x86)\Accuro - ON
The chosen JVM: m_jvmPath=C:\Program Files (x86)\Accuro - SK\jvm_run\jre\bin\client\jvm.dll,m_javaBundle=,m_binJvmDir=,m_comparableVersion=,m_javaHomePath=,m_version=,m_jrePath=,m_acceptable=true
The properties chosen: m_fullPathAndNameOfExe=C:\Program Files (x86)\Accuro - SK\UpdateAccuro.exe,m_selfHomePath=C:\Program Files (x86)\Accuro - SK,m_customJavaHomePath=C:\Program Files (x86)\Accuro - SK\jvm_run,m_customBinJvmDir=,m_mainClass=com.optimedsoftware.updater.client.app.UpdateMain,m_customJvmPath=,m_minJavaVersion=,m_maxJavaVersion=,m_trapConsoleCtrl=,m_jarsInClasspath=;C:\Program Files (x86)\Accuro - SK\Updater\AccuroCommunication.jar;C:\Program Files (x86)\Accuro - SK\Updater\antlr.jar;C:\Program Files (x86)\Accuro - SK\Updater\asm-attrs.jar;C:\Program Files (x86)\Accuro - SK\Updater\asm.jar;C:\Program Files (x86)\Accuro - SK\Updater\bcprov.jar;C:\Program Files (x86)\Accuro - SK\Updater\cglib.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-cli.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-codec.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-collections.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-httpclient.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-io.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-lang.jar;C:\Program Files (x86)\Accuro - SK\Updater\commons-logging.jar;C:\Program Files (x86)\Accuro - SK\Updater\dom4j.jar;C:\Program Files (x86)\Accuro - SK\Updater\ehcache.jar;C:\Program Files (x86)\Accuro - SK\Updater\ejb3-persistence.jar;C:\Program Files (x86)\Accuro - SK\Updater\fast-md5.jar;C:\Program Files (x86)\Accuro - SK\Updater\hibernate-annotations.jar;C:\Program Files (x86)\Accuro - SK\Updater\hibernate-commons-annotations.jar;C:\Program Files (x86)\Accuro - SK\Updater\hibernate.jar;C:\Program Files (x86)\Accuro - SK\Updater\HikariCP-java6.jar;C:\Program Files (x86)\Accuro - SK\Updater\javassist.jar;C:\Program Files (x86)\Accuro - SK\Updater\jta.jar;C:\Program Files (x86)\Accuro - SK\Updater\jtds.jar;C:\Program Files (x86)\Accuro - SK\Updater\kxml2.jar;C:\Program Files (x86)\Accuro - SK\Updater\log4j.jar;C:\Program Files (x86)\Accuro - SK\Updater\OSCEncryption.jar;C:\Program Files (x86)\Accuro - SK\Updater\proxool.jar;C:\Program Files (x86)\Accuro - SK\Updater\slf4j-api.jar;C:\Program Files (x86)\Accuro - SK\Updater\slf4j-log4j12.jar;C:\Program Files (x86)\Accuro - SK\Updater\swing-layout.jar;C:\Program Files (x86)\Accuro - SK\Updater\swing-worker.jar;C:\Program Files (x86)\Accuro - SK\Updater\UpdateLibrary.jar;C:\Program Files (x86)\Accuro - SK\Updater\UpdatesDataLibrary.jar,m_libraryPath=,m_debugFile=DebugFile.txt,m_callerDir=C:\Users\<USER>\OneDrive - QHR Technologies\Desktop\Scripts\Accuro Updating Script,m_javaSystemProperties[0]=-Xms64m,m_javaSystemProperties[1]=-Xmx256m,m_commandLineArguments[0]=-url,m_commandLineArguments[1]=http://services.optimedsoftware.com/Updates/updates/,m_commandLineArguments[2]=-downloadurl,m_commandLineArguments[3]=https://updateemr-files.optimedsoftware.com/Updates/updates/,m_commandLineArguments[4]=-product,m_commandLineArguments[5]=Accuro,m_commandLineArguments[6]=-checkupdater,m_commandLineArguments[7]=-checkaccuro,m_commandLineArguments[8]=-basedir,m_commandLineArguments[9]=C:\Program Files (x86)\Accuro - SK
