﻿$a = "<style>"
$a = $a + "BODY{background-color:peachpuff;}"
$a = $a + "TABLE{border-width: 1px;border-style: solid;border-color: black;border-collapse: collapse;}"
$a = $a + "TH{border-width: 1px;padding: 0px;border-style: solid;border-color: black;background-color:thistle}"
$a = $a + "TD{border-width: 1px;padding: 0px;border-style: solid;border-color: black;background-color:palegoldenrod}"
$a = $a + "</style>"


$disabledACCOUNTS = ""
$disabledAccounts = Get-ADUser -Filter {Enabled -eq $false -and PasswordNeverExpires -eq $False} -Properties * | Where-Object {$_.UserPrincipalName -match "@qhrtech.com" -and $_.UserPrincipalName -notmatch "da-" -and $_.manager -ne $null} | select -Property UserPrincipalName

$disabledACCOUNTS

$disabledAccounts | ConvertTo-HTML -head $a | Out-File C:\Scripts\Test.htm


Invoke-Expression C:\Scripts\Test.htm