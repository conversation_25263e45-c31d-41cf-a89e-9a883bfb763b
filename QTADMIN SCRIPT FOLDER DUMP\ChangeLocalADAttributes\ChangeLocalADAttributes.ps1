# AD User Attribute Update Script
# Allows updating Province, Department, Manager, and Title attributes for AD users.
# Provides detailed logging and clear console output.

while ($true) {
    # -----------------------------------------------
    # 1. Check Prerequisites: Active Directory Module
    # -----------------------------------------------
    if (-not (Get-Module -ListAvailable -Name ActiveDirectory)) {
        Write-Host "Active Directory module not found. Installing..." -ForegroundColor Yellow
        $choice = Read-Host "Install ActiveDirectory PowerShell module now? (Y/N)"
        if ($choice -match '^(Y|y)') {
            try {
                Install-Module -Name RSAT-AD-PowerShell -Force -ErrorAction Stop
                Import-Module ActiveDirectory
                Write-Host "Active Directory module installed successfully." -ForegroundColor Green
            } catch {
                Write-Host "Installation failed. Please install manually. Exiting." -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host "AD module required. Exiting." -ForegroundColor Red
            exit 1
        }
    } else {
        Import-Module ActiveDirectory -ErrorAction SilentlyContinue
    }

    # -----------------------------------
    # 2. Ensure Logging Directory Exists
    # -----------------------------------
    $logDir = "C:\Temp\Helpdesk\LocalADAttributes"
    $logFile = "$logDir\ChangeLocalADAttributes.txt"
    if (-not (Test-Path $logDir)) {
        New-Item -Path $logDir -ItemType Directory -Force | Out-Null
    }

    # --------------------------------------------
    # 3. Prompt for User Email & Lookup in AD
    # --------------------------------------------
    $email = (Read-Host "Enter user's email (<EMAIL>)").Trim()
    if (-not $email -or $email -notlike "*@qhrtech.com") {
        Write-Host "Invalid email format. Please try again." -ForegroundColor Red
        continue
    }

    # Search user by email or SamAccountName
    $user = Get-ADUser -Filter "Mail -eq '$email'" -Properties State, Department, Title, Manager
    if (-not $user) {
        $samID = $email.Split('@')[0]
        $user = Get-ADUser -Filter "SamAccountName -eq '$samID'" -Properties State, Department, Title, Manager
    }

    if (-not $user) {
        Write-Host "User not found. Please verify and retry." -ForegroundColor Red
        continue
    }

    # -----------------------------------
    # 4. Display Current User Attributes
    # -----------------------------------
    $originalAttrs = [PSCustomObject]@{
        Province   = $user.State
        Department = $user.Department
        Manager    = if ($user.Manager) { (Get-ADUser $user.Manager).Name } else { '<None>' }
        Title      = $user.Title
    }

    Write-Host "`nCurrent attributes for [$($user.SamAccountName)]:" -ForegroundColor Cyan
    $originalAttrs | Format-List

    # -----------------------------------------
    # 5. Prompt for Attributes to Update
    # -----------------------------------------
    $updates = @{}

    if ((Read-Host "Update Province? (Y/N)") -match '^(Y|y)') {
        $updates.State = (Read-Host "New Province (current: $($originalAttrs.Province))").Trim()
    }
    if ((Read-Host "Update Department? (Y/N)") -match '^(Y|y)') {
        $updates.Department = (Read-Host "New Department (current: $($originalAttrs.Department))").Trim()
    }
    if ((Read-Host "Update Manager? (Y/N)") -match '^(Y|y)') {
        $mgrInput = (Read-Host "New Manager (username or email)").Trim()
        $newManagerObj = if ($mgrInput -like '*@*') {
            Get-ADUser -Filter "Mail -eq '$mgrInput'"
        } else {
            Get-ADUser -Filter "SamAccountName -eq '$mgrInput'"
        }
        if ($newManagerObj) { $updates.Manager = $newManagerObj }
        else { Write-Host "Manager not found. Skipping manager update." -ForegroundColor Red }
    }
    if ((Read-Host "Update Title? (Y/N)") -match '^(Y|y)') {
        $updates.Title = (Read-Host "New Title (current: $($originalAttrs.Title))").Trim()
    }

    if ($updates.Count -eq 0) {
        Write-Host "No attributes selected for update." -ForegroundColor Yellow
        continue
    }

    # ------------------------------------------
    # 6. Apply Changes to AD User Account
    # ------------------------------------------
    Set-ADUser -Identity $user @updates

    # Fetch updated attributes for verification
    $updatedUser = Get-ADUser $user.SamAccountName -Properties State, Department, Title, Manager
    $updatedAttrs = [PSCustomObject]@{
        Province   = $updatedUser.State
        Department = $updatedUser.Department
        Manager    = if ($updatedUser.Manager) { (Get-ADUser $updatedUser.Manager).Name } else { '<None>' }
        Title      = $updatedUser.Title
    }

    # --------------------------------------
    # 7. Clearly Display Changes on Console
    # --------------------------------------
    Write-Host "`nAttribute changes applied for [$($user.SamAccountName)]:" -ForegroundColor Green
    "{0,-15} {1,-25} {2,-25}" -f 'Attribute', 'Previous Value', 'Updated Value' | Write-Host -ForegroundColor Yellow
    Write-Host ('-' * 70) -ForegroundColor Yellow

    foreach ($attr in $updates.Keys) {
        $displayName = switch ($attr) {
            'State'      { 'Province' }
            'Department' { 'Department' }
            'Manager'    { 'Manager' }
            'Title'      { 'Title' }
        }
        $oldVal = $originalAttrs.$displayName
        $newVal = $updatedAttrs.$displayName
        "${displayName}{}: '$oldVal' -> '$newVal'" | Write-Host -ForegroundColor Green
    }

    # ------------------------------------------
    # 8. Detailed Logging of Attribute Changes
    # ------------------------------------------
    $logDetails = foreach ($attr in $updates.Keys) {
        $displayName = switch ($attr) { 'State' { 'Province' } default { $attr } }
        "    $displayName : '$($originalAttrs.$displayName)' -> '$($updatedAttrs.$displayName)'"
    }

    $logEntry = @"
===========================================================
Date/Time   : $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Admin User  : $env:USERNAME
Target User : $($user.SamAccountName)

Changes Made:
$($logDetails -join "`n")
===========================================================

"@

    Add-Content -Path $logFile -Value $logEntry -Encoding UTF8

    # -------------------------
    # 9. Prompt to Loop Again
    # -------------------------
    if ((Read-Host "`nUpdate another user? (Y/N)") -notmatch '^(Y|y)') { break }
}
