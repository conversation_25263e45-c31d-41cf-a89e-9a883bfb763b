﻿Try {
  $connected = Get-mailbox <EMAIL>
  Write-Host "Already Connected to Exchange Online!`n`n" -ForegroundColor Green
}
Catch {
  $LiveCred = Get-Credential <EMAIL>

    Clear
    Write-Host "Connecting to Exchange Online..."

    $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
    Import-PSSession $Session -AllowClobber
    Connect-MsolService -Credential $LiveCred
}



$filename = "c:\scripts\Disabled-Licensed-Users.txt"

$liccheck = get-msoluser -all | ? {$_.islicensed -eq $true}| select DisplayName,UserPrincipalName,isLicensed,BlockCredential | sort DisplayName
Write-Host "Bulding list @ $filename..."
Write "`n`nENTERPRISEPACK=E3 License`nMCOMEETADV=Audio Conferencing`nSMB_APPS= Business Apps (Free)`nVISIOCLIENT = Visio Online Plan 2`n`nAccounts that are Licensed:`n" |Out-File $filename



foreach ($user in $liccheck) {
     $lictype = get-msoluser -UserPrincipalName $user.userprincipalname | ? BlockCredential -match "true" | select -ExpandProperty Licenses
     #$lastlogon = get-mailbox $user.UserPrincipalName |Get-MailboxStatistics | select lastlogontime

    #$user.AccountSku.SkuPartNumber

    if ($lictype) {
        Write "$($user.displayname) - License: $($lictype.AccountSku.SkuPartNumber)" | Out-File $filename -Append
    }

    $lictype = $null

}


Exit-PSSession
Write-Host "List complete!"
Pause