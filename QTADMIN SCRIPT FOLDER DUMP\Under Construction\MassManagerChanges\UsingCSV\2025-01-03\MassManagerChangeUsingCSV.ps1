$result = Import-Csv "C:\Scripts\Under Construction\MassManagerChanges\UsingCSV\2025-01-03\UpdateManagersCSV.csv" | ForEach-Object {
    $csvUser    = $_.EmployeeUserName  # for convenience
    $csvManager = $_.ManagerUserName
   
 # create an object to output
    $out = [PsCustomObject]@{User = $csvUser; NewManager = $null; Result = $null }

    
# if there is no manager found in the csv:
    if ([string]::IsNullOrWhiteSpace($csvManager)) {
        Write-Warning "User '$csvUser' does not have a manager specified in the csv"
        $out.Result = "Error: User does not have a manager specified in the csv"
        $out
        continue
    }

    $ADUser = Get-ADUser -Filter "sAMAccountName -eq '$csvUser'" -Properties Manager
    if ($ADUser) {
        # try and get the user object for the manager as stated in the csv
        $ADManager = Get-ADUser -Filter "sAMAccountName -eq '$csvManager'"
        if ($ADManager) {
            $out.NewManager = $csvManager
            try {
                $currentManager = (Get-ADUser -Identity $ADUser.Manager -ErrorAction Stop).SamAccountName
            }
            catch { $currentManager = $null }
            if ($currentManager -ne $csvManager) {
                $ADUser | Set-ADUser -Manager $ADManager.DistinguishedName
                $out.Result = "Success: New manager '$csvManager' set for this user"
            }
            else {
                $out.Result = "Skipped: Manager for this user was already correct"
            }
        }
        else {
            Write-Warning "Manager '$csvManager' does not exist"
            $out.Result = "Error: Manager '$csvManager' does not exist"
        }
    }
    else {
        Write-Warning "User '$csvUser' does not exist"
        $out.Result = "Error: User '$csvUser' does not exist"
    }
    # output the object so it gets collected in variable $result
    $out
}

# output result on screen
$result | Format-Table -AutoSize

# write result to csv file
$result | Export-Csv -Path 'C:\Scripts\Under Construction\MassManagerChanges\UsingCSV\2025-01-03\ChangeReport\UpdateManagersResults.csv' -NoTypeInformation
