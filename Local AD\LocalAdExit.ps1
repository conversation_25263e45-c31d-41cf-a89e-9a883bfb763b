# Updated by <PERSON><PERSON>

# Synopsis
# This script is used to exit a user from a corporate environment or perform an audit of a user's account.
# It has two modes: 'Exit' and 'Audit'. In Exit mode, it performs actions such as disabling the user account, 
# revoking Exchange Online sessions, blocking sign-ins to Office apps, and removing the user from group memberships.
# In Audit mode, it audits the current state of the user's account without making changes and lists what would be modified.
# The script requires modules for Active Directory, Microsoft Graph PowerShell, and Exchange Online.

# Install necessary modules if missing and import them
function Install-Dependencies {
    if (-not (Get-Module -ListAvailable Microsoft.Graph)) {
        Install-Module Microsoft.Graph -Force
    }
    $GraphModules = Get-Module -ListAvailable Microsoft.Graph.* | Where-Object { $_.Name -ne "Microsoft.Graph" }
    foreach ($Module in $GraphModules) {
        if (-not (Get-Module -ListAvailable $Module.Name)) {
            Write-Host "Installing Module: $($Module.Name) $($Module.Version)" -ForegroundColor Yellow
            Install-Module $Module.Name -RequiredVersion $Module.Version -Force
        }
    }
}

Install-Dependencies
Import-Module Microsoft.Graph -ErrorAction Stop

# Set maximum function and variable counts for the session to avoid exceeding limits.
$maximumFunctionCount = 32768
$maximumVariableCount = 32768
$ExecutionContext.SessionState.PSVariable.Set("MaximumFunctionCount", $maximumFunctionCount)
$ExecutionContext.SessionState.PSVariable.Set("MaximumVariableCount", $maximumVariableCount)

# Function to import required modules with error handling and verbose suppression
function Import-ModuleIfMissing {
    param (
        [string]$moduleName
    )
    try {
        if (-not (Get-Module -ListAvailable -Name $moduleName)) {
            Write-Host "Module '$moduleName' not found. Attempting to install..." -ForegroundColor Yellow
            if ($moduleName -eq "ActiveDirectory") {
                if (Get-WindowsCapability -Online | Where-Object { $_.Name -like "RSAT.ActiveDirectory*" -and $_.State -eq "NotPresent" }) {
                    Add-WindowsCapability -Online -Name "Rsat.ActiveDirectory.DS-LDS.Tools~~~~*******" -ErrorAction Stop
                }
            } else {
                Install-Module -Name $moduleName -Force -ErrorAction Stop
            }
        }
        Import-Module $moduleName -ErrorAction Stop -Verbose:$false
        Write-Host "Module '$moduleName' imported successfully." -ForegroundColor Green
    } catch {
        Write-Host "Error importing or installing module '$moduleName': $_" -ForegroundColor Red
        exit
    }
}

# Import necessary modules
Import-ModuleIfMissing -moduleName "ActiveDirectory"
Import-ModuleIfMissing -moduleName "ExchangeOnlineManagement"

# Function to Connect to Microsoft Graph
function Connect-ToGraph {
    try {
        Connect-MgGraph -Scopes "User.ReadWrite.All, Group.ReadWrite.All, Directory.ReadWrite.All, AuditLog.Read.All, GroupMember.ReadWrite.All" -NoWelcome
        Write-Host "Successfully connected to Microsoft Graph." -ForegroundColor Green
    } catch {
        Write-Host "Error connecting to Microsoft Graph: $_" -ForegroundColor Red
        exit
    }
}

# Ensure Graph connection
Connect-ToGraph

# Function to Confirm Action
function Confirm-Action {
    param (
        [string]$message
    )
    $confirmation = Read-Host "$message (y/n)"
    return ($confirmation -eq 'y')
}

# Function to Record and Display Changes
function Record-Change {
    param (
        [string]$propertyName,
        [string]$oldValue,
        [string]$newValue
    )
    Write-Host "[Change] ${propertyName}: $oldValue -> $newValue" -ForegroundColor Yellow
    [PSCustomObject]@{
        PropertyName = $propertyName
        OldValue = $oldValue
        NewValue = $newValue
    }
}

# Function to retrieve Azure AD user using UPN
function Get-AzureADUser {
    param (
        [string]$upn
    )
    try {
        # Retrieve the Azure AD user and explicitly select the required properties
        $user = Get-MgUser -Filter "userPrincipalName eq '$upn'" -Property "id,displayName,userPrincipalName,accountEnabled"
        if ($user) {
            return $user
        } else {
            Write-Host "User with UPN '$upn' not found." -ForegroundColor Red
            exit
        }
    } catch {
        Write-Host "Error retrieving Azure AD user: $_" -ForegroundColor Red
        exit
    }
}

# Initialize changes array
$changes = @()

# Prompt for the Mode
$mode = Read-Host "Enter the mode (Audit or Exit)"

# Validate the Mode
if ($mode -ne "Audit" -and $mode -ne "Exit") {
    Write-Host "Invalid mode. Please enter either 'Audit' or 'Exit'." -ForegroundColor Red
    exit
}

# Prompt for the User Logon Name
$userLogonName = Read-Host "Enter the user logon name (e.g., jdoe)"

# Get User Object from AD
try {
    $user = Get-ADUser -Identity $userLogonName -Properties * -ErrorAction Stop
    $userUPN = $user.UserPrincipalName
} catch {
    Write-Host "User '$userLogonName' not found in Active Directory." -ForegroundColor Red
    exit
}

# Get the corresponding Azure AD user using UPN
$azureUser = Get-AzureADUser -upn $userUPN
if ($null -eq $azureUser -or [string]::IsNullOrEmpty($azureUser.Id)) {
    Write-Host "Error: Azure AD user not found or not returned correctly." -ForegroundColor Red
    exit
} else {
    # Store the Azure AD user ID in a variable for use in subsequent sections
    $azureUserId = $azureUser.Id

    # Print only the ID and the UPN (username) of the Azure AD user to the console
    Write-Host "Azure AD User ID: $azureUserId" -ForegroundColor Green
    Write-Host "Azure AD User UPN: $($azureUser.UserPrincipalName)" -ForegroundColor Green
}

# Section 1: Block Sign-In to Office Apps
if (Confirm-Action "Do you want to block sign-in to Office apps for '$userLogonName'?") {
    try {
        $azureUser = Get-MgUser -UserId $azureUser.Id -Property "accountEnabled"
        $oldValue = if ($null -ne $azureUser.AccountEnabled) { $azureUser.AccountEnabled.ToString() } else { "Unknown" }
        
        if ($mode -eq "Audit") {
            Write-Host "[Audit] Blocking sign-in for Office apps would change 'AccountEnabled' from '$oldValue' to 'False'." -ForegroundColor Yellow
            $changes += Record-Change -propertyName "AccountEnabled (Office Apps)" -oldValue $oldValue -newValue $false
        } else {
            Update-MgUser -UserId $azureUser.Id -AccountEnabled:$false
            $newValue = (Get-MgUser -UserId $azureUser.Id -Property "accountEnabled").AccountEnabled
            Write-Host "Blocked sign-in for '$userLogonName' in Office apps." -ForegroundColor Green
            $changes += Record-Change -propertyName "AccountEnabled (Office Apps)" -oldValue $oldValue -newValue $newValue
        }
    } catch {
        Write-Host "Error blocking sign-in: $_" -ForegroundColor Red
    }
}


# Section 2: Revoke all Office 365 sessions
if (Confirm-Action "Do you want to revoke all Office 365 sessions for '$userLogonName'?") {
    try {
        if ($mode -eq "Audit") {
            Write-Host "[Audit] Revoked all Office 365 sessions." -ForegroundColor Yellow
            $changes += Record-Change -propertyName "Office 365 Sessions" -oldValue "Active Sessions" -newValue "Revoked"
        } else {
        $uri = "https://graph.microsoft.com/v1.0/users/$($azureUser.Id)/invalidateAllRefreshTokens"
        $headers = @{ "Content-Type" = "application/json" }
        $body = "{}" # An empty JSON payload

        # Execute the request
        Invoke-MgGraphRequest -Method POST -Uri $uri -Headers $headers -Body $body
        $changes += Record-Change -propertyName "Office 365 Sessions" -oldValue "Active Sessions" -newValue "Revoked"
        }
    } catch {
        Write-Host "Error retrieving or revoking Office 365 sessions: $_" -ForegroundColor Red
    }
}


# Section 3: Revoke Exchange Sessions
if (Confirm-Action "Do you want to revoke Exchange Online sessions for '$userLogonName'?") {
    $oldValue = "Permissions Assigned"
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Revoking Exchange Online sessions would change permissions from '$oldValue' to 'Permissions Revoked'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Exchange Online Sessions" -oldValue $oldValue -newValue "Permissions Revoked"
    } else {
        try {
            $adminUPN = Read-Host "Enter your Exchange Online admin UPN"
            Connect-ExchangeOnline -UserPrincipalName $adminUPN -Verbose
            Revoke-MgUserSession -UserId $user.ObjectGuid
            Write-Host "Revoked Exchange Online sessions for '$userLogonName'." -ForegroundColor Green
            $changes += Record-Change -propertyName "Exchange Online Sessions" -oldValue $oldValue -newValue "Permissions Revoked"
        } catch {
            Write-Host "Error revoking Exchange Online sessions: $_" -ForegroundColor Red
        } finally {
            Disconnect-ExchangeOnline -Confirm:$false -Verbose
        }
    }
}

# Section 4: Disable Corp AD Account
if (Confirm-Action "Do you want to disable the AD account for '$userLogonName'? (Including setting expiry, resetting password, removing attributes, etc.)") {
    $oldValue = $user.Enabled
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Disabling AD account would change 'Account Enabled' from '$oldValue' to 'False'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Account Status" -oldValue $oldValue -newValue "False"
    } else {
        $newPassword = Read-Host -AsSecureString "Enter the new password for the user"
        try {
            Disable-ADAccount -Identity $userLogonName -Verbose
            Set-ADUser -Identity $userLogonName -PasswordNeverExpires $false -AccountExpirationDate (Get-Date).AddDays(-1)
            Set-ADUser -Identity $userLogonName -Clear "company,department,directReports"
            Set-ADAccountPassword -Identity $userLogonName -NewPassword $newPassword -Reset
            $newValue = (Get-ADUser -Identity $userLogonName).Enabled
            Write-Host "AD account for '$userLogonName' has been disabled, attributes cleared, and password reset." -ForegroundColor Green
            $changes += Record-Change -propertyName "Account Status" -oldValue $oldValue -newValue $newValue
        } catch {
            Write-Host "Error disabling AD account: $_" -ForegroundColor Red
        }
    }
}

# Section 5: Disable Slack Account
if (Confirm-Action "Does this user need their Slack account disabled?") {
    $oldValue = "Enabled"
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Disabling Slack account would change status from '$oldValue' to 'Disabled'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Slack Account" -oldValue $oldValue -newValue "Disabled"
    } else {
        Write-Host "Please proceed to disable the Slack account manually." -ForegroundColor Yellow
        # Example of using Slack API to disable a user account:
        # $slackToken = 'xoxp-your-slack-token'
        # $userId = 'user-id-to-disable'
        # Invoke-RestMethod -Uri "https://slack.com/api/users.admin.setInactive" -Method Post -Headers @{ Authorization = "Bearer $slackToken" } -Body @{ user = $userId } | ConvertFrom-Json
    }
}

# Section 6: Terminate Zscaler Sessions
if (Confirm-Action "Does this user need their Zscaler sessions terminated?") {
    $oldValue = "Active"
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Terminating Zscaler sessions would change status from '$oldValue' to 'Terminated'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Zscaler Sessions" -oldValue $oldValue -newValue "Terminated"
    } else {
        Write-Host "Please run the Terminate ZCC User script to disable any active Zscaler sessions." -ForegroundColor Yellow
    }
}

# Section 7: Disable All Accounts in Client Facing and Test Environments
if (Confirm-Action "Does this user have accounts in client-facing or test environments (ASPv4, ACDC-P, ACDC-D) that need to be disabled?") {
    $oldValue = "Active"
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Disabling client-facing and test environment accounts would change status from '$oldValue' to 'Disabled'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Client Facing and Test Environments" -oldValue $oldValue -newValue "Disabled"
    } else {
        Write-Host "Please disable all relevant accounts in ASPv4, ACDC-P, and ACDC-D environments manually." -ForegroundColor Yellow
    }
}

# Section 8: Move User to Disabled OU
if (Confirm-Action "Do you want to move '$userLogonName' to the 'Disabled Users' OU?") {
    $oldValue = $user.DistinguishedName
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Moving user to 'Disabled Users' OU would change 'Organizational Unit' from '$oldValue' to 'OU=Disabled Users,OU=Graveyard,DC=QuadrantHR,DC=com'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Organizational Unit" -oldValue $oldValue -newValue "OU=Disabled Users,OU=Graveyard,DC=QuadrantHR,DC=com"
    } else {
        $targetOU = "OU=Disabled Users,OU=Graveyard,DC=QuadrantHR,DC=com"
        try {
            Move-ADObject -Identity $user.DistinguishedName -TargetPath $targetOU -Verbose
            $newValue = (Get-ADUser -Identity $userLogonName).DistinguishedName
            Write-Host "User '$userLogonName' has been moved to the 'Disabled Users' OU." -ForegroundColor Green
            $changes += Record-Change -propertyName "Organizational Unit" -oldValue $oldValue -newValue $newValue
        } catch {
            Write-Host "Error moving user to Disabled Users OU: $_" -ForegroundColor Red
        }
    }
}

# Section 9: Update Local AD Group Membership
if (Confirm-Action "Do you want to update group memberships for '$userLogonName'? (Including setting primary group to 'No Access')") {
    try {
        $userGroups = Get-ADUser -Identity $userLogonName -Properties MemberOf | Select-Object -ExpandProperty MemberOf

        if ($userGroups -and $userGroups.Count -gt 0) {
            Write-Host "The user is currently a member of the following groups:" -ForegroundColor Cyan
            foreach ($group in $userGroups) {
                try {
                    # Explicitly get the group details to validate its existence and name
                    $groupName = (Get-ADGroup -Identity $group).Name
                    Write-Host "- $groupName" -ForegroundColor Yellow
                } catch {
                    Write-Host "Error retrieving details for group '$group': $_" -ForegroundColor Red
                }
            }
            $oldValue = "List of AD Groups"
        } else {
            Write-Host "No groups assigned to the user." -ForegroundColor Yellow
            $oldValue = "No Groups Assigned"
        }

        if ($mode -eq "Audit") {
            Write-Host "[Audit] Updating group memberships would change 'Group Membership' from the list above to 'Primary Group: No Access, All other groups removed'." -ForegroundColor Yellow
            $changes += Record-Change -propertyName "Group Membership" -oldValue "List of AD Groups" -newValue "Primary Group: No Access, All other groups removed"
        } else {
            $noAccessGroup = "no access"
            try {
                Set-ADUser -Identity $userLogonName -PrimaryGroupId (Get-ADGroup -Identity $noAccessGroup).PrimaryGroupToken
                foreach ($group in $userGroups) {
                    if ($group -ne (Get-ADGroup -Identity $noAccessGroup).DistinguishedName) {
                        Remove-ADGroupMember -Identity $group -Members $userLogonName -Confirm:$false -Verbose
                        Write-Host "Removed '$userLogonName' from group '$((Get-ADGroup $group).Name)'." -ForegroundColor Yellow
                    }
                }
                $newValue = "Primary Group: No Access, All other groups removed"
                Write-Host "Updated group memberships for '$userLogonName', setting primary group to 'No Access'." -ForegroundColor Green
                $changes += Record-Change -propertyName "Group Membership" -oldValue "List of AD Groups" -newValue $newValue
            } catch {
                Write-Host "Error updating group memberships: $_" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "Error retrieving group memberships: $_" -ForegroundColor Red
    }
}

# Section 10: Manage Mailbox Access
if (Confirm-Action "Does someone need Full Control of '$userLogonName' mailbox?") {
    $oldValue = "No Full Control"
    if ($mode -eq "Audit") {
        Write-Host "[Audit] Granting Full Control of mailbox would change access from '$oldValue' to 'Full Control Granted to specified user'." -ForegroundColor Yellow
        $changes += Record-Change -propertyName "Mailbox Access" -oldValue $oldValue -newValue "Full Control Granted to specified user"
    } else {
        $newMailboxUser = Read-Host "Enter the username to be given Full Control of '$userLogonName' mailbox"
        try {
            Add-MailboxPermission -Identity $userLogonName -User $newMailboxUser -AccessRights FullAccess -InheritanceType All -Confirm:$false -Verbose
            $newValue = "Full Control Granted to $newMailboxUser"
            Write-Host "Granted Full Control of '$userLogonName' mailbox to '$newMailboxUser'." -ForegroundColor Green
            $changes += Record-Change -propertyName "Mailbox Access" -oldValue $oldValue -newValue $newValue
            Write-Host "Please ensure that appropriate access to OneDrive is also provided if needed." -ForegroundColor Yellow
        } catch {
            Write-Host "Error granting Full Control of mailbox: $_" -ForegroundColor Red
        }
    }
}

# Section 11: Remove User from Azure AD Groups
if (Confirm-Action "Do you want to remove '$userLogonName' from all Azure AD groups?") {
    try {
        # Ensure the azureUserId variable is valid before proceeding
        if ($null -eq $azureUserId -or [string]::IsNullOrEmpty($azureUserId)) {
            throw "Azure user ID is not available. Unable to proceed with removing user from groups."
        }

        # Debug Output: Ensure we see the Azure User ID
        Write-Host "Azure AD User ID being used: $azureUserId" -ForegroundColor Cyan

        # Retrieve all groups that the user is directly a member of, with pagination handling
        $allGroups = @()
        $response = Get-MgUserMemberOf -UserId $azureUserId -All -ConsistencyLevel eventual -CountVariable groupsCount

        # Iterate through paginated results
        while ($response) {
            $allGroups += $response
            $response = $response.NextLink ? (Invoke-MgGraphRequest -Uri $response.NextLink -Method Get) : $null
        }

        # Filter groups from the retrieved results
        $azureGroups = $allGroups | Where-Object { $_.'@odata.type' -eq '#microsoft.graph.group' }

        # Check if any groups were retrieved
        if ($null -eq $azureGroups -or $azureGroups.Count -eq 0) {
            Write-Host "No groups found for user '$userLogonName'. Exiting section." -ForegroundColor Yellow
            $oldValue = "No Groups Assigned"
        } else {
            # Display each group in a list format for clarity
            Write-Host "The user is a member of the following groups:" -ForegroundColor Cyan
            foreach ($group in $azureGroups) {
                try {
                    # Fetch the group details to get types and other attributes
                    $groupDetails = Get-MgGroup -GroupId $group.Id -Property displayName,groupTypes,mailEnabled,securityEnabled
                    $groupType = if ($groupDetails.groupTypes -contains "DynamicMembership") { "Dynamic" } elseif ($groupDetails.securityEnabled) { "Security" } elseif ($groupDetails.mailEnabled) { "Distribution" } else { "Other" }

                    Write-Host "- $($groupDetails.DisplayName) (Type: $groupType)" -ForegroundColor Yellow
                } catch {
                    Write-Host "Error retrieving details for group with ID: $($group.Id). Error: $_" -ForegroundColor Red
                }
            }

            $oldValue = ($azureGroups | ForEach-Object { $_.DisplayName }) -join ", "
        }

        if ($mode -eq "Audit") {
            Write-Host "[Audit] Removing from Azure AD groups would change 'Azure AD Groups' from the list of groups to 'Removed from all Azure AD groups'." -ForegroundColor Yellow
            $changes += Record-Change -propertyName "Azure AD Groups" -oldValue $oldValue -newValue "Removed from all Azure AD groups"
        } else {
            # Loop through each group and remove the user if it's not dynamic
            foreach ($group in $azureGroups) {
                try {
                    # Fetch group details again to confirm the group type
                    $groupDetails = Get-MgGroup -GroupId $group.Id -Property displayName,groupTypes
                    $groupType = if ($groupDetails.groupTypes -contains "DynamicMembership") { "Dynamic" } else { "Assigned" }

                    Write-Host "Attempting to remove user from group '$($groupDetails.DisplayName)' (Type: $groupType)" -ForegroundColor Cyan

                    if ($groupType -eq "Assigned") {
                        Remove-MgGroupMember -GroupId $group.Id -MemberId $azureUserId -Verbose:$true
                        Write-Host "Removed '$userLogonName' from Azure AD group '$($groupDetails.DisplayName)'." -ForegroundColor Yellow
                    } else {
                        Write-Host "Skipping removal from dynamic group '$($groupDetails.DisplayName)' as membership is automatically managed." -ForegroundColor Cyan
                    }
                } catch {
                    Write-Host "Error removing user from Azure AD group '$($group.DisplayName)': $_" -ForegroundColor Red
                }
            }
            $newValue = "Removed from all Azure AD groups"
            $changes += Record-Change -propertyName "Azure AD Groups" -oldValue $oldValue -newValue $newValue
        }
    } catch {
        Write-Host "Error retrieving or removing user from Azure AD groups: $_" -ForegroundColor Red
    }
}

# Section 12: Set msExchHideFromAddressLists Attribute
if (Confirm-Action "Do you want to set the msExchHideFromAddressLists attribute to 'True' for '$userLogonName'?") {
    try {
        # Get the user object from Active Directory
        $user = Get-ADUser -Identity $userLogonName -Properties msExchHideFromAddressLists

        # Check if the attribute is present and display its current value
        if ($null -ne $user) {
            $hideFromAddressLists = $user.msExchHideFromAddressLists
            if ($null -eq $hideFromAddressLists) {
                Write-Host "The msExchHideFromAddressLists attribute is not set." -ForegroundColor Yellow
            } else {
                Write-Host "Current value of msExchHideFromAddressLists: $hideFromAddressLists" -ForegroundColor Yellow
            }

            # In Audit mode, log the intended change
            if ($mode -eq "Audit") {
                Write-Host "[Audit] Setting msExchHideFromAddressLists attribute to 'True'." -ForegroundColor Yellow
                $changes += Record-Change -propertyName "msExchHideFromAddressLists" -oldValue $hideFromAddressLists -newValue $true
            } else {
                # Set the msExchHideFromAddressLists attribute to True
                Set-ADUser -Identity $userLogonName -Replace @{msExchHideFromAddressLists=$true}
                Write-Host "The msExchHideFromAddressLists attribute has been set to 'True'." -ForegroundColor Green
                $changes += Record-Change -propertyName "msExchHideFromAddressLists" -oldValue $hideFromAddressLists -newValue $true
            }
        } else {
            Write-Host "User not found in Active Directory." -ForegroundColor Red
        }
    } catch {
        Write-Host "Error setting msExchHideFromAddressLists attribute: $_" -ForegroundColor Red
    }
}


# Output final list of changes
Write-Host "`nFinal List of Changes:" -ForegroundColor Cyan
$changes | Format-Table -AutoSize
Write-Host "`nUser $mode process for '$userLogonName' has been completed." -ForegroundColor Cyan
Read-Host -Prompt "Press Enter to exit"