﻿## Connect to Office 365 (http://technet.microsoft.com/library/jj151815.aspx#BKMK_connect):
#$LiveCred = Get-Credential <EMAIL>
#$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
#Import-PSSession $Session -AllowClobber
#coConnect-MsolService -Credential $LiveCred
$filename = "c:\scripts\E3-Licensed-Users.txt"


Try {
  $connected = Get-mailbox <EMAIL>
  Write-Host "Already Connected to Exchange Online!`n`n" -ForegroundColor Green
}
Catch {
  $LiveCred = Get-Credential <EMAIL>

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
Connect-MsolService -Credential $LiveCred
 }

$liccheck = get-msoluser -all | ? {$_.islicensed -eq $true}| select DisplayName,UserPrincipalName,isLicensed,BlockCredential | sort DisplayName
Write-Host "Bulding list @ $filename..."
Write "`n`nAccounts that are Licensed (E3):`n" |Out-File $filename
foreach ($user in $liccheck) {
 $lictype = get-msoluser -UserPrincipalName $user.userprincipalname | select -ExpandProperty Licenses | ? AccountSkuId -match "ENTERPRISEPACK" 
 $lastlogon = get-mailbox $user.UserPrincipalName |Get-MailboxStatistics | select lastlogontime
if ($lictype) {
  Write "$($user.displayname) ($($user.userprincipalname)) - Last Logon: $($lastlogon.LastLogonTime)" |Out-File $filename -Append
  }

$lictype = $null

}



Exit-PSSession
Write-Host "List complete!"
Pause