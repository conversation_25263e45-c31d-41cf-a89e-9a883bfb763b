<#
.SYNOPSIS
Interactively guides an administrator through updating an Active Directory user's display name components
and synchronizing associated email attributes (mail, proxyAddresses, targetAddress, Entra ID UPN).
Includes prerequisite checks, detailed logging, and manual step reminders.

.DESCRIPTION
This script performs the following actions:
1. Checks for and attempts to install required PowerShell modules (ActiveDirectory, Microsoft.Graph.Users).
2. Sets up file-based logging in C:\temp\helpdesk\NameChange\.
3. Prompts interactively for the user identity (e.g., sAMAccountName) and primary email domain.
4. Attempts to connect to Microsoft Graph API (requires User.ReadWrite.All scope).
5. Retrieves the specified AD user and displays current details.
6. Interactively prompts for the type of name change (First, Last, or Both) and the new name(s).
7. Calculates new values for AD attributes (GivenName, SN, DisplayName, UserPrincipalName, mail, proxyAddresses, targetAddress).
8. Displays all planned changes and requires explicit administrator confirmation [Y/N] before proceeding.
9. Updates the AD user object attributes (excluding sAMAccountName and CN). The AD UPN change triggers synchronization to Entra ID.
10. Queries Microsoft Entra ID via Graph *after* the AD update to verify the current UPN status.
11. Provides comprehensive instructions for required manual follow-up steps (CN change, checking systems, testing).

This script DOES NOT automatically change the sAMAccountName or the AD Object's CN (Common Name).
This script DOES NOT directly update the UPN in Entra ID; it relies on Entra Cloud Sync after the AD UPN is updated.

.NOTES
- Version: 3.1
- Author: Slader Sheppard
- Date: 2025-04-30
- Run PowerShell as Administrator.
- Ensure PowerShell Execution Policy allows script execution.
- Assumes Microsoft Entra Cloud Sync is used.
- Follow pre-run manual steps (sign out user, revoke sessions).
#>

#region --- Script Configuration and Logging Setup ---

$ScriptName = $MyInvocation.MyCommand.Name
$LogDirectory = "C:\temp\helpdesk\NameChange"
$LogFile = $null # Will be set after getting user identity

# Function for logging to console and file
function Write-Log {
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('INFO', 'WARN', 'ERROR', 'ACTION')]
        [string]$Level = 'INFO',

        [Parameter(Mandatory = $false)]
        [switch]$NoConsole
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $formattedMessage = "[$timestamp][$Level] $Message"

    # Console Output
    if (-not $NoConsole) {
        $color = switch ($Level) {
            'INFO'   { 'White' }
            'WARN'   { 'Yellow' }
            'ERROR'  { 'Red' }
            'ACTION' { 'Cyan' }
            default  { 'White' }
        }
        Write-Host $formattedMessage -ForegroundColor $color
    }

    # File Output (only if LogFile is set)
    if ($LogFile) {
        try {
            Out-File -FilePath $LogFile -InputObject $formattedMessage -Append -Encoding UTF8 -ErrorAction Stop
        }
        catch {
            Write-Warning "[$timestamp][WARN] Failed to write to log file '$LogFile'. Error: $($_.Exception.Message)"
        }
    }
}

# Create Log Directory if it doesn't exist
if (-not (Test-Path -Path $LogDirectory -PathType Container)) {
    Write-Host "[$([DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))][INFO] Log directory '$LogDirectory' not found. Attempting to create..." -ForegroundColor White
    try {
        New-Item -Path $LogDirectory -ItemType Directory -Force -ErrorAction Stop | Out-Null
        Write-Host "[$([DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))][INFO] Successfully created log directory." -ForegroundColor Green
    }
    catch {
        # Cannot use Write-Log yet as $LogFile isn't defined
        Write-Error "[$([DateTime]::Now.ToString('yyyy-MM-dd HH:mm:ss'))][ERROR] Failed to create log directory '$LogDirectory'. Please create it manually and ensure permissions. Error: $($_.Exception.Message)"
        exit 1
    }
}

Write-Log -Message "--- Starting Script Execution: $ScriptName ---" -Level 'INFO'

#endregion

#region --- Prerequisite Check and Module Installation ---

Write-Log -Message "Checking prerequisites..." -Level 'INFO'

function Ensure-Module {
    param(
        [Parameter(Mandatory = $true)]
        [string]$ModuleName,
        [switch]$SystemModule # Indicates if admin rights might be needed
    )

    Write-Log -Message "Checking for module: $ModuleName..." -Level 'INFO'
    $module = Get-Module -Name $ModuleName -ListAvailable
    if (-not $module) {
        Write-Log -Message "Module '$ModuleName' not found. Attempting installation (Scope: CurrentUser)..." -Level 'WARN'
        Write-Log -Message "Please wait, this may take a moment. Internet connection required." -Level 'INFO'
        if ($SystemModule) {
             Write-Log -Message "Note: Installation of system modules like 'ActiveDirectory' might require RSAT installation via Windows Features or Settings (Admin rights needed)." -Level 'WARN'
             # We can't install RSAT features directly here easily, just warn.
        }
        try {
            # Attempt install for CurrentUser, suppress confirmation prompts
            Install-Module -Name $ModuleName -Scope CurrentUser -AllowClobber -Force -Confirm:$false -ErrorAction Stop
            Write-Log -Message "Successfully installed module '$ModuleName' for the current user." -Level 'INFO'
            # Verify it's now available
             $module = Get-Module -Name $ModuleName -ListAvailable
             if (-not $module) {
                 throw "Module '$ModuleName' installed but still not found by Get-Module. Please check manually or restart PowerShell."
             }
        }
        catch {
            Write-Log -Message "Failed to automatically install module '$ModuleName'. Error: $($_.Exception.Message)" -Level 'ERROR'
            Write-Log -Message "Please install '$ModuleName' manually and restart the script. Check PowerShell Execution Policy and internet connection. Admin rights might be needed." -Level 'ERROR'
            exit 1 # Exit if essential modules cannot be installed
        }
    }
    else {
        Write-Log -Message "Module '$ModuleName' found." -Level 'INFO'
    }
    # Import the module
    try {
        Import-Module $ModuleName -ErrorAction Stop
        Write-Log -Message "Successfully imported module '$ModuleName'." -Level 'INFO'
    } catch {
         Write-Log -Message "Failed to import module '$ModuleName' even though it seems installed. Error: $($_.Exception.Message)" -Level 'ERROR'
         exit 1
    }

}

# Check/Install Active Directory Module (RSAT)
# Note: Install-Module won't work for AD; RSAT feature install is needed. This check primarily ensures it's present.
Write-Log -Message "Checking for module: ActiveDirectory..." -Level 'INFO'
if (-not (Get-Module -Name ActiveDirectory -ListAvailable)) {
     Write-Log -Message "Module 'ActiveDirectory' not found. This is part of Remote Server Administration Tools (RSAT)." -Level 'ERROR'
     Write-Log -Message "Please install 'RSAT: Active Directory Domain Services and Lightweight Directory Services Tools' via Windows Settings (Optional Features) or Server Manager (Features), then restart PowerShell." -Level 'ERROR'
     exit 1
} else {
    Write-Log -Message "Module 'ActiveDirectory' found." -Level 'INFO'
    Import-Module ActiveDirectory -ErrorAction Stop
     Write-Log -Message "Successfully imported module 'ActiveDirectory'." -Level 'INFO'
}


# Check/Install Microsoft Graph Users Module
Ensure-Module -ModuleName Microsoft.Graph.Users

Write-Log -Message "Prerequisite check completed." -Level 'INFO'

#endregion

#region --- Gather Initial Inputs ---

Write-Log -Message "Gathering initial information..." -Level 'ACTION'

$Identity = $null
while ([string]::IsNullOrWhiteSpace($Identity)) {
    $Identity = Read-Host "Enter the AD User Identifier (e.g., sAMAccountName, current UPN)"
    if ([string]::IsNullOrWhiteSpace($Identity)) { Write-Warning "User identifier cannot be empty." }
}

$EmailDomain = $null
while ([string]::IsNullOrWhiteSpace($EmailDomain)) {
    $EmailDomain = Read-Host "Enter the user's PRIMARY Email Domain (e.g., qhrtech.com)"
    if ([string]::IsNullOrWhiteSpace($EmailDomain)) { Write-Warning "Email domain cannot be empty." }
    elseif ($EmailDomain -notlike '*.*' -or $EmailDomain -like '*@*') {
        Write-Warning "Please enter a valid domain name (e.g., contoso.com)."
        $EmailDomain = $null # Force re-entry
    }
}
$EmailDomain = $EmailDomain.ToLower() # Standardize domain format

# Define Log File Path now that we have an identity
$timestampFile = Get-Date -Format "yyyyMMdd_HHmmss"
$LogFile = Join-Path -Path $LogDirectory -ChildPath "$($Identity -replace '[^a-zA-Z0-9]','_')_NameChange_$timestampFile.log"
Write-Log -Message "Logging detailed output to file: $LogFile" -Level 'INFO'

Write-Log -Message "Input collected: Identity='$Identity', EmailDomain='$EmailDomain'" -Level 'INFO'

#endregion

#region --- Connect to Microsoft Graph ---

Write-Log -Message "Connecting to Microsoft Graph..." -Level 'ACTION'
Write-Log -Message "Microsoft Graph connection is needed for post-AD update verification." -Level 'INFO'
Write-Log -Message "If prompted, authenticate using an account with at least 'User.Read.All' permissions (or 'User.ReadWrite.All' if previously used)." -Level 'INFO'

if (-not (Get-MgContext)) {
    try {
        # Request minimum needed scope for Get-MgUser
        Connect-MgGraph -Scopes "User.Read.All" -ErrorAction Stop
        Write-Log -Message "Successfully connected to Microsoft Graph." -Level 'INFO'
        $graphContext = Get-MgContext
        Write-Log -Message "Connected as: $($graphContext.Account) Tenant: $($graphContext.TenantId)" -Level 'INFO' -NoConsole # Log context detail to file only
    } catch {
        Write-Log -Message "Failed to connect to Microsoft Graph. Error: $($_.Exception.Message)" -Level 'ERROR'
        Write-Log -Message "Verification of Entra ID UPN will be skipped. Please check manually after sync." -Level 'WARN'
        # Allow script to continue, but verification step will be skipped/fail
    }
} else {
     Write-Log -Message "Already connected to Microsoft Graph." -Level 'INFO'
     $graphContext = Get-MgContext
     Write-Log -Message "Connected as: $($graphContext.Account) Tenant: $($graphContext.TenantId)" -Level 'INFO' -NoConsole # Log context detail to file only
}

#endregion

#region --- Get Current AD User Info ---

Write-Log -Message "Attempting to retrieve AD user '$Identity'..." -Level 'ACTION'
$adUser = $null
try {
    $adUser = Get-ADUser -Identity $Identity -Properties GivenName, SN, DisplayName, UserPrincipalName, mail, proxyAddresses, targetAddress, SamAccountName, DistinguishedName -ErrorAction Stop
    Write-Log -Message "Successfully retrieved AD user '$($adUser.SamAccountName)'." -Level 'INFO'
    Write-Host "`n--- Current AD User Details ---" -ForegroundColor Cyan
    Write-Host "  sAMAccountName   : $($adUser.SamAccountName)"
    Write-Host "  DistinguishedName: $($adUser.DistinguishedName)"
    Write-Host "  GivenName        : $($adUser.GivenName)"
    Write-Host "  SN (LastName)    : $($adUser.SN)"
    Write-Host "  DisplayName      : $($adUser.DisplayName)"
    Write-Host "  UserPrincipalName: $($adUser.UserPrincipalName)"
    Write-Host "  Mail             : $($adUser.mail)"
    Write-Host "  TargetAddress    : $($adUser.targetAddress)"
    Write-Host "  ProxyAddresses   :"
    $adUser.proxyAddresses | Sort-Object | ForEach-Object { Write-Host "    $_" }
    Write-Host "------------------------------"

    # Log current details to file
    Write-Log -Message "Current AD Details for '$($adUser.SamAccountName)':" -Level 'INFO' -NoConsole
    # Use Format-List for better logging of multi-valued attributes like proxyAddresses
    $adUser | Select-Object SamAccountName, DistinguishedName, GivenName, SN, DisplayName, UserPrincipalName, mail, targetAddress, @{N='proxyAddresses'; E={$_.proxyAddresses | Sort-Object}} | Format-List | Out-String | ForEach-Object { Write-Log -Message "$_" -Level 'INFO' -NoConsole }


}
catch {
    Write-Log -Message "Failed to retrieve AD user '$Identity'. Error: $($_.Exception.Message)" -Level 'ERROR'
    Write-Log -Message "Please check the identifier and ensure the user exists in AD." -Level 'ERROR'
    exit 1
}

#endregion

#region --- Interactive Prompt for Name Change Details ---

Write-Log -Message "Gathering name change details..." -Level 'ACTION'

# Define EmailPrefixFormat here if you need it to be configurable
$EmailPrefixFormat = 'FirstName.LastName' # Options: 'FirstName.LastName', 'FirstInitialLastName'

Write-Host "`nPlease select the type of name change:" -ForegroundColor Yellow
Write-Host "1. First Name Only"
Write-Host "2. Last Name Only"
Write-Host "3. Both First and Last Name"

$choice = Read-Host "Enter choice (1-3)"

$NewFirstName = $null
$NewLastName = $null
$changeType = ""

switch ($choice) {
    '1' {
        $changeType = "First Name Only"
        while ([string]::IsNullOrWhiteSpace($NewFirstName)) {
             $NewFirstName = Read-Host "Enter the NEW First Name"
             if ([string]::IsNullOrWhiteSpace($NewFirstName)) { Write-Warning "New First Name cannot be empty."}
        }
        $updatedFirstName = $NewFirstName.Trim()
        $updatedLastName = $adUser.SN # Keep existing last name
    }
    '2' {
         $changeType = "Last Name Only"
        while ([string]::IsNullOrWhiteSpace($NewLastName)) {
             $NewLastName = Read-Host "Enter the NEW Last Name"
              if ([string]::IsNullOrWhiteSpace($NewLastName)) { Write-Warning "New Last Name cannot be empty."}
        }
        $updatedFirstName = $adUser.GivenName # Keep existing first name
        $updatedLastName = $NewLastName.Trim()
    }
    '3' {
         $changeType = "Both First and Last Names"
         while ([string]::IsNullOrWhiteSpace($NewFirstName)) {
             $NewFirstName = Read-Host "Enter the NEW First Name"
             if ([string]::IsNullOrWhiteSpace($NewFirstName)) { Write-Warning "New First Name cannot be empty."}
        }
         while ([string]::IsNullOrWhiteSpace($NewLastName)) {
             $NewLastName = Read-Host "Enter the NEW Last Name"
              if ([string]::IsNullOrWhiteSpace($NewLastName)) { Write-Warning "New Last Name cannot be empty."}
        }
        $updatedFirstName = $NewFirstName.Trim()
        $updatedLastName = $NewLastName.Trim()
    }
    default {
        Write-Log -Message "Invalid choice '$choice'. Exiting." -Level 'ERROR'
        exit 1
    }
}

Write-Log -Message "User selected change type: $changeType" -Level 'INFO'
Write-Log -Message "Raw Inputs: NewFirstName='$NewFirstName', NewLastName='$NewLastName'" -Level 'INFO' -NoConsole
Write-Log -Message "Processed Names: updatedFirstName='$updatedFirstName', updatedLastName='$updatedLastName'" -Level 'INFO'

#endregion

#region --- Calculate New Values ---

Write-Log -Message "Calculating new attribute values..." -Level 'INFO'
# Basic validation after processing
if ([string]::IsNullOrWhiteSpace($updatedFirstName) -or [string]::IsNullOrWhiteSpace($updatedLastName)) {
    Write-Log -Message "Resulting first name ('$updatedFirstName') or last name ('$updatedLastName') is empty. Cannot proceed. Check current AD values if only changing one name." -Level 'ERROR'
    exit 1
}

$newDisplayName = "$updatedFirstName $updatedLastName"

# Calculate Email Prefix based on selected format
$emailPrefix = switch ($EmailPrefixFormat) {
    'FirstName.LastName' { ($updatedFirstName + "." + $updatedLastName).ToLower() }
    'FirstInitialLastName' { ($updatedFirstName.Substring(0, 1) + $updatedLastName).ToLower() }
    # Add more formats here if needed
    default { ($updatedFirstName + "." + $updatedLastName).ToLower() } # Default fallback
}
# Basic sanitization (remove spaces) - adjust if more complex rules needed
$emailPrefix = $emailPrefix -replace '\s', ''
$newEmailAddress = "$emailPrefix@$EmailDomain"
$newPrimarySMTP = "SMTP:$newEmailAddress" # Primary is uppercase SMTP:
$newUPN = $newEmailAddress             # UPN usually matches primary email
$newTargetAddress = $newEmailAddress     # Set targetAddress to match primary email

# Calculate new Proxy Addresses
$currentProxyAddresses = $adUser.proxyAddresses
$newProxyAddresses = [System.Collections.Generic.List[string]]::new()
$oldPrimarySMTP = $null

# Add the new primary address first
$newProxyAddresses.Add($newPrimarySMTP)
Write-Log -Message "Adding new primary proxy address: '$newPrimarySMTP'" -Level 'INFO' -NoConsole

# Process existing addresses
foreach ($address in $currentProxyAddresses) {
    if ($address -clike 'SMTP:*') {
        # Found the old primary
        $oldPrimarySMTP = $address
        Write-Log -Message "Found old primary SMTP: '$oldPrimarySMTP'" -Level 'INFO' -NoConsole
        if ($oldPrimarySMTP -ne $newPrimarySMTP) {
            $secondaryAddress = $oldPrimarySMTP.Replace('SMTP:', 'smtp:') # Convert to secondary
            if (-not $newProxyAddresses.Contains($secondaryAddress)) {
                $newProxyAddresses.Add($secondaryAddress)
                Write-Log -Message "Adding old primary as secondary: '$secondaryAddress'" -Level 'INFO' -NoConsole
            } else {
                 Write-Log -Message "Old primary '$oldPrimarySMTP' converted to '$secondaryAddress' is already present or same as new primary. Skipping." -Level 'WARN' -NoConsole
            }
        } else {
             Write-Log -Message "Old primary SMTP is the same as the new one. Not adding as secondary." -Level 'INFO' -NoConsole
        }
    }
    elseif ($address -clike 'smtp:*') {
        # Keep existing secondary addresses, ensure no duplicates with new list
        if (-not $newProxyAddresses.Contains($address)) {
            $newProxyAddresses.Add($address)
            Write-Log -Message "Keeping existing secondary address: '$address'" -Level 'INFO' -NoConsole
        } else { Write-Log -Message "Skipping duplicate secondary address: '$address'" -Level 'WARN' -NoConsole }
    }
    else {
         # Keep other address types (X400, SIP, etc.), ensure no duplicates
         if (-not $newProxyAddresses.Contains($address)) {
            $newProxyAddresses.Add($address)
            Write-Log -Message "Keeping other address type: '$address'" -Level 'INFO' -NoConsole
         } else { Write-Log -Message "Skipping duplicate other address type: '$address'" -Level 'WARN' -NoConsole }
    }
} # end foreach address

# Prepare parameter hashtable for Set-ADUser NOW, so planned changes are accurate
$setADParams = @{ Identity = $adUser }
if ($updatedFirstName -ne $adUser.GivenName) { $setADParams.Add('GivenName', $updatedFirstName) }
if ($updatedLastName -ne $adUser.SN) { $setADParams.Add('Surname', $updatedLastName) }
if ($newDisplayName -ne $adUser.DisplayName) { $setADParams.Add('DisplayName', $newDisplayName) }
if ($newEmailAddress -ne $adUser.mail) { $setADParams.Add('EmailAddress', $newEmailAddress) } # Sets 'mail' attribute
if ($newTargetAddress -ne $adUser.targetAddress) { $setADParams.Add('targetAddress', $newTargetAddress)}
# Add UPN change to AD Params if needed
if ($newUPN -ne $adUser.UserPrincipalName) {
    $setADParams.Add('UserPrincipalName', $newUPN)
}
# Always replace proxy addresses as the list calculation logic runs regardless
$setADParams.Add('Replace', @{proxyAddresses = $newProxyAddresses.ToArray() })


Write-Log -Message "Finished calculating new values." -Level 'INFO'

#endregion

#region --- Display Planned Changes and Confirmation ---

Write-Host "`n--- Planned Changes ---" -ForegroundColor Yellow
Write-Host "** Active Directory Changes for '$($adUser.SamAccountName)' ($($adUser.DistinguishedName)) **"
if ($setADParams.ContainsKey('GivenName')) { Write-Host "  Set GivenName      : '$($setADParams.GivenName)'" } else {Write-Host "  GivenName        : (Unchanged)"}
if ($setADParams.ContainsKey('Surname')) { Write-Host "  Set SN (LastName)  : '$($setADParams.Surname)'" } else {Write-Host "  SN (LastName)    : (Unchanged)"}
if ($setADParams.ContainsKey('DisplayName')) { Write-Host "  Set DisplayName    : '$($setADParams.DisplayName)'" } else {Write-Host "  DisplayName      : (Unchanged)"}
if ($setADParams.ContainsKey('UserPrincipalName')) { Write-Host "  Set UserPrincipalName: '$($setADParams.UserPrincipalName)'"} else {Write-Host "  UserPrincipalName: (Unchanged)"}
if ($setADParams.ContainsKey('EmailAddress')) { Write-Host "  Set mail           : '$($setADParams.EmailAddress)'" } else {Write-Host "  mail             : (Unchanged)"}
if ($setADParams.ContainsKey('targetAddress')) { Write-Host "  Set targetAddress  : '$($setADParams.targetAddress)'" } else {Write-Host "  targetAddress    : (Unchanged)"}
Write-Host "  Replace proxyAddresses with:"
$newProxyAddresses | Sort-Object | ForEach-Object { Write-Host "    $_" }
Write-Host "--------------------------"

# Log planned changes to file
Write-Log -Message "--- Planned Changes ---" -Level 'INFO' -NoConsole
Write-Log -Message "** Active Directory Changes for '$($adUser.SamAccountName)' ($($adUser.DistinguishedName)) **" -Level 'INFO' -NoConsole
Write-Log -Message "  Set GivenName      : $(if ($setADParams.ContainsKey('GivenName')) { "'$($setADParams.GivenName)'" } else { '(Unchanged)' })" -Level 'INFO' -NoConsole
Write-Log -Message "  Set SN (LastName)  : $(if ($setADParams.ContainsKey('Surname')) { "'$($setADParams.Surname)'" } else { '(Unchanged)' })" -Level 'INFO' -NoConsole
Write-Log -Message "  Set DisplayName    : $(if ($setADParams.ContainsKey('DisplayName')) { "'$($setADParams.DisplayName)'" } else { '(Unchanged)' })" -Level 'INFO' -NoConsole
Write-Log -Message "  Set UserPrincipalName: $(if ($setADParams.ContainsKey('UserPrincipalName')) { "'$($setADParams.UserPrincipalName)'"} else { '(Unchanged)' })" -Level 'INFO' -NoConsole
Write-Log -Message "  Set mail           : $(if ($setADParams.ContainsKey('EmailAddress')) { "'$($setADParams.EmailAddress)'" } else { '(Unchanged)' })" -Level 'INFO' -NoConsole
Write-Log -Message "  Set targetAddress  : $(if ($setADParams.ContainsKey('targetAddress')) { "'$($setADParams.targetAddress)'" } else { '(Unchanged)' })" -Level 'INFO' -NoConsole
Write-Log -Message "  Replace proxyAddresses with:" -Level 'INFO' -NoConsole
$newProxyAddresses | Sort-Object | ForEach-Object { Write-Log -Message "    $_" -Level 'INFO' -NoConsole }
Write-Log -Message "--------------------------" -Level 'INFO' -NoConsole


# Confirmation Prompt
$confirmation = Read-Host "`nApply these AD changes? (Entra ID UPN will sync automatically) (Y/N)"
if ($confirmation -ne 'Y') {
    Write-Log -Message "User chose not to apply changes. Script exiting." -Level 'WARN'
    Write-Host "Operation cancelled by user." -ForegroundColor Yellow
    exit 0
}

Write-Log -Message "User confirmed applying changes." -Level 'INFO'

#endregion

#region --- Perform AD Update ---

Write-Log -Message "--- Applying Active Directory Update ---" -Level 'ACTION'

$adUpdateSuccess = $false
try {
    Write-Log -Message "Executing Set-ADUser for '$($adUser.SamAccountName)'..." -Level 'INFO'
    # $setADParams was prepared earlier
    Set-ADUser @setADParams -ErrorAction Stop
    Write-Log -Message "Successfully updated AD attributes for user '$($adUser.SamAccountName)'." -Level 'INFO'
    Write-Host "Successfully updated AD attributes for user '$($adUser.SamAccountName)'." -ForegroundColor Green
    $adUpdateSuccess = $true
}
catch {
    Write-Log -Message "Failed to update AD user '$($adUser.SamAccountName)'. Error: $($_.Exception.Message)" -Level 'ERROR'
    # Log parameters attempted
    Write-Log -Message "Parameters attempted for Set-ADUser:" -Level 'ERROR' -NoConsole
    $setADParams | Out-String | ForEach-Object { Write-Log -Message "$_" -Level 'ERROR' -NoConsole }
    exit 1 # Stop script if AD update fails critically
}

#endregion

#region --- Verify Entra ID UPN (Microsoft Graph) ---

if ($adUpdateSuccess) {
    Write-Log -Message "--- Verifying Entra ID User Status (Microsoft Graph) ---" -Level 'ACTION'

    # Check if we are connected to Graph first
    if (-not (Get-MgContext)) {
         Write-Log -Message "Not connected to Microsoft Graph. Skipping Entra ID verification." -Level 'WARN'
         Write-Host "Not connected to Microsoft Graph. Skipping Entra ID verification." -ForegroundColor Yellow
    } else {

        $originalUPNForSearch = $adUser.UserPrincipalName # Use the original UPN to find the user reliably
        $graphUser = $null
        $entraUPN = $null

        try {
            Write-Log -Message "Searching for Entra ID user corresponding to original AD UPN '$originalUPNForSearch' via Graph..." -Level 'INFO'
            # Select only needed properties
            $graphUser = Get-MgUser -UserId $originalUPNForSearch -ErrorAction Stop -Select Id, UserPrincipalName
            if ($graphUser) {
                $entraUPN = $graphUser.UserPrincipalName
                Write-Log -Message "Found Entra ID User ID: $($graphUser.Id). Current Entra ID UPN: '$entraUPN'" -Level 'INFO'
                Write-Host "Successfully queried Entra ID. Current UPN found: '$entraUPN'" -ForegroundColor Cyan

                # Compare with the *new* expected UPN (which is $newUPN calculated earlier)
                if ($setADParams.ContainsKey('UserPrincipalName')) { # Check if AD UPN was actually changed
                    if ($entraUPN -eq $newUPN) {
                        Write-Log -Message "Entra ID UPN already matches the new expected value '$newUPN'." -Level 'INFO'
                        Write-Host "Entra ID UPN already matches the new value." -ForegroundColor Green
                    } else {
                        Write-Log -Message "Entra ID UPN '$entraUPN' does not yet match the new expected value '$newUPN'. Entra Cloud Sync should update this." -Level 'WARN'
                        Write-Host "Entra ID UPN found ('$entraUPN') does not yet match the expected new UPN ('$newUPN'). This is normal - monitor Entra Cloud Sync." -ForegroundColor Yellow
                    }
                } else {
                     Write-Log -Message "AD UPN was not changed, current Entra UPN is '$entraUPN'." -Level 'INFO'
                }

            } else {
                 # This case means Get-MgUser ran but didn't find the object
                 Write-Log -Message "Could not find user in Entra ID using original UPN '$originalUPNForSearch'. Cannot verify Entra UPN." -Level 'WARN'
                 Write-Host "Could not retrieve user from Entra ID using original UPN '$originalUPNForSearch' for verification. Check Entra ID manually." -ForegroundColor Yellow
            }
        } catch {
            # This case means Get-MgUser itself failed (e.g., permissions, connectivity, typo if user entered UPN manually)
             Write-Log -Message "Error querying user '$originalUPNForSearch' in Entra ID via Graph during verification. Error: $($_.Exception.Message)" -Level 'ERROR'
             Write-Host "Error querying Entra ID for verification. Check logs and Entra ID manually." -ForegroundColor Red
        }
    } # End if Get-MgContext
}

#endregion

#region --- Post-Update Instructions ---

Write-Host "`n--- Required Manual Steps & Verification ---" -ForegroundColor Yellow
Write-Log -Message "--- Required Manual Steps & Verification ---" -Level 'INFO'

Write-Host "1. Entra ID Synchronization: Using Entra Cloud Sync, changes from AD should sync automatically."
Write-Log -Message "1. Entra ID Synchronization: Using Entra Cloud Sync, changes from AD should sync automatically." -Level 'INFO' -NoConsole
Write-Host "   Monitor sync status in the Entra Portal (Identity > Hybrid management > Entra Connect > Cloud sync)."
Write-Log -Message "   Monitor sync status in the Entra Portal (Identity > Hybrid management > Entra Connect > Cloud sync)." -Level 'INFO' -NoConsole
Write-Host "   Allow time for changes to propagate (can take several minutes)."
Write-Log -Message "   Allow time for changes to propagate (can take several minutes)." -Level 'INFO' -NoConsole

Write-Host "2. Verify Entra ID/M365: After sync seems complete, double-check the user's UPN, Display Name, and email addresses in the Entra Portal & M365 Admin Center."
Write-Log -Message "2. Verify Entra ID/M365: After sync seems complete, double-check the user's UPN, Display Name, and email addresses in the Entra Portal & M365 Admin Center." -Level 'INFO' -NoConsole

Write-Host "3. Sign-Out/Revoke Sessions: Ensure user was signed out of M365 sessions and tokens were revoked BEFORE running the script, as per prerequisites."
Write-Log -Message "3. Sign-Out/Revoke Sessions: Ensure user was signed out of M365 sessions and tokens were revoked BEFORE running the script, as per prerequisites." -Level 'INFO' -NoConsole

Write-Host "4. AD Common Name (CN): This script *did not* change the AD object's CN. If your policy requires it:"
Write-Log -Message "4. AD Common Name (CN): This script *did not* change the AD object's CN. If your policy requires it:" -Level 'INFO' -NoConsole
Write-Host "   - Open 'Active Directory Users and Computers' (with Advanced Features enabled)."
Write-Log -Message "   - Open 'Active Directory Users and Computers' (with Advanced Features enabled)." -Level 'INFO' -NoConsole
Write-Host "   - Find the user '$($adUser.SamAccountName)', right-click, select 'Rename'."
Write-Log -Message "   - Find the user '$($adUser.SamAccountName)', right-click, select 'Rename'." -Level 'INFO' -NoConsole
Write-Host "   - Enter the new CN (e.g., '$newDisplayName' or '$emailPrefix'). *Use Caution! Changing CN has wider implications.*"
Write-Log -Message "   - Enter the new CN (e.g., '$newDisplayName' or '$emailPrefix'). *Use Caution! Changing CN has wider implications.*" -Level 'INFO' -NoConsole

Write-Host "5. Other Systems: Manually update the username/email/display name in other required systems:"
Write-Log -Message "5. Other Systems: Manually update the username/email/display name in other required systems:" -Level 'INFO' -NoConsole
Write-Host "   - Citrix, Okta (Accuro.Cloud P & D), Jira, SendGrid, Teams (verify number assignment if applicable),"
Write-Log -Message "   - Citrix, Okta (Accuro.Cloud P & D), Jira, SendGrid, Teams (verify number assignment if applicable)," -Level 'INFO' -NoConsole
Write-Host "   - 1Password (User profile name/email - may require user action), Jamf, Quest, Salesforce, GitHub, etc."
Write-Log -Message "   - 1Password (User profile name/email - may require user action), Jamf, Quest, Salesforce, GitHub, etc." -Level 'INFO' -NoConsole

Write-Host "6. Test Aliases: Send test emails to:"
Write-Log -Message "6. Test Aliases: Send test emails to:" -Level 'INFO' -NoConsole
Write-Host "   - New primary address: $newEmailAddress"
Write-Log -Message "   - New primary address: $newEmailAddress" -Level 'INFO' -NoConsole
if ($oldPrimarySMTP -and ($oldPrimarySMTP -ne $newPrimarySMTP)) {
     $oldPrimaryEmail = $oldPrimarySMTP -replace 'SMTP:', ''
     Write-Host "   - Old primary address (now secondary): $oldPrimaryEmail"
     Write-Log -Message "   - Old primary address (now secondary): $oldPrimaryEmail" -Level 'INFO' -NoConsole
}
Write-Host "   Confirm delivery to the user's mailbox."
Write-Log -Message "   Confirm delivery to the user's mailbox." -Level 'INFO' -NoConsole

Write-Host "7. Inform User: Notify the user of the changes and any actions they need to take (e.g., update Slack profile, confirm logins, use new UPN)."
Write-Log -Message "7. Inform User: Notify the user of the changes and any actions they need to take (e.g., update Slack profile, confirm logins, use new UPN)." -Level 'INFO' -NoConsole

Write-Host "`nScript finished. Please review the log file: $LogFile" -ForegroundColor Green
Write-Log -Message "--- Script Execution Finished ---" -Level 'INFO'

#endregion