<#
.SYNOPSIS
    GUI-based tool for updating user attributes in Active Directory and related M365 groups.

.DESCRIPTION
    GUI PowerShell script simplifies modifying user attributes within Active Directory (AD)
    and manages corresponding "All Staff - [Province]" distribution group membership based on Province changes.
    Supported attributes: Province, Department, Manager, and Title.
    Uses a multiline textbox for specifying one or more target users.
    Requires Microsoft.Graph and ExchangeOnlineManagement modules.

.EXAMPLE
    .\ADAttributesGUI.ps1

    Convert to EXE:
    Invoke-ps2exe -inputFile ".\ADAttributesGUI.ps1" -outputFile ".\ADAttributeEditor.exe" -noConsole -requireAdmin

.NOTES
    Author: Slader Sheppard
    Version: 2.32
    Created: 2025-03-24
    Updated: 2025-03-25 (Fix PSSA warnings, Runtime errors, GUI layout calculations)
#>

#region Prerequisite Module Checks
# Check if required modules are installed
$requiredModules = @(
    @{Name='ActiveDirectory'; ErrorMessage='Install Active Directory module (RSAT).'}
    @{Name='ExchangeOnlineManagement'; ErrorMessage='Install ExchangeOnlineManagement module (Install-Module ExchangeOnlineManagement).'}
    @{Name='Microsoft.Graph.Authentication'; ErrorMessage='Install Microsoft Graph Authentication module (Install-Module Microsoft.Graph.Authentication).'}
    @{Name='Microsoft.Graph.Users'; ErrorMessage='Install Microsoft Graph Users module (Install-Module Microsoft.Graph.Users).'}
    # Add other modules as needed, e.g., Microsoft.Graph.Groups if used directly
)
$missingModules = @()
foreach ($moduleInfo in $requiredModules) {
    if (-not (Get-Module -ListAvailable -Name $moduleInfo.Name)) {
        $missingModules += $moduleInfo.ErrorMessage
    }
}
if ($missingModules.Count -gt 0) {
    # Attempt to load Forms assembly just for the error message box
    try { Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop } catch {}
    [System.Windows.Forms.MessageBox]::Show("Required PowerShell modules are missing:`n`n$($missingModules -join "`n")`n`nPlease install them and restart the tool.", "Missing Modules", "OK", "Error")
    exit
}
#endregion

#region Load Modules and Connect
try {
    # Import necessary modules
    Import-Module ActiveDirectory -ErrorAction Stop
    Import-Module ExchangeOnlineManagement -ErrorAction Stop
    Import-Module Microsoft.Graph.Authentication -ErrorAction Stop
    Import-Module Microsoft.Graph.Users -ErrorAction Stop
    # Import-Module Microsoft.Graph.Groups # Not strictly needed if using EXO cmdlets for groups

    # Connect to services - Add necessary scopes
    # Consider using -TenantId parameter if needed
    Write-Host "Connecting to Microsoft Graph..."
    Connect-MgGraph -Scopes "User.Read.All", "Directory.Read.All" -ErrorAction Stop -NoWelcome
    Write-Host "Connecting to Exchange Online..."
    $msalPath = [System.IO.Path]::GetDirectoryName((Get-Module ExchangeOnlineManagement).Path);
    Add-Type -Path "$msalPath\Microsoft.IdentityModel.Abstractions.dll";
    Add-Type -Path "$msalPath\Microsoft.Identity.Client.dll";
    [Microsoft.Identity.Client.IPublicClientApplication] $application = [Microsoft.Identity.Client.PublicClientApplicationBuilder]::Create("fb78d390-0c51-40cd-8e17-fdbfab77341b").WithDefaultRedirectUri().Build();
    $result = $application.AcquireTokenInteractive([string[]]"https://outlook.office365.com/.default").ExecuteAsync().Result;
    Connect-ExchangeOnline -AccessToken $result.AccessToken -UserPrincipalName $result.Account.Username -ShowBanner:$false -ErrorAction Stop 
    Write-Host "Connections successful."

} catch {
    # Attempt to load Forms assembly just for the error message box
    try { Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop } catch {}
    [System.Windows.Forms.MessageBox]::Show("Failed to import modules or connect:`n$($_.Exception.Message)`nPlease ensure modules are installed and you can authenticate.", "Connection Error", "OK", "Error")
    exit
}
#endregion

#region Helper Functions

function Get-ADUserByEmailOrSamAccountName {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$EmailOrSamAccountName
    )

    $user = $null
    try {
        $trimmedInput = $EmailOrSamAccountName.Trim()
        if ([string]::IsNullOrWhiteSpace($trimmedInput)){
            # Don't Write-Warning here, let the caller handle empty input if needed
            return $null
        }

        # Try finding by Mail attribute or explicit SamAccountName first (more specific)
        $user = Get-ADUser -Filter "(Mail -eq '$trimmedInput') -or (SamAccountName -eq '$trimmedInput')" -Properties st, Department, Title, Manager, UserPrincipalName -ErrorAction SilentlyContinue

        # If not found, try deriving SamAccountName from email format
        if (-not $user -and $trimmedInput -like '*@*') {
            $samAccountName = $trimmedInput.Split('@')[0]
            $user = Get-ADUser -Filter "SamAccountName -eq '$samAccountName'" -Properties st, Department, Title, Manager, UserPrincipalName -ErrorAction SilentlyContinue
        }

         # If still not found, try UserPrincipalName (often matches email)
         if (-not $user) {
             $user = Get-ADUser -Filter "UserPrincipalName -eq '$trimmedInput'" -Properties st, Department, Title, Manager, UserPrincipalName -ErrorAction SilentlyContinue
         }

    } catch {
        # Log or handle specific errors if necessary, but often handled by the caller checking $null
        # Write-Warning "Error looking up user '$EmailOrSamAccountName': $($_.Exception.Message)" # Optional debug/warning
    }

    if (-not $user) { Write-Warning "AD User '$EmailOrSamAccountName' not found."} # Keep this warning for script user feedback

    return $user
}

function Update-UserAttribute {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [Microsoft.ActiveDirectory.Management.ADUser]$UserObject,
        [Parameter(Mandatory = $true)]
        [string]$AttributeName,
        [Parameter(Mandatory = $true)]
        [string]$NewValue,
        [Parameter(Mandatory = $false)]
        [bool]$IsKelownaResident = $false # Specific for Province -> Group logic
    )

    $result = @{
        Success = $false
        ADChangeDetail = $null       # String for the specific AD attribute change
        GroupChangeDetails = $null # Will be a [List[string]] for group add/remove actions
        ErrorMessage = $null
        OldProvince = $null          # Store for group logic header
        NewProvince = $null          # Store for group logic header
    }

    try {
        # Map GUI names to AD property names
        $adProperties = @{
            Province   = "st"         # 'st' is the AD attribute for State/Province
            Department = "Department"
            Manager    = "Manager"
            Title      = "Title"
        }
        if (-not $adProperties.ContainsKey($AttributeName)) {
            throw "Invalid attribute name specified: $AttributeName"
        }
        $adProp = $adProperties[$AttributeName]

        # Get the current value for logging
        $oldVal = $null
        if ($adProp -eq "Manager") {
            if ($UserObject.Manager) {
                try {
                    # Get the manager's display name for friendlier logging
                    $oldVal = (Get-ADUser $UserObject.Manager -Properties Name -ErrorAction Stop).Name
                } catch {
                    Write-Warning "Could not get manager display name for DN: $($UserObject.Manager). Logging DistinguishedName."
                    $oldVal = $UserObject.Manager # Fallback to DN
                }
            } else {
                $oldVal = "[None]"
            }
        } else {
            $oldVal = $UserObject.$adProp
        }

        $originalNewValue = $NewValue # Keep original input for potential error messages

        # --- Province Group Mapping ---
        $provinceGroupMap = @{
            'BC' = '<EMAIL>'
            'AB' = '<EMAIL>'
            'SK' = '<EMAIL>'
            'MB' = '<EMAIL>'
            'ON' = '<EMAIL>'
            'NS' = '<EMAIL>'
            # Add other provinces and their corresponding group emails here
        }
        $kelownaGroupName = '<EMAIL>' # Specific Kelowna group

        #region --- Attribute Update Logic ---
        switch ($AttributeName) {
            'Province' {
                # Update the 'st' attribute in AD
                Set-ADUser $UserObject -Replace @{ st = $NewValue }
                $result.NewProvince = $NewValue # Store for group logic
                $result.OldProvince = $oldVal   # Store for group logic
            }
            'Department' {
                Set-ADUser $UserObject -Department $NewValue
            }
            'Title' {
                Set-ADUser $UserObject -Title $NewValue
            }
            'Manager' {
                # Handle setting or clearing the manager
                if ([string]::IsNullOrWhiteSpace($NewValue)) {
                    # Clear the manager attribute
                    Set-ADUser $UserObject -Clear Manager
                    $NewValue = "[Cleared]" # Adjust NewValue for logging after successful clear
                } else {
                    # Find the new manager AD object
                    $mgr = Get-ADUserByEmailOrSamAccountName -EmailOrSamAccountName $NewValue
                    if (-not $mgr) {
                        throw "Manager '$originalNewValue' not found in Active Directory."
                    }
                    # Set the manager using the DistinguishedName
                    Set-ADUser $UserObject -Manager $mgr.DistinguishedName
                    $NewValue = $mgr.Name # Adjust NewValue to manager's name for logging
                }
            }
            default {
                throw "Unsupported attribute: $AttributeName"
            }
        }
        #endregion --- Attribute Update Logic ---

        # Store AD change detail (with leading space for consistent log indentation)
        $result.ADChangeDetail = "    $AttributeName : '$oldVal' -> '$NewValue'"

        # --- Province Group Management ---
        if ($AttributeName -eq 'Province') {
            $oldProvince = $result.OldProvince -as [string] # Use stored value
            $newProvince = $result.NewProvince -as [string] # Use stored value

            # Check if the province actually changed and the new province is not blank
            if ($oldProvince -ne $newProvince -and (-not [string]::IsNullOrWhiteSpace($newProvince))) {
                $currentGroupChanges = [System.Collections.Generic.List[string]]::new()

                # 1. Determine Group to ADD based on the NEW province
                $groupToAdd = $null
                if ($newProvince -eq 'BC') {
                    if ($IsKelownaResident) { $groupToAdd = $kelownaGroupName } else { $groupToAdd = $provinceGroupMap['BC'] }
                } elseif ($provinceGroupMap.ContainsKey($newProvince)) {
                    $groupToAdd = $provinceGroupMap[$newProvince]
                } else {
                    Write-Warning "No 'All Staff' distribution group mapping found for the new province '$newProvince'. User will not be added to a province group."
                    $currentGroupChanges.Add("    WARNING: No group mapping for new province '$newProvince'.")
                }

                # 2. Determine Group(s) to REMOVE based on the OLD province and BC/Kelowna logic
                $groupsToRemove = [System.Collections.Generic.List[string]]::new()
                if ($provinceGroupMap.ContainsKey($oldProvince)) { $groupsToRemove.Add($provinceGroupMap[$oldProvince]) }
                # Additional Kelowna/BC logic - ensure we don't try to remove the group we're adding, and remove the *other* BC group if needed
                if ($oldProvince -eq 'BC'){
                    if ($provinceGroupMap.ContainsKey('BC') -and ($provinceGroupMap['BC'] -ne $groupToAdd) -and (-not $groupsToRemove.Contains($provinceGroupMap['BC']))) {
                        $groupsToRemove.Add($provinceGroupMap['BC'])
                    }
                    if ($kelownaGroupName -and ($kelownaGroupName -ne $groupToAdd) -and (-not $groupsToRemove.Contains($kelownaGroupName))) {
                        $groupsToRemove.Add($kelownaGroupName)
                    }
                }

                # 3. Perform Add/Remove Operations using ExchangeOnline cmdlets
                $userUPN = $UserObject.UserPrincipalName
                if ([string]::IsNullOrWhiteSpace($userUPN)) {
                     Write-Warning "Cannot perform group operations for '$($UserObject.SamAccountName)' because UserPrincipalName is missing."
                     $currentGroupChanges.Add("    ERROR: UserPrincipalName missing for $($UserObject.SamAccountName). Cannot manage groups.")
                } else {
                    # Remove Operations
                    foreach($groupAddress in $groupsToRemove | Select-Object -Unique){
                         # Don't remove if it's the group we're adding (shouldn't happen with improved logic above, but safe)
                         if ($groupAddress -eq $groupToAdd){ continue }
                         try {
                             Remove-DistributionGroupMember -Identity $groupAddress -Member $userUPN -Confirm:$false -ErrorAction Stop
                             $currentGroupChanges.Add("    Removed from Distribution Group: $groupAddress")
                         } catch {
                             # v2.32 Fix: Use -gt 0 comparison, not >
                             # Only log errors if it's not 'member not found' or 'group not found'
                             if ($_.Exception.Message -notmatch "couldn't be found" -and $_.Exception.Message -notmatch "isn't a member") {
                                 Write-Warning " -> Failed to remove $userUPN from '$groupAddress': $($_.Exception.Message)"
                                 $currentGroupChanges.Add("    ERROR Removing from '$groupAddress': $($_.Exception.Message)")
                             }
                         }
                    } # End foreach groupToRemove

                    # Add Operation
                    if ($null -ne $groupToAdd) {
                        try {
                            Add-DistributionGroupMember -Identity $groupToAdd -Member $userUPN -ErrorAction Stop
                            $currentGroupChanges.Add("    Added to Distribution Group: $groupToAdd")
                        } catch {
                             # v2.32 Fix: Use -gt 0 comparison, not > (Implicit check here)
                             # Only log errors if it's not 'already a member'
                             if ($_.Exception.Message -notmatch "already a member") {
                                Write-Warning " -> Failed to add $userUPN to '$groupToAdd': $($_.Exception.Message)"
                                $currentGroupChanges.Add("    ERROR Adding to '$groupToAdd': $($_.Exception.Message)")
                             }
                        }
                    } # End if groupToAdd
                } # End if has UPN

                # Assign collected group changes to the result hashtable
                if ($currentGroupChanges.Count -gt 0) { # v2.32 Fix: Use -gt 0 comparison
                    $result.GroupChangeDetails = $currentGroupChanges
                }
            } # End if province changed
        } # End if attribute is Province
        # --- End Province Group Management ---

        $result.Success = $true # Mark success if no exceptions were thrown

    } catch {
        $result.Success = $false
        $result.ErrorMessage = "Error updating attribute '$AttributeName' for user '$($UserObject.SamAccountName)': $($_.Exception.Message)"
    }

    return $result
}

#endregion Helper Functions

#region --- GUI Setup ---

# Load Assemblies
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Logging Setup
$logFile = "C:\temp\Helpdesk\LocalADAttributes\AD_Attribute_Audit.log"
if (-not (Test-Path $logFile)) {
    try {
        New-Item -Path $logFile -ItemType File -ErrorAction Stop | Out-Null
    } catch {
        Write-Error "Failed to create log file at '$logFile'. Exiting. Error: $($_.Exception.Message)"
        try { [System.Windows.Forms.MessageBox]::Show("Failed to create log file at '$logFile'.`nError: $($_.Exception.Message)`nExiting.", "Logging Error", "OK", "Error") } catch {}
        exit
    }
}
Add-Content -Path $logFile -Value "--- Script Started: $(Get-Date) --- User: $($env:USERNAME) ---"

# GUI Styling and Variables
$defaultFont = New-Object System.Drawing.Font("Segoe UI", 9)
$formWidth = 550
$formHeight = 600
$panelPadding = 10
$controlPadding = 5 # Padding between controls within a panel row
$controlRowHeight = 25 # Height of a single row (textbox/button)
$checkboxHeight = 20 # Height of the checkbox control
# v2.32 Fix: Correct panelWidth calculation
$panelWidth = $formWidth - (2 * $panelPadding) # Usable width within panels (Form width minus left/right padding)

# --- Form ---
$form = New-Object System.Windows.Forms.Form
$form.Text = "AD User Attribute Editor"
$form.Size = New-Object System.Drawing.Size($formWidth, $formHeight)
$form.MinimumSize = New-Object System.Drawing.Size(550, 550)
$form.StartPosition = "CenterScreen"
$form.Font = $defaultFont
# $form.Icon = ...

# --- Main Layout Panel (TableLayoutPanel) ---
$mainTableLayout = New-Object System.Windows.Forms.TableLayoutPanel
$mainTableLayout.Dock = [System.Windows.Forms.DockStyle]::Fill
$mainTableLayout.ColumnCount = 1
[void]$mainTableLayout.ColumnStyles.Add((New-Object System.Windows.Forms.ColumnStyle([System.Windows.Forms.SizeType]::Percent, 100)))
$mainTableLayout.RowCount = 4 # User Input, Attribute Input, Buttons, Log Display
[void]$mainTableLayout.RowStyles.Add((New-Object System.Windows.Forms.RowStyle([System.Windows.Forms.SizeType]::AutoSize))) # User Input
[void]$mainTableLayout.RowStyles.Add((New-Object System.Windows.Forms.RowStyle([System.Windows.Forms.SizeType]::AutoSize))) # Attribute Input
[void]$mainTableLayout.RowStyles.Add((New-Object System.Windows.Forms.RowStyle([System.Windows.Forms.SizeType]::AutoSize))) # Buttons
[void]$mainTableLayout.RowStyles.Add((New-Object System.Windows.Forms.RowStyle([System.Windows.Forms.SizeType]::Percent, 100))) # Log Display
$form.Controls.Add($mainTableLayout)

#region User Input Section (Panel 1)
$userInputPanel = New-Object System.Windows.Forms.Panel
$userInputPanel.AutoSize = $true
$userInputPanel.AutoSizeMode = [System.Windows.Forms.AutoSizeMode]::GrowAndShrink
$userInputPanel.Margin = New-Object System.Windows.Forms.Padding($panelPadding) # Equal margin all sides

$userListLabel = New-Object System.Windows.Forms.Label
$userListLabel.Text = "User Emails (one per line):"
$userListLabel.Location = New-Object System.Drawing.Point(0, 0)
$userListLabel.AutoSize = $true
$userInputPanel.Controls.Add($userListLabel)

$userListTextBox = New-Object System.Windows.Forms.TextBox
$userListTextBox.Multiline = $true
$userListTextBox.ScrollBars = [System.Windows.Forms.ScrollBars]::Vertical
$userListTextBox.AcceptsReturn = $true
$userListTextBoxY = $userListLabel.Location.Y + $userListLabel.Height + $controlPadding
$userListTextBox.Location = New-Object System.Drawing.Point(0, $userListTextBoxY)
$userListTextBoxHeight = 80
# v2.32 Fix: Simplified width calculation, rely on anchor. Removed bad arithmetic.
$userListTextBox.Size = New-Object System.Drawing.Size($panelWidth, $userListTextBoxHeight)
$userListTextBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$userInputPanel.Controls.Add($userListTextBox)

# Add panel to layout AFTER controls added (for AutoSize calculation)
$mainTableLayout.Controls.Add($userInputPanel, 0, 0)
#endregion User Input Section

#region Attribute Input Panel (Panel 2) v2.32 Layout Fixes
$attributePanel = New-Object System.Windows.Forms.Panel
# Removed AutoSize, manually calculate height later
$attributePanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
$attributePanel.Margin = New-Object System.Windows.Forms.Padding($panelPadding, 0, $panelPadding, $panelPadding)

# Add panel to the second row of the main layout - BEFORE calculating height/adding controls
$mainTableLayout.Controls.Add($attributePanel, 0, 1)

# Define widths and padding INSIDE the attribute panel
$labelWidth = 150      # Width for the labels
$buttonWidth = 150     # Width for the update buttons
$internalPadding = 5   # Padding between label/textbox/button

# v2.32 Fix: Simplified calculations based on overall $panelWidth, not ClientSize (which might be 0 initially)
$textboxX = $internalPadding + $labelWidth + $internalPadding # X pos of textbox (Left Panel Pad + Label Width + Internal Pad)
# Width = Total Panel Width - Left Panel Pad - Label Width - Internal Pad - Button Width - Internal Pad - Right Panel Pad
$textboxWidth = $panelWidth - $labelWidth - $buttonWidth - (4 * $internalPadding)
$buttonX = $panelWidth - $buttonWidth - $internalPadding # X pos of button (Right edge of Panel - Button Width - Right Panel Pad)


$attributes = @("Province", "Department", "Manager", "Title")
$inputBoxes = @{} # Hashtable to store references to textboxes
$yPositionInAttributePanel = $panelPadding # Starting Y position inside the panel
$kelownaCheckBox = $null # Initialize Kelowna checkbox variable

# --- Loop to create Label, TextBox, and Button for each attribute ---
foreach ($attr in $attributes) {
    $currentAttr = $attr # Store attribute name for closure in button click event
    $controlY = $yPositionInAttributePanel
    $labelY = $controlY + (($controlRowHeight - $defaultFont.Height) / 2) # Center label text vertically within row height

    # Label
    $label = New-Object System.Windows.Forms.Label
    $labelText = switch ($attr) {
        'Province'   { 'Province (ON, BC, etc.):' }
        'Manager'    { 'Manager (Email):' }
        'Department' { 'Department:' }
        'Title'      { 'Title:' }
        default      { "$attr{}:" }
    }
    $label.Text = $labelText
    $label.Location = New-Object System.Drawing.Point($internalPadding, [int]$labelY) # Start near left edge
    $label.Size = New-Object System.Drawing.Size($labelWidth, $controlRowHeight) # Use row height
    $label.TextAlign = [System.Drawing.ContentAlignment]::MiddleRight
    $attributePanel.Controls.Add($label)

    # Textbox
    $textbox = New-Object System.Windows.Forms.TextBox
    $textbox.Location = New-Object System.Drawing.Point($textboxX, $controlY)
    # v2.32 Fix: Use corrected $textboxWidth calculation. Ensure height is numeric.
    $textbox.Size = New-Object System.Drawing.Size($textboxWidth, ([int]$controlRowHeight - 4)) # Make slightly shorter than row height
    $textbox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right # Anchor left/right for width
    $attributePanel.Controls.Add($textbox)
    $inputBoxes[$currentAttr] = $textbox # Store reference

    # Update Button
    $button = New-Object System.Windows.Forms.Button
    $button.Size = New-Object System.Drawing.Size($buttonWidth, $controlRowHeight)
    $button.Location = New-Object System.Drawing.Point($buttonX, $controlY)
    $button.Text = "Update $currentAttr"
    $button.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Right # Anchor to right
    $attributePanel.Controls.Add($button)

    # Increment Y position for the start of the next control row
    $yPositionInAttributePanel = $controlY + $controlRowHeight + $controlPadding

    # --- Special handling for Province: Add Kelowna checkbox ---
    if ($attr -eq 'Province') {
        # v2.32 Fix: Removed unused $provinceTextBox assignment

        # Add space for checkbox REGARDLESS of visibility initially
        $checkboxStartY = $yPositionInAttributePanel # Checkbox starts where next row would have
        $yPositionInAttributePanel = $checkboxStartY + $checkboxHeight + $controlPadding # Reserve space AFTER checkbox

        $kelownaCheckBox = New-Object System.Windows.Forms.CheckBox
        $kelownaCheckBox.Text = "Lives in Kelowna (BC Only)"
        $kelownaCheckBox.AutoSize = $true
        $kelownaCheckBox.Location = New-Object System.Drawing.Point($textboxX, $checkboxStartY) # Align with textbox start X
        $kelownaCheckBox.Visible = $false # Initially hidden
        $kelownaCheckBox.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left
        $attributePanel.Controls.Add($kelownaCheckBox)

        # Action to show/hide Kelowna checkbox based on Province text
        $provinceTextChangedAction = {
            param($eventSender, $e)
            $provinceText = $eventSender.Text.Trim()
            if ($null -ne $kelownaCheckBox) {
                $needsCheckbox = ($provinceText -ieq 'BC')
                if ($kelownaCheckBox.Visible -ne $needsCheckbox) {
                    $kelownaCheckBox.Visible = $needsCheckbox
                    if (-not $needsCheckbox) {
                        $kelownaCheckBox.Checked = $false # Uncheck if province changes from BC
                    }
                }
            }
        }
        $textbox.Add_TextChanged($provinceTextChangedAction)
    } # --- End special Province handling ---

    # --- Individual Update Button Click Logic --- (Logic largely unchanged, logging/error handling refined)
    $button.Add_Click({
        param($eventSender, $e)
        $clickedButton = $eventSender
        $attrToUpdate = $clickedButton.Text -replace "Update ",""
        $newValue = $inputBoxes[$attrToUpdate].Text.Trim()

        # Allow clearing Manager by leaving it blank AND explicitly clicking the 'Update Manager' button
        if ($attrToUpdate -ne 'Manager' -and [string]::IsNullOrWhiteSpace($newValue)) {
            [System.Windows.Forms.MessageBox]::Show("Please enter a value for '$attrToUpdate'.", "Input Required", "OK", "Warning")
            $inputBoxes[$attrToUpdate].Focus()
            return
        }

        $usersRaw = $userListTextBox.Text
        $usersToProcess = $usersRaw.Split([Environment]::NewLine, [StringSplitOptions]::RemoveEmptyEntries) | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

        if ($usersToProcess.Count -eq 0) {
            [System.Windows.Forms.MessageBox]::Show("Please enter at least one user email or SamAccountName in the list.", "Input Required", "OK", "Warning")
            $userListTextBox.Focus()
            return
        }

        $form.Cursor = [System.Windows.Forms.Cursors]::WaitCursor # Indicate processing

        $globalSuccessCount = 0
        $globalFailureCount = 0
        $globalErrorMessages = [System.Collections.Generic.List[string]]::new()

        foreach ($userIdentifier in $usersToProcess) {
            $user = Get-ADUserByEmailOrSamAccountName -EmailOrSamAccountName $userIdentifier
            if (-not $user) {
                $globalFailureCount++
                $globalErrorMessages.Add("User '$userIdentifier' not found.")
                continue # Skip to the next user
            }

            $isKelowna = $false
            if ($attrToUpdate -eq 'Province' -and $null -ne $kelownaCheckBox -and $kelownaCheckBox.Visible) {
                $isKelowna = $kelownaCheckBox.Checked
            }

            # If clearing manager, newValue is empty string, Update-UserAttribute handles it
            $updateResult = Update-UserAttribute -UserObject $user -AttributeName $attrToUpdate -NewValue $newValue -IsKelownaResident $isKelowna

            if ($updateResult.Success) {
                $globalSuccessCount++
                $logEntryParts = [System.Collections.Generic.List[string]]::new()
                $logEntryParts.Add("========================================================================") # Consistent length
                $logEntryParts.Add("Date/Time   : $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")")
                $logEntryParts.Add("Admin User  : $($env:USERNAME)")
                $logEntryParts.Add("Target User : $($user.SamAccountName)")
                $logEntryParts.Add("") # Blank line

                $provinceChangeHeader = $null

                # Log Order: Add AD Change Header & Detail
                if (-not [string]::IsNullOrWhiteSpace($updateResult.ADChangeDetail)) {
                     $logEntryParts.Add("Local AD Changes Made:")
                     $logEntryParts.Add($updateResult.ADChangeDetail)
                     $logEntryParts.Add("")
                }

                # Add Group Changes (if any)
                if ($null -ne $updateResult.GroupChangeDetails -and $updateResult.GroupChangeDetails.Count -gt 0) { # v2.32 Fix: Use -gt 0
                     $provinceChangeHeader = "(Province: '$($updateResult.OldProvince)' -> '$($updateResult.NewProvince)')"
                     $logEntryParts.Add("Exchange Group Changes $provinceChangeHeader{}:")
                     $logEntryParts.AddRange($updateResult.GroupChangeDetails)
                     # No blank line needed here, footer comes next
                }

                $logEntryParts.Add("========================================================================") # Consistent length
                $logEntryParts.Add("")

                try {
                    Add-Content -Path $logFile -Value ($logEntryParts -join [Environment]::NewLine) -ErrorAction Stop
                } catch {
                    Write-Warning "Failed to write success log entry for $($user.SamAccountName). Error: $($_.Exception.Message)"
                    $globalErrorMessages.Add("WARNING: Failed to write success log entry for $($user.SamAccountName).")
                }

            } else { # Update failed
                $globalFailureCount++
                $errorMessageText = if($updateResult.ErrorMessage){$updateResult.ErrorMessage}else{"Unknown error during update."}
                $globalErrorMessages.Add("Error for '$userIdentifier' ($attrToUpdate): $errorMessageText")
                $errorLogEntry = @"
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
Date/Time   : $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Admin User  : $($env:USERNAME)
Target User : $($user.SamAccountName)
ERROR Updating $attrToUpdate{}: $errorMessageText
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

"@
                try {
                    Add-Content -Path $logFile -Value $errorLogEntry -ErrorAction Stop
                } catch {
                     Write-Warning "Failed to write ERROR log entry for $($user.SamAccountName). Error: $($_.Exception.Message)"
                     $globalErrorMessages.Add("WARNING: Failed to write ERROR log entry for $($user.SamAccountName).")
                }
            }
        } # End foreach user

        $form.Cursor = [System.Windows.Forms.Cursors]::Default # Reset cursor

        # --- Feedback and Cleanup ---
        $updateErrorsForThisAttribute = $globalErrorMessages.Where({$_ -notlike 'User * not found.' -and $_ -notlike 'WARNING:*'})
        if ($globalSuccessCount -gt 0 -and $updateErrorsForThisAttribute.Count -eq 0) {
             # Only clear if successful AND no other errors occurred for THIS attribute update batch
             $inputBoxes[$attrToUpdate].Clear()
             if ($attrToUpdate -eq 'Province' -and $null -ne $kelownaCheckBox) {$kelownaCheckBox.Checked = $false; $kelownaCheckBox.Visible = $false}
             # Don't reset modified flag for manager here, user might want to clear another user's manager next
             # if($attrToUpdate -eq 'Manager') { $inputBoxes[$attrToUpdate].Modified = $false }
        }

        # Combine all feedback
        $usersFailedLookupCount = $globalErrorMessages.Where({$_ -like 'User * not found.'}).Count
        $updateErrorsOnly = $globalErrorMessages.Where({$_ -notlike 'User * not found.' -and $_ -notlike 'WARNING:*'})
        $logWarnings = $globalErrorMessages.Where({$_ -like 'WARNING:*'})
        $hasUpdateErrors = $updateErrorsOnly.Count -gt 0
        $hasLookupErrors = $usersFailedLookupCount -gt 0
        $hasWarnings = $logWarnings.Count -gt 0

        $message = ""
        $title = "Update Result"
        $icon = [System.Windows.Forms.MessageBoxIcon]::Information

        if ($globalSuccessCount -gt 0) { $message += "Update attempts for '$attrToUpdate' completed.`nSuccess count (attribute updates): $globalSuccessCount`n" }

        if ($hasUpdateErrors -or $hasLookupErrors -or $hasWarnings) {
            $icon = [System.Windows.Forms.MessageBoxIcon]::Warning
            if ($hasUpdateErrors -or $hasLookupErrors) {
                 $totalErrors = $usersFailedLookupCount + $updateErrorsOnly.Count
                 $message += "`nTotal Failures/Errors: $totalErrors"
                 if($hasLookupErrors){ $message += "`n- Users not found: $usersFailedLookupCount"}
                 if($hasUpdateErrors){ $message += "`n- Update Errors: $($updateErrorsOnly.Count)" }
                 $message += "`n$($updateErrorsOnly -join "`n")`n" # Add specific update errors
            }
            if ($hasWarnings) {
                 $message += "`nLog Warnings:`n$($logWarnings -join "`n")"
            }
            $title = "Update Result with Issues"
        } elseif ($globalSuccessCount -eq 0 -and -not $hasUpdateErrors -and -not $hasLookupErrors -and -not $hasWarnings) {
            # This case means no users found, or no operation attempted?
            $message += "No successful updates performed."
            if ($globalFailureCount -gt 0) {$message += " Encountered $globalFailureCount issues (e.g., user not found)."}
        } else { # Only success and maybe log warnings
            $title = "Update Successful"
            if($hasWarnings) {
                $message += "`nLog Warnings:`n$($logWarnings -join "`n")"
                $title = "Update Successful with Log Warnings"
                $icon = [System.Windows.Forms.MessageBoxIcon]::Warning
            }
        }

        [System.Windows.Forms.MessageBox]::Show($message.Trim(), $title, "OK", $icon)

        Update-LogDisplay
    }) # End Add_Click

} # End foreach attribute loop

# Calculate and set the final panel height AFTER adding all controls
# $yPositionInAttributePanel now represents the Y coordinate *below* the last control's reserved space
$attributePanelHeight = $yPositionInAttributePanel # Use the final Y position as the basis for height
$attributePanel.Size = New-Object System.Drawing.Size($panelWidth, $attributePanelHeight) # Set height explicitly (Width should be handled by TableLayout parent)
# Set Width explicitly too, to be safe before render
$attributePanel.Width = $panelWidth
#endregion Attribute Input Panel

#region Button Panel (Update All) (Panel 3)
$buttonPanel = New-Object System.Windows.Forms.Panel
$buttonPanelHeight = $controlRowHeight + (2*$controlPadding) # Height for one button row + padding
$buttonPanel.Size = New-Object System.Drawing.Size($panelWidth, $buttonPanelHeight) # Use panel width
$buttonPanel.BorderStyle = [System.Windows.Forms.BorderStyle]::None
$buttonPanel.Margin = New-Object System.Windows.Forms.Padding($panelPadding, 0, $panelPadding, $panelPadding)
$mainTableLayout.Controls.Add($buttonPanel, 0, 2)

$updateAllBtn = New-Object System.Windows.Forms.Button
# v2.32 Fix: Cast height calculation to ensure numeric args for Size
$updateAllBtn.Size = New-Object System.Drawing.Size(260, ([int]$controlRowHeight + 4)) # Slightly taller button
$updateAllBtnX = ($buttonPanel.ClientSize.Width - $updateAllBtn.Width) / 2 # Center the button
$updateAllBtn.Location = New-Object System.Drawing.Point([int]$updateAllBtnX, $controlPadding)
$updateAllBtn.Text = "Update All Filled Attributes"
$updateAllBtn.Anchor = [System.Windows.Forms.AnchorStyles]::Top # Keep simple anchor
$buttonPanel.Controls.Add($updateAllBtn)

# --- Update All Button Click Logic --- (Logic largely unchanged, logging/error handling refined)
$updateAllBtn.Add_Click({
    $usersRaw = $userListTextBox.Text
    $usersToProcess = $usersRaw.Split([Environment]::NewLine, [StringSplitOptions]::RemoveEmptyEntries) | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

    if ($usersToProcess.Count -eq 0) {
        [System.Windows.Forms.MessageBox]::Show("Please enter at least one user email or SamAccountName in the list.", "Input Required", "OK", "Warning")
        $userListTextBox.Focus()
        return
    }

    $attributesToUpdate = @{}
    $anyFieldsFilled = $false
    foreach ($attr in $attributes) {
        $newValue = $inputBoxes[$attr].Text.Trim()
        # Consider a field "filled" if it's not Manager and has text, OR if it IS Manager and has been modified (even if now empty)
        if (($attr -ne 'Manager' -and -not [string]::IsNullOrWhiteSpace($newValue)) -or ($attr -eq 'Manager' -and $inputBoxes[$attr].Modified)) {
             $attributesToUpdate[$attr] = $newValue
             $anyFieldsFilled = $true
        }
    }

    if (-not $anyFieldsFilled) {
        [System.Windows.Forms.MessageBox]::Show("Please enter a value in at least one attribute field (or clear the Manager field) to update.", "No Values Entered", "OK", "Information")
        return
    }

    $form.Cursor = [System.Windows.Forms.Cursors]::WaitCursor # Indicate processing

    $globalSuccessCount = 0 # Counts successful attribute *updates* (not users)
    $globalFailureCount = 0 # Counts attribute *update* failures + user lookup failures
    $globalErrorMessages = [System.Collections.Generic.List[string]]::new()
    $processedSomething = $false # Tracks if any user/attribute combination was processed

    # Determine Kelowna status ONCE before looping through users, only if Province is being updated
    $isKelownaForUpdateAll = $false
    if ($attributesToUpdate.ContainsKey('Province')) {
        $isKelownaForUpdateAll = ($null -ne $kelownaCheckBox -and $inputBoxes['Province'].Text.Trim() -ieq 'BC' -and $kelownaCheckBox.Checked)
    }

    foreach ($userIdentifier in $usersToProcess) {
        $user = Get-ADUserByEmailOrSamAccountName -EmailOrSamAccountName $userIdentifier
        if (-not $user) {
            $globalFailureCount++
            $globalErrorMessages.Add("User '$userIdentifier' not found.")
            $processedSomething = $true
            continue # Skip to next user
        }

        $currentUserADChanges = [System.Collections.Generic.List[string]]::new()
        $currentUserGroupChanges = [System.Collections.Generic.List[string]]::new()
        $currentUserErrors = [System.Collections.Generic.List[string]]::new()
        $provinceChangeHeader = $null
        $currentUserSuccessCount = 0

        foreach ($attrEntry in $attributesToUpdate.GetEnumerator()) {
            $attrName = $attrEntry.Name
            $attrValue = $attrEntry.Value
            $processedSomething = $true

            $isKelownaParamValue = if ($attrName -eq 'Province') { $isKelownaForUpdateAll } else { $false }
            $updateResult = Update-UserAttribute -UserObject $user -AttributeName $attrName -NewValue $attrValue -IsKelownaResident $isKelownaParamValue

            if ($updateResult.Success) {
                $currentUserSuccessCount++ # Count success per attribute for this user
                if (-not [string]::IsNullOrWhiteSpace($updateResult.ADChangeDetail)) {
                    $currentUserADChanges.Add($updateResult.ADChangeDetail)
                }
                if ($null -ne $updateResult.GroupChangeDetails -and $updateResult.GroupChangeDetails.Count -gt 0) { # v2.32 Fix: Use -gt 0
                    $currentUserGroupChanges.AddRange($updateResult.GroupChangeDetails)
                    # Determine header only if group changes occurred
                    $provinceChangeHeader = "(Province: '$($updateResult.OldProvince)' -> '$($updateResult.NewProvince)')"
                }
            } else {
                $globalFailureCount++ # Increment global failure count
                $errorMessageText = if($updateResult.ErrorMessage){$updateResult.ErrorMessage}else{"Unknown error during update."}
                $currentUserErrors.Add("Error updating $attrName{}: $errorMessageText")
            }
        } # End attribute loop for this user

        $globalSuccessCount += $currentUserSuccessCount # Add user's successes to global count

        # Log if changes/errors occurred for this user
        if ($currentUserADChanges.Count -gt 0 -or $currentUserGroupChanges.Count -gt 0 -or $currentUserErrors.Count -gt 0) { # v2.32 Fix: Use -gt 0
            $logEntryParts = [System.Collections.Generic.List[string]]::new()
            $isErrorEntry = $currentUserErrors.Count -gt 0
            $headerFooterLine = if ($isErrorEntry) { "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!" } else { "========================================================================" }
            $headerText = if($isErrorEntry) { "UPDATE ALL Summary (with Errors)" } else { "UPDATE ALL Summary" }

            $logEntryParts.Add($headerFooterLine)
            $logEntryParts.Add("Date/Time   : $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")")
            $logEntryParts.Add("Admin User  : $($env:USERNAME)")
            $logEntryParts.Add("Target User : $($user.SamAccountName)")
            $logEntryParts.Add("Summary     : $headerText") # Added summary line
            $logEntryParts.Add("")

            # Log Order: Add AD Changes First
            if ($currentUserADChanges.Count -gt 0) { # v2.32 Fix: Use -gt 0
                 $logEntryParts.Add("Local AD Changes Made")
                 $logEntryParts.AddRange($currentUserADChanges)
                 $logEntryParts.Add("")
            }

            # Add Group Changes
            if ($currentUserGroupChanges.Count -gt 0) { # v2.32 Fix: Use -gt 0
                 # Check if header was set (it's only set if group changes actually happened)
                 $effectiveHeader = if ($provinceChangeHeader) { $provinceChangeHeader } else { "" }
                 $logEntryParts.Add("Exchange Group Changes $effectiveHeader{}")
                 $logEntryParts.AddRange($currentUserGroupChanges)
                 $logEntryParts.Add("")
            }

            # Add Errors
             if ($currentUserErrors.Count -gt 0) { # v2.32 Fix: Use -gt 0
                $globalErrorMessages.AddRange($currentUserErrors) # Add user-specific errors to global list
                $logEntryParts.Add("Errors Encountered:")
                $logEntryParts.AddRange($currentUserErrors)
                $logEntryParts.Add("")
             }

            $logEntryParts.Add($headerFooterLine)
            $logEntryParts.Add("")

            try {
                Add-Content -Path $logFile -Value ($logEntryParts -join [Environment]::NewLine) -ErrorAction Stop
            } catch {
                 Write-Warning "Failed to write log summary for $($user.SamAccountName) (Update All). Error: $($_.Exception.Message)"
                 $globalErrorMessages.Add("WARNING: Failed to write log summary entry for $($user.SamAccountName).")
            }
        } # End if changes/errors occurred for user

    } # End user loop

    $form.Cursor = [System.Windows.Forms.Cursors]::Default # Reset cursor

    # --- Feedback and Cleanup --- (Refined Feedback Logic)
    $usersFailedLookup = $globalErrorMessages.Where({$_ -like 'User * not found.'})
    $updateErrorsOnly = $globalErrorMessages.Where({$_ -notlike 'User * not found.' -and $_ -notlike 'WARNING:*'})
    $logWarnings = $globalErrorMessages.Where({$_ -like 'WARNING:*'})

    $hasUpdateErrors = $updateErrorsOnly.Count -gt 0
    $hasLookupErrors = $usersFailedLookup.Count -gt 0
    $hasWarnings = $logWarnings.Count -gt 0

    # Clear fields only if there were successes AND NO update errors (ignore lookup errors for clearing)
    if ($globalSuccessCount -gt 0 -and -not $hasUpdateErrors) {
        foreach ($attrName in $attributesToUpdate.Keys) {
            $inputBoxes[$attrName].Clear()
             if($attrName -eq 'Manager') { $inputBoxes[$attrName].Modified = $false } # Reset modified flag
        }
        # Reset Kelowna checkbox if Province was successfully updated without error
        if ($attributesToUpdate.ContainsKey('Province') -and $null -ne $kelownaCheckBox) {
            $kelownaCheckBox.Checked = $false
            $kelownaCheckBox.Visible = $false
        }
    }

    # Construct Feedback Message
    if ($processedSomething) {
        $message = ""
        $title = "Update All Result"
        $icon = [System.Windows.Forms.MessageBoxIcon]::Information

        if ($globalSuccessCount -gt 0) { $message += "Completed $globalSuccessCount attribute update(s) across processed users.`n" }

        if ($hasUpdateErrors -or $hasLookupErrors -or $hasWarnings) {
            $icon = [System.Windows.Forms.MessageBoxIcon]::Warning
             if ($hasUpdateErrors -or $hasLookupErrors) {
                 $totalErrors = $usersFailedLookup.Count + $updateErrorsOnly.Count
                 $message += "`nTotal Failures/Errors: $totalErrors"
                 if($hasLookupErrors){ $message += "`n- Users not found: $($usersFailedLookup.Count)"}
                 if($hasUpdateErrors){ $message += "`n- Update Errors: $($updateErrorsOnly.Count)" }
                 # List specific update errors
                 if($hasUpdateErrors){ $message += "`n$($updateErrorsOnly -join "`n")"}
                 $message += "`n"
            }
             if ($hasWarnings) {
                 $message += "`nLog Warnings:`n$($logWarnings -join "`n")"
            }
            $title = "Update All Result with Issues"

        } elseif ($globalSuccessCount -eq 0) {
             $message += "No successful updates were performed."
             if ($globalFailureCount -gt 0) {$message += " Encountered $globalFailureCount issues (e.g., users not found). See log."}
             $icon = [System.Windows.Forms.MessageBoxIcon]::Warning # Indicate nothing happened or only errors
        } else { # Only success and maybe log warnings
            $title = "Update All Successful"
            if($hasWarnings) {
                $message += "`nLog Warnings:`n$($logWarnings -join "`n")"
                $title = "Update All Successful with Log Warnings"
                $icon = [System.Windows.Forms.MessageBoxIcon]::Warning
            }
        }

        [System.Windows.Forms.MessageBox]::Show($message.Trim(), $title, "OK", $icon)
    } else {
         # Case where nothing was processed (e.g., no users entered, or no fields filled - though latter is checked earlier)
         [System.Windows.Forms.MessageBox]::Show("No operations were performed. Please check user list and attribute fields.", "No Action Taken", "OK", "Information")
    }
    Update-LogDisplay
})
#endregion Button Panel

#region Log Display Panel (Panel 4)
$logPanel = New-Object System.Windows.Forms.Panel
$logPanel.Dock = [System.Windows.Forms.DockStyle]::Fill
$logPanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
$logPanel.Margin = New-Object System.Windows.Forms.Padding($panelPadding, 0, $panelPadding, $panelPadding)
$mainTableLayout.Controls.Add($logPanel, 0, 3)

# Label for the log box
$logLabel = New-Object System.Windows.Forms.Label
$logLabel.Text = "Recent Activity Log (Latest First):"
$logLabel.Dock = [System.Windows.Forms.DockStyle]::Top
$logLabel.AutoSize = $true
$logLabel.Padding = New-Object System.Windows.Forms.Padding($internalPadding, $controlPadding, $internalPadding, $controlPadding) # Consistent padding
$logPanel.Controls.Add($logLabel)

# Note at the bottom
$logNoteHeight = 20
$logNote = New-Object System.Windows.Forms.Label
$logNote.Text = "Full audit trail saved in AD_Attribute_Audit.log in the script directory."
$logNote.Dock = [System.Windows.Forms.DockStyle]::Bottom
$logNote.Height = $logNoteHeight
$logNote.Padding = New-Object System.Windows.Forms.Padding($internalPadding, $controlPadding, $internalPadding, $controlPadding)
$logNote.TextAlign = [System.Drawing.ContentAlignment]::MiddleLeft
$logPanel.Controls.Add($logNote)

# Log display textbox
$logBox = New-Object System.Windows.Forms.TextBox
$logBox.Dock = [System.Windows.Forms.DockStyle]::Fill
$logBox.Margin = New-Object System.Windows.Forms.Padding($internalPadding, 0, $internalPadding, 0)
$logBox.Multiline = $true
$logBox.ReadOnly = $true
$logBox.ScrollBars = "Vertical"
$logBox.Font = New-Object System.Drawing.Font("Consolas", 8)
$logPanel.Controls.Add($logBox)
$logBox.BringToFront() # Ensure it's drawn over the panel background

#endregion Log Display Panel

#region Helper Functions (Post-GUI elements)

function Update-LogDisplay {
    if (-not (Test-Path $logFile)) {
        $logBox.Text = "Log file '$logFile' not found or no entries yet."
        return
    }
    try {
        # Show slightly more lines to better fill the box if available
        $linesToShow = 200
        $logContent = Get-Content $logFile -Tail $linesToShow -ErrorAction SilentlyContinue
        if ($logContent) {
            [array]::Reverse($logContent)
            $logBox.Text = $logContent -join [Environment]::NewLine
            $logBox.SelectionStart = 0 # Go to top
            $logBox.ScrollToCaret()   # Scroll to top
        } else {
            $logBox.Text = "--- Log file '$($logFile.Split('\')[-1])' is empty ---"
        }
    } catch {
        $logBox.Text = "Error reading log file '$logFile': $($_.Exception.Message)"
    }
}

#endregion Helper Functions

#region Form Closing Event
$form.Add_FormClosing({
    param($eventSender, $e)
    # Gracefully disconnect from services
    Write-Host "Disconnecting from services..."
    $exoSessionVar = Get-Variable ExoPSSession -ErrorAction SilentlyContinue
    if ($null -ne $exoSessionVar -and $null -ne $exoSessionVar.Value) {
        Disconnect-ExchangeOnline -Confirm:$false -ErrorAction SilentlyContinue
        Write-Host "Disconnected from Exchange Online."
    }
    if(Get-MgContext -ErrorAction SilentlyContinue){
        Disconnect-MgGraph -ErrorAction SilentlyContinue
        Write-Host "Disconnected from Microsoft Graph."
    }
})
#endregion Form Closing Event

#region Form Display
# Initial log display and show the form
$form.Add_Shown({ $form.Activate(); Update-LogDisplay })
$form.Topmost = $false
[void]$form.ShowDialog()
#endregion

#endregion GUI Setup

Write-Host "Script finished."