#TYPE Selected.Microsoft.ActiveDirectory.Management.ADUser
"UserPrincipalName","extensionAttribute1"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"matti.kali<PERSON><PERSON>@QHRtech.com",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"amanda.kore<PERSON>@QHRtech.com",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>","0"
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
"<EMAIL>",
