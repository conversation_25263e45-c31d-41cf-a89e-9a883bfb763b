#Title: DeviceName-to-DeviceID.ps1
#Description: This script was made to be able to read device names from a CSV, then search Intune for the Device ID's and export them into the same CSV

#Author: <PERSON><PERSON>
#Version: 1.0

#PreReqs
#None :) The Script checks for everything automatically, It may take a little while to run and install everything the first time

function Get-ValidPath {
    $isValidPath = $false
    while (-not $isValidPath) {
        $inputPath = Read-Host "Please enter the full path (Include Double Quotes)"
        if ($inputPath -match '^\".*\"$' -and (Test-Path $inputPath.Trim('"'))) {
            $inputPath = $inputPath.Trim('"')
            $isValidPath = $true
        } else {
            Write-Host "Please enter a valid path enclosed in double quotes."
        }
    }
    return $inputPath
}

# Check if the Microsoft.Graph module is installed
$moduleInstalled = Get-Module -Name Microsoft.Graph -ListAvailable

if (-not $moduleInstalled) {
    Write-Host "Microsoft.Graph module not found. Installing..."
    
    # Install the Microsoft.Graph module
    Install-Module -Name Microsoft.Graph -Force -Scope CurrentUser
    Write-Host "Microsoft.Graph module installed."
}

Write-Host "Importing Microsoft.Graph module..."

# Import the Microsoft.Graph module
Import-Module Microsoft.Graph -Cmdlet Get-MgUser, Connect-MgGraph, Get-MgUserManager
Write-Host "Microsoft.Graph module imported."

# Check if the maximumfunctioncount setting exists in the PowerShell profile
$profilePath = $PROFILE
$settingExists = Select-String -Path $profilePath -Pattern '^\s*\$maximumfunctioncount\s*='

# If the setting doesn't exist, add it to the profile
if (-not $settingExists) {
    # Set the new maximum function count
    $maximumfunctioncount = 32768

    # Add the setting to the profile
    Add-Content -Path $profilePath -Value "`$maximumfunctioncount = $maximumfunctioncount"
    
    Write-Host "Function capacity limit set to $maximumfunctioncount in profile."
}

# Authenticate to Microsoft Graph
# You can use your own method to authenticate, e.g., with Connect-MgGraph, app registration, or other methods.
Connect-MgGraph -TenantId qhrtech.com -Scopes "User.Read.All" -NoWelcome

# Function to get a valid input file path
$inputFileLocation = Get-ValidPath
Write-Host "Reading device names from the input file..."

# Initialize an empty array to store the results
$results = @()

# Read device names from the input file
$deviceNames = Get-Content $inputFileLocation

Write-Host "Searching Intune for devices..."

# Loop through each device name
foreach ($deviceName in $deviceNames) {
    Write-Host "Searching for device: $deviceName"
    
    # Search for the device based on its name using the Microsoft Graph Intune API
    $device = Get-MgDevice -Filter "displayName eq '$deviceName'"

    if ($device) {
        Write-Host "Device found: $deviceName"

        # Get the Device ID of the found device
        $deviceID = $device.id

        # Create a custom object with the device name and its ID
        $result = [PSCustomObject]@{
            "DeviceName" = $deviceName
            "DeviceID" = $deviceID
        }

        # Add the result to the results array
        $results += $result
    } else {
        Write-Host "Device not found: $deviceName"
    }
}

Write-Host "Exporting results to CSV..."

# Export the results to a CSV file
$results | Export-Csv -Path $inputFileLocation -NoTypeInformation

Write-Host "CSV export complete. Data saved to $inputFileLocation"
