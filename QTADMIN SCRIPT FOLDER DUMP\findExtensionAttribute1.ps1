﻿
$userAccounts = Get-ADUser -Filter {Enabled -eq $true -and PasswordNeverExpires -eq $False} -Properties * | Where-Object {$_.UserPrincipalName -match "@qhrtech.com" -and $_.UserPrincipalName -notmatch "da-" -and $_.extensionAttribute1 -match "1" -or $_.extensionAttribute1 -match "2" -or $_.extensionAttribute1 -match "3" -or $_.extensionAttribute1 -match "4" -or $_.extensionAttribute1 -match "5"} | select -Property UserPrincipalName, extensionAttribute1
$userAccounts

$userAccounts | export-csv C:\Scripts\extensionOutput.csv
