#Title: IntuneGroup-BulkRemove-And-BulkExportInfo.ps1
#Description: This script interacts with Intune/AAD groups using MSGraph, allowing 
# bulk export of device info, bulk removal from groups, and bulk import from CSV

#Author: <PERSON><PERSON>
#Updated by: <PERSON><PERSON>
#Version: 1.7
#Date Updated: 2024-08-28 

# Function to validate user-provided path
function Get-ValidPath {
    $isValidPath = $false
    while (-not $isValidPath) {
        $inputPath = Read-Host "Please enter the directory path where you want to save/load the CSV file"
        $inputPath = $inputPath.Trim()

        try {
            [System.IO.Path]::GetFullPath($inputPath) | Out-Null
            $isValidPath = $true
        } catch {
            Write-Host "Please enter a valid path." -ForegroundColor Red
        }
    }
    return $inputPath
}

# Function to get user's desired action
function Get-UserAction {
    $validOptions = @(
        @{ Number = 1; Action = "bulk remove all users" },
        @{ Number = 2; Action = "bulk export of all users Device ID and Device Name" },
        @{ Number = 3; Action = "import csv to add users to group" },
        @{ Number = 4; Action = "exit" }
    )
    $selectedAction = $null

    while ($true) {
        Write-Host "Choose an action:" -ForegroundColor Cyan
        $validOptions | ForEach-Object { Write-Host "$($_.Number) - $($_.Action)" } # Corrected line

        $selectedNumber = Read-Host "Enter the number of the action you want to perform"

        try {
            $selectedNumber = [int]$selectedNumber
            $selectedAction = $validOptions | Where-Object { $_.Number -eq $selectedNumber } | Select-Object -ExpandProperty Action
            if ($selectedAction) { break } 
        } catch {
            # Ignore invalid input and continue the loop
        }

        Write-Host "Invalid input. Please enter a number from the list." -ForegroundColor Red
    }

    return $selectedAction
}

# Function to get the user associated with a device
function Get-UserFromDevice {
    param(
        [Parameter(Mandatory=$true)]
        [string]$DeviceId
    )

    # Get the device object
    $device = Get-MgDevice -DeviceId $DeviceId -ErrorAction SilentlyContinue

    if ($device) {
        # If the device is enrolled to a user, get the user ID
        if ($device.UserId) {
            return Get-MgUser -UserId $device.UserId
        } else {
            Write-Host "Device $($DeviceId) is not enrolled to a user." -ForegroundColor Yellow
            return $null
        }
    } else {
        Write-Host "Device $($DeviceId) not found." -ForegroundColor Yellow
        return $null
    }
}

# Install Microsoft.Graph module if needed
$moduleInstalled = Get-Module -Name Microsoft.Graph -ListAvailable
if (-not $moduleInstalled) {
    Write-Host "Microsoft.Graph module not found. Installing..." -ForegroundColor Yellow
    Install-Module -Name Microsoft.Graph -Force -Scope CurrentUser
    Write-Host "Microsoft.Graph module installed." -ForegroundColor Green
}

# Import the module
Import-Module Microsoft.Graph

# Authenticate to Microsoft Graph
Connect-MgGraph -TenantId qhrtech.com -Scopes "User.ReadWrite.All", "Device.ReadWrite.All", "Group.ReadWrite.All", "GroupMember.ReadWrite.All" -NoWelcome

# Main script loop
while ($true) {

    # Get Intune group name from user
    $intuneGroupName = Read-Host "Please enter the Intune group name"

    # Search for the group, with error handling
    try {
        $intuneGroup = Get-MgGroup -Filter "displayName eq '$intuneGroupName'"
    } catch {
        Write-Host "Error finding the group. Please check the name and try again." -ForegroundColor Red
        continue 
    }

    if (-not $intuneGroup) {
        Write-Host "Intune group '$intuneGroupName' not found." -ForegroundColor Red
        continue 
    }

    Write-Host "Intune group '$intuneGroupName' found." -ForegroundColor Green

    # Get devices in the group
    $groupMembers = Get-MgGroupMember -GroupId $intuneGroup.Id
    $devicesInGroup = @()
    foreach ($member in $groupMembers) {
        $device = Get-MgDevice -DeviceId $member.Id -ErrorAction SilentlyContinue
        if ($device) {
            $devicesInGroup += New-Object PSObject -Property @{
                Group = $intuneGroup.DisplayName
                Name = $device.DisplayName
                Id = $device.Id
            }
        }
    }

    $devicesInGroup | ConvertTo-Json | Write-Host

    # Display device details if any found
    if ($devicesInGroup) {
        Write-Host "Devices associated with group '$intuneGroupName':" -ForegroundColor Cyan
        $devicesInGroup | Select-Object Group, Name, Id | Format-Table -AutoSize
    } else {
        Write-Host "No devices found for the group '$intuneGroupName'." -ForegroundColor Yellow
    }

    # Get user's desired action
    $selectedAction = Get-UserAction

    # Perform the chosen action
    switch ($selectedAction) {
        "bulk remove all users" {
            $confirmation = Read-Host "Are you sure you want to remove all devices from the group '$intuneGroupName'? (y/n)"
            if ($confirmation -eq 'y') {
                Write-Host "Removing all devices in the group: $intuneGroupName"
                foreach ($device in $devicesInGroup) {
                    Remove-MgGroupMemberByRef -GroupId $intuneGroup.Id -DirectoryObjectId $device.Id
                }
                Write-Host "Removal successful!" -ForegroundColor Green
            } else {
                Write-Host "Removal cancelled." -ForegroundColor Yellow
            }
        }
        "bulk export of all users Device ID and Device Name" {
            Write-Host "Exporting Device ID for all users in the group: $intuneGroupName"

            # Get directory path from user
            $exportDirectory = Get-ValidPath

            # Get filename from user
            $exportFilename = Read-Host "Please enter the desired filename for the CSV file (without extension)"
            $csvFilePath = Join-Path $exportDirectory ($exportFilename + ".csv")

            try {
                # Create a custom object with the correct header
                $csvHeader = New-Object psobject -Property @{ 'version:v1.0' = '' }

                # Collect device IDs into an array
                $deviceIds = $devicesInGroup | ForEach-Object { $_.Id }

                # Create an object with the device IDs under the 'memberObjectIdOrUpn' property
                $exportData = New-Object psobject -Property @{
                    'version:v1.0' = '' 
                    'memberObjectIdOrUpn' = $deviceIds
                }

                # Export the header first, then append the data object
                $csvHeader | Export-Csv -Path $csvFilePath -NoTypeInformation
                $exportData | Export-Csv -Path $csvFilePath -Append -NoTypeInformation  

                Write-Host "Export successful!" -ForegroundColor Green
            } catch {
                Write-Host "Error exporting data. Details:" -ForegroundColor Red
                Write-Error $_.Exception.Message
            }
        }
        "import csv to add users to group" {
            Write-Host "Importing users from CSV to group: $intuneGroupName"

            # Get directory path from the user
            $importDirectory = Get-ValidPath

            # Get filename from the user
            $importFilename = Read-Host "Please enter the filename for the CSV file (with extension)"
            $csvFilePath = Join-Path $importDirectory $importFilename

            try {
                $importData = Import-Csv -Path $csvFilePath -Header @("","memberObjectIdOrUpn") | Select-Object -Skip 1
                $usersAdded = 0
                foreach ($user in $importData) {
                    if (![string]::IsNullOrEmpty($user.memberObjectIdOrUpn)) {
                        try {
                            Write-Host "Processing user with ID/UPN: $($user.memberObjectIdOrUpn)" -ForegroundColor Cyan 

                            # Try to get the user by ID first
                            $userObject = Get-MgUser -UserId $user.memberObjectIdOrUpn -ErrorAction SilentlyContinue

                            # If not found by ID, try to get by User Principal Name (UPN)
                            if (-not $userObject) {
                                $userObject = Get-MgUser -Filter "userPrincipalName eq '$($user.memberObjectIdOrUpn)'"
                            }

                            if ($userObject) {
                                try {
                                    # Add the user to the group using their user ID
                                    New-MgGroupMember -GroupId $intuneGroup.Id -UserId $userObject.Id
                                    Write-Host "User $($user.memberObjectIdOrUpn) added to group successfully." -ForegroundColor Green
                                    $usersAdded++
                                } catch {
                                    Write-Host "Error adding user $($user.memberObjectIdOrUpn) to the group. Details:" -ForegroundColor Red
                                    Write-Error $_.Exception.Message
                                }
                            } else {
                                Write-Host "User with ID/UPN $($user.memberObjectIdOrUpn) not found." -ForegroundColor Yellow
                            }
                        } catch {
                            Write-Host "Error processing user $($user.memberObjectIdOrUpn). Details:" -ForegroundColor Red
                            Write-Error $_.Exception.Message
                        }
                    } 
                }
                Write-Host "Import completed! $usersAdded users added successfully." -ForegroundColor Green
            } catch {
                Write-Host "Error importing data. Please check the file path and format." -ForegroundColor Red
            }
        }
        "exit" {
            break 
        }
        Default {
            Write-Host "Invalid action selected." -ForegroundColor Red
        }
    }
} 

Write-Host "Script execution completed." -ForegroundColor Green