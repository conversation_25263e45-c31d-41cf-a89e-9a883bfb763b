#TYPE Microsoft.Open.AzureAD.Model.User
"ExtensionProperty","DeletionTimestamp","ObjectId","ObjectType","AccountEnabled","AgeGroup","AssignedLicenses","AssignedPlans","City","CompanyName","ConsentProvidedForMinor","Country","CreationType","Department","DirSyncEnabled","DisplayName","FacsimileTelephoneNumber","GivenName","IsCompromised","ImmutableId","JobTitle","LastDirSyncTime","LegalAgeGroupClassification","Mail","MailNickName","Mobile","OnPremisesSecurityIdentifier","OtherMails","PasswordPolicies","PasswordProfile","PhysicalDeliveryOfficeName","PostalCode","PreferredLanguage","ProvisionedPlans","ProvisioningErrors","ProxyAddresses","RefreshTokensValidFromDateTime","ShowInAddressList","SignInNames","SipProxyAddress","State","StreetAddress","Surname","TelephoneNumber","UsageLocation","UserPrincipalName","UserState","UserStateChangedOn","UserType"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ee19bafb-2dd2-42c9-9c6e-64295e785c86","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Stefanie Giddens",,"Stefanie",,"tX2crJ4IpkOa14nH/AExkA==","Director of Marketing","8/15/2019 5:34:20 PM",,"<EMAIL>","stefanie.giddens","+****************","S-1-5-21-**********-**********-*********-15740","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 5:22:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Giddens","+**************** ,3527","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2452663f-39ca-4446-ad08-883f000bd356","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","QHR Technologies Inc.",,,,"Finance","True","Temi Beckley",,"Temi",,"Dfwx8gg5XEGICKkEITXUyQ==","Revenue Management Clerk","8/19/2019 2:07:16 PM",,"<EMAIL>","temi.beckley",,"S-1-5-21-**********-**********-*********-17258","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:03:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Beckley","+**************** ,7058","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"27e3f431-9f35-4582-b5d8-035b98733945","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Aaron Hartnell",,"Aaron",,"Fm+cR5hkt0q80AqcNGULvg==","Technical Lead","8/6/2019 4:03:20 PM",,"<EMAIL>","aaron.hartnell",,"S-1-5-21-**********-**********-*********-17559","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 3:58:44 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hartnell","+**************** ,3598","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a151c898-4b48-4102-81d6-fc852b083747","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Technology","True","Devin Nate",,"Devin",,"auP0aD8ySke/IFgsUTc5aA==","Director of Technology","7/24/2019 7:18:38 PM",,"<EMAIL>","dnate","+1 (403) 650-0871","S-1-5-21-**********-**********-*********-16716","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 4:06:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta","#2, 1440 ? 28th Street NE","Nate","+**************** ,6330","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"908e0ddf-ee3d-45d4-80b7-cdf29fb7c67c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Executive","True","Jerry Diener",,"Jerry",,"PeKpq5p0dU++jAuQv4VkBw==","Senior Director of Corporate Development","8/13/2019 11:32:40 PM",,"<EMAIL>","jerry.diener","+1 (778) 214-1382","S-1-5-21-**********-**********-*********-17014","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna/Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/17/2019 2:03:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Diener","+**************** ,3060","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"556c4b20-232a-44f9-8ec0-0a838daa2e26","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Operations","True","Ken Gordon",,"Ken",,"ymYjGnCwK02BP78+MnKm9g==","Senior Salesforce Administrator","8/23/2019 7:51:02 PM",,"<EMAIL>","kgordon",,"S-1-5-21-**********-**********-*********-16924","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 7:42:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Gordon","+**************** ,3110","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fa5656ac-8b77-4e9d-8afa-6726f2f1e51a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Finance","True","Nicol Solomonides",,"Nicol",,"bwdKB+uw5EeOo/nbsRr/7w==","Manager, Revenue Management","7/15/2019 3:35:18 PM",,"<EMAIL>","nicol.solomonides","+1 (778) 336-6654","S-1-5-21-**********-**********-*********-17495","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:20:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Solomonides","+**************** ,3056","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5f96f0c7-fe1e-4f15-9c9b-fe99ccb8057e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Technology","True","Mark McLean",,"Mark",,"mFX9HdIq+EOxfkcUcWqs3Q==","Sr. Manager, Enterprise Architecture","8/9/2019 3:07:21 PM",,"<EMAIL>","mmclean","+1 (403) 617-8489","S-1-5-21-**********-**********-*********-16718","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 3:02:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta","#225 - 1015 4th Street SW","McLean","+**************** ,6329","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6f9f1253-d887-47b3-bd7c-ec6432bdb557","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR",,,,"Executive","True","Mike Checkley",,"Mike",,"sewo1mu+Uk28GyFR+XR4Iw==","President","8/12/2019 5:31:38 PM",,"<EMAIL>","mike.checkley","+1 (250) 870-6888","S-1-5-21-**********-**********-*********-1432","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 2:16:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Checkley","+**************** ,3575","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5304ce6a-8eb2-49b6-99f9-ac349385b6e1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Finance","True","Amanda Korecki",,"Amanda",,"A/efkOYrL02zVb6n5YhASw==","Revenue Management Lead","7/23/2019 2:17:37 PM",,"<EMAIL>","amanda.korecki",,"S-1-5-21-**********-**********-*********-14635","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 2:06:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Korecki","+**************** ,3057","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"abe94e32-5780-4c65-a822-8987af9b57e3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Shelley Hughes",,"Shelley",,"nQEh49QZvk+ohQ6gIhDoXg==","Director of Product Management","6/12/2019 6:47:34 PM",,"<EMAIL>","shelley.hughes","+1 (250) 215-4026","S-1-5-21-**********-**********-*********-18704","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/12/2019 6:30:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hughes","+**************** ,3592","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6560c853-25b1-4a1e-a82c-659aadcffd98","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Administration","True","Lee-Ann Tiede",,"Lee",,"JrTtqFyniE+Fgmv5p4XwcQ==","Administrative Operations Manager","8/20/2019 4:21:51 PM",,"<EMAIL>","lee.tiede","+1 (250) 878-4146","S-1-5-21-**********-**********-*********-15173","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/19/2019 9:26:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tiede","+**************** ,3051","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"22bf9366-420a-4832-b29d-7b425a1d097a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Nicola Austin",,"Nicola",,"p7kP8BD+4Ei96mlFbh43Pw==","Data Analysis Manager","6/28/2019 3:06:21 PM",,"<EMAIL>","nicola.austin",,"S-1-5-21-**********-**********-*********-16970","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/28/2019 3:02:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Austin","+**************** ,3589","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"007f7c37-bccb-4617-b1d5-f8ca615d8136","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","QHR Technologies Inc.",,,,"Technology","True","Alex Mehl",,"Alex",,"zikzXcYbcEqELc4QCZ55hA==","Lead Systems Administrator","7/24/2019 7:18:38 PM",,"<EMAIL>","amehl","+1 (250) 718-2892","S-1-5-21-**********-**********-*********-15368","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/22/2019 12:27:00 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mehl","+**************** ,3107","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6d75edf7-aa15-44a7-9486-6b605b2218c1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Jonathan Chalaturnyk",,"Jonathan",,"DRctQnFsb0mwgs40poxTEw==","Senior QA Analyst","8/1/2019 4:20:15 PM",,"<EMAIL>","jonathan.chalaturnyk",,"S-1-5-21-**********-**********-*********-18342","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/1/2019 4:05:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chalaturnyk",,"CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"acece227-bf2e-430b-a12f-12a0166e55e1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Tim Fox",,"Tim",,"sVvQHuqD8kij8mnUTGjjVA==","Senior QA Analyst","6/25/2019 3:45:54 PM",,"<EMAIL>","tim.fox",,"S-1-5-21-**********-**********-*********-18625","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 3:15:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Fox","+**************** ,3583","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d3ec66db-0a78-45a9-8b23-5dbaafa5f6c6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Louise Richardson",,"Louise",,"e/2CTdf2NESta7kcKOJdjw==","Product Operations Support Team Lead","8/14/2019 9:33:24 PM",,"<EMAIL>","louise.richardson",,"S-1-5-21-**********-**********-*********-15581","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 12:11:13 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Richardson","+**************** ,3565","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"faac72ae-51d6-4f48-9a64-49e4d99593b9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Finance","True","Cheryl Cain","+1 (250) 717-5266","Cheryl",,"q1Mo6eoetESIw2t/Fd3x9g==","Senior Accountant","8/20/2019 4:51:53 PM",,"<EMAIL>","cheryl.cain",,"S-1-5-21-**********-**********-*********-1156","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 3:27:35 PM","True","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cain","+**************** ,3053","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e57c687c-31d8-4616-aec1-0b50823128ee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Blake Dickie",,"Blake",,"tPUK8trfykSE3M/nZp1ElA==","Principal Software Developer","7/29/2019 3:52:15 PM",,"<EMAIL>","blake.dickie",,"S-1-5-21-**********-**********-*********-8785","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 3:43:30 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Dickie","+**************** ,3578","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dd7a4373-2791-4612-a2b8-62c9dd341a6e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Brian Ellis",,"Brian",,"ywDoic/RAUq/gA3CRCbS4Q==","Senior Director of Product Development","8/20/2019 4:51:53 PM",,"<EMAIL>","bellis","+1 (250) 870-6875","S-1-5-21-**********-**********-*********-1428","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/19/2019 3:12:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ellis","+**************** ,3576","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"eda23921-100f-4986-98e2-a1dec993f7f8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Michael Hall",,"Michael",,"AdHnyiUiEkGlPSnrRFIxIQ==","Director of Sales","7/22/2019 5:46:58 PM",,"<EMAIL>","michael.hall","+1 (416) 220-7417","S-1-5-21-**********-**********-*********-18754","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 5:25:54 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hall","+**************** ,7050","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fb0b178c-a4fb-42ad-803a-f3935238d55b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Finance","True","Jeff VanDenHeuvel",,"Jeff",,"yvN1CZpRxUi7cEbjDOWfRQ==","Controller","7/24/2019 3:48:27 PM",,"<EMAIL>","jeff.vandenheuvel","+1 (250) 869-5637","S-1-5-21-**********-**********-*********-16712","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/24/2019 3:28:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"VanDenHeuvel","+**************** ,3052","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"520194d7-1e10-4630-b7ea-c76898bb53a1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Daryl Laverdure",,"Daryl",,"aS3xOMvEFkS4Bkrkp5OHZw==","Director of Product Operations","8/19/2019 2:07:16 PM",,"<EMAIL>","dlaverdure","+1 (250) 486-0392","S-1-5-21-**********-**********-*********-18204","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:04:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Laverdure","+**************** ,3586","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"eeb735ea-2703-421a-aeab-6ddf98547d98","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Finance","True","Bob Gemmell",,"Bob",,"G1hil7rcmk2g0a8qha63Gw==","Accountant","7/10/2019 3:21:50 PM",,"<EMAIL>","bob.gemmell",,"S-1-5-21-**********-**********-*********-14608","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 3:13:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Gemmell","+**************** ,3054","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1e2a0928-0ef1-4f27-92a2-51da406902d5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Finance","True","Dayna McInnis",,"Dayna",,"Aa9yiJ0EaU+6jJ2bJjKi3Q==","Revenue Management Clerk","7/29/2019 8:22:21 PM",,"<EMAIL>","dayna.mcinnis","+1 (250) 718-1271","S-1-5-21-**********-**********-*********-18127","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 8:06:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McInnis","+**************** ,3529","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6c13368d-7509-42cd-9433-887a58e8cb14","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Operations","True","Sara Konkin",,"Sara",,"L965bB5d406vRaqn/CiSXw==","Salesforce Administrator","7/10/2019 3:21:50 PM",,"<EMAIL>","skonkin","+1 (778) 214-0773","S-1-5-21-**********-**********-*********-15498","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 3:05:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Konkin","+**************** ,3608","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"804e2512-305b-40a8-a14a-c6e9211fd279","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Aron Ashmead",,"Aron",,"722Tnono20ywy1BX66Xw8w==","Privacy Officer","8/21/2019 3:28:06 PM",,"<EMAIL>","aashmead","+1 (778) 214-0350","S-1-5-21-**********-**********-*********-17387","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/21/2019 3:03:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ashmead","+**************** ,3609","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2b6bfb70-15c1-4f1a-b9ff-b1b7b791c087","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Paul Wait",,"Paul",,"9l5y/KrzQk6MYWi3b0ZoPA==","Technical Services Specialist","7/23/2019 3:17:40 PM",,"<EMAIL>","pwait","+1 (250) 801-9950","S-1-5-21-**********-**********-*********-18669","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 3:03:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wait","+**************** ,3604","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"620f85c0-98e3-4961-b515-e1eeccd422ae","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Lisa St. Laurent",,"Lisa",,"iYvlhfeNnESYGOTf5QGK3Q==","Director of Data, CHN","7/8/2019 3:24:13 PM",,"<EMAIL>","lisa.stlaurent",,"S-1-5-21-**********-**********-*********-1431","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 3:15:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"St Laurent","+**************** ,3577","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e8005ccc-bd82-4d13-9fb5-a31310d166e2","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Lauren Romano",,"Lauren",,"u4TUck6g2kazIvxj78nkXQ==","Lead Trainer","8/7/2019 8:04:16 PM",,"<EMAIL>","lauren.romano","+1 (250) 826-5526","S-1-5-21-**********-**********-*********-18128","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/7/2019 7:39:50 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Romano","+**************** ,3562","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"89a7d488-15f5-47c0-90eb-7a8ea4416b1b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Jennifer Davidoff",,"Jennifer",,"X1+qdXSmxU6sQNktunMnVA==","Senior Trainer","8/6/2019 10:03:32 PM",,"<EMAIL>","jennifer.davidoff","+1 (250) 826-0578","S-1-5-21-**********-**********-*********-18728","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Castlegar",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 10:00:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Davidoff","+**************** ,3560","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f34f5e92-9ac0-4e76-afeb-133b96b25cbd","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Implementations","True","Adele Williams",,"Adele",,"3ICspd2MdEihzabJzC9+pg==","Project Manager","7/22/2019 5:16:57 PM",,"<EMAIL>","adele.williams","+1 (250) 862-0894","S-1-5-21-**********-**********-*********-18176","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 4:47:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Williams","+**************** ,3607","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5112b062-223a-4f28-b144-0578016c8b7d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Raj Diocee",,"Raj",,"csAjnxDnyECvnUUoUMXpQg==","Lead Software Developer","8/16/2019 4:04:59 PM",,"<EMAIL>","raj.diocee",,"S-1-5-21-**********-**********-*********-19720","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 4:30:44 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Diocee",,"CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0b02afd2-ae18-49d2-9d6f-7c8db9717818","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Benjamin Schellenberger",,"Benjamin",,"WXsZwulP506KFxDcHZkzmg==","Technical Lead","7/23/2019 9:17:52 PM",,"<EMAIL>","ben.schellenberger",,"S-1-5-21-**********-**********-*********-20104","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 3:41:48 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Schellenberger","+**************** ,3414","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4b0524d5-8940-4063-a74a-ae2844311da6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Roxanne Geiger",,"Roxanne",,"wQGxVai8+EWIcODCX3HUwA==","Implementation Team Lead","8/13/2019 4:32:27 PM",,"<EMAIL>","roxanne.geiger","+1 (403) 608-5027","S-1-5-21-**********-**********-*********-13582","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/11/2019 8:14:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Geiger","+**************** ,6108","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"71f88f97-ef75-4a59-994a-dea705fc25eb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Tony Elumir",,"Tony",,"p5HUxNJPAUq8U4CkzJQlBg==","Senior Software Developer","8/6/2019 1:03:12 PM",,"<EMAIL>","tony.elumir",,"S-1-5-21-**********-**********-*********-14659","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 1:01:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Elumir","+**************** ,6113","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c1b710f3-0034-485b-97ac-26d7f6a9364c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Jen Danchuk",,"Jen",,"2pxZl4xjoEuoyoKRhR0Zdg==","Business Analyst","8/9/2019 5:07:25 PM",,"<EMAIL>","jen.danchuk",,"S-1-5-21-**********-**********-*********-15541","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 4:46:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Danchuk","+**************** ,3564","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e13445a8-5998-4912-981b-f783c4165faa","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Claire Blaker",,"Claire",,"jHWD6q/cnEaE/k0vg5Y8tw==","Implementation Manager","7/5/2019 4:33:51 PM",,"<EMAIL>","claire.blaker","+1 (250) 870-6871","S-1-5-21-**********-**********-*********-15817","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/5/2019 4:25:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Blaker","+**************** ,3553","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"612ba6d0-40e3-4eb6-bddd-043006681421","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Chris Hollman",,"Chris",,"fFBaE9Ae1UuBgqU+RIqKIg==","Configuration Lead","6/26/2019 2:32:24 PM",,"<EMAIL>","chris.hollman","+1 (250) 808-0865","S-1-5-21-**********-**********-*********-18706","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/26/2019 2:17:40 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hollman","+**************** ,3556","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"22c64ed9-53db-437b-a410-02cc41901666","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Technology","True","Neil Hylton",,"Neil",,"JP9Vg+mtn0K0G0FW0o8u+w==","Vendor and Partner Program Specialist","7/29/2019 6:22:18 PM",,"<EMAIL>","neil.hylton","+1 (403) 512-0225","S-1-5-21-**********-**********-*********-16719","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 6:19:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta","#2, 1440 ? 28th Street NE","Hylton","+**************** ,6323","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"206916aa-01b1-4a43-aaeb-47a4b57b7a53","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Allen McCarty",,"Allen",,"Ds40DlhYd0e/OMSm4VSU4Q==","Business Analyst","7/31/2019 10:49:39 PM",,"<EMAIL>","amccarty","+1 (250) 826-2492","S-1-5-21-**********-**********-*********-18111","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 10:43:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McCarty","+**************** ,3108","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5f38b4ca-bf3b-4664-a6a3-c583b5a6c3a3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Matti Kalij?rvi",,"Matti",,"MmDeZJ0UZU28D8T2iqN5kA==","Account Executive","4/9/2019 1:22:36 PM",,"<EMAIL>","MKalijarvi","+1 (705) 931-4333","S-1-5-21-**********-**********-*********-12865","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Saskatoon",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","4/9/2019 1:04:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kalijarvi","+**************** ,3555","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"efc60a52-7551-43e1-ac71-a58d84c30d17","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Jennifer Makar",,"Jennifer",,"DDKx4VUzvEqUyKD8GU5Avg==","Partnership Manager","7/2/2019 3:20:43 PM",,"<EMAIL>","jennifer.makar","+1 (778) 363-6616","S-1-5-21-**********-**********-*********-15789","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:18:35 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Makar","1-778-363-6616","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4b721e02-93a2-4b51-af20-6bc87b86813e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Kyle Newton",,"Kyle",,"Wj4HSBxfzkCwo0YwXxAkjQ==","Product Owner","7/12/2019 10:33:02 PM",,"<EMAIL>","kyle.newton",,"S-1-5-21-**********-**********-*********-20179","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 5:12:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Newton",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"79488381-fbe3-40ff-b647-53f63d8455d3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Lesley Beamond",,"Lesley",,"aBfQWaZYEEWacLRtMNXCAA==","Implementer","7/2/2019 2:50:42 PM",,"<EMAIL>","lesley.beamond","+1 (604) 816-0301","S-1-5-21-**********-**********-*********-18659","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"British Columbia",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 2:45:11 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Beamond","+1 (604) 816-0301","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"15b35583-e35f-4c3f-8656-0f965357d3be","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Cory Ingram",,"Cory",,"fnjKbIommkqXYJLeU2lV4w==","Software Developer","8/23/2019 5:00:34 PM",,"<EMAIL>","cory.ingram",,"S-1-5-21-**********-**********-*********-20181","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 4:39:30 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ingram",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"959b2c4a-d40c-4484-95a6-3842007ebef7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Brenda Undiri",,"Brenda",,"rH6rYUhIrE2X9WcQ/JrD8g==","Project Manager","6/17/2019 12:15:42 PM",,"<EMAIL>","brenda.undiri","+1 (416) 938-0377","S-1-5-21-**********-**********-*********-15429","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/17/2019 12:03:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Undiri","+**************** ,7014","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bfacbab3-23a3-489c-b653-17ac923b5aa1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Brad Reibin",,"Brad",,"nKfN5swo/Ui3CbWhtVjIbQ==","Enterprise Project Portfolio Manager","7/16/2019 3:13:25 PM",,"<EMAIL>","brad.reibin",,"S-1-5-21-**********-**********-*********-18727","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 3:02:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Reibin","+**************** ,3574","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"69a1852e-a75f-491f-83c9-b64a32c9495a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Wayne Knorr",,"Wayne",,"/2V1BejrGEGUar/GzoPhBA==","Senior QA Analyst","8/2/2019 4:21:01 PM",,"<EMAIL>","wayne.knorr",,"S-1-5-21-**********-**********-*********-15448","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 3:58:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Knorr","+**************** ,6115","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8448e865-cfd3-4074-84d7-a4a275293489","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Mark Vanrietschoten",,"Mark",,"a1pWQ3T1ZUK6Ayfe8y2p1w==","Senior Software Developer","8/7/2019 4:04:09 PM",,"<EMAIL>","mark.vanrietschoten",,"S-1-5-21-**********-**********-*********-18626","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/7/2019 3:57:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Vanrietschoten","+**************** ,3585","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bb873269-877e-45aa-9622-3b318bca4dbb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Sandra Baker",,"Sandra",,"TsgWqp6qI0ui2zyYarTkzw==","Trainer","8/20/2019 2:51:48 PM",,"<EMAIL>","sandra.baker","+1 (519) 280-0251","S-1-5-21-**********-**********-*********-19729","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"London, Ontario",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 2:45:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Baker","+**************** ,7047","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6cfa62d7-ab48-47eb-af3b-71562e045b0f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Alfred Loh",,"Alfred",,"vkFiIfJEMESmKSGsAY8zeg==","Software Development Manager","8/6/2019 8:03:30 PM",,"<EMAIL>","alfred.loh",,"S-1-5-21-**********-**********-*********-15743","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 7:54:38 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Loh","+**************** ,3580","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0429f238-de2d-4cb2-bcee-4f441e320ddc","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Shaun O'Grady",,"Shaun",,"lK0MliHBWk+9mB9oXpNOpQ==","Technical Project Manager","7/23/2019 5:17:45 PM",,"<EMAIL>","shaun.ogrady","+1 (778) 363-1771","S-1-5-21-**********-**********-*********-12831","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/21/2019 9:42:13 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"O'Grady","+****************","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"352f4adc-722d-4d24-89c5-1b10c32e9cc6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Lucy Montagnese",,"Lucy",,"9vKReNzaE0G34e0L0omVEQ==","Business Development Representative","7/19/2019 2:14:41 PM",,"<EMAIL>","lucy.montagnese","+1 (705) 229-8260","S-1-5-21-**********-**********-*********-18225","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Barrie",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 8:34:28 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Montagnese","+**************** ,7057","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"402cddbe-ffb2-4177-8b44-f5cca81b38e3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Angie Jarabe",,"Angie",,"sWd+LMC880WJI+d3J283NA==","Implementer","8/20/2019 1:51:46 PM",,"<EMAIL>","angie.jarabe","+1 (647) 210-8788","S-1-5-21-**********-**********-*********-18651","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Ontario",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 1:48:31 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jarabe","+1 (647) 210-8788","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6da78a00-0922-49b8-b8d6-ff11f531eb76","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Victoria","EMR Division - QHR",,,,"Product Management","True","Rebecca Ferrie",,"Rebecca",,"Sv9pBFmiaUSWfcfETcVZpA==","Product Manager","8/1/2019 3:49:49 AM",,"<EMAIL>","rebecca.ferrie","+1 (416) 992-0453","S-1-5-21-**********-**********-*********-16901","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Ontario",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/1/2019 3:42:20 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Ferrie","+1 (416) 992-0453","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e75edf7f-6d63-4886-98a5-1017aa5e7c01","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Janet Hatfield",,"Janet",,"G/i3akkfeEGead5UyYOxTQ==","Implementer","8/6/2019 5:33:22 PM",,"<EMAIL>","janet.hatfield","+1 (905) 630-2543","S-1-5-21-**********-**********-*********-16690","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Burlington",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 5:14:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hatfield","+1 (905) 630-2543","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d21b86dc-a630-44c0-bd92-4c86d689b528","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Luba O'Brien",,"Luba",,"p6FoOjKzt0Gd8NJR5jMu7g==","Implementer","6/27/2019 3:38:43 PM",,"<EMAIL>","luba.obrien","+1 (416) 993-8343","S-1-5-21-**********-**********-*********-15597","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Hagersville",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 3:28:29 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"O'Brien","+1 (416) 993-8343","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"754d6130-3659-4884-a7eb-f44cda1cdf67","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","EMR Division - QHR",,"Canada",,"Implementations","True","Dylan Wood",,"Dylan",,"GDYv2O3kZEaC7B/2NVke7Q==","Lead Technical Services","7/22/2019 3:16:52 PM",,"<EMAIL>","dylan.wood","+1 (647) 880-0650","S-1-5-21-**********-**********-*********-17005","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:01:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta",,"Wood","+1 (647) 880-0650","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ce3a668c-35b1-4383-8a0a-322243f25ec5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Judy Zeeben",,"Judy",,"JzppsyRvCU+l4RZZsUeILQ==","Practice Consultant","8/14/2019 8:33:22 PM",,"<EMAIL>","judy.zeeben","+1 (250) 300-1125","S-1-5-21-**********-**********-*********-20136","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 4:44:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Zeeben",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e8c282a3-ff4c-4174-bb94-297db562525c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Jessica Severiano",,"Jessica",,"FHGKz/vlXU+ShyFUCf8rag==","Lead Implementer","7/4/2019 4:03:04 PM",,"<EMAIL>","jessica.severiano","+1 (416) 937-8848","S-1-5-21-**********-**********-*********-18865","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 6:31:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Severiano","+1 (416) 937-8848","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"959a0a31-f74d-4982-9526-a83a6a211824","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Ravi Anandarajah",,"Ravi",,"vmzYBWncQkWEV1mLc6VhHQ==","Project Management Lead","8/19/2019 4:37:20 PM",,"<EMAIL>","ravi.anandarajah","+1 (647) 456-5702","S-1-5-21-**********-**********-*********-18761","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 4:25:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Anandarajah","+**************** ,7018","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4754286d-58a3-4e86-a47b-d6447c2efa3b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Christopher Cadieux",,"Christopher",,"AeTA0reej0G/iXrLANO7SA==","Lead Software Developer","5/30/2019 4:35:37 PM",,"<EMAIL>","christopher.cadieux",,"S-1-5-21-**********-**********-*********-18316","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","5/30/2019 4:27:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cadieux","+**************** ,3594","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c654569c-2357-478f-a11f-a971b6ee132b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","EMR Division - QHR",,"Canada",,"Implementations","True","Brad Paffe",,"Brad",,"XnaxPmYM40SY6MmwR+AoxQ==","Senior Technical Services Specialist","6/27/2019 3:38:43 PM",,"<EMAIL>","brad.paffe","+1 (416) 220-7464","S-1-5-21-**********-**********-*********-16722","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 3:32:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta","#2, 1440 ? 28th Street NE","Paffe","+1 (416) 220-7464","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b1bae51a-2a1c-4b70-bcf1-396d61850acb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Ottawa","EMR Division - QHR",,"Canada",,"Implementations","True","Tim Melmoth",,"Tim",,"JqlJ3KcCHUaaqpdxCJY5kg==","Director of Client Services","7/5/2019 1:03:44 PM",,"<EMAIL>","tim.melmoth","+1 (613) 408-0110","S-1-5-21-**********-**********-*********-17590","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Ottawa","K1H 1E1",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/5/2019 12:45:40 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","ON","2430 Don Reid Drive
Suite 101","Melmoth","+**************** ,6502","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d36a4bfc-adb6-4c07-b372-6384717f0f61","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Zsolt Kiss",,"Zsolt",,"rXShSic7SEuEBeHP7t9f+w==","Data Analyst","8/3/2019 1:30:49 AM",,"<EMAIL>","zsolt.kiss",,"S-1-5-21-**********-**********-*********-17569","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/3/2019 1:19:44 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kiss","+**************** ,3622","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2be960c4-3323-4837-a3fa-7aabcf984dd9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Erik Adamson",,"Erik",,"leaOPrsv5UCBakxaFIxEbw==","Technical Services Specialist","6/18/2019 2:46:31 PM",,"<EMAIL>","erik.adamson","+1 (250) 717-7314","S-1-5-21-**********-**********-*********-17441","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/18/2019 2:32:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Adamson","+**************** ,3613","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7884d692-daf9-4559-8f48-79b89749842d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Operations","True","Chenoa McMullen-Hunt",,"Chenoa",,"oR+Wx25ZtUyx9hcxqJ+bjw==","Operations Analyst","8/20/2019 5:21:53 PM",,"<EMAIL>","chenoa.mhunt","+1 (250) 469-4327","S-1-5-21-**********-**********-*********-18775","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 1:36:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McMullen-Hunt","+**************** ,3602","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8a06f22e-f328-45cc-847d-d4ed91a6b156","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Stevan Christiansen",,"Stevan",,"HYW4IfWqt0OzUkwawmpX8Q==","Data Services Representative","7/30/2019 3:22:55 PM",,"<EMAIL>","stevan.christiansen",,"S-1-5-21-**********-**********-*********-18749","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"St. Catharines",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/30/2019 3:03:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Christiansen","+**************** ,7226","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6bce2d18-9fea-4f55-a7d4-d7d88b20a541","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Justin Hebert",,"Justin",,"xrnQ+q5izE2kbiMs7vpOgQ==","Lead Software Developer","7/30/2019 8:17:24 PM",,"<EMAIL>","justin.hebert",,"S-1-5-21-**********-**********-*********-18280","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:10:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hebert","+**************** ,3588","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1be4bf8e-7c2d-4adb-92c8-8f17603f1646","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Sales","True","Elton Mahabir",,"Elton",,"maXTy7fMzUu4vkwf55GXEQ==","Account Executive-ON","8/20/2019 3:21:24 AM",,"<EMAIL>","elton.mahabir","+1 (647) 269-5773","S-1-5-21-**********-**********-*********-15655","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Ontario",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/1/2019 2:33:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","ON",,"Mahabir","+1 (647) 269-5773","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5a6dc017-f25c-4ee5-a946-8cbac931f9d9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Coldstream","EMR Division - QHR",,"Canada",,"Sales","True","Ron Hughes",,"Ron",,"jafH+svh+0u82NsGfKLzFg==","Account Executive","6/24/2019 7:20:58 PM",,"<EMAIL>","ron.hughes","+1 (250) 550-6270","S-1-5-21-**********-**********-*********-15549","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vernon","V1B 4A8",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 6:58:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC","475 Mt. Moore Place","Hughes","+1 (250) 550-6270","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"108ac911-58ed-4a70-a9b1-6d86c75b6fd0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Sales","True","Viktor Velkovski",,"Viktor",,"YGW9GRVvmE6whSOt0P6Y+g==","Account Executive-ON","5/22/2018 6:09:00 PM",,"<EMAIL>","viktor.velkovski","+1 (416) 220-7376","S-1-5-21-**********-**********-*********-18307","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","12/21/2017 3:25:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","ON",,"Velkovski","+1 (416) 220-7376","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bbbf8430-9ed2-434e-8af7-6a2996807126","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Winnipeg","EMR Division - QHR",,"Canada",,"Sales","True","Nan Adams","+1 (866) 421-9332","Nan",,"x+65sGw9Nk21xJUufpSgpQ==","Regional Client Relationship Manager MB","8/21/2019 4:58:09 PM",,"<EMAIL>","nan.adams","+1 (204) 898-5371","S-1-5-21-**********-**********-*********-13396","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Winnipeg",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 7:09:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","MB",,"Adams","+1 (204) 898-5371","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4c2dbacb-a938-4b03-aead-c7e3d70cfd30","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Marion Sherback",,"Marion",,"3hjlxPNoWUunSLHqrNsEog==","Regional Client Relationship Manager BC","8/19/2019 3:37:17 PM",,"<EMAIL>","marion.sherback","+1 (604) 314-5067","S-1-5-21-**********-**********-*********-11242","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 3:30:38 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Sherback","+1 (604) 314-5067","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0ca08a6a-697d-4fc2-9dea-05135c2eca70","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Cody Cudmore",,"Cody",,"dmGMdctBGUSurVrQS4niUw==","Lead Data Services Representative","7/31/2019 11:49:42 PM",,"<EMAIL>","cody.cudmore","+1 (250) 870-2639","S-1-5-21-**********-**********-*********-18314","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:20:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cudmore","+**************** ,3543","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1ef17fae-0ceb-483d-b713-33cb980cad59","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Julie Duncan",,"Julie",,"+EZEzQTTkU2zlqZcg3tx3Q==","Client Services Analyst","7/31/2019 5:49:31 PM",,"<EMAIL>","julie.duncan",,"S-1-5-21-**********-**********-*********-19039","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Ottawa",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 5:20:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Duncan","+**************** ,3623","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0b3cb5c1-60f1-4aa0-8385-427a27420d21","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Lisa Gunnlaugson",,"Lisa",,"3BvGrikyhEW9gseLI+FQSA==","Training Team Lead","8/14/2019 4:02:48 AM",,"<EMAIL>","lisa.gunnlaugson","+1 (250) 801-7274","S-1-5-21-**********-**********-*********-18459","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 4:01:27 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Gunnlaugson","+**************** ,3614","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9b1a0836-08c9-453b-967c-c2ca3a8b6433","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Sales","True","Alan Zantingh",,"Alan",,"x458n6GxlESAKz9lmbltqA==","Sales Engineer","8/13/2019 4:32:27 PM",,"<EMAIL>","alan.zantingh","+1 (416) 992-0574","S-1-5-21-**********-**********-*********-18533","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 4:13:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Zantingh","+**************** ,7032","CA","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b133b9a4-cfc4-449a-b14b-74afd518751b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Sales","True","Shelly Arsenault",,"Shelly",,"zqAg1cKjo060ZWINTM/5hA==","Sales Coordinator","8/6/2019 4:33:21 PM",,"<EMAIL>","shelly.arsenault","+1 (647) 202-9000","S-1-5-21-**********-**********-*********-18757","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto","M5C 1C4",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 4:10:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","ON","18 King Street East, Suite 1002","Arsenault","+**************** ,7012","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1639109a-5e3b-4f3d-873d-edfb58c5c741","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Shawn Manary",,"Shawn",,"HRgZ5qc0KkKa+7yeBa7+lQ==","Account Executive","7/24/2019 8:18:36 PM",,"<EMAIL>","smanary","+1 (416) 797-7178","S-1-5-21-**********-**********-*********-18753","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/19/2019 7:01:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Manary","+1 (416) 797-7178","US","<EMAIL>",,,
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c82a31e3-8320-4a52-a01f-d3e1ad19771c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Adam Sinai",,"Adam",,"hMsJpbFOl0mnOcs/CuOIpw==","Account Executive","7/1/2019 11:20:10 PM",,"<EMAIL>","adam.sinai","+1 (416) 220-7454","S-1-5-21-**********-**********-*********-18752","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/1/2019 10:54:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Sinai","+**************** ,7108","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8739e086-cf26-4559-b2aa-3d97df8e1991","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Colin Greenway",,"Colin",,"deSgc09UcESo2w2nPkLRdA==","Manager, Sales Engineering","8/8/2019 1:34:52 PM",,"<EMAIL>","colin.greenway","+1 (416) 220-7401","S-1-5-21-**********-**********-*********-18755","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/8/2019 1:22:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Greenway","+**************** ,7147","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fcbf3252-5639-4339-9bbd-aefdca2595ee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Robert Thornton",,"Robert",,"xVDA5aXUiEmKnR2Evrds7Q==","Technical Services Specialist","8/1/2019 3:50:14 PM",,"<EMAIL>","robert.thornton","+1 (905) 327-9221","S-1-5-21-**********-**********-*********-18231","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"St. Catharines",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/1/2019 3:38:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Thornton","+**************** ,7159","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1bac7c12-79bc-4980-b55f-d8c483a588fc","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Ottawa","QHR Technologies Inc.",,,,"Training","True","Susan Poisson",,"Susan",,"Rm15NaXr7EOntN4lIto9CQ==","Trainer","8/21/2019 3:58:06 PM",,"<EMAIL>","susan.poisson","+1 (613) 889-4449","S-1-5-21-**********-**********-*********-18234","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Ottawa","K1C 5R1",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/21/2019 3:44:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","ON","6094 Rivercrest Drive","Poisson","+**************** ,7095","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a19f2f47-9f4a-4de5-a1fd-adc4fa3d45ca","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Technology","True","Craig Hounsham",,"Craig",,"4Op6qe/+YEGugbNkmOk0Hg==","Jr Systems Administrator","8/6/2019 8:03:30 PM",,"<EMAIL>","craig.hounsham",,"S-1-5-21-**********-**********-*********-18760","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"St. Catharines",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/19/2019 8:03:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hounsham","+**************** ,7224","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e05b296f-2da1-4f65-96fd-edcaed27bfd4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Andrew Bondarenko",,"Andrew",,"0PGWzLmUM0+JRWkF0VxPGQ==","Assistant Data Analysis Manager","7/25/2019 3:49:16 PM",,"<EMAIL>","andrew.bondarenko",,"S-1-5-21-**********-**********-*********-20229","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 12:55:11 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bondarenko","+**************** , 3593","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"99c37836-174a-481b-a67f-b95f1e23c87c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Bryan Bergen",,"Bryan",,"ab0Iips/j02OYvYNbfYKcQ==","Junior Application Architect","8/12/2019 2:39:44 PM",,"<EMAIL>","bbergen",,"S-1-5-21-**********-**********-*********-20228","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 2:34:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bergen",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2b98f8d4-a0e0-43ab-bcf5-702481b6fe7a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Kevan Poeschek",,"Kevan",,"1Oh+pRhqDUuEIp1rxsz3Tw==","Data Systems Administrator","7/25/2019 3:49:16 PM",,"<EMAIL>","kevan.poeschek",,"S-1-5-21-**********-**********-*********-20230","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:06:06 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Poeschek","3558","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"892fb087-335e-4e9e-ad6d-ee8a05178e02","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Scott Chipman",,"Scott",,"Z1+UlquhrEiOmnu4nqE9Tw==","Senior Data Analyst","7/5/2019 5:33:30 AM",,"<EMAIL>","scott.chipman",,"S-1-5-21-**********-**********-*********-19885","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/5/2019 5:09:32 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chipman","+**************** ,3542","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e6ee7f20-40f5-42c9-a2d4-33c4562723ad","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Colleen Piotrowski",,"Colleen",,"JM6IOoNUKk+eKSzr4y1Vyg==","Implementer","8/5/2019 3:32:29 PM",,"<EMAIL>","colleen.piotrowski","+1 (416) 700-8776","S-1-5-21-**********-**********-*********-20274","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Remote Home Office",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/5/2019 3:21:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Piotrowski","+**************** ,7041","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f921a0b2-bcf2-4de0-a73c-34883d4080e5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Nancy Chapeskie",,"Nancy",,"ynLQZgy0dUaaSOWT6ZK9Fw==","National Account Manager","7/17/2019 5:13:17 PM",,"<EMAIL>","nancy.chapeskie","+1 (519) 535-3110","S-1-5-21-**********-**********-*********-19889","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Remote Ontario",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 4:59:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chapeskie","+1 (866) 534-3627, 7042","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6a424ac0-3048-4bdb-ba23-ddab279830c8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Vicki Henckel",,"Vicki",,"A/52p//ktUKL4drAthP5+w==","Enterprise Relationship Analyst","8/15/2019 4:34:19 PM",,"<EMAIL>","vicki.henckel",,"S-1-5-21-**********-**********-*********-19896","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 4:23:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Henckel","+**************** ,3626","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7b91f0bb-4423-4976-a874-7306f89c760c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Client Services","True","Jaclyn Canas",,"Jaclyn",,"Wu1wnubCdkWGhY+tpC4zgg==","Client Services Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","jaclyn.canas",,"S-1-5-21-**********-**********-*********-20294","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 12:06:50 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Canas","+**************** ,7015","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"010a7635-d9ed-465e-97c7-36c90613fd9c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Finance","True","Shelley Watson",,"Shelley",,"HjGm3Qlyt0qgRA2Sd04imQ==","Revenue Management Data Entry Clerk","7/25/2019 4:49:19 PM",,"<EMAIL>","shelley.watson",,"S-1-5-21-**********-**********-*********-19922","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 4:40:57 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Watson","+**************** ,3066","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"60114c85-54b0-4c6d-8a9f-590a4b04919f","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Derek Ward",,"Derek",,"b0eSjCWRnEWUVGt+/km+rQ==",,"8/23/2019 10:50:31 PM",,"<EMAIL>","derek.ward",,"S-1-5-21-**********-**********-*********-20325","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 8:33:40 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,"BC",,"Ward",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e14b1834-04e5-4a10-8d61-b60a3c77e639","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Curtis Rose",,"Curtis",,"RvCbmNIqT0KlZDncKFX3qg==","Lead Software Developer","7/24/2019 6:18:36 PM",,"<EMAIL>","curtis.rose",,"S-1-5-21-**********-**********-*********-20334","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:31:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Rose","+****************, 3514","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"95f11a15-1947-4a0e-a974-df7e54d83435","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Tawny Rother",,"Tawny",,"QAZAQJKnKUCLL53mSW4uAw==","Configuration Specialist","8/16/2019 9:35:05 PM",,"<EMAIL>","tawny.rother","+1 (250) 681-2839","S-1-5-21-**********-**********-*********-20341","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 9:07:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Rother","+**************** ,3630","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a2945391-8915-4235-b01d-101b48e37428","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Alison Cooney",,"Alison",,"7ca6lR63QUK8y6kvZeA4bQ==","Product Owner","7/19/2019 5:44:44 PM",,"<EMAIL>","alison.cooney","+1 (250) 801-7984","S-1-5-21-**********-**********-*********-19969","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","5/6/2019 2:53:29 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Cooney","+**************** ,3633","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"514aae36-bba1-45a7-a872-c5731a783692","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Vincent Crauffon",,"Vincent",,"YavKOWXBHUqk9kx0cGQ9qA==","Project Manager","6/27/2019 2:38:41 PM",,"<EMAIL>","vincent.crauffon","+1 (647) 376-8980","S-1-5-21-**********-**********-*********-20000","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 2:15:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Crauffon","+**************** ,7030","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"56668914-8764-4ffd-85e5-0ed2f9a6042a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Demetri Tsoycalas",,"Demetri",,"UEqFJn6hMUyECzO36j9qWA==","Data Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","demetri.tsoycalas",,"S-1-5-21-**********-**********-*********-20414","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/22/2019 1:33:51 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Tsoycalas","+**************** ,3634","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"449d82d1-6a85-47b7-a020-395c27fbaada","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Kevin Koehler",,"Kevin",,"qswQCDAvDEWrbfdKIfxPww==","Senior Software Developer","8/15/2019 3:34:17 PM",,"<EMAIL>","kevin.koehler",,"S-1-5-21-**********-**********-*********-20436","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 3:12:06 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Koehler",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bbbaa64b-3882-4938-be02-96781a04070f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Katherine Awad",,"Katherine",,"TNQMAyd6KUWiXZSkfgcrPw==","Implementer","8/19/2019 2:06:52 AM",,"<EMAIL>","katherine.awad","+1 (416) 220-7484","S-1-5-21-**********-**********-*********-20036","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:01:37 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Awad","+**************** ,7019","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e2e192d6-e64a-4998-bd0e-5d2ae1f92f50","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Jay Andrews",,"Jay",,"HxAJCGyTaUi3zvCH6zjT0g==","Central Desk Client Care Coordinator","8/2/2019 3:20:58 PM",,"<EMAIL>","jay.andrews",,"S-1-5-21-**********-**********-*********-20467","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 3:16:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Andrews","+**************** ,3644","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b5c32f3f-9c9a-4a65-8d50-d074f3c1a3f0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,"Canada",,"Product Management","True","Wayne Bullock",,"Wayne",,"w745OMjZ40escjQ1zPSFKQ==","Senior Business Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","wayne.bullock",,"S-1-5-21-**********-**********-*********-23106","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/26/2019 8:12:32 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bullock","+**************** ,3590","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ddc5c142-fb95-4d6c-bd5d-91da64c1d72b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Kamran Khan",,"Kamran",,"5jqZpedTDkScSK/lk+hcbA==","Project Manager, Product Development","8/13/2019 7:02:30 PM",,"<EMAIL>","kamran.khan","+1 (416) 993-3144","S-1-5-21-**********-**********-*********-23111","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 6:59:54 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Toronto",,"Khan","+**************** ,7025","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"514b4bf8-9e2a-4aef-9a71-87a856f4e606","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Sam Mullen",,"Sam",,"rfXHqAqk60eDsi+qLa3cgw==","Client Services Analyst","8/12/2019 6:31:41 PM",,"<EMAIL>","sam.mullen",,"S-1-5-21-**********-**********-*********-23121","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 6:21:38 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mullen","+**************** ,3651","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fd0cae96-043e-49d4-8d7b-6a61fc2ef62a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Human Resources","True","Pavan Brar",,"Pavan",,"C5OGZp9+2UOlPRUu7bVWHg==","Human Resources Team Lead","7/24/2019 6:18:36 PM",,"<EMAIL>","pavan.brar","+1 (250) 689-1522","S-1-5-21-**********-**********-*********-20480","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 5:33:42 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Brar","+**************** ,3067","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fe90cb9a-a899-4896-81ec-55b9087a205c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Kelley Mullen",,"Kelley",,"L9d1MHgqb0u1x+dRGynjBg==","Client Services Analyst","7/18/2019 12:43:55 PM",,"<EMAIL>","kelley.mullen",,"S-1-5-21-**********-**********-*********-23124","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 12:23:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mullen","+**************** ,3652","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e1074038-aca2-4db3-9a21-127a413e406b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Duncan Ritchie",,"Duncan",,"xJiKNC7wOUWEYw5upa7dzQ==","Development Services Coordinator","8/9/2019 5:07:25 PM",,"<EMAIL>","duncan.ritchie",,"S-1-5-21-**********-**********-*********-20481","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 4:55:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Ritchie","+**************** ,3646","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"092a542e-f3eb-4955-9a0c-31f08ba82dec","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Chris Stickney",,"Chris",,"bJKXge0XBUu7QrlDElgTYg==","Client Services Team Lead","8/17/2019 12:05:08 AM",,"<EMAIL>","chris.stickney",,"S-1-5-21-**********-**********-*********-20490","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 11:51:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Stickney","+**************** ,3639","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ad6cec27-8ed1-40e7-b75a-242434c423d6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Melissa Brooks",,"Melissa",,"lAad5riRxkq8j2z0yUo4QA==","Junior DevOps Engineer","7/24/2019 7:18:38 PM",,"<EMAIL>","melissa.brooks","+1 (250) 801-3453","S-1-5-21-**********-**********-*********-20491","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 7:48:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Brooks","+**************** ,3109","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"126b7c29-292c-4133-bbfe-383757070bd9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,"Canada",,"Development","True","Preston Cooper",,"Preston",,"vra2ZJegQkeHu3QqcaeJtA==","Junior Software Developer","8/10/2019 2:07:42 AM",,"<EMAIL>","preston.cooper",,"S-1-5-21-**********-**********-*********-23138","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/10/2019 1:58:13 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cooper",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"799960c5-4697-49a3-af8a-7d328ca0ef06","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Dejan Gudjevski",,"Dejan",,"u+0vIapyv0ulQbvpQwQbbA==","Data Services Representative","8/15/2019 6:04:22 PM",,"<EMAIL>","dejan.gudjevski",,"S-1-5-21-**********-**********-*********-23143","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 5:42:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Gudjevski","+**************** ,7026","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"70ad8ff4-4423-4a0a-96f3-fc39ec036ec4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","QHR Technologies Inc.",,,,"Technology","True","Felix Lau",,"Felix",,"hYQW8Pd5GEOmZZw4q9iZqA==","Systems Administrator","7/24/2019 7:18:38 PM",,"<EMAIL>","felix.lau","+1 (647) 465-0353","S-1-5-21-**********-**********-*********-20503","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 2:30:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lau","+**************** ,3105","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d61b90fd-2a80-4d88-a26a-1e6605d11663","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Alison Moore",,"Alison",,"z9b3LmSGq0eoVRbbqO6AWA==","Audit Coordinator","7/24/2019 7:18:38 PM",,"<EMAIL>","alison.moore","+1 (250) 718-5687","S-1-5-21-**********-**********-*********-23620","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"250-448-2273",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 2:03:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Moore","+****************, 3041","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"68fcbd0c-8a64-45e9-a80c-b44990cf9745","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Ryan Faith",,"Ryan",,"8bxtWCMbbEurLX8JkPQc8g==","Director of Software Engineering","8/12/2019 4:31:39 PM",,"<EMAIL>","ryan.faith","+1 (403) 397-0053","S-1-5-21-**********-**********-*********-22122","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 4:23:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Faith","+**************** ,3587","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"eb0563e5-99f2-4783-9a58-8b0b0efc3491","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Lucas Shoesmith",,"Lucas",,"jZsG8Qxrb0+oVadikD+JBQ==","Software Development Manager","6/25/2019 5:32:07 PM",,"<EMAIL>","lucas.shoesmith",,"S-1-5-21-**********-**********-*********-23632","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 5:13:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shoesmith",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"cf600187-301a-41a5-8810-6a106b5a7313","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Steve Bailey",,"Steve",,"BF+56c5odEm4jibA60OIQQ==","Senior Business Analyst","8/8/2019 4:06:33 PM",,"<EMAIL>","stephen.bailey",,"S-1-5-21-**********-**********-*********-23172","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/8/2019 3:41:58 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bailey","+**************** ,3566","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1763f17d-a423-4fb0-b30d-ff8f3ac23766","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Finance","True","Cali Rendulic",,"Cali",,"/3tBLnxoM0aM3iyzzwf5Ew==","Revenue Management Lead","7/24/2019 4:48:30 PM",,"<EMAIL>","cali.rendulic",,"S-1-5-21-**********-**********-*********-22157","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 2:08:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta",,"Rendulic","+**************** ,3531","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5aa11fca-83c1-41d5-bc20-4d583b35f17e","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto",,,,,,"True","Mimi Yacob",,"Mimi",,"q7/z/hxrU0aM+6LFousw7A==",,"7/3/2019 9:55:07 PM",,"<EMAIL>","mimi.yacob",,"S-1-5-21-**********-**********-*********-20534","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/12/2019 2:39:47 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Yacob","+**************** ,7053","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5e4e4dcf-ddb0-4d44-b5b0-cb2311ea33bc","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sudha Verma",,"Sudha",,"zQd9yoTghUyAdg8KY3+IzA==","Software Developer","7/24/2019 6:18:36 PM",,"<EMAIL>","Sudha.verma",,"S-1-5-21-**********-**********-*********-23176","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 9:01:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Verma",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bb19a358-f6ff-47cf-84c6-aa0a579e4355","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Stephanie Farenhorst",,"Stephanie",,"14e2KbX7ckmDaTRi/uMMUQ==","Account Executive","7/19/2019 2:14:41 PM",,"<EMAIL>","stephanie.farenhorst","+1 (778) 903-3335","S-1-5-21-**********-**********-*********-23642","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","5/24/2019 10:32:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Farenhorst","+**************** ,3533","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"75139bc4-6506-4825-a2a8-7bde1f48964c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Human Resources","True","Jeff Wimmer",,"Jeff",,"fTJkoVeBgEi+fuslgOh9tA==","Director of Human Resources","8/23/2019 9:21:09 PM",,"<EMAIL>","jeff.wimmer",,"S-1-5-21-**********-**********-*********-23645","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/14/2019 10:17:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wimmer","+**************** ,3065","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d7d9a198-b7f6-4974-abc3-81c3fe39906c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Chakks Paramasivam",,"Chakks",,"Maun4vcxlUWaRbZGDmm+Gg==","Lead Software Developer","7/24/2019 6:18:36 PM",,"<EMAIL>","chakks.paramasivam",,"S-1-5-21-**********-**********-*********-23648","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/26/2019 4:21:59 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Paramasivam","+**************** ,3544","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d8ce144b-14dd-4e3e-8876-4718cf1bb5bd","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Candus Hunter",,"Candus",,"ue324Z0lQ0KHdIVHJ4WvAw==","Client Development Manager","7/30/2019 1:22:51 PM",,"<EMAIL>","candus.hunter","+1 (250) 469-1513","S-1-5-21-**********-**********-*********-20538","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/30/2019 12:56:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hunter","+**************** ,3530","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"96847fdf-9f30-435f-8040-c4765d60faf7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Domain Admin- Felix Lau",,"Domain Admin- Felix",,"a8itBavb+EGohBQzeUTk9w==",,"7/11/2019 7:10:48 PM",,,"da-flau",,"S-1-5-21-**********-**********-*********-23182","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 7:05:05 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Lau",,,"<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9b870196-2db4-4bec-aba1-4ee43749d52b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Fan Jin",,"Fan",,"FeaP9kzHjUqBxCskPITKwA==","Software Developer","7/22/2019 6:16:59 PM",,"<EMAIL>","Fan.Jin",,"S-1-5-21-**********-**********-*********-22171","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 5:54:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jin",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"538a7863-4035-429b-9031-f90c6d8c0dfe","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,"Product Management","True","Camila Serrano",,"Camila",,"454GoZOfz0+FqxIijWaZJQ==","UX Designer","7/24/2019 6:18:36 PM",,"<EMAIL>","Camila.Rueda",,"S-1-5-21-**********-**********-*********-22174","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 5:27:27 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Serrano",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7b1889e5-c230-4a94-974a-b6a302acddee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Vancouver","QHR Technologies Inc.",,,,"Business Development","True","Paul Casey",,"Paul",,"q6ep1Q3FvkGZJoJxZBoTtA==","Director of Business Development","7/17/2019 7:13:20 PM",,"<EMAIL>","paul.casey","+1 (604) 868-5414","S-1-5-21-**********-**********-*********-22176","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 6:54:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Casey","+**************** ,3042","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6b7f9c33-647d-4772-9322-0a9964e3c486","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Samuel Bradford",,"Samuel",,"Z63Ov+zVz0CbNP/HF5Hhzg==","Systems Administrator","7/24/2019 7:18:38 PM",,"<EMAIL>","samuel.bradford","+1 (250) 212-2353","S-1-5-21-**********-**********-*********-23188","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 9:49:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bradford","+**************** ,3106","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d53ce825-5659-4fd1-9bba-75b1b9eef779","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Ryan Wood",,"Ryan",,"ZxcY/2ITjEm0p61hTYevFA==","Senior Product Marketing Manager","7/24/2019 6:18:36 PM",,"<EMAIL>","ryan.wood","+1 (250) 870-1770","S-1-5-21-**********-**********-*********-20557","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 3:42:11 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wood","+**************** ,3525","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1c922bb2-e87c-41fe-a078-96f188795e79","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Tanya Winsor",,"Tanya",,"6yODFIOMA0mwdfpqMrOBXQ==","Senior Software Developer","7/24/2019 6:18:36 PM",,"<EMAIL>","tanya.winsor",,"S-1-5-21-**********-**********-*********-20559","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 7:14:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Winsor",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b59f1d83-080b-44a6-aa36-5883c9204b6d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Michal Hoppe",,"Michal",,"3+XNmWz8sUuBKtQ+AURJaw==","Lead Systems Administrator","8/22/2019 6:59:46 PM",,"<EMAIL>","Michal.Hoppe","+1 (778) 581-6979","S-1-5-21-**********-**********-*********-20560","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 4:26:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hoppe","+**************** ,3111","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"131cd0c9-13a6-48db-a6bf-b4f45b3b1ea9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Abraham Tio",,"Abraham",,"sreAw2+gQEeH1jU6LX5+bw==","Software Developer","7/24/2019 6:18:36 PM",,"<EMAIL>","Abraham.Tio",,"S-1-5-21-**********-**********-*********-22191","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/5/2019 3:45:15 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tio",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dafcc65e-f623-4825-85e6-91e106fc0275","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Steven Mathers",,"Steven",,"4ChVJ64d8k2+NYElLwV2YA==","Senior Software Developer","7/25/2019 4:49:19 PM",,"<EMAIL>","Steven.Mathers",,"S-1-5-21-**********-**********-*********-22192","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 2:43:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mathers",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"722f414e-c469-43e2-ab89-edff5d36cf00","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Cole Senger",,"Cole",,"j5SXGDS3yEGeLFh/l/OCSg==","Documentation Specialist","8/14/2019 3:03:12 PM",,"<EMAIL>","cole.senger",,"S-1-5-21-**********-**********-*********-23208","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 2:44:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Senger","+**************** ,3653","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"eebfcab7-57c3-4dd6-8c0b-90d0d7146d33","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Dave Munday",,"Dave",,"/wDcpXO3N0ichZg035aEuQ==","Client Services Analyst","7/25/2019 4:19:21 PM",,"<EMAIL>","dave.munday",,"S-1-5-21-**********-**********-*********-23209","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/24/2019 3:00:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Munday","+**************** ,3631","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c5fa8c14-5bc4-4b16-8c32-9ba11a7a14f9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Oniel Wilson",,"Oniel",,"Q4qYHO3qrUqtom/7adpuwA==","Implementer","7/9/2019 3:51:01 PM",,"<EMAIL>","Oniel.Wilson",,"S-1-5-21-**********-**********-*********-23210","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 3:45:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Wilson","+**************** ,3632","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ff872299-4720-4450-a1f7-019d83a5270d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Administration","True","Colleen Safinuk",,"Colleen",,"t3+fKLrVFE2aKG8SLCmeAw==","Office Manager - EA to the President","7/24/2019 4:48:30 PM",,"<EMAIL>","colleen.safinuk","+1 (250) 859-0929","S-1-5-21-**********-**********-*********-23685","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/19/2019 7:10:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Safinuk","+**************** ,3002","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6a1faf67-b454-4dc8-89bf-6fcb6029bcc5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Petr Stroner",,"Petr",,"crlDDMxUbUe42Y9BZ+45Ig==","Digital Graphic Designer","7/2/2019 8:20:53 PM",,"<EMAIL>","petr.stroner",,"S-1-5-21-**********-**********-*********-23687","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 8:01:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Stroner","+**************** , 3534","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e7fe44da-65ea-4cf1-93bf-19f61b9144b4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Emily Cooney",,"Emily",,"PQc4FxUEI0yKvEFbF1l/+A==","Production Manager","8/23/2019 4:30:36 PM",,"<EMAIL>","emily.cooney",,"S-1-5-21-**********-**********-*********-23690","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 4:01:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cooney","+**************** ,3524","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"80543a29-6e64-45a0-be4f-1f24e3fa4764","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Ted Sorensen",,"Ted",,"s8lpuLswrUmXpXKVYGq5Hg==","Senior Software Developer","7/4/2019 4:33:04 PM",,"<EMAIL>","ted.sorensen",,"S-1-5-21-**********-**********-*********-20572","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 4:07:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Sorensen",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"12172482-b7fe-4810-8fc0-c39bb646585c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Sales","True","Parth Bhatt",,"Parth",,"N4S/5YMK6kWrzg6bGlvWkg==","Sales Engineer","8/14/2019 1:03:08 PM",,"<EMAIL>","parth.bhatt","+1 (416) 668-4153","S-1-5-21-**********-**********-*********-20574","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 7:56:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bhatt","+**************** ,7043","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4e511117-ae3a-4765-92db-4fef0c4607e6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Tony Cheng",,"Tony",,"vJhiFScPiEObqp6FI25BSg==","Senior Software Developer","7/22/2019 5:16:57 PM",,"<EMAIL>","Tony.Cheng",,"S-1-5-21-**********-**********-*********-20576","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 5:12:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cheng",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"22d352ac-1a04-4b0e-9e65-856d9758a99b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Melissa DeLeon",,"Melissa",,"eaDqOWXltU2uQj29QiLVwQ==","Client Services Analyst","8/8/2019 3:36:36 PM",,"<EMAIL>","melissa.DeLeon",,"S-1-5-21-**********-**********-*********-23231","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/8/2019 3:30:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"DeLeon","+**************** ,3619","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f4fa2f99-5ebd-4649-847f-0d21d0df1da1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Amelia Lang",,"Amelia",,"WepRcLzKhkK5Qy8kDJSEpQ==","Software Developer","7/15/2019 3:35:18 PM",,"<EMAIL>","Amelia.Lang",,"S-1-5-21-**********-**********-*********-23232","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:20:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"08d127d7-3077-4b2d-a240-6527d349eeee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Daniel Moon",,"Daniel",,"zCe6au+eek2gbgraQRIqVw==","Client Services Team Lead","7/15/2019 3:05:17 PM",,"<EMAIL>","Daniel.Moon",,"S-1-5-21-**********-**********-*********-23234","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:00:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Moon","+**************** , 3649","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"79dda10c-9ae1-48a4-8777-38c67afef1b5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Adam Peacock",,"Adam",,"Q1yM2+eNrEm4Dp6l9hHcqg==","Client Satisfaction Representative","7/24/2019 5:48:32 PM",,"<EMAIL>","adam.peacock",,"S-1-5-21-**********-**********-*********-23236","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 2:26:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Peacock","+**************** , 3618","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0fa2dcbb-e313-4ef1-a7e4-28ad9ba7fd11","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Chris Spinov",,"Chris",,"iLjCCfnbWUe0f+7OSr3UQQ==","Client Experience Team Lead","8/9/2019 6:07:27 PM",,"<EMAIL>","chris.spinov",,"S-1-5-21-**********-**********-*********-23233","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 11:50:57 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Spinov","+**************** ,3647","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e8927810-c088-4719-af15-7d28b0254513","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Bryce Chernecki",,"Bryce",,"nOCBKcGi0kWduOgYT1afNw==","Lead Software Developer","7/2/2019 3:20:43 PM",,"<EMAIL>","bryce.chernecki",,"S-1-5-21-**********-**********-*********-23703","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:09:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chernecki",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3aefdd63-7e3c-40f9-9100-7e4ad7c8ce1f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Technology","True","Scott May",,"Scott",,"IaUCsBi0LEOmz72e6ksxnQ==","Junior Database Administrator","7/24/2019 7:18:38 PM",,"<EMAIL>","scott.may","+1 (250) 878-5068","S-1-5-21-**********-**********-*********-22217","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/13/2019 3:05:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"May","+**************** ,3654","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"31e5661a-432e-44d8-8727-5a6bc15fd180","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Development","True","Nina Chnek",,"Nina",,"54phW2Go6k2xlEVWDoT73w==","QA Analyst","8/9/2019 11:07:36 PM",,"<EMAIL>","nina.chnek",,"S-1-5-21-**********-**********-*********-23711","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 11:07:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chnek","+**************** ,7029","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"49ec9bca-dec5-4bcd-b136-52baafa2cc2c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Kyle Somogyi",,"Kyle",,"nW46b34Vh0W27NBgBu+Eug==","Senior Software Developer","8/21/2019 7:28:14 PM",,"<EMAIL>","kyle.somogyi",,"S-1-5-21-**********-**********-*********-20595","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Remote",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 9:21:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Somogyi",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"52608eb6-fcc0-481f-abee-af983571cfb3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Trecell Richards",,"Trecell",,"BTaoml6q6USUckaqJn6UYQ==","Lead Software Developer","8/20/2019 3:21:49 PM",,"<EMAIL>","trecell.richards",,"S-1-5-21-**********-**********-*********-23721","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 3:19:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Richards",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"046dab7f-3de3-44be-acc8-76cc7c4168dd","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Arezou Alekhorshid",,"Arezou",,"IenKUG2Sk0Cnyl5cfjmfqg==","Software Developer","8/19/2019 4:37:20 PM",,"<EMAIL>","arezou.alekhorshid",,"S-1-5-21-**********-**********-*********-22604","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 4:20:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Alekhorshid",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c5a0df20-4096-4fa9-a88a-82fe56937bb4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Scott Johnston",,"Scott",,"wdqK7DqsgUST+m+z9uIOdg==","Marketing Web Developer","7/24/2019 6:48:36 PM",,"<EMAIL>","scott.johnston",,"S-1-5-21-**********-**********-*********-22605","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 2:53:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Johnston","+**************** ,3523","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"29f5be02-dcad-427d-90f5-4d47502d62d2","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Mike Fassakhov",,"Mike",,"iYnBmzn+80aO4Awg6+jlgg==","Business Analyst","7/24/2019 6:48:36 PM",,"<EMAIL>","mike.fassakhov",,"S-1-5-21-**********-**********-*********-22240","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:34:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Fassakhov","+**************** ,3541","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"86fed9c8-8d7c-4c15-b4bf-e18b96e019e1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Jesse Pasos",,"Jesse",,"RyOiU/moX021HoQ37/0kLw==","Software Developer","8/23/2019 4:00:31 PM",,"<EMAIL>","jesse.pasos",,"S-1-5-21-**********-**********-*********-22244","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 3:32:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Pasos",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fc669de2-1873-4f9e-85e7-799a0df2fd8c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Stefan Richardson",,"Stefan",,"estQENSU7kanxny3/yfbrA==","Junior Software Developer","8/14/2019 11:33:28 PM",,"<EMAIL>","stefan.richardson",,"S-1-5-21-**********-**********-*********-22615","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/20/2019 4:41:10 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Richardson",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0c5df1f8-fe88-42cb-ad3a-2e330be71784","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Stephen Dobrozsi",,"Stephen",,"OBqUZnjk3k28kfISpTQ3Rg==","QA Analyst","7/24/2019 6:48:36 PM",,"<EMAIL>","Stephen.Dobrozsi",,"S-1-5-21-**********-**********-*********-22617","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/19/2019 1:48:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Dobrozsi",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e4f93cd9-dfc1-4f2b-9074-f63c8667520a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Brent Forder",,"Brent",,"7F2sWYnpzk6I57801qWp3g==","Junior Application Architect","8/15/2019 4:34:19 PM",,"<EMAIL>","Brent.Forder",,"S-1-5-21-**********-**********-*********-22616","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 4:04:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Forder",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"90684d2a-14d7-40e6-941e-a9292f08dd1c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Md Mishu",,"Md",,"dIMirwLbJUGV+lteWNwwHA==","Software Developer","8/14/2019 5:03:17 PM",,"<EMAIL>","Md.Mishu",,"S-1-5-21-**********-**********-*********-22618","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 4:54:06 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mishu",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"38696e78-f2a2-4b6a-8787-22fc49c34073","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,"Product Management","True","Adam Coppock",,"Adam",,"6XQGz850okm/q6JHxiLa3w==","Product Design Lead","7/24/2019 6:48:36 PM",,"<EMAIL>","adam.coppock",,"S-1-5-21-**********-**********-*********-23264","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/7/2019 10:17:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Coppock","+**************** ,3540","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b6bfaf38-25da-422a-95e0-eccfde446261","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","David Huang",,"David",,"+uVn1yAbqkePSL4fo/Olhg==","Software Developer","7/24/2019 6:48:36 PM",,"<EMAIL>","David.Huang",,"S-1-5-21-**********-**********-*********-22252","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 3:20:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Huang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a388096f-4853-494b-bf44-26ab141106f9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Mychal Hackman",,"Mychal",,"V2Ng2WGkaky23LCvhwTgNQ==","Senior Software Developer","8/13/2019 6:02:29 PM",,"<EMAIL>","mychal.hackman",,"S-1-5-21-**********-**********-*********-22259","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/26/2019 3:01:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hackman",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e19b9c24-b447-41eb-bf8e-253d8f4773dd","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Corey Doty",,"Corey",,"1YIZFRQo0kaP7lbjz0b3CA==","Lead QA Analyst","7/25/2019 4:19:21 PM",,"<EMAIL>","corey.doty",,"S-1-5-21-**********-**********-*********-23276","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/12/2019 2:47:31 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Doty",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7afe0fdb-b204-4dec-84dc-817b83a1f92d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Andrew McFadden",,"Andrew",,"JJGyQ4B920u4WoKkLUl2eA==","Systems Administrator","8/19/2019 4:07:20 PM",,"<EMAIL>","andrew.mcfadden","+1 (250) 575-1878","S-1-5-21-**********-**********-*********-23279","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 3:52:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McFadden","+**************** ,3118","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8c912116-675b-4b3c-a04b-7e094f2c7363","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Stephanie Smith",,"Stephanie",,"n3Zwu3zbs0qCN/R+fn61Fw==","Process Administrator","7/24/2019 7:18:39 PM",,"<EMAIL>","Stephanie.Smith","+1 (778) 242-3120","S-1-5-21-**********-**********-*********-23280","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 3:19:15 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Smith","+**************** ,3655","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7d108fe1-d92c-491b-b419-7ab195c80ed3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Veronika Havelkova",,"Veronika",,"WeK7Krzq2kSYnhAgo3voOQ==","Graphic Designer","7/25/2019 3:49:16 PM",,"<EMAIL>","veronika.havelkova",,"S-1-5-21-**********-**********-*********-23804","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 7:09:11 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Havelkova","+**************** ,3535","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9247384b-8165-4b6f-93ee-77651c9d8441","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Benjamin Belanger",,"Benjamin",,"uyD3r8RQtE+/gIdQxw07EA==","Software Developer","7/24/2019 6:48:36 PM",,"<EMAIL>","benjamin.belanger",,"S-1-5-21-**********-**********-*********-23283","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 5:59:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Belanger",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fa067e09-fc2b-4747-bfff-04fdfbce97a3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Domain Admin - Andrew Stavert",,"Domain Admin -",,"NoQWbcqkIUKQ5VBFH3HiQg==",,"8/20/2019 8:57:31 PM",,,"da-astavert",,"S-1-5-21-**********-**********-*********-22643","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 8:29:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Andrew Stavert",,,"<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a1e24606-55e5-4c83-bd95-63e2d11773ce","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Mathew Levasseur",,"Mathew",,"DcZ3S19s7EG8R2KfxaCeHw==","Lead Software Developer","8/12/2019 11:31:50 PM",,"<EMAIL>","mathew.levasseur",,"S-1-5-21-**********-**********-*********-22644","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 11:24:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Levasseur",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dfc9d0cd-2680-4408-928a-6676c8803188","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Darcy Senger",,"Darcy",,"LG1SjiUbekmA4VWHxpAaQQ==","Graphic Designer","7/25/2019 4:19:21 PM",,"<EMAIL>","darcy.senger",,"S-1-5-21-**********-**********-*********-22646","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 4:19:59 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Senger","+****************, 3537","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"66b72994-874f-48ad-b414-246f3d2fb58c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Yuping Shang",,"Yuping",,"0PvDor96gUefjqvVBd/KJA==","Software Developer","8/2/2019 6:30:37 PM",,"<EMAIL>","yuping.shang",,"S-1-5-21-**********-**********-*********-22648","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 3:42:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e86dfc0c-694a-4fad-91a8-236b2b1652b4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Sofi Mondesir",,"Sofi",,"uWpC13JcXUacuUfMOIimcA==","Implementer","7/3/2019 12:54:47 PM",,"<EMAIL>","sofi.mondesir","+1 (416) 892-8059","S-1-5-21-**********-**********-*********-23810","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 12:32:11 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mondesir","+**************** ,7062","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5474b10a-2a90-4e4b-80af-252ed038e48e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Ewa Godlewska",,"Ewa",,"XF84CY2vlUem5fQSot6ayg==","Software Developer","7/12/2019 10:03:02 PM",,"<EMAIL>","ewa.godlewska",,"S-1-5-21-**********-**********-*********-22650","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/26/2019 4:28:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Godlewska",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9b87673f-cfd6-4c9a-86cf-864458c06c11","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Richelle Ferguson",,"Richelle",,"X3edCMLzQ0SPrikzjgbpPg==","Marketing Manager","8/6/2019 3:33:18 PM",,"<EMAIL>","richelle.ferguson",,"S-1-5-21-**********-**********-*********-23813","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 3:24:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ferguson","+**************** ,3522","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7084cd6b-c304-4de0-89d2-9c7ebc14d21e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Nynke Adams",,"Nynke",,"IZ1w3QVLZUSDoforLyT7VQ==","Project Manager","7/17/2019 5:43:18 PM",,"<EMAIL>","nynke.adams","+1 (403) 829-5067","S-1-5-21-**********-**********-*********-22277","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 5:30:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Adams","+****************","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ab10bde9-be92-48ad-ba84-151b2dd71d01","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Thomas Laehren",,"Thomas",,"i37XRjhrO0CVg69+Fh6Puw==","Senior Business Analyst","7/25/2019 3:49:16 PM",,"<EMAIL>","thomas.laehren",,"S-1-5-21-**********-**********-*********-23815","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 3:30:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Laehren","+****************, 3539","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"07e5156e-1117-4f5f-a55c-dcd000779a7f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Joanne Spatola",,"Joanne",,"rOU894x1iESjU6YPLhGoUg==","Lead Generation Specialist","7/25/2019 3:49:16 PM",,"<EMAIL>","joanne.spatola",,"S-1-5-21-**********-**********-*********-22657","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 4:56:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Spatola","+****************, 3521","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d588938b-fde8-4e51-8063-c3165f060acf","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Carson Milligen",,"Carson",,"DPZzKG5J8UmJHUwX5H53Ew==","Lead Software Developer","7/25/2019 3:49:16 PM",,"<EMAIL>","carson.milligen",,"S-1-5-21-**********-**********-*********-23817","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:01:27 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Milligen",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"53221f3e-ff00-4ff1-b3f1-8cf766e42720","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Paolo Aquino",,"Paolo",,"EAVDKfBifk+CU3CCM0nbOA==","Software Architecture Manager","8/20/2019 3:51:51 PM",,"<EMAIL>","paolo.aquino",,"S-1-5-21-**********-**********-*********-23293","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 3:23:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Aquino","+**************** ,3538","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ad18b135-6f30-4624-87ac-f282a28a2fb5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Kevin Hall",,"Kevin",,"8g2MoWHPqkuc781NcQKhZg==","Software Developer","8/22/2019 9:29:51 PM",,"<EMAIL>","kevin.hall",,"S-1-5-21-**********-**********-*********-23292","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 9:46:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hall",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"59b3119f-67ff-420b-9988-dc917f1fa299","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Nicolas Wourms",,"Nicolas",,"ASMSsUwslEe2SYnts/nPGQ==","Lead Software Developer","8/6/2019 4:33:21 PM",,"<EMAIL>","nicolas.wourms",,"S-1-5-21-**********-**********-*********-22283","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 4:18:13 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wourms",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"191f18eb-d4ab-4c70-9a17-3661ae8c1817","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sean Mikalson",,"Sean",,"6ZRNlPiodku7PJ51TQUaCg==","Platform Integration Specialist","7/25/2019 3:49:16 PM",,"<EMAIL>","sean.mikalson",,"S-1-5-21-**********-**********-*********-22284","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:34:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mikalson",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7dae45cd-f940-401a-9402-4429fe15c104","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Implementations","True","Carminda Fernandez",,"Carminda",,"FsRIb7bUREu6sRWQ2WU46w==","Data Services Representative","6/24/2019 12:14:02 PM",,"<EMAIL>","carminda.fernandez",,"S-1-5-21-**********-**********-*********-23296","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 12:05:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Fernandez","+**************** ,7063","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2758f4fa-7902-499c-884a-966d34931640","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Chase Jensen",,"Chase",,"kSa39fSk+Eyd98SQvIAb4A==","Junior Software Developer","7/24/2019 6:48:36 PM",,"<EMAIL>","chase.jensen",,"S-1-5-21-**********-**********-*********-22661","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 3:39:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jensen",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"41c8c051-1ce2-4b90-8099-afc2960f5a88","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Stacey Tovey",,"Stacey",,"Zld+V/sFUEeasElbMH9LLA==","Senior Data Analyst","7/15/2019 8:05:27 PM",,"<EMAIL>","stacey.tovey",,"S-1-5-21-**********-**********-*********-23823","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 8:00:32 PM","True","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tovey",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c9c772e5-10e2-4662-874a-85ead6b3da75","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Liam Anderson",,"Liam",,"5IHTTwIZFkWarTkQOXB4Yg==","Software Development Manager","8/7/2019 10:34:22 PM",,"<EMAIL>","liam.anderson",,"S-1-5-21-**********-**********-*********-23825","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/7/2019 10:07:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Anderson",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8a2e0044-8f7f-41bc-923a-37603aeb71ae","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Jessica Burtney",,"Jessica",,"GXFSqzpgoUa6AnDnCY8TXQ==","Implementer","6/28/2019 4:17:06 PM",,"<EMAIL>","jessica.burtney","+1 (416) 801-3759","S-1-5-21-**********-**********-*********-22663","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/28/2019 3:48:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Burtney","+**************** ,7065","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dbfabcf9-fea5-41cd-b182-2597b68d1e4e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Rohith Mannem",,"Rohith",,"RgRgPiAKLkeHH5wgCl+T3g==","Software Developer","7/25/2019 3:49:16 PM",,"<EMAIL>","rohith.mannem",,"S-1-5-21-**********-**********-*********-23301","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/24/2019 10:33:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mannem",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b12bda21-4e3d-4c26-8c65-87ebbf8bc44e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Justin Harrington",,"Justin",,"3+k+YKzTSEyuqn88OxQqFg==","Implementer","8/6/2019 6:03:23 PM",,"<EMAIL>","justin.harrington","+1 (416) 527-2301","S-1-5-21-**********-**********-*********-22667","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 5:50:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Harrington","+**************** ,7066","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"237ca44e-14bc-4063-aa8a-2250bfb41e9f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Jun Song",,"Jun",,"8c3SkkzTfEuJfKZcAm6YoQ==","Software Developer","8/6/2019 4:33:21 PM",,"<EMAIL>","jun.song",,"S-1-5-21-**********-**********-*********-22293","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 4:24:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Song",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"09a662e7-2ede-4ad4-843d-be48f6e04cc5","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Satinder Sidhu",,"Satinder",,"paitUQWz+EOuVYZHC9jrYQ==",,"8/1/2019 1:49:46 AM",,"<EMAIL>","satinder.sidhu",,"S-1-5-21-**********-**********-*********-22674","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 4:12:28 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,"BC",,"Sidhu","+**************** ,3659","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c12f3f03-9623-46dd-a730-ca5b4b9230d4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Iram Hussain",,"Iram",,"SfA7TQpXmk+cT8MP3APRow==","Client Services Team Lead","7/22/2019 12:16:46 PM",,"<EMAIL>","iram.hussain",,"S-1-5-21-**********-**********-*********-22673","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 12:03:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Hussain","+**************** ,3658","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d6ec4919-e76a-49d7-ac34-ea557715220e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Tim Sylvester",,"Tim",,"6qDlqfW9u0GDNcNQfhNZRg==","Central Desk Client Care Coordinator","7/15/2019 12:05:11 PM",,"<EMAIL>","tim.sylvester",,"S-1-5-21-**********-**********-*********-22675","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 11:59:47 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Sylvester","+**************** ,3661","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f12bc5a4-7f18-4d84-a184-7404e749dfed","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Christine Karpinsky",,"Christine",,"HIuRuHNUTkarLpLUryXoDA==","Trainer","8/20/2019 6:51:56 PM",,"<EMAIL>","christine.karpinsky",,"S-1-5-21-**********-**********-*********-22672","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 2:42:15 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Karpinsky","+****************, 3657","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"30f81a48-5da8-4f03-a507-2eacbff37526","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Mike Eburne",,"Mike",,"abKZpZs0cUSH59twJUsOpw==","Senior Creative Strategist","7/25/2019 4:49:19 PM",,"<EMAIL>","mike.eburne","250-801-2472","S-1-5-21-**********-**********-*********-22678","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/25/2019 4:37:29 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Eburne","+**************** ,3528","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"33af1c28-9c79-4e47-ac5e-b726a953ddb1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Client Services","True","Cara Dwyer",,"Cara",,"dRvEuxYTU0qbcqFT78YC6Q==","Senior Data Services Representative","7/3/2019 3:24:52 PM",,"<EMAIL>","cara.dwyer","905-449-4851","S-1-5-21-**********-**********-*********-22681","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Peterborough",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 3:22:35 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Dwyer","403-297-0270 ext 775","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f2d01f5c-c382-45dc-ae5e-9a8f5442acec","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Client Services","True","Graham Pomfret",,"Graham",,"Zuf1xdh6ZkWF9y0ckpOyeQ==","Configuration Specialist","7/16/2019 6:42:29 PM",,"<EMAIL>","graham.pomfret",,"S-1-5-21-**********-**********-*********-22683","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Peterborough",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 6:31:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Pomfret","403-297-0270 ext 716","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e8a4763c-8631-40b2-b4c1-b50510e9098b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Nick Janzen",,"Nick",,"wfJxqbg3zUGzLJ4K4b1u7Q==","Manager, Corporate IT","7/24/2019 7:18:38 PM",,"<EMAIL>","nick.janzen",,"S-1-5-21-**********-**********-*********-22685","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/20/2019 4:00:32 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Janzen","+**************** ,3112","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0fc91ef8-c140-40cf-972e-9a0202c2df29","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Liam Leppard",,"Liam",,"Qe2k+/zIjUCvTNSV0PLECQ==","Software Developer","7/25/2019 3:49:16 PM",,"<EMAIL>","liam.leppard",,"S-1-5-21-**********-**********-*********-22302","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/20/2019 4:27:00 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Leppard",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"12c55d16-b04f-4764-a799-c7cc9ef6b875","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Kyle Ramey",,"Kyle",,"Ri7LJglgi0SJ/PlOs3241g==","Software Developer","7/25/2019 3:49:16 PM",,"<EMAIL>","kyle.ramey",,"S-1-5-21-**********-**********-*********-22304","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/28/2019 5:55:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ramey",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"636a7cc9-2cbc-413f-8915-8fb0b3b59781","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Odette Roy",,"Odette",,"Mwo6g3JY/0iZxkuV0fEQzQ==","Application Architect","7/4/2019 3:33:02 PM",,"<EMAIL>","odette.roy",,"S-1-5-21-**********-**********-*********-22692","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 3:29:05 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Roy",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"635ff539-24b5-446b-b899-91b55c117442","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Shabnam Ahmmed",,"Shabnam",,"sdaeAOYog0imh+39lnxHZA==","QA Analyst","7/25/2019 3:49:16 PM",,"<EMAIL>","shabnam.ahmmed",,"S-1-5-21-**********-**********-*********-23848","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 2:11:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ahmmed",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0729c570-a038-4b01-9319-ad53d2413662","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Rakesh Jammula",,"Rakesh",,"G8aLs19MIk6cicslhy74ZQ==","QA Analyst","7/25/2019 3:49:16 PM",,"<EMAIL>","rakesh.jammula",,"S-1-5-21-**********-**********-*********-23323","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 3:05:58 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jammula",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"19463575-5cda-442c-8265-d98f18814911","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","David Krish",,"David",,"mjCUzYB+V0+GOFBfsMYjwA==","Regional Sales & Implementations Manager","7/25/2019 4:19:21 PM",,"<EMAIL>","david.krish","+1 (902) 830-0838","S-1-5-21-**********-**********-*********-22705","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/13/2018 4:55:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Krish",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fabf3f65-0964-4d0f-8a29-fd925b47aefc","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Sam Bassett",,"Sam",,"+m9elplHVkq121zZsqofWA==","Senior Software Developer","8/20/2019 3:51:51 PM",,"<EMAIL>","sam.bassett",,"S-1-5-21-**********-**********-*********-22707","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 3:48:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bassett",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"74ddf8f1-84aa-4b07-bfe9-4ba0fd56881b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Raquel Teixeira",,"Raquel",,"Jb8QrsgKFkSMc9hLYp+8UQ==","Configuration Specialist","7/26/2019 1:19:58 PM",,"<EMAIL>","raquel.teixeira","+1 (647) 355-4781","S-1-5-21-**********-**********-*********-22711","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/26/2019 1:08:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Teixeira","+**************** ,3579","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ef058026-9871-4e8d-b7c7-a3a2bc268514","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Divya Chhabra",,"Divya",,"trHoPUvqRUuXQR8+FNImtQ==","Configuration Specialist","8/15/2019 1:34:12 PM",,"<EMAIL>","divya.chhabra",,"S-1-5-21-**********-**********-*********-22710","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 1:05:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chhabra","+**************** ,7071","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1c5a2690-74e3-48c0-9296-05a168775c54","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Carly Rigg",,"Carly",,"MHNy+LWlA0e+r8BCZ4jRhw==","Junior Business Analyst","8/15/2019 3:04:17 PM",,"<EMAIL>","carly.rigg",,"S-1-5-21-**********-**********-*********-22713","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 3:33:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Rigg","+**************** ,3642","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"71d01972-107b-4a68-85b2-c27e5f67a03f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Chris Lo",,"Chris",,"S3Gtl/ClhE2e8/LyUmrBQg==","Product Engagement Specialist","8/12/2019 9:31:46 PM",,"<EMAIL>","chris.lo",,"S-1-5-21-**********-**********-*********-23866","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/28/2019 3:57:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Lo",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fc7e6f61-537a-4a52-8769-187408506fae","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Heather Gardiner",,"Heather",,"BwR8McueNEmag+s27roH5Q==","Central Desk Client Care Coordinator","7/18/2019 12:13:54 PM",,"<EMAIL>","heather.gardiner",,"S-1-5-21-**********-**********-*********-22718","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 11:56:00 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Gardiner","+**************** ,3663","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1c2c0e7a-56b3-4acf-846d-a5f879dc4844","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,"Client Services","True","Parker Burns",,"Parker",,"pWlgVrWrY0qU8VtZhTawpA==","Central Desk Client Care Coordinator","8/23/2019 3:30:29 PM",,"<EMAIL>","parker.burns",,"S-1-5-21-**********-**********-*********-22719","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 3:25:06 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Burns","+**************** ,3635","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0e4e49b7-9abf-475e-8eec-4081dd0dffdb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Shannon Kennelly",,"Shannon",,"/6KoBk6fvketEWO6+DJEog==","Client Services Team Lead","7/24/2019 5:48:32 PM",,"<EMAIL>","shannon.kennelly",,"S-1-5-21-**********-**********-*********-22720","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/12/2019 3:31:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Kennelly","+**************** ,3665","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"de9a0ebb-b3a2-4700-8bcc-af4e32afabc8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Joshua Abaloyan",,"Joshua",,"smmry/06W0GDZKZD+dgFgA==","Lead Software Developer","8/19/2019 4:07:20 PM",,"<EMAIL>","joshua.abaloyan",,"S-1-5-21-**********-**********-*********-22721","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 3:56:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Abaloyan",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0585c3d1-ce7f-456d-b8d1-548ff8df7259","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Human Resources","True","Shannon Ballance",,"Shannon",,"qRLZIU+Ic0OxB2B2DXMdHg==","Learning and Development Lead","7/23/2019 3:17:40 PM",,"<EMAIL>","shannon.ballance",,"S-1-5-21-**********-**********-*********-22722","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 3:09:50 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ballance","+**************** ,3569","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7d770b24-91cd-4944-af8c-85dbba44a7f9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Ryan Cotter",,"Ryan",,"xL6kSKEkpkSRzGcUfIBMrA==","Business Analyst Team Lead","8/19/2019 4:07:20 PM",,"<EMAIL>","ryan.cotter",,"S-1-5-21-**********-**********-*********-22320","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 4:03:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Cotter","+**************** ,3570","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4dba9c91-7fb9-4f36-af51-7cd573a83efe","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Zohra Charaniya",,"Zohra",,"DEk1V1HBXUKDoZ83w4E2jg==","Implementer","8/15/2019 4:04:19 PM",,"<EMAIL>","zohra.charaniya","+1 (416) 435-8524","S-1-5-21-**********-**********-*********-22725","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 3:36:00 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Charaniya","+**************** ,7073","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"10341947-8f88-42c9-bbca-e75802a79c1a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","QHR Technologies Inc.",,"Canada",,"Finance","True","Tanice Fadden",,"Tanice",,"KdoQ7Nwrxk2x3sOYdGFxzQ==","Revenue Management Clerk","7/24/2019 4:48:30 PM",,"<EMAIL>","tanice.fadden",,"S-1-5-21-**********-**********-*********-23874","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 9:37:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Fadden","+****************, 3069","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d4a51f90-a82d-4f2c-abd2-4cc6d6ec1194","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","QHR Technologies Inc.",,"Canada",,"Finance","True","Kelly McFarlane",,"Kelly",,"vBph20V27U+LSCDutpXg5A==","Revenue Management Clerk","7/24/2019 4:48:30 PM",,"<EMAIL>","kelly.mcfarlane",,"S-1-5-21-**********-**********-*********-23873","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 5:08:32 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McFarlane","+**************** ,3058","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6ba1717b-6c04-4045-9cc3-34e682f187d0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Business Development","True","Nama Vythilingum",,"Nama",,"Hxv+1lVvPEKStiZjauXV+Q==","Account Executive","7/22/2019 1:46:48 PM",,"<EMAIL>","nama.vythilingum",,"S-1-5-21-**********-**********-*********-23876","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 1:40:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Vythilingum",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e24bf0c8-7e4d-4060-8b21-ecf25cbf740c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Srinivas Vemulapalli",,"Srinivas",,"T3lss99ajk2hNDwS6AP4YQ==","Software Developer","8/2/2019 11:30:45 PM",,"<EMAIL>","srinivas.vemulapalli",,"S-1-5-21-**********-**********-*********-22331","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 11:13:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Vemulapalli",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"49816ebe-b860-4a5b-9ce6-0b257a272986","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Megan Owens",,"Megan",,"ojHfr1D92U2BBxRsmZnbKQ==","Implementer","7/8/2019 6:41:02 PM",,"<EMAIL>","megan.owens","+1 (403) 796-7460","S-1-5-21-**********-**********-*********-22733","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 6:23:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Owens",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"43de1f11-8de9-4d00-a316-6217bef5b569","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Lori Dencsak",,"Lori",,"5pecalarzUqTActZId81hw==","Lead QA Analyst","7/18/2019 3:44:01 PM",,"<EMAIL>","lori.dencsak",,"S-1-5-21-**********-**********-*********-22736","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 3:34:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Dencsak","+**************** ,3572","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"674b080f-381f-4484-8dae-9a5cb8429497","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Audrey Blatz",,"Audrey",,"qXOBHDbYckW/rc28Lzj6yg==","Account Executive","8/13/2019 3:32:24 PM",,"<EMAIL>","audrey.blatz","+1 (204) 541-0523","S-1-5-21-**********-**********-*********-22335","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 3:16:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Blatz",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"954f83ef-c644-455a-9c5d-ca37a8cdde33","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sergiu Barsa",,"Sergiu",,"738cTN3zEUiY4t3et9pSNg==","Senior Software Developer","8/6/2019 5:33:22 PM",,"<EMAIL>","sergiu.barsa",,"S-1-5-21-**********-**********-*********-22337","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 5:20:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Barsa",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8db9982c-c302-4a18-8fe7-17a7bf586a4f","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Congsong Zhang",,"Congsong",,"2Iu56EAF+E+BAYrqPicc+w==","Software Developer","8/9/2019 10:37:38 PM",,"<EMAIL>","congsong.zhang",,"S-1-5-21-**********-**********-*********-22336","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 3:50:29 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Zhang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"577985d6-3be7-48db-90b1-ffba8ee29acf","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sviatlana Vinnikava",,"Sviatlana",,"R0ktKYRa+Eab6Xr5l5uBMg==","QA Analyst","7/24/2019 3:48:27 PM",,"<EMAIL>","sviatlana.vinnikava",,"S-1-5-21-**********-**********-*********-22338","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/24/2019 3:30:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Vinnikava","+**************** ,3559","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fabe91de-e42e-4fdc-8304-663424a2a734","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Sales","True","Danielle Semple",,"Danielle",,"9+6vDBbdBUyNATwlbW69vQ==","Practice Consultant","6/3/2019 3:39:55 PM",,"<EMAIL>","danielle.semple","+1 (416) 992-3873","S-1-5-21-**********-**********-*********-22342","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","5/22/2019 6:35:58 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Semple",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f5a8db52-4c67-4803-a633-f08772b2f163","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Jo Yoshida",,"Jo",,"/MDvph+n10WuDjxYOvFyvg==","Senior Software Developer","7/3/2019 8:55:02 PM",,"<EMAIL>","jo.yoshida",,"S-1-5-21-**********-**********-*********-22344","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 8:48:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Yoshida",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bed28acd-6443-49ab-bcb2-4717d405c37e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","James Blackmer",,"James",,"2dLSRQvfO0mpHmO44p/pww==","Senior Software Developer","8/12/2019 10:31:48 PM",,"<EMAIL>","james.blackmer",,"S-1-5-21-**********-**********-*********-22343","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 3:02:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Blackmer",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5edde28f-c5c5-40da-841c-a9249ef0989e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Client Services","True","Aamir Khan",,"Aamir",,"2FdgtP/CTE6JtgTo+gzXwQ==","Client Services Analyst","7/31/2019 12:19:19 PM",,"<EMAIL>","aamir.khan",,"S-1-5-21-**********-**********-*********-23344","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 11:53:11 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Khan","+**************** ,7077","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2f81203c-417b-4a1d-a977-84987918c333","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Client Services","True","Lubna Shahid",,"Lubna",,"dbUXs/auO0CtSuLAY6sVqg==","Enterprise Relationship Analyst","8/20/2019 5:51:54 PM",,"<EMAIL>","lubna.shahid",,"S-1-5-21-**********-**********-*********-23343","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 3:09:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shahid","+**************** ,7076","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a43aa937-e8d1-4037-888d-d5e894233c2d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Justin Calvin",,"Justin",,"L0H8RAaJxU+rfgYcTYFFEA==","Client Services Analyst","8/7/2019 3:04:08 PM",,"<EMAIL>","justin.calvin",,"S-1-5-21-**********-**********-*********-23348","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/7/2019 2:37:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Calvin","+**************** ,3669","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c70e475f-da4b-4dba-b065-8bd019179ad0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Kendre Scott",,"Kendre",,"SD14p8xYhUCx8WHOprTbfA==","Instructional Designer","8/13/2019 3:32:25 PM",,"<EMAIL>","kendre.scott","+1 (250) 212-7846","S-1-5-21-**********-**********-*********-23350","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 3:08:57 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Scott","+**************** ,3724","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a6a3a3ff-0ade-4ec1-91b8-b6b43f0c6551","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Holli Farrell",,"Holli",,"SIk0mfPDekCIDfX2Z9+q3g==","Training Content Developer","7/4/2019 3:33:02 PM",,"<EMAIL>","holli.farrell",,"S-1-5-21-**********-**********-*********-23347","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 3:12:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Farrell","+**************** ,3421","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e1b11900-ced3-46b0-b4a4-e0c926024ef0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Karley Davis",,"Karley",,"jh097qUXZk+J40t27zT4fA==","Trainer","8/15/2019 3:34:17 PM",,"<EMAIL>","karley.davis","+1 (250) 864-4295","S-1-5-21-**********-**********-*********-23349","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 5:54:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Davis","+**************** ,3413","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fc4d1043-7289-42bd-90ab-1bee320b5c2b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Richard Welsh",,"Richard",,"wvts78zzoUmFNJj+V7/MWg==","Client Services Analyst","8/22/2019 3:29:39 PM",,"<EMAIL>","richard.welsh",,"S-1-5-21-**********-**********-*********-22348","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/22/2019 3:29:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Welsh","+**************** ,3672","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b67e236c-20de-4dcf-a17b-ff3c5a28b3d7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","James Michaud",,"James",,"WPcwqbjKWUatpbrK9rgruw==","Software Developer","8/20/2019 2:51:48 PM",,"<EMAIL>","james.michaud",,"S-1-5-21-**********-**********-*********-22757","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 2:45:58 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Michaud",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"25edd0d7-598b-4796-9a0f-a05c0ebd2cb6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Mark Coutts",,"Mark",,"KTfucJAKZE2c1hlVmIRbIQ==","Data Analyst","7/23/2019 5:17:45 PM",,"<EMAIL>","mark.coutts",,"S-1-5-21-**********-**********-*********-22758","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 4:51:32 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Coutts",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f7687ef6-4eff-4ac3-a717-0130f5e81e85","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Courtney Annesley",,"Courtney",,"kNDlicpXkkWoOzJ3Tfu9/g==","Senior Business Analyst","6/17/2019 4:45:46 PM",,"<EMAIL>","courtney.annesley",,"S-1-5-21-**********-**********-*********-22760","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/17/2019 4:31:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Annesley",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e9ddbc07-6b6f-4b64-8ae8-3f29948f9e70","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Naomi Mack",,"Naomi",,"8lacwDNGvEiC1dYAHUOfoQ==","Technical Product Manager","8/16/2019 9:35:05 PM",,"<EMAIL>","naomi.mack",,"S-1-5-21-**********-**********-*********-22766","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 9:34:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Mack","+**************** ,3519","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"77fd7529-d2f5-45d5-bccf-98013d17e7ee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Technology","True","Robert Kac",,"Robert",,"HyJLP6ySPE6UTQ39m7pooQ==","Database Administration Team Lead","8/21/2019 3:58:06 PM",,"<EMAIL>","robert.kac","+1 (403) 608-3372","S-1-5-21-**********-**********-*********-22764","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,"T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 4:17:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta",,"Kac",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5066d588-149f-4230-ae94-e02feb0bbc57","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Neegam Panchal",,"Neegam",,"XQtwwFBGXEul3Ez8z2B0Ow==","Lead Data Analyst","8/16/2019 5:05:01 PM",,"<EMAIL>","neegam.panchal",,"S-1-5-21-**********-**********-*********-22768","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 4:41:38 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Panchal","+**************** ,3517","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"724dd06b-e799-42bd-a6ba-3f861223e3ba","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Trish Smith",,"Trish",,"8CcKNieNV02S6/SdvQk/NQ==","Product Manager","7/2/2019 10:24:20 PM",,"<EMAIL>","trish.smith",,"S-1-5-21-**********-**********-*********-22771","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 10:22:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Smith","+**************** ,3546","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fafb644b-60e2-4649-ac3a-bd3b259f53ca","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Executive","True","Chris MacPherson",,"Chris",,"s1RzknQkTkuWoz3c/0OuVw==","Director of Operations","7/25/2019 3:49:16 PM",,"<EMAIL>","chris.macpherson","+1 (778) 363-1637","S-1-5-21-**********-**********-*********-22773","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 3:04:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"MacPherson","+**************** ,3072","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a2a6de97-e111-4269-bd1e-765d01fab1b6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Implementations","True","Ryan Alexander",,"Ryan",,"ynHczxHODkuIpfnsJckD/w==","Data Analyst","8/6/2019 2:33:15 PM",,"<EMAIL>","ryan.alexander",,"S-1-5-21-**********-**********-*********-23384","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 2:22:28 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Alexander","+**************** ,3512","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f62378a0-cd89-4dbc-a307-1558f126b3ea","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Reilly Harper",,"Reilly",,"4yLztd4X9EW1Pr31E+zTwA==","Central Desk Client Care Coordinator","7/29/2019 3:52:15 PM",,"<EMAIL>","reilly.harper",,"S-1-5-21-**********-**********-*********-23391","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 3:28:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Harper","+**************** ,3624","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"717294e0-2b2b-465f-a65e-49fac759ccbe","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Sienna Kohn",,"Sienna",,"+7kdej7UZ0i7o1/4YY9gSQ==","Technical Writer","8/21/2019 10:28:19 PM",,"<EMAIL>","sienna.kohn",,"S-1-5-21-**********-**********-*********-23392","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 3:54:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Kohn","+**************** ,3625","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"55fd171a-e80b-44a2-8640-c2bb687897fa","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Kailyn Pederson",,"Kailyn",,"SVQFp/NwKEqezw0VbnyRqQ==","Product Services Coordinator","8/16/2019 3:35:05 PM",,"<EMAIL>","kailyn.pederson",,"S-1-5-21-**********-**********-*********-23388","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 3:05:38 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Pederson","+**************** ,3734","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c2e281f8-3308-49f5-aeb3-25758c83d03e","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Cheryl Hannah",,"Cheryl",,"7kjHS56LbUKhEa8CtGhJoA==",,"8/16/2019 11:35:07 PM",,"<EMAIL>","cheryl.hannah",,"S-1-5-21-**********-**********-*********-23387","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/14/2019 3:03:18 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,"BC",,"Hannah",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"41311d68-b80d-4f5a-bdbc-bb4529be40da","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Marketing","True","Megan Folster",,"Megan",,"pOTDcrV4cUWFOFCW+F9jEQ==","Lead Generation Specialist","8/13/2019 10:32:39 PM",,"<EMAIL>","megan.folster",,"S-1-5-21-**********-**********-*********-23389","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 3:01:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Folster","+**************** ,3550","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a6e24652-bf88-40be-bb01-12ef32f8b68d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Randy Lewis",,"Randy",,"66xadQwWn0m8n5dMQVaE1w==","Client Services Analyst","7/22/2019 3:46:53 PM",,"<EMAIL>","randy.lewis",,"S-1-5-21-**********-**********-*********-23390","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:26:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Lewis","+**************** ,3612","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"03b890ad-8d51-4da6-9696-f841b5a27797","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Client Services","True","Ashika Balakrishnan",,"Ashika",,"Zg9hYj/dREO8YklZDV+nIQ==","Client Satisfaction Representative","8/20/2019 5:51:54 PM",,"<EMAIL>","ashika.balakrishnan",,"S-1-5-21-**********-**********-*********-23904","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/4/2019 2:44:25 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Balakrishnan","+**************** ,7083","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6fec2413-4348-495e-82a1-5ba6c1142de3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Client Services","True","Paige O'Hearn",,"Paige",,"+KryfTqJfUmjPzeQa4AfDQ==","Enterprise Relationship Analyst","7/25/2019 4:19:21 PM",,"<EMAIL>","paige.ohearn",,"S-1-5-21-**********-**********-*********-23903","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 12:37:44 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"O'Hearn","+**************** ,7082","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6e9fb3b9-9853-4501-8c7e-b9c856f902d3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Richard Millard",,"Richard",,"nRQQ7IYeIkWSZDg8QrekNA==","Lead Software Developer","8/19/2019 2:37:17 PM",,"<EMAIL>","richard.millard",,"S-1-5-21-**********-**********-*********-23396","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:34:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Millard",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c07719fe-0183-4694-991d-c58448f7cd6c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Sharon Gupta",,"Sharon",,"M7Ma29hf00uBWFlb8wezcw==","Client Services Analyst","4/4/2019 3:48:54 PM",,"<EMAIL>","sharon.gupta",,"S-1-5-21-**********-**********-*********-23401","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","4/4/2019 3:19:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Gupta","+**************** ,3641","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4ba571f1-f3bb-412e-ae57-ca5cf4c45ca4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Jonathan Chapman",,"Jonathan",,"vVg8Gnk4bUuUuu2FhSfPyw==","Central Desk Client Care Coordinator","8/2/2019 12:20:51 PM",,"<EMAIL>","jonathan.chapman",,"S-1-5-21-**********-**********-*********-23402","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 11:56:54 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Chapman","+**************** ,3645","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3636ca7b-d05c-4c30-8145-ffacaacb5381","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Yu Zhi Xing",,"Yu Zhi",,"PemHknGx3EGX3aq9+wOZkw==","Software Developer","8/19/2019 3:37:17 PM",,"<EMAIL>","yuzhi.xing",,"S-1-5-21-**********-**********-*********-23399","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 3:09:00 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Xing",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c996a9e7-8bcb-4339-91f8-6b8dab78b80f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","QHR Technologies Inc.",,,,"Technology","True","Malcolm Kennedy",,"Malcolm",,"EsxpW4pZBEyGvARNAF6lnA==","Citrix Administrator","7/24/2019 7:18:38 PM",,"<EMAIL>","malcolm.kennedy","+1 (250) 300-7486","S-1-5-21-**********-**********-*********-23405","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/5/2019 4:09:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kennedy","+**************** ,3123","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d949b548-ba4b-41c0-9d90-d9b82b1d7d02","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Steve Lewis",,"Steve",,"x5M301ZQpU+VBPeMLEZW4A==","Reporting Analyst","8/21/2019 11:28:20 PM",,"<EMAIL>","steve.lewis",,"S-1-5-21-**********-**********-*********-22787","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 2:48:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lewis",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e578c8a3-3570-4748-83de-ed3921dd7261","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Ali Merchant",,"Ali",,"iXDTqB1wF0exwMe6SQg2pA==","Lead Backup Architect","8/9/2019 7:37:29 PM",,"<EMAIL>","ali.merchant","+1 (416) 220-6632","S-1-5-21-**********-**********-*********-22392","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 3:09:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Merchant","+**************** ,3124","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"94094173-22a1-4c4a-ba31-aa8710e680cf","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Administration","True","Darci Perdue",,"Darci",,"pUZPGCU/ykaPxkrDqfklNQ==","Office Manager","7/24/2019 4:48:30 PM",,"<EMAIL>","darci.perdue",,"S-1-5-21-**********-**********-*********-22790","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 12:39:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Perdue","+**************** ,7002","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a00882d2-9f45-45e7-ac8c-0f346b729b63","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,"Canada",,"Finance","True","Sharlene Quinn",,"Sharlene",,"fVpnRuOT0EmUvNFfhc8kUQ==","Revenue Management Clerk","7/8/2019 4:20:48 PM",,"<EMAIL>","sharlene.quinn",,"S-1-5-21-**********-**********-*********-23417","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 4:00:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Quinn","+**************** ,3049","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"286006c1-0e6e-42ab-83d9-df10d3885cb8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Frank Kim",,"Frank",,"Uyz+OUkvh0+1985upj1BpA==","Principal Solution Architect","7/25/2019 4:19:21 PM",,"<EMAIL>","frank.kim",,"S-1-5-21-**********-**********-*********-23920","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/25/2019 2:03:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kim",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"82d674d8-4ffc-42b2-947c-18ec740ecfda","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Amit Jathar",,"Amit",,"StZBK0/rf0CQw5u7EjvJLQ==","QA Manager","8/7/2019 5:04:11 PM",,"<EMAIL>","amit.jathar",,"S-1-5-21-**********-**********-*********-23922","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/12/2019 3:33:27 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jathar",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f1f7575c-f8b9-4873-a8b7-e465a61670b1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Neethu Sasidaran",,"Neethu",,"vdwB7Dx6q0GcFrvkOaYaeg==","Lead Software Developer","8/9/2019 9:37:33 PM",,"<EMAIL>","neethu.sasidaran",,"S-1-5-21-**********-**********-*********-23420","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 9:21:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Sasidaran",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6f3b264d-6d67-4bcf-86dc-bbb241089f78","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Paul Hart",,"Paul",,"ZOHRuo/Tn0GuwbJIIT20qA==","Lead Generation Specialist","8/20/2019 10:27:30 PM",,"<EMAIL>","paul.hart",,"S-1-5-21-**********-**********-*********-22796","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 10:23:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hart","+**************** ,3509","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"97fc05e4-8626-4526-b0a4-59002d0a4b63","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Brittany Koehler",,"Brittany",,"rZXtdzyhjUmpAB3xPtoj3g==","Central Desk Client Care Coordinator","7/31/2019 3:19:25 PM",,"<EMAIL>","brittany.koehler",,"S-1-5-21-**********-**********-*********-22412","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 3:12:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Koehler","+**************** ,3648","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2af17de0-4b5a-4514-b0d0-a8e7f23fbda3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Shannon Nebert",,"Shannon",,"MT4+ux91mEOyDLGfF9iVJA==","Client Services Analyst","7/15/2019 12:05:11 PM",,"<EMAIL>","shannon.nebert",,"S-1-5-21-**********-**********-*********-22419","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 11:59:37 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Nebert","+**************** ,3673","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"03fd4b0c-36ae-421c-9f7e-b4945e9614e5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Anett Kalmanczhey",,"Anett",,"1ue1U7AjWUGrF+bmIL32Fw==","Enterprise Relationship Analyst","7/27/2019 2:50:20 AM",,"<EMAIL>","anett.kalmanczhey",,"S-1-5-21-**********-**********-*********-22410","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 2:03:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Kalmanczhey","+**************** ,3603","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c8fdcc5a-c95d-4add-880e-8df120b7ebed","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Christina VandenBrink",,"Christina",,"m4XRNHaDl0e25M71z1KEew==","Implementer","7/23/2019 8:17:53 PM",,"<EMAIL>","christina.vandenbrink",,"S-1-5-21-**********-**********-*********-22415","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 7:53:05 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"VandenBrink",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4e7901e3-07e6-4696-bdaf-fa462d150d98","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Graeme Siewert",,"Graeme",,"cNmQTeIKt0ukNUiqMu1Xsg==","Client Services Analyst","7/11/2019 2:40:38 PM",,"<EMAIL>","graeme.siewert",,"S-1-5-21-**********-**********-*********-22416","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 2:24:57 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Siewert","+**************** ,3656","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e0d81c8c-0fbc-42ce-9d53-1c0d0abef48b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Caitlin Slavik",,"Caitlin",,"bNNYiRVau0Omovx2XN+NtA==","Client Services Manager","7/24/2019 5:48:32 PM",,"<EMAIL>","caitlin.slavik","+1 (250) 808-8699","S-1-5-21-**********-**********-*********-22413","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 2:51:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Slavik","+**************** ,3674","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1398a1a0-2fa7-4f72-b936-cee7ba2c5f14","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Jagmeet Kaur",,"Jagmeet",,"BvQOyREcTESdhsXGIVv6GQ==","Client Services Analyst","7/3/2019 3:24:52 PM",,"<EMAIL>","jagmeet.kaur",,"S-1-5-21-**********-**********-*********-22417","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 3:18:27 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Kaur","+**************** ,3664","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b9cd4472-74c1-4f81-aa58-8b380b98fba9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,"Canada",,"Finance","True","Christina Bye",,"Christina",,"mIUZzkRTLUOlWznJUUt7vw==","Revenue Management Clerk","7/24/2019 4:48:30 PM",,"<EMAIL>","christina.bye",,"S-1-5-21-**********-**********-*********-22414","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 11:03:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bye",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d9e1e366-d43a-47e1-bd30-ff9484a49382","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Operations","True","David Wiens",,"David",,"JM+JcGgrX0Sj//FQyjppPQ==","SalesForce Product Owner","7/24/2019 6:48:36 PM",,"<EMAIL>","david.wiens","+1 (250) 863-1772","S-1-5-21-**********-**********-*********-22426","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 10:33:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Wiens","+**************** ,3507","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a2d7c868-9429-473b-8789-93b41bd1e2ff","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Heather DiPalma",,"Heather",,"b74FIBdmO0uu8OnRwe8PMw==","Privacy Analyst","8/22/2019 6:29:45 PM",,"<EMAIL>","heather.dipalma",,"S-1-5-21-**********-**********-*********-22427","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/22/2019 6:16:53 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"DiPalma","+**************** ,3506","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"74789e3c-3ac6-49e3-95b8-0a62ea68272a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Marcelo Ferreira",,"Marcelo",,"GSusQjbNtUSgeNZOZG4nCA==","Senior Software Developer","8/2/2019 4:51:03 PM",,"<EMAIL>","marcelo.ferreira",,"S-1-5-21-**********-**********-*********-22429","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 4:35:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ferreira",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"65971d81-22c2-4081-86f7-11140eca3019","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Levi Miller",,"Levi",,"nRumZu7480y7vPZ9F4+FmA==","Junior Software Developer","6/26/2019 5:02:30 PM",,"<EMAIL>","levi.miller",,"S-1-5-21-**********-**********-*********-22428","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/26/2019 4:37:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Miller",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dd70687b-4f53-4af2-9d0d-2c72779552cb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sally Nimmo",,"Sally",,"Y3nzF63qf0KDxIG4OsYqRg==","Junior Software Developer","7/2/2019 3:50:45 PM",,"<EMAIL>","sally.nimmo",,"S-1-5-21-**********-**********-*********-22430","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:50:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Nimmo",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e805baba-a7ca-4a98-84eb-bc2de4f7fc28","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Divya Manyala",,"Divya",,"wYU7+Z0mT0+3rGfmgkT0Pg==","Product Owner","8/13/2019 4:02:25 PM",,"<EMAIL>","divya.manyala",,"S-1-5-21-**********-**********-*********-23446","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto - Plastic Office",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 3:32:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","ON",,"Manyala",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3bf1987e-67eb-4fc5-9a16-f93d8dc8bf2d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","David Braaten",,"David",,"fTJHDIXokk+lfY0aPfncdQ==","Senior Software Developer","7/22/2019 5:16:57 PM",,"<EMAIL>","david.braaten",,"S-1-5-21-**********-**********-*********-23449","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,".",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 6:31:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Braaten",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a782335b-06af-4cc7-bea0-a6f7b3e47ead","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Human Resources","True","Courtney Stokman",,"Courtney",,"OMJoy0i92ECkhyZTft5oWg==","Human Resources Coordinator","8/7/2019 11:34:27 PM",,"<EMAIL>","courtney.stokman",,"S-1-5-21-**********-**********-*********-23448","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 7:39:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Stokman","+**************** ,3064","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7f3a045b-b1b4-4cf5-bf8b-fac1ebc3e458","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Zifang Jiang",,"Zifang",,"hzl1EV6VVE2vnNIf5LUJSw==","Junior Software Developer","7/10/2019 11:10:05 PM",,"<EMAIL>","zifang.jiang",,"S-1-5-21-**********-**********-*********-23451","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 10:59:05 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jiang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b8281ae7-6799-4c3b-a994-cf96873354c1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Dan Thiessen",,"Dan",,"Hr8lmoIsbkishg8F/i9AQA==","Client Services Analyst","8/7/2019 3:04:08 PM",,"<EMAIL>","dan.thiessen",,"S-1-5-21-**********-**********-*********-23953","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/7/2019 3:03:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Thiessen","+**************** ,3678","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5da25c6b-9bbb-446b-b797-dadba3fe4356","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Kayla Raine",,"Kayla",,"p+/1bbcNpkeqb0zr7a/5Tw==","Client Services Analyst","7/29/2019 3:52:15 PM",,"<EMAIL>","kayla.raine",,"S-1-5-21-**********-**********-*********-23954","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 3:29:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Raine","+**************** ,3679","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6eb919dd-2a2b-418d-8a4a-680241fe867d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Steve Forsythe",,"Steve",,"DxSgK/yRl06oGz0EQWmu2g==","Client Services Analyst","7/29/2019 4:22:15 PM",,"<EMAIL>","steve.forsythe",,"S-1-5-21-**********-**********-*********-23955","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 4:04:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Forsythe","+**************** ,3680","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"07bd6f5b-e964-4f57-8bcc-ef9c7636e1f8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Baltej Giri",,"Baltej",,"E2UWs0Hj7kmHkx+iVfUamQ==","Client Services Analyst","7/22/2019 11:17:08 PM",,"<EMAIL>","baltej.giri",,"S-1-5-21-**********-**********-*********-23950","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 11:01:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Giri","+****************, 3638","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2f754f9c-b1f4-4ea0-a80e-d25ebe17753e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Abhishek Dutta",,"Abhishek",,"m6Sv0CviSkqpPwAZsCnDnA==","Lead QA Analyst","7/26/2019 4:20:04 PM",,"<EMAIL>","abhishek.dutta",,"S-1-5-21-**********-**********-*********-23949","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/26/2019 3:54:00 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Dutta",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"29b64c74-9ce3-4c65-a052-ec9ff9ccb728","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Technology","True","Crystal Benoit",,"Crystal",,"DQYaOtH8qkKyeZypPLjwxw==","Process Administrator","7/25/2019 4:19:21 PM",,"<EMAIL>","crystal.benoit","+1 (306) 230-0853","S-1-5-21-**********-**********-*********-23952","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 3:46:09 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Benoit","+**************** ,3515","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2396077e-76ac-4c44-8f61-9ae50585395d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Brian Matte",,"Brian",,"AriyS3CfA0K79kpG+gKvNw==","Junior Software Developer","7/26/2019 4:20:04 PM",,"<EMAIL>","brian.matte",,"S-1-5-21-**********-**********-*********-23960","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/26/2019 4:16:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Matte",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1f3ad758-2248-4bea-bc8e-20c56276e7a6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Brad Fuller",,"Brad",,"FVwA6mbikE64fUfVbl1WvQ==","QA Analyst","8/20/2019 1:51:46 PM",,"<EMAIL>","brad.fuller",,"S-1-5-21-**********-**********-*********-23959","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 1:29:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Fuller",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d77ca51d-5364-4ca8-a77f-637335fdc646","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Eliana Wardle",,"Eliana",,"oTvu+QKS2k2jduB/NtljMg==","Junior Software Developer","7/25/2019 4:19:21 PM",,"<EMAIL>","eliana.wardle",,"S-1-5-21-**********-**********-*********-23961","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 6:09:28 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wardle",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"532fde0c-eb40-46fc-9f92-315a11dcbda3","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Karthik Mokkapati",,"Karthik",,"gETh/ke9JE+8LCPbKDhTjg==",,"7/22/2019 5:46:58 PM",,"<EMAIL>","karthik.mokkapati",,"S-1-5-21-**********-**********-*********-23962","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/19/2019 11:56:45 PM","False","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Mokkapati",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0f252d9c-8060-4dce-9cee-9f12ba9e865c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","David Smekal",,"David",,"IBtb9oUfAEWmDl7VcN6FYw==","Junior Software Developer","8/12/2019 3:09:46 PM",,"<EMAIL>","david.smekal",,"S-1-5-21-**********-**********-*********-22816","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 3:05:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Smekal",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f757f1e5-3e98-4c36-91de-adce7a32b0d7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Carlene Williams",,"Carlene",,"O4e4O8Fb8k2dmTjXOy+7iw==","Implementer","7/31/2019 3:49:26 PM",,"<EMAIL>","carlene.williams","+1 (416) 574-4035","S-1-5-21-**********-**********-*********-23458","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 3:32:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Williams",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5147d23b-7e4b-46a2-aa14-43abca3eaedd","User","False",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Jennifer Tongol",,"Jennifer",,"qxbpPEBmbU+Kh/7taTGBPA==",,"8/7/2019 11:34:27 PM",,"<EMAIL>","jennifer.tongol",,"S-1-5-21-**********-**********-*********-23459","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 4:47:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Tongol",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2db72fda-37e0-4a73-bc5c-19cc3d6ff91a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Punita Gosar",,"Punita",,"/rAt8oU4JUaPoWTqzwtBew==","Senior Software Developer","8/23/2019 7:00:11 AM",,"<EMAIL>","punita.gosar",,"S-1-5-21-**********-**********-*********-23465","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 6:34:46 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Gosar",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b1fa1108-f684-4aed-8e8d-2af3235408e5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Damian Hamilton",,"Damian",,"Efqaefs6/02N2R5uWBlRNA==","Junior Software Developer","8/14/2019 3:03:12 PM",,"<EMAIL>","damian.hamilton",,"S-1-5-21-**********-**********-*********-23464","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 2:44:15 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hamilton",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"34171931-7466-4fe9-adca-78e739a7ef74","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Brett Evans",,"Brett",,"EjPG6Mms40efPzuGmOId6A==","QA Analyst","8/12/2019 3:09:46 PM",,"<EMAIL>","brett.evans",,"S-1-5-21-**********-**********-*********-23463","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 3:06:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Evans",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5db9aefc-c7e3-47cd-b9c1-5c24beaac35d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Katie Foster",,"Katie",,"0evoVACyC0yH04U3qr4XMg==","CS Knowledge Base Analyst","8/21/2019 2:58:04 PM",,"<EMAIL>","katie.foster",,"S-1-5-21-**********-**********-*********-22450","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/21/2019 2:50:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Foster","+**************** ,3683","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"816ad179-899a-4354-94a7-91e672fb87f9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Claas Koenig",,"Claas",,"iYsgMo+fFEmWssndUve6Wg==","Client Services Analyst","7/24/2019 5:48:32 PM",,"<EMAIL>","claas.koenig",,"S-1-5-21-**********-**********-*********-22447","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 2:35:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Koenig","+**************** ,3667","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d8fb1469-3b70-4055-a2af-e8ee9924b99d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Bib Patel",,"Bib",,"Svk7Tcei406EU/mV0Cgoqg==","Client Satisfaction Representative","7/24/2019 5:48:32 PM",,"<EMAIL>","bib.patel",,"S-1-5-21-**********-**********-*********-22445","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 11:44:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Patel","+**************** ,3666","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e6c45aad-a5a4-4aa6-aafd-e47d5e0d1aa5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Cassandra Rose",,"Cassandra",,"4uwBBgzgmkG82pEtb2WxAA==","Lead QA Analyst","8/6/2019 3:33:18 PM",,"<EMAIL>","cassandra.rose",,"S-1-5-21-**********-**********-*********-22446","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 3:16:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Rose",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"50a0ad46-cc9b-49c0-b831-0d0b434eabef","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Michelle Fraser",,"Michelle",,"AXB6Ijn49UOdlqKU6EMdWw==","Client Services Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","michelle.fraser",,"S-1-5-21-**********-**********-*********-22453","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 7:28:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Fraser","+**************** ,3686","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2b842806-a213-419f-bf98-0ef7ea39c401","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Mallory Conn",,"Mallory",,"4rRLFgM+a0uybjK9K+TQNg==","Client Services Analyst","8/15/2019 6:34:21 PM",,"<EMAIL>","mallory.conn",,"S-1-5-21-**********-**********-*********-22452","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 6:22:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Conn","+**************** ,3685","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bb06ce6d-ed39-495d-bd36-c21c0a8838c0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Ryan Prevost",,"Ryan",,"/pOnnJEQC0uwRkR2uQCODg==","Client Services Analyst","7/22/2019 10:47:07 PM",,"<EMAIL>","ryan.prevost",,"S-1-5-21-**********-**********-*********-22456","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 10:21:19 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Prevost","+**************** ,3689","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bdcb7231-6c96-4cd9-9c08-7655086ea99a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Sukhdeep Sidhu",,"Sukhdeep",,"OvzkrWNFR0SOFT2yrxtDzw==","Client Services Analyst","6/19/2019 5:22:19 PM",,"<EMAIL>","sukhdeep.sidhu",,"S-1-5-21-**********-**********-*********-22457","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/19/2019 5:06:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Sidhu","+**************** ,3690","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"214db67b-f1d6-413b-809b-5798fe55affe","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Lyndsay Mokonen",,"Lyndsay",,"PItnGesr/kWXWZZUASVCzg==","Client Services Analyst","8/14/2019 3:33:14 PM",,"<EMAIL>","lyndsay.mokonen",,"S-1-5-21-**********-**********-*********-22460","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 3:11:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Mokonen","+**************** ,3684","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b2ea1c50-1070-4e28-b513-a3d2a3a22b88","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Connor Moran",,"Connor",,"dB7YxAF+oUyFmuI+YiMpoA==","Client Services Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","connor.moran",,"S-1-5-21-**********-**********-*********-22461","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/17/2019 12:01:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Moran","+**************** ,3681","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"18627a02-d626-478f-8e83-ec29dda80fa7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Miguel Hernandez",,"Miguel",,"irZBRI1DTk6RjwIGaQBVcw==","Network Team Lead","7/24/2019 7:18:38 PM",,"<EMAIL>","miguel.hernandez","+1 (250) 307-3323","S-1-5-21-**********-**********-*********-22827","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 2:50:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hernandez","+**************** ,3129","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"815cac66-8299-4d92-9140-4487c0404c73","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Nolan Frymire",,"Nolan",,"+4Yqsvtuo0W1UiVRGUy4BA==","Junior Software Developer","6/26/2019 3:02:25 PM",,"<EMAIL>","nolan.frymire",,"S-1-5-21-**********-**********-*********-22831","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/26/2019 3:02:02 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Frymire",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2b920bae-4e2c-4fb8-bd4f-00d2377d43ca","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Alan McNaughton",,"Alan",,"Lb3lesDh/kaAdWysgi+XdQ==","Senior Manager Technology Operations","7/24/2019 7:18:38 PM",,"<EMAIL>","alan.mcnaughton","+1 (403) 805-9002","S-1-5-21-**********-**********-*********-22829","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/23/2019 3:25:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McNaughton","+**************** ,3128","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"58ac520a-5229-4b3d-ac49-510ebe3ddf19","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Jolanda Kondrak",,"Jolanda",,"5K6WDxRZQ0yDlACulw81qw==","Marketing Coordinator","8/6/2019 3:03:17 PM",,"<EMAIL>","jolanda.kondrak",,"S-1-5-21-**********-**********-*********-23478","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 2:40:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kondrak","+**************** ,3504","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dff9102d-1577-4d57-8e4c-20ec6332afb3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Gee Mary Tan",,"Gee Mary",,"M/94grsWq0O6HaIDscJJAQ==","QA Analyst","8/19/2019 2:37:17 PM",,"<EMAIL>","geemary.tan",,"S-1-5-21-**********-**********-*********-23477","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:10:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tan","+**************** ,3596","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bbef735f-ae6c-47b4-9d27-e6d101710e1f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Mark Ramsden",,"Mark",,"WLlc+bsKoUmWn6Zn6aS1qw==","QA Analyst","7/15/2019 6:05:24 PM",,"<EMAIL>","mark.ramsden",,"S-1-5-21-**********-**********-*********-23481","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/12/2019 8:46:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ramsden",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"59b9c940-529d-4f50-ab46-6334e45a5914","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Yuliya Voytsekhivska",,"Yuliya",,"87o4EbVJcEaKMgjBebbJ4w==","QA Analyst","7/23/2019 9:17:52 PM",,"<EMAIL>","yuliya.voytsekhivska",,"S-1-5-21-**********-**********-*********-23483","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 9:14:59 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Voytsekhivska",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"63cf1197-7b3e-4790-a1d5-bbd633579de0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,"Canada",,"Finance","True","Dianne Standring",,"Dianne",,"awc3LDt27kufs03xQHsAFw==","Revenue Management Data Entry Clerk","7/24/2019 4:48:30 PM",,"<EMAIL>","dianne.standring",,"S-1-5-21-**********-**********-*********-23986","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 6:44:54 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Standring","+**************** ,3526","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"39824ab8-49aa-4452-95af-13e504b21c89","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Training","True","Dee Rooks",,"Dee",,"ecoiUnFEKEaEbSrkLH18Ag==","Training Solutions Lead","7/25/2019 4:19:21 PM",,"<EMAIL>","dee.rooks",,"S-1-5-21-**********-**********-*********-23985","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 5:00:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Rooks","+**************** ,3502","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"49f3802b-f31f-4bec-81df-0ba3dd2e34fa","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","KT Nguyen",,"KT",,"jtb35xmh+0622aFvLGJ6/A==","Junior Software Developer","7/26/2019 6:20:07 PM",,"<EMAIL>","kt.nguyen",,"S-1-5-21-**********-**********-*********-23987","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/26/2019 6:10:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Nguyen",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c812f356-4a18-4bef-81e9-fd3dec744ec7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Ernesto Silva",,"Ernesto",,"nrEFlxI0UUaLtmI4aZm0OA==","Junior Software Developer","7/25/2019 4:19:21 PM",,"<EMAIL>","ernesto.silva",,"S-1-5-21-**********-**********-*********-23992","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 3:04:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Silva",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fdcff93a-9ac5-4f76-aa50-9ddbe1b68e5d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Fang Shi",,"Fang",,"uUZUlGp1XkijE5UzsLi7lA==","Junior Software Developer","8/8/2019 7:36:40 PM",,"<EMAIL>","fang.shi",,"S-1-5-21-**********-**********-*********-23996","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/8/2019 7:13:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shi",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3a5ff28b-72a3-432a-ae9c-447f59f3bfa5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Toby Ling",,"Toby",,"8AP9nyqy+Emqfvcm2P0nog==","Junior Software Developer","8/7/2019 3:34:08 PM",,"<EMAIL>","toby.ling",,"S-1-5-21-**********-**********-*********-23995","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/7/2019 3:30:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Ling",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"36b87f3c-2528-4c30-a8ff-9c01f70122f7","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Chris Bremmer",,"Chris",,"AF0hQJPTwEq8zu+PSbnXfg==","Senior Software Developer","7/31/2019 5:49:31 PM",,"<EMAIL>","Chris.Bremmer",,"S-1-5-21-**********-**********-*********-23998","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 5:24:40 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bremmer",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a0358776-7a9e-4650-b98a-88804469e616","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","TianHao Zhang",,"TianHao",,"8aE9MRw3206yf2IL9H5pDg==","Junior Software Developer","8/19/2019 4:07:20 PM",,"<EMAIL>","Tianhao.zhang",,"S-1-5-21-**********-**********-*********-24000","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 4:03:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Zhang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"274dcae4-68ef-4fe5-ac1c-84054531865f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Marshall Melnychuk",,"Marshall",,"05jw0ab2qkqtYaJvaoitsQ==","Senior Business Analyst","7/15/2019 2:35:17 PM",,"<EMAIL>","marshall.melnychuk",,"S-1-5-21-**********-**********-*********-23999","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 2:24:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Melnychuk",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4d858754-9f6a-474a-8b2a-4f719ed6ba6f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Carson Judd",,"Carson",,"gwc8editoEm/uCfuvugutg==","Junior Software Developer","7/25/2019 3:49:16 PM",,"<EMAIL>","carson.judd",,"S-1-5-21-**********-**********-*********-22847","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/5/2019 3:01:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Judd",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"382a38c9-48db-4be8-aeb4-9b9625b66e8b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Michael Tang",,"Michael",,"JkEqQOsPfki0c6nZsR4Ckw==","Junior Software Developer","7/22/2019 8:17:02 PM",,"<EMAIL>","Michael.Tang",,"S-1-5-21-**********-**********-*********-22849","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 8:01:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"18afc508-6ba2-437e-8260-d58197b66330","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Diane Goodwin",,"Diane",,"tPit+z4ORE+3uXrgcwa58w==","Implementer","8/16/2019 3:04:58 PM",,"<EMAIL>","diane.goodwin","+1 (902) 478-6839","S-1-5-21-**********-**********-*********-23498","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 3:01:59 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Goodwin",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1af28f88-aa23-455a-a87b-90059444f805","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Tricia Nason",,"Tricia",,"krFjoO2LxkKAD1/RnIszVQ==","Implementer","6/29/2019 9:48:06 PM",,"<EMAIL>","tricia.nason","+1 (902) 240-0811","S-1-5-21-**********-**********-*********-23497","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/29/2019 9:41:35 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Nason",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7dc08452-7e0f-4db7-8c97-e5daa2d22691","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Ekaterina Skudnova",,"Ekaterina",,"Mmss8bMG0EOPqWLZ6+iklg==","Product Engagement Specialist","8/12/2019 9:31:46 PM",,"<EMAIL>","ekaterina.skudnova","+1 (647) 326-7754","S-1-5-21-**********-**********-*********-22860","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 2:54:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Skudnova",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"95b98643-a137-429b-b591-f5d5e0bf4b03","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sri Adusumilli",,"Sri",,"OwMgIA68mkyNZRt2gmJEWg==","QA Analyst","8/8/2019 5:36:36 PM",,"<EMAIL>","Sri.Adusumilli",,"S-1-5-21-**********-**********-*********-23499","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/8/2019 5:27:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Adusumilli",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7c8319b0-5d92-45c2-8359-35e685dbc2e5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Barrett Sharpe",,"Barrett",,"kL4Kt8CG/ECeEDI1v9PLaw==","Junior Software Developer","8/19/2019 5:37:24 PM",,"<EMAIL>","barrett.sharpe",,"S-1-5-21-**********-**********-*********-22864","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 5:11:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Sharpe",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"eb67b608-3cb7-4260-83dc-61af343866aa","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Kerry Slater",,"Kerry",,"yvFB9VaIpES7o0pHSj96Sw==","Project Manager","8/22/2019 3:59:41 PM",,"<EMAIL>","kerry.slater",,"S-1-5-21-**********-**********-*********-22865","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 3:52:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Slater","+**************** ,3411","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6a3721ae-3334-4be6-b049-b74d9934279c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Angela Tam",,"Angela",,"Pw65uCMhckiznjjYWAvItQ==","Project Manager","8/13/2019 5:02:27 PM",,"<EMAIL>","angela.tam",,"S-1-5-21-**********-**********-*********-22866","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 4:52:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tam",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bb1d209f-3562-4801-b154-ad4b45f809db","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Harrison Kroeker",,"Harrison",,"KfAtBjEFEkWjreIiHo29jw==","Business Analyst","8/23/2019 6:51:00 PM",,"<EMAIL>","harrison.kroeker",,"S-1-5-21-**********-**********-*********-22868","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 6:40:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kroeker","+**************** ,3415","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d4dece12-7561-4635-b65d-9ef81636bcee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Avinash Tiwari",,"Avinash",,"5hRrZUEfYUOEFMrUFH40dg==","Junior Software Developer","7/31/2019 12:17:31 AM",,"<EMAIL>","avinash.tiwari",,"S-1-5-21-**********-**********-*********-24013","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 12:16:59 AM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tiwari",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f8a693d3-4e32-4827-ac0b-d78ea0193246","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Ranjeet Bisen",,"Ranjeet",,"k7LZcliN4EKGoYhQwqt8bA==","Lead QA Analyst","8/9/2019 5:37:26 PM",,"<EMAIL>","ranjeet.bisen",,"S-1-5-21-**********-**********-*********-24012","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 5:12:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bisen",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4c5a154c-28a0-46ba-864d-59df93565e1b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Ryan Kleiber",,"Ryan",,"xK8fUF5H6EqHktH5Exu/Bw==","QA Analyst","8/6/2019 3:33:18 PM",,"<EMAIL>","ryan.kleiber",,"S-1-5-21-**********-**********-*********-24011","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 3:16:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kleiber",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"cb59945d-6a6d-41a7-803e-5cf4555586e2","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Rene Kabis",,"Rene",,"+bnwkCZG9EqW2ObEjtofvQ==","Intermediate Software Developer","8/9/2019 4:07:26 PM",,"<EMAIL>","rene.kabis",,"S-1-5-21-**********-**********-*********-22505","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 4:05:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kabis",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a902070d-a2ec-4bf4-886f-50f8e66615cb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Domain Admin - Miguel Hernandez",,"Domain Admin -",,"0t7YjCsMHEaArpgSbS4rJw==",,"6/27/2019 8:08:52 PM",,,"da-mhernandez",,"S-1-5-21-**********-**********-*********-22506","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/27/2019 7:40:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Miguel Hernandez",,,"<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1b17da4d-c033-4450-84cc-c7b4af7ed44e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Stephane Chan",,"Stephane",,"NNM83NB9ikC/sa4rYyboXQ==","Data Analyst","8/9/2019 10:07:35 PM",,"<EMAIL>","stephane.chan",,"S-1-5-21-**********-**********-*********-22507","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 9:55:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Chan","+**************** ,3419","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"30c8f94f-ba09-4997-835f-e5b7d693a12f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Data Analysis","True","Benjamin Luoma",,"Benjamin",,"g7L6oo5kC0yMmwvUlem/ZQ==","Data Analyst","8/2/2019 11:30:45 PM",,"<EMAIL>","benjamin.luoma",,"S-1-5-21-**********-**********-*********-22511","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 11:15:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Luoma",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3f7a7324-df7d-41f9-84c2-165070efc0c9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Dave Anderson",,"Dave",,"9+VPqLnof0WkdoeuaEq1jg==","Data Analyst","7/17/2019 11:13:28 PM",,"<EMAIL>","dave.anderson",,"S-1-5-21-**********-**********-*********-22510","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 10:47:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Anderson",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"66059213-9016-489f-8c7b-f014a9bd5d46","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Kris Siewert",,"Kristen",,"9h5wZHkKSUODNsP33oAMDQ==","Data Analyst","8/14/2019 12:32:46 AM",,"<EMAIL>","kristen.siewert",,"S-1-5-21-**********-**********-*********-22509","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 9:57:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Siewert","+**************** ,3417","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"58052d49-7efb-44a8-b1fd-ecf6dad1b7ce","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Nida Hussain",,"Nida",,"9pT3ZenRn02CTtfn9Stm4A==","Implementer","6/10/2019 7:48:05 PM",,"<EMAIL>","nida.hussain","+1 (780) 608-6500","S-1-5-21-**********-**********-*********-24019","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","4/11/2019 1:38:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hussain","+1 (905)-452-4906","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"dd99249d-5c7b-4bd5-a6b5-b34bfee774a0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,,,"Implementations","True","Tanya Peixoto",,"Tanya",,"EHxrsDBRP0epI8aE8QEcGw==","Implementer","7/15/2019 6:05:24 PM",,"<EMAIL>","tanya.peixoto","+1 (416) 801-7445","S-1-5-21-**********-**********-*********-24018","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 5:58:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Peixoto",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"697a01ff-49d1-45c4-a48b-fdd6ab852b7f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Ralph D'Almeida",,"Ralph",,"NSxj2eHh7EuE7LUhCdKd5Q==","Software Developer","8/8/2019 2:34:55 PM",,"<EMAIL>","ralph.dalmeida",,"S-1-5-21-**********-**********-*********-24021","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/8/2019 2:07:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"D'Almeida",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9bbe1c3e-9d7f-42a3-95b8-6de517b37103","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Mo Kandy",,"Mohammad",,"hEWnHy13c0am471ILhM7Cg==","DevOps Engineer","8/19/2019 4:37:20 PM",,"<EMAIL>","mohammad.kandy",,"S-1-5-21-**********-**********-*********-23516","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 4:29:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kandy",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4a03625f-68db-4b75-a643-b2e0aa742987","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Sara Stacey",,"Sara",,"Oke9LErwmk+4Y3qc/JISeg==","Documentation Specialist","8/2/2019 3:20:58 PM",,"<EMAIL>","sara.stacey",,"S-1-5-21-**********-**********-*********-23515","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/2/2019 3:03:11 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Stacey","+**************** ,3425","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b8aac0d9-4a1a-469e-bf9c-041c8f598989","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Ryan Yakiwchuk",,"Ryan",,"APea+MKk1UK55CsQayc5DQ==","Product Owner","7/31/2019 3:49:26 PM",,"<EMAIL>","ryan.yakiwchuk",,"S-1-5-21-**********-**********-*********-23514","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 3:43:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Yakiwchuk",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2dc0d258-3f1c-4a97-9ae2-48352d05ba7e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Rebekka Augustine",,"Rebekka",,"5TYMfnpr9UGqrnd61I4J1w==","Marketing Content Writer","7/25/2019 3:19:14 PM",,"<EMAIL>","rebekka.augustine",,"S-1-5-21-**********-**********-*********-23513","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/25/2019 3:14:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Augustine",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"01e2fa92-0c10-45b7-9483-e2b4df89826b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,"Product Management","True","Jocelyn Smith",,"Jocelyn",,"fiVP074ln0aB7IgNnfHQ7Q==","UX Designer","7/24/2019 6:48:36 PM",,"<EMAIL>","jocelyn.smith",,"S-1-5-21-**********-**********-*********-23517","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 7:51:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Smith",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a5066131-a30e-4f6c-bc1c-39c99e3d6f71","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Ashley Delaney",,"Ashley",,"UUMjGjVqaUuSYkbzPOlaNA==","Client Services Analyst","7/24/2019 5:48:32 PM",,"<EMAIL>","ashley.delaney",,"S-1-5-21-**********-**********-*********-24027","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 7:00:44 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Delaney",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"98048d0a-5b52-414b-b73d-2767c0bb5fc5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Danica Barker",,"Danica",,"wZuPqx7R0EypgDO7wSfmTw==","Client Services Analyst","8/19/2019 2:37:17 PM",,"<EMAIL>","danica.barker",,"S-1-5-21-**********-**********-*********-24028","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:31:31 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Barker","+**************** ,3628","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"46586be2-269b-4ccf-b10c-8feda653d9ae","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Jill Sprinkling",,"Jill",,"POuzVm0u6EeHt9T5vNy2SQ==","Client Services Analyst","8/15/2019 3:34:17 PM",,"<EMAIL>","jill.sprinkling",,"S-1-5-21-**********-**********-*********-24029","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/14/2019 12:50:57 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Sprinkling","+**************** ,3637","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7ddf2062-6a02-4910-ab9d-f7ecb9137005","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Lorenn Floor",,"Lorenn",,"6Y2i6hq2YE+Z0JQ72YMADw==","Client Services Analyst","6/20/2019 5:41:04 PM",,"<EMAIL>","lorenn.floor",,"S-1-5-21-**********-**********-*********-24031","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/20/2019 5:11:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Floor","+**************** ,3692","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4aaf113d-70a9-4104-91f6-008ec72169ca","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Justine Widmer",,"Justine",,"Enm2kvJ/qUi9poKF21YoiA==","Client Services Analyst","8/6/2019 4:33:21 PM",,"<EMAIL>","justine.widmer",,"S-1-5-21-**********-**********-*********-24030","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 4:16:42 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Widmer","+**************** ,3691","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"92a933ab-bfa4-43eb-ac4f-5086ea2065a1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Hussain Shaikh",,"Hussain",,"5yqI/vFNZUeIxUCH0ngXVA==","Project Manager","6/25/2019 4:32:05 PM",,"<EMAIL>","hussain.shaikh",,"S-1-5-21-**********-**********-*********-24032","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 4:28:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Shaikh",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6e93da54-77a8-4281-aeed-77a4af9586c8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Quality Assurance","True","Mohamed Abdillahi",,"Mohamed",,"x4EBCtp8v0m3jOv2sATUVg==","Javascript Software Developer Co-Op","6/25/2019 6:02:08 PM",,"<EMAIL>","Mohamed.abdillahi",,"S-1-5-21-**********-**********-*********-23523","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 5:41:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Abdillahi",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d8016bb2-ad25-458c-ad52-5b2f5b203635","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Quality Assurance","True","Bogdan Lykhosherstov",,"Bogdan",,"VHmg3HfF1U6rfQfTaEq9oA==","Javascript Software Developer Co-Op","7/25/2019 4:19:21 PM",,"<EMAIL>","bogdan.lykhosherstov",,"S-1-5-21-**********-**********-*********-23524","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 3:25:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lykhosherstov",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b3817f8b-a3e2-4752-b75d-e07b35fb276c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,"Digital Healthcare","True","Davena Singh",,"Davena",,"Q3IBHGUJoEW/4LcvpWiuQQ==","Senior Product Manager","7/25/2019 4:19:21 PM",,"<EMAIL>","davena.singh",,"S-1-5-21-**********-**********-*********-24038","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 1:46:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Singh",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4e19cdbb-7809-4b09-b7c2-7187d5bea617","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,"Digital Healthcare","True","David Bach",,"David",,"KOWqyS7SW0C9TgDTs+mr4g==","Senior Product Manager","7/25/2019 4:19:21 PM",,"<EMAIL>","david.bach",,"S-1-5-21-**********-**********-*********-24040","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Toronto",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 2:09:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bach",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7af2daf4-f288-4b03-81f3-4d92a51520d6","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Charisse Abaloyan",,"Charisse",,"WvS7oQVDBkO/0YF47y0sgw==","Client Services Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","charisse.abaloyan",,"S-1-5-21-**********-**********-*********-24041","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 10:33:35 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Abaloyan","+**************** ,3693","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"594e45af-84a2-4d7a-8e7d-11ff10db227e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Harleen Kohli",,"Harleen",,"y9kFX1KA1EC5C0fODTAlCw==","Client Services Analyst","6/25/2019 3:45:54 PM",,"<EMAIL>","harleen.kohli",,"S-1-5-21-**********-**********-*********-24043","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 3:38:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Kohli","+**************** ,3695","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5dd393ec-851e-4e0a-898b-b0b176ad6e10","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Megan Bowker",,"Megan",,"x5Y5kfACcki/pBJY0xF2bw==","Client Services Analyst","7/24/2019 6:18:36 PM",,"<EMAIL>","megan.bowker",,"S-1-5-21-**********-**********-*********-24044","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 2:01:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Bowker","+**************** ,3696","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"35011a1b-cb3e-44b6-8925-fa517f7c5472","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Nathan Poehlke",,"Nathan",,"grY8nBMQekm1bQBpO2cbFg==","Client Services Analyst","7/23/2019 12:47:34 PM",,"<EMAIL>","nathan.poehlke",,"S-1-5-21-**********-**********-*********-24045","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/23/2019 12:29:08 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Poehlke","+**************** ,3687","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4ff26f93-f546-473c-a008-bf458a18fe1c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Raymun Khunkhun",,"Raymun",,"sWJopMC5F0+g17sQM58SEA==","Client Services Analyst","7/15/2019 12:05:11 PM",,"<EMAIL>","raymun.khunkhun",,"S-1-5-21-**********-**********-*********-24046","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 12:03:01 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Khunkhun","+**************** ,3682","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"38139f0a-42a9-463a-9adb-ea72c6a40e4f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Sufyan Ahmed",,"Sufyan",,"oropZVvw1kq7Ux6e0i3zhQ==","Client Services Analyst","7/15/2019 3:35:18 PM",,"<EMAIL>","sufyan.ahmed",,"S-1-5-21-**********-**********-*********-24048","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:32:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Ahmed","+**************** ,3713","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b0f300c9-3c7c-4811-94b6-05d55fa8f15c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Rick Poor",,"Rick",,"+4d/r7X4KECmpYDUJvU0vQ==","Client Services Analyst","7/15/2019 3:35:18 PM",,"<EMAIL>","rick.poor",,"S-1-5-21-**********-**********-*********-24047","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:30:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Poor","+**************** ,3714","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b79b4185-da24-4467-a560-517b0d580394","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Administration","True","Ben Krell",,"Ben",,"iSJh7r5Lb0aJHAWlnypv4A==","Facility Assistant - Employee Care","8/15/2019 8:34:25 PM",,"<EMAIL>","ben.krell",,"S-1-5-21-**********-**********-*********-23527","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 8:20:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Krell",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2261c53d-31fa-46a1-9f0d-3c123ab0fee9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Kirat Virk",,"Kirat",,"CQxJBAvmmkupw0oIifN1Vg==","Junior Software Developer","7/22/2019 5:16:57 PM",,"<EMAIL>","kirat.virk",,"S-1-5-21-**********-**********-*********-23530","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:30:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Virk",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a902d476-c137-4a5e-bfc2-14b6ebfd7b24","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","QHR Technologies Inc.",,"Canada",,"Finance","True","Holli Hyatt",,"Holli",,"Ehn2R3bR3kCdDzGUWUhPrg==","Revenue Management Clerk","8/23/2019 3:30:29 PM",,"<EMAIL>","holli.hyatt",,"S-1-5-21-**********-**********-*********-23528","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 3:04:03 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hyatt","+**************** ,3536","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d9d2c176-533a-4d33-a24f-7a1557b054dc","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Brent Basil",,"Brent",,"obgXOhrPOkqW5MC3hW8d0Q==","Client Services Analyst","7/24/2019 5:48:32 PM",,"<EMAIL>","brent.basil",,"S-1-5-21-**********-**********-*********-24054","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 12:00:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Basil","+**************** ,3715","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6ba66cf4-7e6e-4ce9-8123-7e909949359f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Jake Redekop",,"Jake",,"uwqh8iBD5UK2LOqqw1sklw==","Product Owner Manager","6/25/2019 3:15:53 PM",,"<EMAIL>","jake.redekop",,"S-1-5-21-**********-**********-*********-24055","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 3:06:09 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Redekop","+**************** ex3717","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5918966e-7750-437d-b1be-9fc83ff6150d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Quality Assurance","True","Justin Lin",,"Justin",,"eG0o2Xd/70SmLKYzIGOWOg==","Junior QA Analyst","8/6/2019 7:33:25 PM",,"<EMAIL>","justin.lin",,"S-1-5-21-**********-**********-*********-24056","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 7:27:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lin",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bccf11df-5f23-430c-ac26-96847694f2a0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Development","True","Earl Cooke",,"Earl",,"VLaRARPof0WkXKVDiOzNBw==","Senior Build Engineer","7/24/2019 6:48:36 PM",,"<EMAIL>","earl.cooke",,"S-1-5-21-**********-**********-*********-24058","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/11/2019 3:56:05 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Cooke",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"110efb12-f617-4c81-aa6d-1b61951eadf3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Paul Kroes",,"Paul",,"xZZkYLC8fUSyOfbXYkNl3w==",,"5/29/2019 11:05:02 PM",,"<EMAIL>","Paul.Kroes",,"S-1-5-21-**********-**********-*********-23541","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","5/29/2019 11:03:15 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Kroes",,,"<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9d0666a1-90d5-4580-a66c-dae768cd6dc0","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Spencer Shupe",,"Spencer",,"dbmjrXL1yECw0lRt00/cjA==","Junior Software Developer","7/22/2019 4:46:55 PM",,"<EMAIL>","spencer.shupe",,"S-1-5-21-**********-**********-*********-23545","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 4:28:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shupe",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1eacd661-b0b0-415f-be9d-dab9611594bb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Tamika Leslie",,"Tamika",,"ddXlXKSwTEe92aRULeyUJw==","Product Owner","7/18/2019 9:14:12 PM",,"<EMAIL>","tamika.leslie",,"S-1-5-21-**********-**********-*********-23550","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 8:49:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Leslie","+**************** ,3720","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2a622ebe-54ce-4a51-926a-9ffc1bb0348c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Tawfiq Menad",,"Tawfiq",,"qtv2Gov19E6/iQtJnawNJQ==","Junior Software Developer","7/12/2019 10:33:02 PM",,"<EMAIL>","tawfiq.menad",,"S-1-5-21-**********-**********-*********-23549","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 4:04:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Menad",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e9631430-adc1-43da-8993-05dd524ce2fe","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Mark Paul",,"Mark",,"c1W9zKH4aUuYbksMCR912w==","Junior Software Developer","7/22/2019 3:46:53 PM",,"<EMAIL>","mark.paul",,"S-1-5-21-**********-**********-*********-23551","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 3:46:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Paul",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"66040921-802d-439e-9c0c-5ec2b2360550","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Development","True","Graham Fawcett",,"Graham",,"g8W63EB7x0atZX/EkvrfJg==","Business Analyst","7/15/2019 7:35:25 PM",,"<EMAIL>","graham.fawcett",,"S-1-5-21-**********-**********-*********-23552","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 7:25:09 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Fawcett",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"27e57b9f-f5cf-41fd-a252-c0f116714457","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","David Lacho",,"David",,"iLeY4TSP6k+rzWArLrLOkQ==","Junior Software Developer","7/18/2019 7:14:08 PM",,"<EMAIL>","david.lacho",,"S-1-5-21-**********-**********-*********-22538","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/18/2019 7:01:48 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lacho",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a08e3293-fb31-4b23-af97-9348bf93d607","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Operations","True","Carly Innes",,"Carly",,"SqOZXWTa2UWUaZy8Untgjg==","Project Manager","7/23/2019 9:17:52 PM",,"<EMAIL>","carly.innes",,"S-1-5-21-**********-**********-*********-22539","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 4:22:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Innes","+**************** , 3721","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2e6a5764-b09f-48bf-b0d1-5d908cef195a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","QHR Technologies Inc.",,"Canada",,"Finance","True","Liane Blake",,"Liane",,"cdwANMLPIEuwmFFBPK2P/A==","Revenue Management Clerk","7/31/2019 1:19:21 PM",,"<EMAIL>","liane.blake",,"S-1-5-21-**********-**********-*********-22540","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 1:10:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Blake",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a347d316-a59c-473e-9d20-560cacc61216","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Administration","True","Nancy Sauer",,"Nancy",,"mBOlyC3UK0SQeBS3sMRUHA==","Administrative Coordinator","7/31/2019 7:19:32 PM",,"<EMAIL>","nancy.sauer",,"S-1-5-21-**********-**********-*********-22542","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 3:19:31 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Sauer","+****************, 3038","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b0c3850a-2d08-4771-b4a0-27a2b0d3fd20","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Voltaire Bazurto",,"Voltaire",,"oEiTaUs1hUe9PWyLfSBb+A==","Junior Software Developer","8/20/2019 11:27:34 PM",,"<EMAIL>","voltaire.bazurto",,"S-1-5-21-**********-**********-*********-23565","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 11:14:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bazurto",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"bbb481f7-334d-4394-b472-ef5111432e57","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Becca Hembling",,"Becca",,"lPb4pVK9z0utPA4sS2K3pw==","Software Developer","8/13/2019 3:32:24 PM",,"<EMAIL>","becca.hembling",,"S-1-5-21-**********-**********-*********-23564","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 3:15:54 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Hembling","+**************** ,3104","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f1dc7eb7-9a99-4145-8b9c-579e98e46a51","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Alex Shaw",,"Alex",,"r44gBLD1BUmF89k1ncPR+w==","Junior Software Developer","8/14/2019 5:03:17 PM",,"<EMAIL>","alex.shaw",,"S-1-5-21-**********-**********-*********-23563","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 4:43:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shaw",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"f64a0708-9623-4121-a55a-7938d35770cd","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Sami Valkama",,"Sami",,"DQxYWUHfL0Kb0ZlMy8X7Bw==","Project Manager","8/21/2019 4:28:08 PM",,"<EMAIL>","sami.valkama","+1 (250) 575-9376","S-1-5-21-**********-**********-*********-23571","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/21/2019 4:15:17 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Valkama",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fc53a7a2-43ab-485a-815b-132a03f964b9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Frank Yan",,"Frank",,"5IatGcCf7UOMxqxgtvMBCA==","Software Developer","8/15/2019 3:34:17 PM",,"<EMAIL>","frank.yan",,"S-1-5-21-**********-**********-*********-23568","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 3:19:23 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Yan",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"102e98e4-bdd8-4555-b320-ad30c43df735","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Development","True","Mingyuan Yang",,"Mingyuan",,"uVAEFWkfKkakyp39ElDIfA==","QA Analyst","8/15/2019 9:34:27 PM",,"<EMAIL>","mingyuan.yang",,"S-1-5-21-**********-**********-*********-23570","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 9:18:14 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Yang",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"37eb9ca2-df1c-4e91-8818-3af42080090f","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","QHR Technologies Inc.",,"Canada",,"Finance","True","Jessica Wright",,"Jessica",,"8/Y3+mvvSUSiklhKdMQjaQ==","Revenue Management Clerk","8/19/2019 3:07:18 PM",,"<EMAIL>","jessica.wright",,"S-1-5-21-**********-**********-*********-23569","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/19/2019 2:59:39 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wright","+**************** ,3723","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"1504b184-2e3c-4ac3-bdb9-b0985bf2a5e9","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Sandra Tuppert",,"Sandra",,"55xgcjF1h021PctFhyYTzg==","Business Development Sales Manager","6/13/2019 3:32:58 PM",,"<EMAIL>","sandra.tuppert",,"S-1-5-21-**********-**********-*********-23574","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/13/2019 3:32:00 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tuppert","+**************** ,3127","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5a9ae534-72ae-48c1-b6fc-445b3cbf9b37","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Hong He",,"Hong",,"KQZL40gAOU63lnThJWUgnA==","Junior Software Developer","8/23/2019 4:30:36 PM",,"<EMAIL>","hong.he",,"S-1-5-21-**********-**********-*********-23578","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 4:03:52 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"He",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"d26d49bf-18ce-40e9-acb9-b0001cdf36e8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Phil Campbell",,"Phil",,"lq5avwU73UGRSOC4qPABow==","Junior Application Architect","8/20/2019 3:21:49 PM",,"<EMAIL>","phil.campbell",,"S-1-5-21-**********-**********-*********-23579","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 3:16:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Campbell",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4d594300-1610-4699-81a1-ce5674549760","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Lynette Fourie",,"Lynette",,"R667RC2pLk6Cvp2HCETOJw==","BI Developer","6/19/2019 5:25:46 PM",,"<EMAIL>","lynette.fourie",,"S-1-5-21-**********-**********-*********-23581","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/19/2019 5:25:09 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Fourie",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2caeba50-4ddf-4f42-bf7a-648760d09b70","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Sheetal Jathar",,"Sheetal",,"0dYwNQDy1Eq3+PbUR8L7eA==","Lead Software Developer","8/20/2019 7:21:57 PM",,"<EMAIL>","sheetal.jathar",,"S-1-5-21-**********-**********-*********-24087","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 7:19:31 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Jathar",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"130ca396-2ab3-4257-bfb4-ca8fbe64d8ee","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Finance","True","Rodney Earl",,"Rodney",,"5c/5fBMA4kaQEQmKyFiVGA==","Revenue Management Clerk","7/24/2019 4:48:30 PM",,"<EMAIL>","rodney.earl",,"S-1-5-21-**********-**********-*********-22917","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 7:18:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta",,"Earl",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"fecd2843-775d-4bf4-aa7c-e3b8f86d19a8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Development","True","Bibin Baby",,"Bibin",,"wUaDmZbYm0yirMGuYJNM3w==","QA Analyst","7/25/2019 4:19:21 PM",,"<EMAIL>","bibin.baby",,"S-1-5-21-**********-**********-*********-22920","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/25/2019 3:52:28 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Baby",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6f6abfd3-affb-418b-946d-425e97a51fc1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Larry Lin",,"Larry",,"NIAJd+zjEEWZ+gvB7WI6lA==","Lead Software Developer","6/24/2019 8:45:21 PM",,"<EMAIL>","larry.lin",,"S-1-5-21-**********-**********-*********-22918","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/24/2019 8:16:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Lin",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b8617c16-0016-4a1a-bd90-ac39eb29c5b2","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Jeff Brown",,"Jeff",,"kHXTPUj4KEiIiQd33hqlmw==","Solution Architect","6/25/2019 8:02:12 PM",,"<EMAIL>","jeff.brown",,"S-1-5-21-**********-**********-*********-22919","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 7:48:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Brown",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"29dc1503-2e2c-45dd-8ed0-ebbc3880de94","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","QHR Technologies Inc.",,,,"Technology","True","Anup Gandhi",,"Anup",,"EvAOy8xk5keBnXPqs4+png==","Senior Citrix Administrator","7/24/2019 7:18:39 PM",,"<EMAIL>","anup.gandhi",,"S-1-5-21-**********-**********-*********-24101","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/28/2019 3:03:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Gandhi",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7d111750-c49d-4ec4-ab27-bf5224509a37","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Development","True","Clinton Edwards",,"CJ",,"E9P9oCbn8EC1sTbuEI6yCQ==","JIRA Administrator","7/3/2019 5:24:57 PM",,"<EMAIL>","cj.edwards",,"S-1-5-21-**********-**********-*********-24106","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/3/2019 5:11:09 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Edwards",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b06acd5b-3d49-4911-8daf-9685e1bba812","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Cintia Schutt",,"Cintia",,"KvFTyR+oGESQ8kpf0JW2kg==","Junior Software Developer","7/25/2019 3:49:16 PM",,"<EMAIL>","cintia.schutt",,"S-1-5-21-**********-**********-*********-24108","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/25/2019 4:53:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Schutt",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"51cdfa56-c992-49fd-9e1f-622e0517e278","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Diego Silva",,"Diego",,"fvBGGYXLAU6lGe3+hCBa6Q==","Lead Software Developer","7/4/2019 9:03:13 PM",,"<EMAIL>","diego.silva",,"S-1-5-21-**********-**********-*********-24107","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/28/2019 3:55:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Silva","+**************** ,3726","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"96704afe-2623-4dae-ba32-383877eae178","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Nishant Vyas",,"Nishant",,"MlsOmXkAlUCaW19CVBeyFw==","Junior Software Developer","7/2/2019 3:50:45 PM",,"<EMAIL>","nishant.vyas",,"S-1-5-21-**********-**********-*********-22559","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 3:34:58 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Vyas",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"9cb95b72-ae01-4b85-a750-175795c97c4d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Peter Laudenklos",,"Peter",,"hTSOLlorxEqAVYqx6Tkd9Q==","Virtual Infrastructure Administrator","7/24/2019 7:18:39 PM",,"<EMAIL>","Peter.Laudenklos","+1 (403) 829-8214","S-1-5-21-**********-**********-*********-23593","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 7:56:45 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Laudenklos","+**************** ,3428","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"a790c9bb-3f4d-4078-8586-c7318730d379","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Quality Assurance","True","Dan Dunareanu",,"Dan",,"9X66c2PYJkmKJrzIlGY6Gw==","Javascript Software Developer Co-Op","7/25/2019 4:19:21 PM",,"<EMAIL>","dan.dunareanu",,"S-1-5-21-**********-**********-*********-23592","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/12/2019 3:11:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Dunareanu",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"6dd1dfb6-69ea-4a17-9c19-081014ce415b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Victoria Philips",,"Victoria",,"pTdFHvZ1iUuTA+KGt2Cftw==","Technical Writer","7/24/2019 7:18:39 PM",,"<EMAIL>","victoria.philips",,"S-1-5-21-**********-**********-*********-22563","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 1:34:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Philips","+**************** ,3729","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"45fcf4e6-427e-476c-9b81-40a204dc1bc8","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Quality Assurance","True","Rish Kumaria",,"Rish",,"hIfX9i1mMkuut795MZiBcg==","Javascript Software Developer Co-Op","8/23/2019 9:50:30 PM",,"<EMAIL>","rish.kumaria",,"S-1-5-21-**********-**********-*********-23595","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 7:29:21 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kumaria",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ea448c8f-0adb-4ab4-996c-7a396c3f7281","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Brandon Unger",,"Brandon",,"WG3Gd7hU+0y6aOnJAAr8RA==","Software Developer Co-Op","7/24/2019 6:48:36 PM",,"<EMAIL>","brandon.unger",,"S-1-5-21-**********-**********-*********-22565","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 3:44:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Unger",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5cf521a6-81ab-442f-af00-2ab1264f479a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Butch Albrecht",,"Butch",,"wvcpW+fV0UK9M2YrrfKG1Q==","Solution Architect","7/24/2019 6:48:36 PM",,"<EMAIL>","butch.albrecht",,"S-1-5-21-**********-**********-*********-22566","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 2:34:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Albrecht",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ff2501b5-4a5d-4d14-ab77-4b146e4e1172","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Emma Edghill",,"Emma",,"J7F1G1eQ1EKy2cSfhg1taQ==","Business Analyst Team Lead","7/24/2019 6:48:36 PM",,"<EMAIL>","emma.edghill",,"S-1-5-21-**********-**********-*********-22569","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 3:17:49 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Edghill","+**************** ,3730","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"219e015e-9d63-4d84-adb7-b778ef2ad63b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Marcos Pereira",,"Marcos",,"Rx9kX7aVfU+D5cd+zs0TFQ==","Senior Software Developer","7/22/2019 5:16:57 PM",,"<EMAIL>","marcos.pereira",,"S-1-5-21-**********-**********-*********-22570","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/16/2019 8:00:36 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Pereira",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3e6fa6b7-16cc-4cd6-8947-cc4e4fe535f3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Owen Read",,"Owen",,"Pjqu0AZUdEajffENbCGySw==","Project Coordinator","7/9/2019 3:20:58 PM",,"<EMAIL>","owen.read",,"S-1-5-21-**********-**********-*********-22571","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/9/2019 3:04:04 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Read",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2e78770e-3b00-4271-be30-7cdb327dec11","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Rajan Mahadevan",,"Rajan",,"c0wLZNBfP0CUkILrVAj5UA==","Lead Software Developer","8/19/2019 2:37:17 PM",,"<EMAIL>","rajan.mahadevan",,"S-1-5-21-**********-**********-*********-22572","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Vancouver",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:25:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Mahadevan","+**************** ,3731","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"485f0e78-9ace-4457-98f5-4ed92b54fd54","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Shubham Malik",,"Shubham",,"NNKDQxkoBE+vOduW/HnP8Q==","Privacy Analyst","7/17/2019 3:43:13 PM",,"<EMAIL>","shubham.malik",,"S-1-5-21-**********-**********-*********-22573","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 3:38:42 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Malik","+**************** ,3732","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b67c4297-0023-4533-93cf-88bacde74db3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Liam Shaw",,"Liam",,"uN3ljJarSE6D47pqJ7B7LQ==","Javascript Software Developer Co-Op","8/1/2019 5:50:17 PM",,"<EMAIL>","liam.shaw",,"S-1-5-21-**********-**********-*********-24112","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 3:23:41 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Shaw",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"893dbdc6-b490-4dc1-9f8e-af76a44625de","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Technology","True","Ernie Moreau",,"Ernie",,"A8hyF6kGe0uMaBvGhlHKsQ==","DevOps Engineer","7/24/2019 7:18:39 PM",,"<EMAIL>","ernie.moreau",,"S-1-5-21-**********-**********-*********-22936","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/17/2019 10:43:20 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Moreau",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"643c4ec4-fba6-49e2-b15d-8679cd918f6c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Calgary","QHR Technologies Inc.",,"Canada",,"Technology","True","Ina Kebet",,"Ina",,"tBvP+5RxpUiiH7AZUzKVUQ==","Procurement Specialist","7/31/2019 3:19:25 PM",,"<EMAIL>","ina.kebet",,"S-1-5-21-**********-**********-*********-22576","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Calgary","T2A 7W6",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 2:53:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","Alberta",,"Kebet","+**************** ,6325","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7ab41edb-4cf8-4259-8484-b4935855572c","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Helder Necker",,"Helder",,"01gIgIHrFUygVkCWr2PXNg==","Software Developer Co-Op","7/31/2019 2:49:23 PM",,"<EMAIL>","helder.necker",,"S-1-5-21-**********-**********-*********-22577","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/31/2019 2:40:13 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Necker",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"633379cf-1cae-4c2d-8695-e5c02c80077a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Brandon Chesley",,"Brandon",,"u4x+UrgakUClJvxzx4CkVA==","Software Developer Co-Op","7/29/2019 2:52:12 PM",,"<EMAIL>","brandon.chesley",,"S-1-5-21-**********-**********-*********-22578","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 2:37:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Chesley",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"713b5571-1139-4983-84aa-b236b7b588bb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Jonathan Dunville",,"Jonathan",,"t9xPq0eFwk6PScbyB5+Jvg==","Product Engagement Manager","8/12/2019 9:31:46 PM",,"<EMAIL>","jonathan.dunville",,"S-1-5-21-**********-**********-*********-22580","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/1/2019 4:06:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Dunville","+**************** ,3733","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"48854771-2c12-4db6-a70f-a07d738e5185","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,,,,,,"True","Domain Admin - Peter Laudenklos",,"Domain Admin -",,"zwX1b4BTNU25FQkGIijdyg==",,"7/15/2019 3:05:17 PM",,,"Da-Plaudenklos",,"S-1-5-21-**********-**********-*********-24118","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 3:01:57 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]",,,,"Peter Laudenklos",,,"<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"00d8252a-bca0-4a2d-893f-32d385aaab8e","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Tyler Cooney",,"Tyler",,"qyI1qkw4IEO32Zrkl4/fgg==","Client Services Analyst","8/22/2019 12:58:48 PM",,"<EMAIL>","tyler.cooney",,"S-1-5-21-**********-**********-*********-22943","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/22/2019 12:42:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Cooney","+**************** ,3741","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"76472e54-313d-4221-bed8-915a04d43f2a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Matt Wall",,"Matt",,"qUvjDBQcg0WQWaDZLrCv9A==","Junior Software Developer","7/22/2019 5:46:58 PM",,"<EMAIL>","matt.wall",,"S-1-5-21-**********-**********-*********-22581","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/21/2019 10:51:13 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Wall",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b27e6ad8-aa75-4022-9fb9-cf452562a19a","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Yoko Tanakura",,"Yoko",,"6UpNo0+F1U+yV1L17Am32A==","Software Development Co-Op","7/29/2019 3:22:13 PM",,"<EMAIL>","yoko.tanakura",,"S-1-5-21-**********-**********-*********-24120","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/29/2019 3:03:43 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Tanakura",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3c2ae581-ae9c-4497-a3b0-2dee3c18fd50","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Chris Heiss",,"Chris",,"6/wjWS6cuEiHmsWyUIAZOA==","Client Services Analyst","8/14/2019 5:03:17 PM",,"<EMAIL>","chris.heiss",,"S-1-5-21-**********-**********-*********-24124","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/14/2019 5:02:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Heiss","+**************** ,3739","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"828430ce-0ab1-48b9-b7bf-447b4dcf6ff4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Elise Richardson",,"Elise",,"Emug4BbQpEiILUle6IHaBQ==","Client Services Analyst","8/6/2019 1:33:13 PM",,"<EMAIL>","elise.richardson",,"S-1-5-21-**********-**********-*********-24123","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 1:26:26 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Richardson","+**************** ,3738","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"811b1067-2fec-4600-8e22-cea71ea5c9b4","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Alyssa McCauley",,"Alyssa",,"f/haUzAK40eVnd2RfkaI/w==","Client Services Analyst","8/13/2019 5:02:27 PM",,"<EMAIL>","alyssa.mccauley",,"S-1-5-21-**********-**********-*********-22584","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/13/2019 4:39:27 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"McCauley","+**************** ,3737","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"30d12346-bdbd-43a9-a5c8-aea75b7d27eb","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Vanessa Stembridge",,"Vanessa",,"0lSKBZAbPkSy3otc5HiSJA==","Client Services Analyst","8/9/2019 3:07:21 PM",,"<EMAIL>","vanessa.stembridge",,"S-1-5-21-**********-**********-*********-22585","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 3:02:11 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Stembridge","+**************** ,3735","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2a8d5b6d-c81c-4541-8e2e-aa4d4f77da75","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Tom Hannah",,"Tom",,"kFbZTKMKIkq3gMAyvjLwpQ==","Client Services Analyst","8/9/2019 12:07:14 PM",,"<EMAIL>","tom.hannah",,"S-1-5-21-**********-**********-*********-22586","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 12:05:56 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Hannah","+**************** ,3736","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"5deeaba7-c7b6-49d7-8c45-dc683d623827","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Taylor Floor",,"Taylor",,"Cqtny51GQkuM6kCgfq0tyQ==","Client Services Analyst","8/12/2019 4:09:48 PM",,"<EMAIL>","taylor.floor",,"S-1-5-21-**********-**********-*********-22587","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 4:00:09 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Floor","+**************** ,3740","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3d62907e-53fd-4300-b5c6-6c6a78831b25","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Lindsay Bronskill",,"Lindsay",,"2XAjyDAA/0KOyQlwNc7Bpg==","Implementer","8/1/2019 1:50:08 PM",,"<EMAIL>","lindsay.bronskill","+1 (403) 472-1772","S-1-5-21-**********-**********-*********-24606","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/1/2019 1:47:59 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bronskill",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"0cbe0720-78ba-4e61-8c21-eab74e9b48b3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","James Koss",,"James",,"HbRRK4V95UCjwb9YdkeltQ==","Junior Software Developer","8/9/2019 5:07:25 PM",,"<EMAIL>","james.koss",,"S-1-5-21-**********-**********-*********-24127","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/9/2019 4:41:18 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Koss",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"913286ad-738d-42fc-b990-4e1940cbd887","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Aditya Kumar Pothana",,"Aditya Kumar",,"9R5EuA34JE6IKv96IbMa0g==","QA Analyst","8/20/2019 3:21:49 PM",,"<EMAIL>","adityakumar.pothana",,"S-1-5-21-**********-**********-*********-24128","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/20/2019 3:15:10 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Pothana",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"eae57488-6da0-451b-9c3e-5b08d78cf551","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Development","True","Vrinda Monga",,"Vrinda",,"01F+fXglgEu0pqX3QZ1zDw==","QA Analyst","7/24/2019 6:48:36 PM",,"<EMAIL>","vrinda.monga",,"S-1-5-21-**********-**********-*********-22951","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/17/2019 4:46:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Monga",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"be64449f-946f-4d11-ab97-a93a17f28031","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Denis Ivanov",,"Denis",,"bUYssAaHLUuLlswvnMmB0g==","Client Services Analyst","8/15/2019 3:34:17 PM",,"<EMAIL>","denis.ivanov",,"S-1-5-21-**********-**********-*********-24134","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/15/2019 3:25:47 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Ivanov","+**************** ,3742","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"85ac14ba-43a7-4999-b801-ce5b4f7dea0d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Jordan Wong",,"Jordan",,"SVRz4ytUGkeBMTU3fJDraA==","Client Services Analyst","8/23/2019 12:30:23 PM",,"<EMAIL>","jordan.wong",,"S-1-5-21-**********-**********-*********-22594","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/23/2019 12:12:46 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Wong","+**************** ,3606","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ab6d46ce-5ad4-42fb-88d6-857f93d5c1d5","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Nikki Mulholland",,"Nikki",,"ln8J9ewDQUKVjcTuJg6tpw==","Client Services Analyst","8/16/2019 3:04:58 PM",,"<EMAIL>","nikki.mulholland",,"S-1-5-21-**********-**********-*********-22596","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/16/2019 3:04:34 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Mulholland","+**************** ,3611","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"aa5ad9a0-8db0-4bcc-b7ce-17d874bd9cf2","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Toronto","EMR Division - QHR",,"Canada",,"Implementations","True","Ken Royea",,"Ken",,"vrI/eI4qBEKFuGJIRS6CBQ==","Data Services Representative","6/17/2019 8:45:56 PM",,"<EMAIL>","ken.royea",,"S-1-5-21-**********-**********-*********-22597","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","6/17/2019 3:05:51 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Royea","+**************** ,3743","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"00840927-c80b-4494-93fb-2d79c3287dd3","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Raza Khan",,"Raza",,"xGdq7Pzbck2aKH3RfBMZFg==","Business Analyst","7/2/2019 8:50:55 PM",,"<EMAIL>","raza.khan",,"S-1-5-21-**********-**********-*********-24617","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 6:51:58 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Khan",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"2e8d508f-baff-4574-aacc-22870c401235","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]","Kelowna","EMR Division - QHR",,,,"Development","True","Ngumba Kamau",,"Ngumba",,"AvXfZJzIx0O7oJXSCAzIqw==","Software Developer","7/2/2019 7:20:51 PM",,"<EMAIL>","ngumba.kamau",,"S-1-5-21-**********-**********-*********-24619","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 7:12:22 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Kamau",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"b464082e-4959-4ff9-811d-e72cd191df06","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Marketing","True","Kari McDonald",,"Kari",,"Q/KprXcnq0i/CSkJFTxwxw==","Lead Generation Specialist","7/25/2019 4:19:21 PM",,"<EMAIL>","kari.mcdonald",,"S-1-5-21-**********-**********-*********-24621","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 7:36:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"McDonald","+****************, 3557","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"809fcf9e-a0b2-4d75-8061-d5cba67d49c1","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Product Management","True","Jason Alleyne",,"Jason",,"tW2pDLKu3EK78kAQstDn8w==","Product Owner","7/2/2019 8:50:55 PM",,"<EMAIL>","jason.alleyne",,"S-1-5-21-**********-**********-*********-25101","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/2/2019 5:13:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Alleyne",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"4ee44a25-4a6f-4f25-9ba7-adc74debc788","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Development","True","Andreas Niemoeller",,"Andreas",,"yZZKERmojEi+YLkRIbwyCg==","Software Developer","7/17/2019 10:13:25 PM",,"<EMAIL>","andreas.niemoeller",,"S-1-5-21-**********-**********-*********-25106","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/8/2019 7:29:38 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Niemoeller",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"be9033ae-5c1f-4fac-8599-3dc75be206ac","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Carla Vallee",,"Carla",,"O2E1Nn1L20W6ntrweAJCyQ==","Implementer","7/4/2019 2:02:59 PM",,"<EMAIL>","carla.vallee",,"S-1-5-21-**********-**********-*********-24625","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/4/2019 1:09:16 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Vallee",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"e804ebb1-d0bf-40ec-b20d-a2292572ab14","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Data Analysis","True","Rowell Selvano",,"Rowell",,"cDqUs85WsEmGpz7noOC/TQ==","Data Analyst","8/8/2019 3:36:36 PM",,"<EMAIL>","rowell.selvano",,"S-1-5-21-**********-**********-*********-24627","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/15/2019 4:04:24 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Selvano",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"ffc6b8a8-2a9d-427c-aed2-ab56b94d5149","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Colin Joseph",,"Colin",,"uiXvBtRLl0G9JjCRrhS5Zw==","Account Executive","8/8/2019 10:36:46 PM",,"<EMAIL>","colin.joseph","+1 (647) 612-9366","S-1-5-21-**********-**********-*********-25109","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"18 King Street East",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/22/2019 12:59:12 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Joseph",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"8bdcec29-872c-44b1-be5d-523ae1c9df50","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Business Development","True","Alisha Bennett",,"Alisha",,"fJDbRrduykesKFTxuaGYKg==","Account Executive","8/8/2019 10:36:46 PM",,"<EMAIL>","alisha.bennett","+1 (306) 201-4346","S-1-5-21-**********-**********-*********-24629","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,,,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","7/10/2019 7:04:33 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Bennett",,"CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"7bc007a7-c46d-4555-8875-7a636826218b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Implementations","True","Stephan Luies",,"Stephan",,"QqfIt3WNB0CTit1SiIFWSg==","Lead Data Analyst","8/6/2019 4:03:20 PM",,"<EMAIL>","stephan.luies",,"S-1-5-21-**********-**********-*********-22974","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/6/2019 4:01:25 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Luies","+**************** ,3513","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"60e85cc6-0079-4cd2-9f19-73354325313d","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Andrey Fedorov",,"Andrey",,"jDkN6VS3DEqzgCimVyp3fA==","Client Services Analyst","8/19/2019 6:59:07 PM",,"<EMAIL>","andrey.fedorov",,"S-1-5-21-**********-**********-*********-24160","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 5:37:42 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Fedorov","+**************** ,3748","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"31037391-f75f-41b0-bfc0-a86a7f5aa26b","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Julianna Schell",,"Julianna",,"NsU3CbWGzkSeiVpYQD7Ajg==","Client Services Analyst","8/12/2019 6:01:40 PM",,"<EMAIL>","julianna.schell",,"S-1-5-21-**********-**********-*********-24157","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 5:36:55 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Schell","+**************** ,3749","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"3b1e60e2-3a99-4508-afd5-8da6921dd191","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Brie-Anne Terhune",,"Brie-Anne",,"pllmYZH6m064SBH7lCdxXQ==","Client Services Analyst","8/12/2019 6:01:40 PM",,"<EMAIL>","brieanne.terhune",,"S-1-5-21-**********-**********-*********-24158","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 5:31:07 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Terhune","+**************** ,3751","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"c268aa45-ff67-4283-98f1-f65b5d054d10","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"EMR Division - QHR",,,,"Client Services","True","Kaitlin Winsor",,"Kaitlin",,"J57dtAykeUq02fiT33Cq9w==","Client Services Analyst","8/12/2019 6:01:40 PM",,"<EMAIL>","kaitlin.winsor",,"S-1-5-21-**********-**********-*********-24161","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 5:36:35 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>","BC",,"Winsor","+**************** ,3747","CA","<EMAIL>",,,"Member"
"System.Collections.Generic.Dictionary`2[System.String,System.String]",,"189adb28-4409-4c63-8f10-28c501b17dbe","User","True",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedLicense]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.AssignedPlan]",,"QHR Technologies Inc.",,,,"Administration","True","Brett Rothenburger",,"Brett",,"xJTAD6e7jkGKyLle09EDfg==","Administrative Coordinator","8/12/2019 6:31:41 PM",,"<EMAIL>","brett.rothenburger",,"S-1-5-21-**********-**********-*********-24164","System.Collections.Generic.List`1[System.String]","DisablePasswordExpiration",,"Kelowna",,,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisionedPlan]","System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.ProvisioningError]","System.Collections.Generic.List`1[System.String]","8/12/2019 6:10:37 PM",,"System.Collections.Generic.List`1[Microsoft.Open.AzureAD.Model.SignInName]","<EMAIL>",,,"Rothenburger","+****************","CA","<EMAIL>",,,"Member"
