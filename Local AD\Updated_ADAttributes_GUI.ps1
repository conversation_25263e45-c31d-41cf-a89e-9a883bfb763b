
<#
.SYNOPSIS
    GUI-based tool for updating user attributes in Active Directory.

.DESCRIPTION
    GUI PowerShell script simplifies modifying user attributes within Active Directory (AD). 
    Supported attributes: Province, Department, Manager, and Title.

.EXAMPLE
    .\ADAttributesGUI.ps1

    Convert to EXE:
    Invoke-ps2exe -inputFile ".\ADAttributesGUI.ps1" `
                  -outputFile ".\ADAttributeEditor.exe" `
                  -noConsole `
                  -requireAdmin

.NOTES
    Author: Slader Sheppard
    Version: 1.5
    Created: 2025-03-24
#>

# Load assemblies
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Import AD Module
if (-not (Get-Module -ListAvailable -Name ActiveDirectory)) {
    [System.Windows.Forms.MessageBox]::Show("Install Active Directory module (RSAT).", "Missing Module","OK","Error")
    exit
}
Import-Module ActiveDirectory

# Logging setup
$logDir = "C:\Temp\Helpdesk\LocalADAttributes"
$logFile = "$logDir\ChangeLocalADAttributes.txt"
if (-not (Test-Path $logDir)) { New-Item $logDir -ItemType Directory | Out-Null }

# GUI Form
$form = New-Object System.Windows.Forms.Form
$form.Text = "AD User Attribute Editor"
$form.Size = New-Object System.Drawing.Size(600,720)
$form.StartPosition = "CenterScreen"

# Email input
$emailLabel = New-Object System.Windows.Forms.Label
$emailLabel.Text = "User Email (<EMAIL>):"
$emailLabel.Location = New-Object System.Drawing.Point(10,20)
$emailLabel.Size = New-Object System.Drawing.Size(350,20)
$form.Controls.Add($emailLabel)

$emailBox = New-Object System.Windows.Forms.TextBox
$emailBox.Location = New-Object System.Drawing.Point(10,45)
$emailBox.Size = New-Object System.Drawing.Size(400,22)
$form.Controls.Add($emailBox)

# Attribute fields/buttons
$attributes = @("Province","Department","Manager","Title")
$adProperties = @{Province="st";Department="Department";Manager="Manager";Title="Title"}
$inputBoxes = @{}
$yPosition = 85

foreach ($attr in $attributes) {
    $currentAttr = $attr
    $currentY = $yPosition

    # Label
    $label = New-Object System.Windows.Forms.Label
    $label.Text = "$currentAttr{}:"
    $label.Location = New-Object System.Drawing.Point(10,$currentY)
    $label.Size = New-Object System.Drawing.Size(100,22)
    $form.Controls.Add($label)

    # Textbox
    $textbox = New-Object System.Windows.Forms.TextBox
    $textbox.Location = New-Object System.Drawing.Point(120,$currentY)
    $textbox.Size = New-Object System.Drawing.Size(260,22)
    $form.Controls.Add($textbox)
    $inputBoxes[$currentAttr] = $textbox

    # Button
    $button = New-Object System.Windows.Forms.Button
    $button.Location = New-Object System.Drawing.Point(400,$currentY)
    $button.Size = New-Object System.Drawing.Size(160,24)
    $button.Text = "Update $currentAttr"
    $form.Controls.Add($button)

    $button.Add_Click({
        $email = $emailBox.Text.Trim()
        $user = Get-ADUser -Filter "(Mail -eq '$email') -or (SamAccountName -eq '$($email.Split('@')[0])')" -Properties st,Department,Title,Manager
        if (-not $user) { [System.Windows.Forms.MessageBox]::Show("User not found."); return }

        $buttonClicked = $this
        $attrToUpdate = $buttonClicked.Text -replace "Update ",""
        $newVal = $inputBoxes[$attrToUpdate].Text.Trim()
        $adProp = $adProperties[$attrToUpdate]

        $oldVal = if ($adProp -eq "Manager" -and $user.Manager) { (Get-ADUser $user.Manager).Name } else { $user.$adProp }

        if ([string]::IsNullOrWhiteSpace($newVal)) { [System.Windows.Forms.MessageBox]::Show("Enter $attrToUpdate."); return }

        try {
            switch ($attrToUpdate) {
                'Province'   { Set-ADUser $user -Replace @{st=$newVal} }
                'Department' { Set-ADUser $user -Department $newVal }
                'Title'      { Set-ADUser $user -Title $newVal }
                'Manager' {
                    $mgr = Get-ADUser -Filter "(Mail -eq '$newVal') -or (SamAccountName -eq '$newVal')"
                    if (-not $mgr) { throw "Manager not found." }
                    Set-ADUser $user -Manager $mgr.DistinguishedName
                    $newVal = $mgr.Name
                }
            }

            # Enhanced log format
            $logEntry = @"
===========================================================
Date/Time   : $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Admin User  : $env:USERNAME
Target User : $($user.SamAccountName)

Changes Made:
    $attrToUpdate : '$oldVal' -> '$newVal'
===========================================================

"@
            Add-Content $logFile $logEntry
            $logBox.Text = Get-Content $logFile -Tail 50 | Out-String
            [System.Windows.Forms.MessageBox]::Show("$attrToUpdate updated successfully.")
        } catch {
            [System.Windows.Forms.MessageBox]::Show("Error updating $attrToUpdate{}: $_")
        }
    })
    $yPosition += 35
}

# Update All Button
$updateAllBtn = New-Object System.Windows.Forms.Button
$updateAllBtn.Location = New-Object System.Drawing.Point(120,($yPosition + 10))
$updateAllBtn.Size = New-Object System.Drawing.Size(260,30)
$updateAllBtn.Text = "Update All Attributes"
$form.Controls.Add($updateAllBtn)

$updateAllBtn.Add_Click({
    $email = $emailBox.Text.Trim()
    $user = Get-ADUser -Filter "(Mail -eq '$email') -or (SamAccountName -eq '$($email.Split('@')[0])')" -Properties st,Department,Title,Manager
    if (-not $user) { [System.Windows.Forms.MessageBox]::Show("User not found."); return }

    $logDetails = ""
    foreach ($attr in $attributes){
        $newVal = $inputBoxes[$attr].Text.Trim()
        if ($newVal) {
            $adProp = $adProperties[$attr]
            $oldVal = if ($adProp -eq "Manager" -and $user.Manager) { (Get-ADUser $user.Manager).Name } else { $user.$adProp }
            try {
                switch ($attr) {
                    'Province'   { Set-ADUser $user -Replace @{st=$newVal} }
                    'Department' { Set-ADUser $user -Department $newVal }
                    'Title'      { Set-ADUser $user -Title $newVal }
                    'Manager' {
                        $mgr = Get-ADUser -Filter "(Mail -eq '$newVal') -or (SamAccountName -eq '$newVal')"
                        if (-not $mgr) { throw "Manager not found." }
                        Set-ADUser $user -Manager $mgr.DistinguishedName
                        $newVal = $mgr.Name
                    }
                }
                $logDetails += "    $attr : '$oldVal' -> '$newVal'`n"
            } catch {
                [System.Windows.Forms.MessageBox]::Show("Error updating $attr{}: $_")
            }
        }
    }

    if ($logDetails) {
        $logEntry = @"
===========================================================
Date/Time   : $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Admin User  : $env:USERNAME
Target User : $($user.SamAccountName)

Changes Made:
$logDetails===========================================================

"@
        Add-Content $logFile $logEntry
        $logBox.Text = Get-Content $logFile -Tail 50 | Out-String
        [System.Windows.Forms.MessageBox]::Show("Attributes updated successfully.")
    }
})

# Log Display
$logLabel = New-Object System.Windows.Forms.Label
$logLabel.Text = "Recent Activity Log:"
$logLabel.Location = New-Object System.Drawing.Point(10,($yPosition + 50))
$form.Controls.Add($logLabel)

$logBox = New-Object System.Windows.Forms.TextBox
$logBox.Location = New-Object System.Drawing.Point(10,($yPosition + 75))
$logBox.Size = New-Object System.Drawing.Size(550,290)
$logBox.Multiline = $true
$logBox.ReadOnly = $true
$logBox.ScrollBars = "Vertical"
$form.Controls.Add($logBox)
$logBox.Text = if (Test-Path $logFile) { Get-Content $logFile -Tail 50 | Out-String } else { "No log entries." }

$form.Topmost = $true
$form.Add_Shown({$form.Activate()})
[void]$form.ShowDialog()
