﻿Import-Module ActiveDirectory
 
# All of the properties you'd like to pull from Get-ADUser
$properties=@(
    'givenName',
    'sn',
#    'sAMAccountType',
    'mail',
    'objectSid',
    'department',
    'title'
    'extensionAttribute10'
    'extensionAttribute11'
    'description',
    'whenCreated',
    'userPrincipalName',
    'lastLogon',
    'sAMAccountName'
    )
 
 
# All of the expressions you want with all of the filtering .etc you'd like done to them
$expressions=@(
    @{Expression={$_.givenName};Label="First Name"},
    @{Expression={$_.sn};Label="Last Name"},
#    @{Expression={$_.sAMAccountType};Label="Account Type"},
    @{Expression={$_.mail};Label="Email Address"},
   @{Expression={$_.department};Label="Department"},
  @{Expression={$_.title};Label="Title"},
    @{Expression={$_.extensionAttribute10};Label="Extension attribute 10"},
    @{Expression={$_.extensionAttribute10};Label="Extension attribute 11"},
    @{Expression={$_.description};Label="Description"},
    @{Expression={$_.whenCreated};Label="Account Created on"},
    @{Expression={[DateTime]::FromFileTime([Int64]::Parse($_.lastLogon))};Label="Last Log-on Date"},
    @{Expression={$_.userPrincipalName};Label="Full User Logon"},
    @{Expression={$_.objectSid};Label="Unique Account ID"},
    @{Expression={$_.sAMAccountName};Label="User Logon"}
    )
 
$path_to_file = "C:\scripts\admin-and-service-user-report_$((Get-Date).ToString('yyyy-MM-dd_hh-mm-ss')).csv"

 
$groups = "Enterprise Admins","Domain Admins", "Schema Admins"
$results = foreach ($group in $groups) {
Get-ADGroupMember $group | Get-aduser -Properties $properties | select $expressions
} 
$results += Get-ADGroupMember $group | Get-aduser -filter {Title -eq "Service Account"}  -Properties $properties | select $expressions
 $results += Get-ADGroupMember $group |Get-aduser -filter {Name -like "*Domain*"} -Properties $properties | select $expressions
$results += Get-ADGroupMember $group | Get-aduser -filter {sAMAccountName -like '*test*'}  -Properties $properties | select $expressions
$results += Get-ADGroupMember $group | Get-aduser -filter {sAMAccountName -like '*svc*'}  -Properties $properties | select $expressions


$results
$results | Export-CSV $path_to_file -notypeinformation -Encoding UTF8