# Find all files containing "log4j" on the C: drive
# ErrorAction SilentlyContinue to ignore permission errors
$log4jFiles = Get-ChildItem -Path C:\ -Recurse -ErrorAction SilentlyContinue -Filter "*log4j*"

# Create a list to hold file paths and names
$fileData = @()

Write-Host "Found log4j files:"

# Iterate over the files, populate the list, and list in console
foreach ($file in $log4jFiles) {
    Write-Host $file.FullName  # List file path in the console
    $fileData += [PSCustomObject]@{
        FilePath = $file.FullName
        FileName = $file.Name
    }
}

# Output to a CSV file (change to XLSX if desired)
# Check if there are any files to output before attempting to export
if ($fileData.Count -gt 0) {
    $fileData | Export-Csv -Path ".\log4j_files_$(Get-Date -Format yyyyMMdd_HHmmss).csv" -NoTypeInformation # Add timestamp to filename
    Write-Host "File list exported to log4j_files.csv"
} else {
    Write-Host "No log4j files found."
}

# Ask the user whether to delete the files
Write-Host "Do you want to delete the log4j files?"
Write-Host "0 = No, 1 = Yes"
do {
    $choice = Read-Host "Enter your choice:"
} while ($choice -notin "0", "1")  # Keep asking until valid input is given

# Delete files if the user chooses to
if ($choice -eq "1") {
    foreach ($file in $log4jFiles) {
        try {
            Remove-Item $file.FullName -Force -Recurse
            Write-Host "Deleted: $($file.FullName)"
        }
        catch {
            Write-Host "Error deleting: $($file.FullName) - $($_.Exception.Message)" 
        }
    }
}

Write-Host "Script completed."
