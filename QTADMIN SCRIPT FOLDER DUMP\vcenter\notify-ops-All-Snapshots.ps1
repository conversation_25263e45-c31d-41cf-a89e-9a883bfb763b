﻿#
#
# notify-ops-All-Snapshots.ps1
#
# March 16, 2017
# Mi<PERSON>
#
# prints all snapshots in vSphere env
# global vars
#$vCenter = "************"
$vCenter = "vcenter161.quadranthr.com"
#$vCenter = "vcenter.quadranthr.com"
$subject = "Daily QTECH vSphere Snapshot Report"
#$recipient = "<EMAIL>"
$recipient = "<EMAIL>"
$sender = "vCenter Admin <<EMAIL>>"
$errorFound = $false
 
$body = @"
    <html><head>
        <style>
            table {
                border-collapse: collapse;
                width: 100%;
            }
            td, th {
                border: 1px solid black;
                padding: 3px;
            }
            .greybackground {
                background-color: #BDC3C7;
            }
            .darkgreybackgroundandbold {
                background-color: #808B96;
                font-weight: bold;
            }
        </style>
        <title>$($subject)</title>
    </head><body>
"@
# pre
### import PowerCLI module
try
{
    Import-Module -Name VMware.PowerCLI -ErrorAction Stop | Out-Null
    Set-PowerCLIConfiguration -InvalidCertificateAction Ignore -confirm:$false
    Set-PowerCLIConfiguration -Scope User -ParticipateInCEIP:$false -confirm:$false
}
catch
{
    $body += "<h1>ERROR: Can't add VMware.VimAutomation.Core module...</h1>"
    $errorFound = $true
}
try
{
 
    #Get-Credential | Export-Clixml "vcenter-ro.clixml"
    $path = ".\creds\vcenter-ro.clixml"
    $VICredentials = Import-Clixml -Path $path
    
    Connect-VIServer -Server $vCenter -Credential $VICredentials -ea Stop -wa SilentlyContinue
    #Connect-VIServer -Server $vCenter
    
}
catch
{
    $body += "<h1>ERROR: Can't connect to vCenter $($vCenter) $($($error[0]))</h1>"
    $errorFound = $true
}
function Get-VM-Path
{
    param($VM)
    $current = $VM.Folder
    $path = ""
    do {
        $parent = $current
        if($parent.Name -ne "vm")
        {
            $path = $parent.Name + "\" + $path
        }
        $current = $current.Parent
    } while ($current.Parent -ne $null)
        
    #return "$(Get-Datacenter -vm $VM)\$($path)$($VM)"
    return "$($path)$($VM)"
}
# main
if (-Not $errorFound)
{
    
    $datacenters = Get-Datacenter | Sort
    ForEach ($datacenter in $datacenters)
    {
        $body += "<table>"
        
        $body += "<tr><td class=`"darkgreybackgroundandbold`">$($datacenter) Datacenter</td></tr>"
        $body += "<tr><td><table>"
        
        $VMs = Get-VM -Location $datacenter | Sort
        $snapInfo = ""
        ForEach ($VM in $VMs)
        {
            $snaps = Get-Snapshot -VM $VM -ErrorAction SilentlyContinue
            if ($snaps)
            {
                $snapInfo += "<tr><td class=`"greybackground`">$(Get-VM-Path $VM)</td></tr>"
                ForEach ($snap in $snaps)
                {
                    $user = ($snap.VM | Get-ViEvent | ?{$_.CreatedTime -match $snap.Created} | ?{$_.FullFormattedMessage -match "snapshot"}).Username
                    if (-Not $user) { $user = "unknown" }
                    
                    $age = New-TimeSpan -Start (Get-Date "$($snap.created)")
                    $snapInfo += "<tr><td><p>VM: $($VM)<br>Snapshot name: $($snap.name)<br>Created: $($snap.created)<br>User: $($user)<br>Age: $($age.days) days<br>Size: " + ([math]::Round($snap.SizeMB)) + " MB<br>Description: $($snap.description)</p></td></tr>"
                }
            }
        }
        if ($snapInfo)
        {
            $body += $snapInfo
        }
        else
        {
            $body += "<tr><td>No snapshots found</td></tr>"
        }
        $body += "</table></td></tr>"
        $body += "</table>"
        $body += "<br>"
    }
    
}
$body += "</body></html>"
#[System.Windows.MessageBox]::Show($body)
<#Send-MailMessage -To $recipient -from $sender `
                 -Subject $subject -Body $body `
                 -SmtpServer ************** -Port 587 -BodyAsHtml#>

Send-MailMessage -To $recipient -from $sender `
                 -Subject $subject -Body $body `
                 -SmtpServer ca-smtp-outbound-1.mimecast.com -Port 25  -BodyAsHtml
# post
Disconnect-VIServer -Server $vCenter -Confirm:$false -ErrorAction SilentlyContinue 