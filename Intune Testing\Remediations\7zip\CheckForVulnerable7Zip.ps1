﻿<#
.SYNOPSIS
    Detects installed versions of 7-Zip known to be vulnerable or outdated.

.DESCRIPTION
    This script scans the local system's registry to identify installed versions of 7-Zip that match known vulnerable or outdated version patterns.
    It specifically searches both 32-bit and 64-bit Windows registry uninstall keys to accurately detect installations.
    If vulnerable versions of 7-Zip are detected, the script outputs the specific version(s) found and exits with code 1.
    If no vulnerable versions are detected, the script reports this and exits cleanly with code 0.
    
    Version patterns checked:
        - 7-Zip 9.*
        - 7-Zip 16.*
        - 7-<PERSON>ip 19.*
        - 7-Zip 21.*
        - 7-Zip 22.*
        - 7-Zip 23.*
        - 7-Zip 24.06

.NOTES
    Author: <PERSON><PERSON><PERSON>
    Edited and Revised By: <PERSON><PERSON>
    Version 2.0
    Date: 2025-03-24
    Requires: Administrator privileges to accurately query registry keys (run in admin powershell window).
#>


# List of vulnerable 7-Zip version patterns
$vulnerableVersions = @(
    "1.*",
    "2.*",
    "3.*",
    "4.*",
    "5.*",
    "6.*",
    "7.*",
    "8.*",
    "9.*", 
    "10.*",
    "11.*",
    "12.*",
    "13.*",
    "14.*",
    "15.*",
    "16.*", 
    "17.*",
    "18.*",
    "19.*", 
    "20.*", 
    "21.*", 
    "22.*", 
    "23.*", 
    "24.00",
    "24.01",
    "24.02",
    "24.03",
    "24.04",
    "24.05",
    "24.06",
    "24.07",
    "24.08"
)

# Registry paths to search for installed applications
$registryPaths = @(
    'HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*',
    'HKLM:\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*'
)

$foundPrograms = @()

# Search each registry path for vulnerable 7-Zip versions
foreach ($path in $registryPaths) {
    $installedApps = Get-ItemProperty $path -ErrorAction SilentlyContinue | 
        Where-Object { $_.DisplayName -like "7-Zip*" }

    foreach ($app in $installedApps) {
        foreach ($versionPattern in $vulnerableVersions) {
            if ($app.DisplayVersion -like $versionPattern) {
                $foundPrograms += "$($app.DisplayName) $($app.DisplayVersion)"
            }
        }
    }
}

# Output results and set appropriate exit code
if ($foundPrograms.Count -gt 0) {
    Write-Output "Vulnerable programs found: $($foundPrograms -join ', ')"
    exit 1
} else {
    Write-Output "No vulnerable programs found"
    exit 0
}
