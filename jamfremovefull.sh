#!/bin/bash

# Shut down Jamf Connect
if pgrep -q "Jamf Connect"; then
    echo "Killing 'Jamf Connect' process..."
    sudo pkill "Jamf Connect"
    echo "'Jamf Connect' process has been killed."
else
    echo "'Jamf Connect' is not currently running."
fi

# Remove "Jamf Connect" from the Applications folder
if [ -e "/Applications/Jamf Connect.app" ]; then
    echo "Removing 'Jamf Connect' from the Applications folder..."
    rm -rf "/Applications/Jamf Connect.app"
    echo "'Jamf Connect' has been removed from the Applications folder."
else
    echo "'Jamf Connect' is not installed in the Applications folder."
fi

# Reset authchanger
echo "Resetting authchanger..."
sudo /usr/local/bin/authchanger -reset
echo "authchanger has been reset."

# Remove files
echo "Removing files related to Jamf Connect..."
sudo rm /usr/local/bin/authchanger
sudo rm /usr/local/lib/pam/pam_saml.so.2
sudo rm -r /Library/Security/SecurityAgentPlugins/JamfConnectLogin.bundle
echo "Files related to <PERSON>f Connect have been removed."

echo "Jamf Connect shutdown and cleanup completed."
