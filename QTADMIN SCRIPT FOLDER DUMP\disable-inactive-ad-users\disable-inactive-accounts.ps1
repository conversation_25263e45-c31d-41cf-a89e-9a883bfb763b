﻿## disable-inactive-accounts for QHR CORP by <PERSON>
## !! Please see confluence document before running or editing this script! !!  https://confluence.qhrtech.com/display/DCO/Automatic+Disable+of+AD+Accounts+-+QHR+CORP

###### Set these variables
$csvExcludePath = "C:\Scripts\disable-inactive-ad-users\excluded-users.csv" #Users set here will be excluded from being disabled
$log = "C:\Scripts\disable-inactive-ad-users\logs\$(get-date -f yyyy-MM-dd)-disable-inactive-accounts.log" # Log Path 
$recentlyDisabledAccounts = "C:\Scripts\disable-inactive-ad-users\recently-disabled-accounts.csv" # This is overwritten each time script is run. Used only to send email attachment of disabled users and if recently disabled users need to be enabled in again
$recipients = @("<EMAIL>") #Email addresses to send alert to. 
$smtpServer = "mail.qhrtech.com" #SMTP server address to send alerts from
######


$users = @() #Clear users
$list = ""  #Clear list

#Used to format email
$Header = @" 
<style>
TABLE {border-width: 1px; padding: 10px; border-style: solid; border-color: black; border-collapse: collapse;}
TD {border-width: 1px; padding: 10px; border-style: solid; border-color: black;}
</style>
"@

#See if log can be written
try { 
    write-host "$(get-date -f g) - Starting Script" | out-file $log -Append -ErrorAction stop
    "$(get-date -f g) - Starting Script"
}
catch { 
    "Unable to write to log $log, stopping script"

    #email alert that log can't be written as to and script won't be run.
    $warningmessage = "<h4>WARNING:  disable-inactive-accounts.ps1 will not run as log can't be generated to $log</h4>"


    Send-MailMessage -To $recipients -From “<EMAIL>” -SmtpServer $smtpServer -Subject “WARNING:  disable-inactive-accounts.ps1 will not run as log can't be generated” `
        -body "$warningmessage ` 
        <br><br><br> NOTE: This script runs weekly on QTADMIN1 Thursdays at 8am as a scheduled task (disable-inactive-accounts.ps1)." -BodyAsHtml 
    break
}

# Generate list of active AD accounts that have not been logged into for 45 days or longer
"$(get-date -f g) - Generating list of active AD accounts that have not been logged into for 45 days or longer" | out-file $log -Append
"$(get-date -f g) - Generating list of active AD accounts that have not been logged into for 45 days or longer"
try{
    #$list = Search-ADAccount -UsersOnly -AccountInactive -TimeSpan 45.00:00:00 | Where {$_.Enabled} | Sort SamAccountName  | Get-ADUser -Prop DisplayName | Select SamAccountName, Name
    #$list = get-aduser -filter 'enabled -eq $true' -Properties name,samaccountname,lastlogondate,whencreated | Where-object {$_.lastlogondate -lt (get-date).AddDays(-45) -AND $_.whencreated -lt (get-date).AddDays(-45)} | Select samaccountname,name
    $list = (get-aduser -filter 'enabled -eq $true' -Properties name,samaccountname,lastlogondate,whencreated).where({$_.lastlogondate -lt (get-date).AddDays(-45) -AND $_.whencreated -lt (get-date).AddDays(-45)}) | Select samaccountname,name
}
catch{
    "$(get-date -f g) - ERROR: can't generate list of active AD accounts that have not been logged into for 45 days or longer. Stopping script" | out-file $log -Append
    "$(get-date -f g) - ERROR: can't generate list of active AD accounts that have not been logged into for 45 days or longer. Stopping script"
    "" | out-file $log -Append
    break
}


#import CSV exclude user list
try {
    "$(get-date -f g) - Importing user exclude CSV from $csvExcludePath" | out-file $log -Append
    $users = import-csv $csvExcludePath
    $numberOfUsersToExclude = $users.count
    "$(get-date -f g) - Successful import of CSV user exclude config, $numberOfUsersToExclude users were imported" | out-file $log -Append
}
catch{
    "ERROR: can't read csv file in $csvExcludePath. Stopping Script"
    "$(get-date -f g) - ERROR: can't read csv file in $csvExcludePath. Stopping Script" | out-file $log -Append
    "" | out-file $log -Append
    break
}

#remove excluded users from list
"$(get-date -f g) - Removing users in exclusion list" | out-file $log -Append
try {
    foreach($user in $users) {
        $list = $list | ? {$_.SamAccountName -notlike $user.SamAccountName} #Exclude users in excell sheet by SamAccountName
    }
}
catch {
    "ERROR: can't exclude users in $csvExlcudePath. Stopping Script"
    "$(get-date -f g) - ERROR: can't exclude users in $csvExcludePath. Stopping Script" | out-file $log -Append
    "" | out-file $log -Append
}

"$(get-date -f g) -v- Users disabled -v-" | out-file $log -Append
"$(get-date -f g) -v- Users disabled -v-"


#disable all accounts left in the list and log them
try {
    foreach($member in $list) {
        $memberSamName = $member.samAccountName
        
        #Disable-ADAccount -Identity $memberSamName
        "$memberSamName" | out-file $log -Append
        "$memberSamName"
    }
}
catch {
    "$(get-date -f g) Error: Can't disable accounts. Might be permissions issue. Stopping Script" | out-file $log -Append
    "$(get-date -f g) Error: Can't disable accounts. Might be permissions issue. Stopping Script" 

}


"$(get-date -f g) -^- Finished Disabling User Accounts -^-" | out-file $log -Append
"$(get-date -f g) -^- Finished Disabling User Accounts -^-" 

#Export list to CSV to use in email alert
$list | export-csv $recentlyDisabledAccounts -NoTypeInformation
"$(get-date -f g) - Created email csv attachment with recently disabled users" | out-file $log -Append


#Send email alert
try {

    $disabledCount = $list.Count
    $warningmessage = "<h4>Alert:  $disabledCount AD accounts have now been disabled due to NOT being logged into for 45 days or longer. See attachment for list of accounts</h4>"


    Send-MailMessage -To $recipients -From “<EMAIL>” -SmtpServer $smtpServer -Attachments $recentlyDisabledAccounts -Subject “Alert - These accounts have been disabled for being inactive for 45 days” `
        -body "$warningmessage ` 
        <br><br><br> NOTE: This script runs weekly on QTADMIN1 Thursdays at 8am as a scheduled task (disable-inactive-accounts.ps1)." -BodyAsHtml 

    "$(get-date -f g) - Sent email alert to $recipients" | out-file $log -Append
    "$(get-date -f g) - Sent email alert to $recipients" 

}
catch {
    "$(get-date -f g)Error: Can't send out email alert, SMTP server "
    "$(get-date -f g)Error: Can't send out email alert" | out-log $log -Append
}
"$(get-date -f g) - Script Complete" | out-file $log -Append
"$(get-date -f g) - Script Complete"
"" | out-file $log -Append

