<#
.SYNOPSIS
    Retrieves comprehensive device details from Microsoft Intune using the Microsoft Graph API.
    Gathers information such as Device Name, Model, Serial Number (Service Tag), and Enrollment Age.
    Exports the collected data into a CSV file located by default in C:\Temp\Helpdesk\IntuneDeviceReports\.

.DESCRIPTION
    This script connects to the Microsoft Graph API with necessary permissions to query Intune managed devices.
    It iterates through all managed devices, extracting key hardware and enrollment information.
    The script calculates the age of the device based on its enrollment date in Intune, providing this age in days.
    Handles potential missing data gracefully by outputting 'Unknown'.
    Includes robust error handling for API connection, data retrieval, and file export operations.
    Provides verbose output and progress tracking for better user experience, especially in large environments.
    Ensures necessary PowerShell modules (Microsoft.Graph) are installed before execution.
    The default output path is C:\Temp\Helpdesk\IntuneDeviceReports\IntuneDeviceReport_{Timestamp}.csv. This can be overridden using the OutputPath parameter.

.PARAMETER OutputPath
    Specifies the full path (including filename) where the generated CSV report will be saved.
    If not specified, defaults to "C:\Temp\Helpdesk\IntuneDeviceReports\IntuneDeviceReport_YYYYMMDD_HHMMSS.csv".

.EXAMPLE
    .\Get-IntuneDeviceReport.ps1
    Runs the script and saves the report to the default path "C:\Temp\Helpdesk\IntuneDeviceReports\IntuneDeviceReport_{Timestamp}.csv". Prompts for login if needed.

.EXAMPLE
    .\Get-IntuneDeviceReport.ps1 -OutputPath "C:\CustomReports\Inventory.csv" -Verbose
    Runs the script with verbose output, saving the device report to "C:\CustomReports\Inventory.csv".

.NOTES
    Author:     slader
    Date:       2025-04-14
    Version:    1.2

    Requirements:
    - PowerShell 5.1 or later.
    - Internet connectivity.
    - Permissions: Requires 'DeviceManagementManagedDevices.Read.All' permission scope for Microsoft Graph. The user running the script will be prompted for authentication upon the first run or if the token expires. Ensure you run this interactively the first time.
    - Module: Requires the 'Microsoft.Graph' PowerShell module. The script attempts to install it if missing.
    - Directory: The script will attempt to create the 'C:\Temp\Helpdesk\IntuneDeviceReports\' directory if it doesn't exist. Ensure the user running the script has write permissions to 'C:\Temp\Helpdesk'.

    Troubleshooting Freezing:
    - Ensure you are running this script in an INTERACTIVE PowerShell console session, not the ISE or a non-interactive process initially.
    - Watch for any pop-up browser windows asking for Microsoft 365 login credentials when 'Connect-MgGraph' runs.
    - Check network connectivity and firewall rules to ensure *.microsoftgraph.com and the PowerShell Gallery (powershellgallery.com) are accessible.
    - If it still hangs, try running commands manually:
        1. `Install-Module Microsoft.Graph -Scope CurrentUser -Force -AllowClobber -Verbose`
        2. `Connect-MgGraph -Scopes "DeviceManagementManagedDevices.Read.All"`
        3. `Get-MgDeviceManagementManagedDevice -Top 10` (to test basic data retrieval)

.LINK
    Microsoft Graph PowerShell SDK: https://learn.microsoft.com/en-us/powershell/microsoftgraph/overview?view=graph-powershell-1.0
    Get-MgDeviceManagementManagedDevice Cmdlet: https://learn.microsoft.com/en-us/powershell/module/microsoft.graph.devicemanagement/get-mgdevicemanagementmanageddevice?view=graph-powershell-1.0
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $false, HelpMessage = "Path to save the output CSV file.")]
    [string]$OutputPath # Default value is now set dynamically below
)

# --- Define Default Path ---
$DefaultReportDirectory = "C:\Temp\Helpdesk\IntuneDeviceReports"
$DefaultReportFileName = "IntuneDeviceReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
$DefaultOutputPath = Join-Path -Path $DefaultReportDirectory -ChildPath $DefaultReportFileName

# Set OutputPath to default if not provided by the user
if ([string]::IsNullOrWhiteSpace($OutputPath)) {
    $OutputPath = $DefaultOutputPath
    Write-Host "[INFO] Output path not specified, using default: $OutputPath" -ForegroundColor Cyan -Force
} else {
     Write-Host "[INFO] Using specified output path: $OutputPath" -ForegroundColor Cyan -Force
}


#Requires -Modules Microsoft.Graph

# --- Start Script Execution ---
Write-Host "[INFO] Starting script execution..." -ForegroundColor Cyan -Force

#region Dependency Management
function Install-RequiredModules {
    Write-Host "[INFO] Checking for required module: Microsoft.Graph..." -ForegroundColor Cyan -Force
    Write-Verbose "Checking for required module: Microsoft.Graph"
    # Check if the module is installed
    if (-not (Get-Module -ListAvailable -Name Microsoft.Graph)) {
        Write-Warning "Microsoft.Graph module not found. Attempting installation (this may take a moment)..."
        Write-Host "[INFO] Attempting to install Microsoft.Graph module..." -ForegroundColor Yellow -Force
        try {
            # Install the module for the current user, force to overwrite older versions, allow clobbering for command name conflicts
            Install-Module Microsoft.Graph -Scope CurrentUser -Force -AllowClobber -Confirm:$false -ErrorAction Stop -Verbose
            Write-Host "[SUCCESS] Microsoft.Graph module installed successfully." -ForegroundColor Green -Force
        } catch {
            Write-Error "[FATAL] Failed to install Microsoft.Graph module. Please install it manually ('Install-Module Microsoft.Graph -Scope CurrentUser') and try again. Error: $($_.Exception.Message)"
            # Exit the script if module installation fails
            exit 1
        }
    } else {
        Write-Verbose "Microsoft.Graph module is already installed."
        Write-Host "[INFO] Microsoft.Graph module found." -ForegroundColor Green -Force
    }
    # Import the specific submodule needed to ensure cmdlets are available
    Write-Host "[INFO] Importing Microsoft.Graph.DeviceManagement module..." -ForegroundColor Cyan -Force
    Import-Module Microsoft.Graph.DeviceManagement -ErrorAction SilentlyContinue # Silently continue if already imported
    Write-Host "[INFO] Module import attempt finished." -ForegroundColor Cyan -Force

}
#endregion

#region Core Logic
function Get-IntuneDeviceReport {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ReportPath
    )

    # Ensure the directory exists for the output path
    $ReportDirectory = Split-Path -Path $ReportPath -Parent
    Write-Host "[INFO] Ensuring output directory exists: $ReportDirectory" -ForegroundColor Cyan -Force
    if (-not (Test-Path -Path $ReportDirectory)) {
        Write-Verbose "Creating directory: $ReportDirectory"
        Write-Host "[INFO] Creating directory: $ReportDirectory" -ForegroundColor Yellow -Force
        try {
            New-Item -ItemType Directory -Path $ReportDirectory -Force -ErrorAction Stop | Out-Null
            Write-Host "[SUCCESS] Output directory created/verified." -ForegroundColor Green -Force
        } catch {
            Write-Error "[FATAL] Failed to create output directory '$ReportDirectory'. Please check permissions or choose a different path. Error: $($_.Exception.Message)"
            exit 1 # Use exit 1 to indicate failure in the function context that stops the script
        }
    } else {
         Write-Host "[INFO] Output directory already exists." -ForegroundColor Green -Force
    }

    # Connect to Microsoft Graph
    Write-Host "[INFO] Attempting to connect to Microsoft Graph API (may require interactive login)..." -ForegroundColor Cyan -Force
    try {
        # Check current connection state
        $currentConnection = Get-MgContext -ErrorAction SilentlyContinue
        if ($null -eq $currentConnection -or $currentConnection.Scopes -notcontains "DeviceManagementManagedDevices.Read.All") {
             Write-Verbose "No existing valid connection found or required scope missing. Establishing new connection."
             Write-Host "[ACTION] Initiating Graph connection. Please check for any login prompts..." -ForegroundColor Yellow -Force
             # Suppress the welcome message for a cleaner output
             Connect-MgGraph -Scopes "DeviceManagementManagedDevices.Read.All" -NoWelcome -ErrorAction Stop
             # Verify connection AFTER the call
             $newConnection = Get-MgContext -ErrorAction SilentlyContinue
             if ($null -eq $newConnection) {
                 Throw "Connect-MgGraph completed but no connection context was established. Authentication might have failed silently."
             }
             Write-Host "[SUCCESS] Successfully connected to Microsoft Graph." -ForegroundColor Green -Force
             Write-Verbose "Connection details: Account=$($newConnection.Account), TenantId=$($newConnection.TenantId), Scopes=$($newConnection.Scopes -join ', ')"
        } else {
             Write-Host "[INFO] Already connected to Microsoft Graph with sufficient scopes." -ForegroundColor Green -Force
             Write-Verbose "Existing Context Scopes: $($currentConnection.Scopes -join ', ')"
        }

    } catch {
        Write-Error "[FATAL] Failed to connect to Microsoft Graph API. Please ensure you have permissions, internet connectivity, and completed any interactive login prompts. Error: $($_.Exception.Message)"
        # Optionally, try to disconnect if a partial connection might exist
        Disconnect-MgGraph -ErrorAction SilentlyContinue
        exit 1
    }

    Write-Host "[INFO] Retrieving Intune device data... (This may take a while for large environments)" -ForegroundColor Cyan -Force
    $deviceReport = [System.Collections.Generic.List[PSCustomObject]]::new()
    $totalDevices = 0
    $processedDevices = 0
    $allDevices = $null # Initialize to null

    # Create a spinner function to show activity during long-running operations
    function Show-Spinner {
        param(
            [int]$Seconds,
            [string]$Activity = "Operation in progress"
        )

        $spinChars = '|', '/', '-', '\'
        $spinIndex = 0
        $startTime = Get-Date
        $endTime = $startTime.AddSeconds($Seconds)

        Write-Progress -Activity $Activity -Status "Starting..." -PercentComplete 0 -Id 2

        while ((Get-Date) -lt $endTime) {
            $elapsedSeconds = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
            $percentComplete = [math]::Min([math]::Round(($elapsedSeconds / $Seconds) * 100), 99)
            $spinChar = $spinChars[$spinIndex++ % $spinChars.Length]
            $status = "$spinChar Please wait... ($elapsedSeconds seconds elapsed)"

            Write-Progress -Activity $Activity -Status $status -PercentComplete $percentComplete -Id 2
            Start-Sleep -Milliseconds 250
        }
    }

    try {
        # Use -All to handle pagination automatically
        # Select only the properties needed to improve performance
        $selectProperties = @(
            "Id",
            "DeviceName",
            "Model",
            "SerialNumber",
            "EnrolledDateTime"
        )

        # Start a background job to show progress during API call
        Write-Host "[INFO] Starting API call to retrieve devices (this may take several minutes)..." -ForegroundColor Yellow -Force
        Write-Host "[INFO] Executing Get-MgDeviceManagementManagedDevice -All -Select Properties..." -ForegroundColor Cyan -Force

        # Start a timer to track elapsed time
        $apiCallStartTime = Get-Date

        # Show progress indicator during API call
        $progressJob = Start-Job -ScriptBlock {
            param($maxSeconds)
            $startTime = Get-Date
            $spinChars = '|', '/', '-', '\'
            $i = 0

            while ($true) {
                $elapsed = (Get-Date) - $startTime
                $elapsedSeconds = [math]::Round($elapsed.TotalSeconds)
                $percentComplete = [math]::Min([math]::Round(($elapsedSeconds / $maxSeconds) * 100), 99)
                $spinChar = $spinChars[$i++ % $spinChars.Length]

                Write-Progress -Activity "Retrieving devices from Microsoft Graph API" -Status "$spinChar Please wait... ($elapsedSeconds seconds elapsed)" -PercentComplete $percentComplete -Id 2

                Start-Sleep -Milliseconds 250

                # Stop after max seconds to prevent infinite loop
                if ($elapsedSeconds -ge $maxSeconds) {
                    break
                }
            }
        } -ArgumentList 300 # Assume max 5 minutes (300 seconds)

        # Execute the actual API call
        $allDevices = Get-MgDeviceManagementManagedDevice -All -Select $selectProperties -ErrorAction Stop

        # Stop the progress job
        Stop-Job -Job $progressJob
        Remove-Job -Job $progressJob -Force

        # Calculate and display elapsed time
        $apiCallElapsedTime = (Get-Date) - $apiCallStartTime
        Write-Host "[INFO] API call completed in $([math]::Round($apiCallElapsedTime.TotalSeconds)) seconds." -ForegroundColor Green -Force
        Write-Host "[INFO] Finished Get-MgDeviceManagementManagedDevice call." -ForegroundColor Cyan -Force

        # Check if any devices were returned
        if ($null -eq $allDevices) {
             Write-Warning "No devices were returned from Intune. Check permissions or if devices exist."
             # Attempt to disconnect before exiting
             Disconnect-MgGraph -ErrorAction SilentlyContinue
             exit 0 # Exit gracefully, but indicate no devices found
        }

        $totalDevices = $allDevices.Count
        Write-Host "[INFO] Found $totalDevices devices. Processing..." -ForegroundColor Green -Force
        Write-Progress -Activity "Processing Intune Devices" -Status "Gathering device details" -PercentComplete 0 -Id 1

        # Update progress every 10 devices to avoid console flooding but still show regular updates
        $updateFrequency = [Math]::Max(1, [Math]::Min(10, [Math]::Floor($totalDevices / 20)))

        foreach ($device in $allDevices) {
            $processedDevices++
            $percentComplete = if ($totalDevices -gt 0) { [int](($processedDevices / $totalDevices) * 100) } else { 0 }

            # Update progress bar
            Write-Progress -Activity "Processing Intune Devices" -Status "Device: $($device.DeviceName) ($processedDevices/$totalDevices)" -PercentComplete $percentComplete -Id 1

            # Provide regular console updates without overwhelming output
            if ($processedDevices % $updateFrequency -eq 0 -or $processedDevices -eq 1 -or $processedDevices -eq $totalDevices) {
                Write-Host "[PROGRESS] Processing device $processedDevices of $totalDevices ($percentComplete% complete)" -ForegroundColor Cyan
            }

            Write-Verbose "Processing device: $($device.DeviceName) (ID: $($device.Id))"

            # Retrieve Device Name
            $deviceName = if ([string]::IsNullOrWhiteSpace($device.DeviceName)) { 'Unknown' } else { $device.DeviceName }

            # Retrieve Model
            $model = if ([string]::IsNullOrWhiteSpace($device.Model)) { 'Unknown' } else { $device.Model }

            # Retrieve Service Tag (Serial Number)
            $serviceTag = if ([string]::IsNullOrWhiteSpace($device.SerialNumber)) { 'Unknown' } else { $device.SerialNumber }

            # Calculate Device Enrollment Age
            $enrollmentAgeInDays = 'Unknown'
            if ($device.EnrolledDateTime) {
                try {
                    $enrolledDate = [datetimeoffset]::Parse($device.EnrolledDateTime, [System.Globalization.CultureInfo]::InvariantCulture)
                    $timespan = [datetimeoffset]::UtcNow - $enrolledDate
                    $enrollmentAgeInDays = [Math]::Floor($timespan.TotalDays)
                } catch {
                    Write-Warning "Could not parse EnrolledDateTime '$($device.EnrolledDateTime)' for device '$deviceName'. Setting age to 'Error Parsing Date'."
                    $enrollmentAgeInDays = 'Error Parsing Date'
                }
            } else {
                 Write-Verbose "Device '$deviceName' has no EnrolledDateTime."
                 $enrollmentAgeInDays = 'No Enrollment Date'
            }

            # Create custom object
            $deviceData = [PSCustomObject]@{
                DeviceName           = $deviceName
                Model                = $model
                ServiceTag           = $serviceTag
                EnrollmentAgeInDays  = $enrollmentAgeInDays
                RawEnrolledDateTime  = if ($device.EnrolledDateTime) { [datetimeoffset]::Parse($device.EnrolledDateTime, [System.Globalization.CultureInfo]::InvariantCulture).ToString('o') } else { $null } # Format as ISO 8601
            }
            $deviceReport.Add($deviceData)

            # Flush output buffer periodically to ensure progress is visible
            if ($processedDevices % 50 -eq 0) {
                [System.Console]::Out.Flush()
            }
        }
        Write-Progress -Activity "Processing Intune Devices" -Status "Completed" -Completed -Id 1
    } catch {
        Write-Progress -Activity "Processing Intune Devices" -Status "Error Occurred" -Completed -Id 1
        Write-Error "[FATAL] Failed to retrieve or process device data. Error: $($_.Exception.Message)"
        # Optionally, try to disconnect before exiting
        Disconnect-MgGraph -ErrorAction SilentlyContinue
        exit 1
    }

    # Export the data
    if ($deviceReport.Count -gt 0) {
        Write-Host "[INFO] Exporting $($deviceReport.Count) device records to CSV file: $ReportPath" -ForegroundColor Cyan -Force
        try {
            # Show progress during export for large datasets
            Write-Progress -Activity "Exporting Data to CSV" -Status "Writing data to $ReportPath" -PercentComplete 50 -Id 3
            $deviceReport | Export-Csv -Path $ReportPath -NoTypeInformation -Encoding UTF8 -ErrorAction Stop
            Write-Progress -Activity "Exporting Data to CSV" -Status "Complete" -Completed -Id 3

            Write-Host "[SUCCESS] Intune device report generated successfully!" -ForegroundColor Green -Force
            Write-Host "[INFO] Report saved to: $ReportPath" -Force
        } catch {
            Write-Progress -Activity "Exporting Data to CSV" -Status "Error" -Completed -Id 3
            Write-Error "[FATAL] Failed to export data to CSV '$ReportPath'. Error: $($_.Exception.Message)"
            Disconnect-MgGraph -ErrorAction SilentlyContinue
            exit 1
        }
    } else {
        Write-Warning "No device data was retrieved or processed to export. The CSV report will not be generated."
    }

    # Disconnect from Microsoft Graph
    Write-Host "[INFO] Disconnecting from Microsoft Graph." -ForegroundColor Cyan -Force
    Disconnect-MgGraph -ErrorAction SilentlyContinue
}
#endregion

# --- Script Main Execution Flow ---

# 1. Install Dependencies
Install-RequiredModules

# 2. Run the main report generation function
Write-Host "[INFO] Starting main report generation function..." -ForegroundColor Cyan -Force
try {
    # Pass calculated $OutputPath to the function
    Get-IntuneDeviceReport -ReportPath $OutputPath -Verbose:$VerbosePreference -ErrorAction Stop
} catch {
    # Catch any errors not handled within Get-IntuneDeviceReport itself
    Write-Error "[FATAL] An unexpected error occurred during script execution: $($_.Exception.Message)"
} finally {
    # Ensure disconnection, even if errors occurred
    if (Get-MgContext -ErrorAction SilentlyContinue) {
        Write-Verbose "Ensuring disconnection from Microsoft Graph in final cleanup."
        Write-Host "[INFO] Performing final check for Graph connection and disconnecting if needed." -Force
        Disconnect-MgGraph -ErrorAction SilentlyContinue
    }
}

Write-Host "[INFO] Script execution finished." -ForegroundColor Cyan -Force