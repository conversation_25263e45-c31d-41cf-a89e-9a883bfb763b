# Function to retrieve registry values safely
function Get-RegistryValue {
    param (
        [string]$Path,
        [string]$Name
    )
    try {
        return (Get-ItemProperty -Path $Path -Name $Name -ErrorAction SilentlyContinue).$Name
    } catch {
        return $null
    }
}

# Expected SecureProtocols value (TLS 1.2 + TLS 1.3)
$ExpectedProtocols = 0x00020000 -bor 0x00800000

# Check SCHANNEL Protocol Settings
$TLS10 = Get-RegistryValue -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.0\Client" -Name "Enabled"
$TLS11 = Get-RegistryValue -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.1\Client" -Name "Enabled"
$TLS12 = Get-RegistryValue -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" -Name "Enabled"
$TLS13 = Get-RegistryValue -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Client" -Name "Enabled"

# Check SecureProtocols (Internet Options UI)
$TLSUser = Get-RegistryValue -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols"
$TLSMachine = Get-RegistryValue -Path "HKLM:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols"

# Check Group Policy Enforcement
$GPOCheck = Get-RegistryValue -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\CurrentVersion\Internet Settings" -Name "SecureProtocols"

# Check .NET Framework TLS enforcement
$NetTls1 = Get-RegistryValue -Path "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" -Name "SystemDefaultTlsVersions"
$NetTls2 = Get-RegistryValue -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\.NETFramework\v4.0.30319" -Name "SystemDefaultTlsVersions"
$NetCrypto1 = Get-RegistryValue -Path "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" -Name "SchUseStrongCrypto"
$NetCrypto2 = Get-RegistryValue -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\.NETFramework\v4.0.30319" -Name "SchUseStrongCrypto"

# If TLS 1.0 or 1.1 is enabled, or TLS 1.2 / 1.3 is missing, exit with failure
if (($null -ne $TLS10 -and $TLS10 -ne 0) -or
    ($null -ne $TLS11 -and $TLS11 -ne 0) -or
    ($TLS12 -ne 1) -or
    ($TLS13 -ne 1) -or
    ($TLSUser -ne $ExpectedProtocols) -or
    ($TLSMachine -ne $ExpectedProtocols) -or
    ($GPOCheck -ne $ExpectedProtocols) -or
    ($NetTls1 -ne 1) -or
    ($NetTls2 -ne 1) -or
    ($NetCrypto1 -ne 1) -or
    ($NetCrypto2 -ne 1)) {
    Exit 1  # Failure: TLS settings are incorrect
} else {
    Exit 0  # Success: Only TLS 1.2 and TLS 1.3 are enabled
}
