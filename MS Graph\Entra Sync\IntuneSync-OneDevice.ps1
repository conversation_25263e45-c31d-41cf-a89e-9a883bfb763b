<#
.SYNOPSIS
   Forces a sync on specified Intune managed devices.

.DESCRIPTION
   This script connects to Microsoft Graph, prompts the user for device names, 
   locates the devices, and then triggers sync operations. It allows users to force syncs on multiple devices in one run.

.PARAMETER DeviceNames
   A comma-separated list of Intune managed device names to force a sync on.

.EXAMPLE
   .\Force-IntuneDeviceSync.ps1

.NOTES
   Author:  <PERSON><PERSON>
   Date:    2024-08-26

   Requires the following PowerShell modules:
   - Microsoft.Graph
   - Microsoft.Graph.DeviceManagement
   - Microsoft.Graph.DeviceManagement.Actions

   Ensure you have the necessary permissions in Intune to perform device sync operations.
#>

# Set policy for the current session
Set-ExecutionPolicy -ExecutionPolicy Unrestricted -Scope CurrentUser -Force

# Function to ensure the necessary modules are installed and imported
function Install-Dependencies {
    # Check if the main Microsoft.Graph module is already installed
    if (-not (Get-Module -ListAvailable Microsoft.Graph)) {
        # Install the main Microsoft.Graph module
        Install-Module Microsoft.Graph
    }

    # Get all available Microsoft.Graph.* modules (excluding the already installed ones)
    $GraphModules = Get-Module -ListAvailable Microsoft.Graph.* | Where-Object { $_.Name -ne "Microsoft.Graph" }

    # Install each module (if not already installed)
    foreach ($Module in $GraphModules) {
        if (-not (Get-Module -ListAvailable $Module.Name)) {
            Write-Host "Installing Module: $($Module.Name) $($Module.Version)"
            Install-Module $Module.Name -RequiredVersion $Module.Version
        }
    }
}

# Function to connect to Microsoft Graph with the required scopes
function Connect-ToGraph {
    try {
        Connect-MgGraph -scope "DeviceManagementManagedDevices.PrivilegedOperations.All, DeviceManagementManagedDevices.ReadWrite.All, DeviceManagementManagedDevices.Read.All" -NoWelcome
        Write-Host "Successfully connected to Microsoft Graph." -ForegroundColor Green
    } catch {
        Write-Host "Error connecting to Microsoft Graph: $_" -ForegroundColor Red
        exit
    }
}

# Function to find a device by name and force a sync
function ForceSync {
    param (
        [string]$DeviceName
    )

    try {
        # Use the contains filter for partial matching
        $filter = "contains(deviceName,'$DeviceName')"
        $devices = Get-MgDeviceManagementManagedDevice -Filter $filter

        if ($devices.Count -eq 0) {
            Write-Host "No device found with the name '$DeviceName'." -ForegroundColor Yellow
        }
        elseif ($devices.Count -eq 1) {
            # Only one device found, proceed to sync
            $device = $devices[0]

            #Write-Host "Debugging: $device properties"
            $device | Format-List * 

            $deviceId = $devices.Id

            if ($deviceId) {
                Sync-MgDeviceManagementManagedDevice -ManagedDeviceId $deviceId 
                Write-Host "Successfully initiated sync for $($DeviceName)" -ForegroundColor Green
                Get-MgDeviceManagementManagedDevice | Where-Object {$_.devicename -eq "$DeviceName"} | Format-List Lastsyncdatetime
            } else {
                Write-Host "Error: Could not retrieve device ID for $($device.deviceName)" -ForegroundColor Red
            }
        }
        else {
            # Multiple devices found, prompt user to select
            Write-Host "Multiple devices found with similar names:" -ForegroundColor Yellow
            $devices | Format-Table DeviceName, Id -AutoSize

            $selectedDeviceName = Read-Host "Please enter the exact device name you want to sync"
            $selectedDevice = $devices | Where-Object { $_.DeviceName -eq $selectedDeviceName }
            #write-host  "THIS IS DEBUG VAR"

            if ($selectedDevice) {
                Sync-MgDeviceManagementManagedDevice -ManagedDeviceId $selectedDevice.id
                Write-Host "Successfully initiated sync for $($selectedDevice.DeviceName)" -ForegroundColor Green
                Get-MgDeviceManagementManagedDevice | Where-Object {$_.devicename -eq "$selectedDeviceName"} | Format-List Lastsyncdatetime
            } else {
                Write-Host "No device found with the exact name '$selectedDeviceName'." -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "Error forcing sync for device ${DeviceName}: $($_.Exception.Message)" -ForegroundColor Red
    }
}


# Main script logic
Install-Dependencies
Connect-ToGraph

while ($true) {
    # Get user input for device names
    $deviceInput = Read-Host "Please enter the device name(s) to force a sync, separated by commas, example: C-TE-SSHEPPARD3"

    # Validate and process user input
    if ([string]::IsNullOrWhiteSpace($deviceInput)) {
        Write-Host "Device name cannot be empty. Please try again." -ForegroundColor Red
        continue
    }

    $deviceNames = $deviceInput -split ','

    foreach ($deviceName in $deviceNames) {
        $trimmedDeviceName = $deviceName.Trim()
        if ($trimmedDeviceName) {
            ForceSync -DeviceName $trimmedDeviceName
        } else {
            Write-Host "Invalid device name: '$deviceName'. Please enter valid device names." -ForegroundColor Red
        }
    }

    $userInput = Read-Host "Do you want to force a sync on another device? (yes/no)"
    if ($userInput -ne "yes") {
        break
    }
}

Write-Host "Script execution completed." -ForegroundColor Green
