﻿"First Name","Last Name","Email Address","Department","Title","Extension attribute 10","Extension attribute 11","Description","Account Created on","Last Log-on Date","Full User Logon","Unique Account ID","User Logon"
,,"<EMAIL>",,"Service Account",,,"PLEASE INFORM HELPDESK IF YOU ACTIVATE THIS ACCOUNTService Account: Corp IT - Built-in account for administering the computer/domain/enterprise","9/10/2001 3:38:27 PM","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"Domain Admin -","<PERSON>","<EMAIL>",,,,,,"3/8/2017 11:41:26 AM","12/30/2022 1:21:19 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22274","da-amcfadden"
"Domain Admin -","<PERSON>","<EMAIL>",,,,,,"5/30/2018 2:23:28 PM","1/31/2023 2:03:27 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,"2/11/2019 2:40:19 PM","3/27/2023 7:52:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24060","da-smay"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,"4/26/2019 11:20:55 AM","3/21/2023 1:00:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,"6/27/2013 3:07:02 PM","1/21/2020 3:05:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19009","da-nated"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,"6/27/2013 3:08:33 PM","4/16/2021 8:46:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,"5/16/2019 2:51:57 PM","3/22/2023 8:29:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,"12/19/2018 2:57:51 PM","2/27/2023 11:16:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,"6/27/2012 1:46:05 PM","3/8/2023 12:04:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,"8/2/2016 10:33:47 AM","2/10/2023 2:45:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22187","da-sbradford"
"da-","mhoppe","<EMAIL>",,,,,,"8/25/2016 2:13:41 PM","12/21/2021 1:48:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23206","da-mhoppe"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,"11/13/2019 10:52:32 AM","8/23/2022 11:35:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25149","da-tdrescher"
"Domain Admin","Craig Hounsham","<EMAIL>",,,,,,"12/6/2019 8:22:00 AM","3/24/2023 6:38:34 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24706","da-chounsham"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,"3/11/2020 5:40:42 AM","9/13/2021 12:09:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26188","da-mkandy"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,"6/2/2020 8:08:38 AM","12/16/2020 11:48:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24838","da-balbrecht"
"Paul","Farry",,,,,,,"12/10/2021 8:07:13 AM","3/29/2023 11:30:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26431","da-pfarry"
"Domain Admin - Sudeep","Mool",,,,,,,"4/13/2022 3:30:37 PM","11/17/2022 2:51:20 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26892","da-smool"
"Domain Admin","Justin Germain",,,,,,,"5/6/2022 11:13:13 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26481","da-jgermain"
"svc-adbackups","account AD backups",,,,,,,"3/9/2023 10:25:44 AM","3/28/2023 10:00:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26983","svc-adbackups"
"Greg","Harshenin","<EMAIL>",,,,,,"2/19/2007 10:56:21 AM","3/28/2023 5:05:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12624","da-gharshenin"
"O","Perator","<EMAIL>",,"Service Account",,,"Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","9/18/2001 8:56:28 AM","1/18/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1161","operator"
"Malcolm","Kennedy",,,,,,,"7/18/2019 3:33:50 PM","9/28/2020 11:52:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24632","da-mkennedy"
"Domain Admin","Nyel English","<EMAIL>",,,,,,"12/10/2018 7:50:15 AM","3/29/2023 11:52:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,"1/14/2019 11:33:33 AM","3/2/2023 10:33:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,"12/11/2017 10:37:39 AM","1/24/2023 2:52:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,"2/27/2017 8:37:17 AM","1/26/2023 9:29:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,"2/9/2010 6:16:34 PM","3/2/2021 2:53:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15393","da-amehl"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,"12/18/2019 11:13:45 AM","2/15/2023 5:05:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24717","da-jbell"
"Jarrid","Pond",,,,,,,"4/19/2021 10:46:03 AM","3/29/2023 9:05:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26768","da-jpond"
"Domain Admin","Dilcia Torres","<EMAIL>",,,,,,"6/14/2021 12:38:47 PM","3/29/2023 11:36:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26392","da-dtorres"
,,"<EMAIL>",,"Service Account",,,"PLEASE INFORM HELPDESK IF YOU ACTIVATE THIS ACCOUNTService Account: Corp IT - Built-in account for administering the computer/domain/enterprise","9/10/2001 3:38:27 PM","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
,,"<EMAIL>",,"Service Account",,,"PLEASE INFORM HELPDESK IF YOU ACTIVATE THIS ACCOUNTService Account: Corp IT - Built-in account for administering the computer/domain/enterprise","9/10/2001 3:38:27 PM","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"MDT",,,,"Service Account",,,"Service Account: Windows deployment account","1/28/2010 1:57:19 PM","3/24/2023 10:01:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-13593","MDT"
"O","Perator","<EMAIL>",,"Service Account",,,"Service Account: Handles QTFin1 Reboot Email and QTAdmin1 password expiration email send","9/18/2001 8:56:28 AM","1/18/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1161","operator"
"MitelRemote",,,,"Service Account","Nick Janzen","Nick Janzen","External Contractor User Account Alan Marjoribanks CTS","9/23/2013 10:58:23 AM","3/29/2021 8:17:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20129","mitelremote"
"Gluu","LDAP Sync",,,"Service Account",,,"Service Account: Development - Gluu Test Account","2/15/2017 2:44:46 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-22628","gluusync"
"JIRA","LDAP SYNC",,,"Service Account",,,"Service Account: Corp IT - Jira and Confluence Service Account","2/8/2017 9:09:06 AM","11/8/2021 12:29:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22624","jirasync"
"QHR Infrastructure","Operator",,,"Service Account",,,"Service Account: Corp IT - Used by WSUS reporting on qhrops","11/28/2012 11:49:43 AM","1/27/2020 4:29:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17511","qtitoperator"
"OneLoginADC",,,,"Service Account",,,"Service Account: Corp IT -  this service account was created for the OneLogin ADC","5/11/2018 1:00:15 PM","12/15/2020 8:56:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22793","OneLoginADC"
"QTADMIN1 service",,,,"Service Account",,,"Service Account: Corp IT - Account used to run scheduled scripts on qtadmin1; Admin monitoring scripts","2/22/2018 3:25:46 PM","3/29/2023 8:00:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22370","qtadmin1service"
"Optimed","Sales","<EMAIL>","Sales","Service Account",,,"Service Account:Email Account for Optimed Software Sales","6/28/2004 7:59:31 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-1456","OPT_Sales"
"Opt","Development",,,"Service Account",,,"Service Account: Development - SQL Server Account","6/3/2004 3:29:25 PM","9/21/2021 9:10:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1426","opt_development"
"QHR Technologies","Support","<EMAIL>","Client Services","Service Account",,,"Service Account: Corp IT - Email / GTA no screen saver / After hours BB Support","6/21/2004 4:05:50 PM","3/22/2023 3:10:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1454","OPT_Support"
"e","learning","<EMAIL>",,"Service Account",,,"Service Account:For learning and Development","7/23/2002 8:50:39 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1260","elearning"
"IT","Admin","<EMAIL>",,"Service Account",,,"Service Account:  Corp IT - Shared email inbox","2/21/2011 2:42:25 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-18118","itadmin"
"Products","EMR","<EMAIL>",,"Service Account",,,"shared email address monitored by the Product Operations Support Team (POST), for notifications from government bodies regarding lists that need to be regularly updated in Accuro.","6/13/2012 10:12:00 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-18339","Products"
"EMRcslt",,"<EMAIL>","Client Services","Service Account",,,"Client Services Leadership Team email box, for communication between CSLT with CS staff.","4/11/2012 9:18:32 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-17324","Optcslt"
"OptimedImpW",,"<EMAIL>","Implementations","Service Account",,,"Linked to an outlook calendar for implementer booking","6/6/2012 2:41:08 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-17018","OptimedImpW"
"Optimed","Development After Hours","<EMAIL>","Development","Service Account",,,"Service Account: This Account is for after hours support so if CS needs to get a hold of DEV that's what they use it for.","1/27/2012 9:52:00 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-18797","osdevah"
"Accounting",,"<EMAIL>",,"Service Account",,,"Service Account: Finance - 	User account with an active email address that was used on qtfin2 to send invoices to clients","1/25/2012 3:20:40 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18796","accounting"
"Optimed","Admin","<EMAIL>",,"Service Account",,,"Master inbox that is used for receiving sales e-fax and general sales related emails.","12/21/2010 9:38:43 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-14774","OSAdmin"
"EMR","Implementations","<EMAIL>","Implementations","Service Account",,,"Service Account: PM's check that inbox routinely for faxes and or emails from client","6/28/2004 1:57:32 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-1458","Implementations"
"EMR","Development Inbox","<EMAIL>","Development","Service Account",,,"Service Account: central developer account for app stores and alerts","3/10/2005 1:48:40 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-9650","optdev"
,,"<EMAIL>",,"Service Account",,,"Service Account: Corp IT -  Accounts used by Exchange for health checks ","9/8/2014 3:25:21 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20291","SM_aacdd242cde649b98"
"qtmitelccm",,"<EMAIL>",,"Service Account",,,"Service Account: Corp IT - Service account on qtmitelcsm, also has scripts running with those credentials; Mitel Services","6/18/2012 11:06:32 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17022","qtmitelccm"
,,"<EMAIL>",,"Service Account",,,"Service Account: Corp IT -  Accounts used by Exchange for health checks ","10/8/2014 3:14:51 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20315","SM_ea45985bae3e4d2f9"
,,,,"Service Account",,,"Service Account: Corp IT - Account created by the Windows Azure Active Directory Sync tool with installation identifier '0e6855de790d47ebaefa653faf59f481' running on computer 'QTADFS2' configured to synchronize to tenant 'qhrtech.onmicrosoft.com'. This account must have directory replication permissions in the local Active Directory and write permission on certain attributes to enable Hybrid Deployment.","1/22/2015 10:23:05 AM","3/29/2023 2:29:48 AM",,"S-1-5-21-**********-**********-*********-20390","MSOL_0e6855de790d"
"bomgarIC",,,,"Service Account",,,"Service Account: Corp IT - User for Bomgar Integration Client Scheduler service running on QTAdmin1","1/27/2016 1:55:58 PM","9/30/2022 2:51:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23635","bomgarIC"
"QHR backup","smtp","<EMAIL>",,"Service Account",,,"Service Account: Tech Infrastructure - Backup notifications to C. Hounsham (SF Case ********)","11/8/2016 1:29:36 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-23228","qhrbackupsmtp"
"QHR Technologies IT",,"<EMAIL>",,"Service Account",,,"Service Account: Corp IT - Contact for IT assistance","5/30/2008 11:04:24 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-13309","IT"
"OXIDIZEDSA",,,,"Service Account",,,"Service Account: Networking backups and scripts","2/5/2019 4:22:57 PM","1/14/2022 3:10:15 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23534","OXIDIZEDSA"
"Trevor","Trainee","<EMAIL>",,"Service Account",,,"Service Account:  HR - Used for Training Video/Document","9/18/2019 8:37:29 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-24176","trevor.trainee"
"SQL-osqasql1",,,,"Service Account",,,"Service Account: QA - SQL Service Account","7/12/2011 2:42:24 PM","3/23/2021 5:01:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18681","sql-osqasql1"
"VMware Reporting","Service Account",,,"Service Account",,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","6/21/2017 9:18:21 AM","3/29/2023 9:42:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23835","vmwarereport"
,,,,"Service Account",,,"Service Account: Tech Infrastructure - Backup account for Asigra","4/18/2017 3:50:09 PM","12/17/2020 1:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23290","asigra-vmware"
"econnect",,,,"Service Account",,,"Service Account: Finance - for Dynamics running on qtfin3","12/8/2015 2:53:09 PM","1/15/2023 2:38:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23165","econnect"
"ActionTec",,,,"Service Account",,,"Service Account: User required for wireless connection of ActionTec devices to ""Quadrant Network""","11/15/2018 1:09:06 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-22489","actiontec"
"jenkins",,,,"Service Account",,,"Service Account: Development/QA - QA Test Account","7/25/2014 2:46:48 PM","3/21/2021 4:59:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19879","jenkins"
"SQL-OSDEVSQL2012",,,,"Service Account",,,"Service Account: Development - SQL Service Account; used to run OSDEVSQL2012\OSDEVSQL2012
OSDEVSQL2012\MSSQL2014
and OSDEVSQL2012\MSSQL2016
MSSQL instances.","2/22/2017 9:11:02 AM","8/10/2022 8:26:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22629","SQL-OSDEVSQL2012"
"Mimecast","LDAPS Sync",,,"Service Account",,,"Service Account: Security - Secure LDAP account for Mimecast","3/27/2014 2:09:06 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-19814","mimecastsync"
"Splunk","Alerts",,,"Service Account",,,"Service Account: Pprovide Alerts generated from Splunk service.","3/20/2019 4:16:50 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-22911","splunk.alerts"
"IT User",,,,"Service Account",,,"Nyel confirmed to Disable this on July 17 2020","5/29/2012 10:17:23 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-17367","ITUser"
"GPeconnect",,,,"Service Account",,,"Service Account: Finance - Dynamics service account used on qtfin3","7/11/2017 11:21:49 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-23840","GPeconnect"
"PFsense","Service Account",,,"Service Account",,,"Service Account: ","1/4/2011 2:15:10 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-17144","pfsense"
"SQL-QTVersionOne",,,,"Service Account",,,"Service Account: Development - SQL Server account for VersionOne. Legacy system - replaced by JIRA - still referred to.","5/14/2013 5:41:45 PM","3/4/2021 3:03:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19680","sql-qtversionone"
"sftp","service",,,"Service Account",,,"Service Account: Corp IT  - Used to mount shares on qtsftp1. Also used by newer Cerberus SFTP service (on QTSFTP1982 that never made it to production) ","6/13/2014 12:15:40 PM","4/1/2021 3:01:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20262","sftp"
"KonicaMinolta",,"<EMAIL>",,"Service Account",,,"Service Account: LDAP & Email (for scans)","4/5/2019 3:33:02 PM","6/30/2020 9:42:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24092","konicaminolta"
"rhel-virt-who",,,,"Service Account",,,"Service Account: RHEL Services Account","6/19/2019 12:53:18 PM","3/29/2023 9:01:32 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22598","rhel-virt-who"
"Commvault","vCenter User - cvvcuser",,,"Service Account",,,"Service Account: Commvault vCenter User","3/13/2019 12:40:22 PM","3/29/2023 9:03:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22544","cvvcuser"
"JiraConfshare",,,,"Service Account",,,"Service Account: Corp IT - Sync account between JIRA servers","6/11/2019 8:46:25 AM","3/29/2023 3:17:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22590","JiraConfshare"
"Kace","User",,,"Service Account",,,"Service Account: Security - Read only user for LDAP imports (do NOT delete)","3/11/2011 8:17:14 AM","2/27/2023 1:56:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16673","KACE_User"
"SQL-osdevsql01",,,,"Service Account",,,"Service Account: Data - SQL service acct. for Lisa St Laurent - DMD. 4 SQL instances. Server (old OS) should be replaced/deprecated.","4/9/2010 3:48:56 PM","8/7/2020 9:51:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-14680","SQL-osdevsql01"
"SQL-EMRDATASQL01",,,,"Service Account",,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2008R2
EMRDATASQL01\SQL2012
and EMRDATASQL01\SQL2017 MSSQL instances.","2/22/2017 9:12:55 AM","3/20/2023 1:08:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22632","SQL-EMRDATASQL01"
"Dynamics MR","Service",,,"Service Account",,,"Service Account: add-on to GP that provides/creates financial reports","5/8/2015 12:33:10 PM","10/27/2022 3:31:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20096","mrsvc"
,,,,"Service Account",,,"Service Account: Tech Infrastructure - Service account for VMWare","11/21/2019 12:43:54 PM","3/29/2023 7:01:32 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25154","vcenter-skyline"
,,,,"Service Account",,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","11/22/2019 12:25:58 PM","3/29/2023 9:39:32 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23012","vcenter-ro"
,,,,"Service Account",,,"Service Account: AD account that it connects to its database with on infrasql where specific permissions are configured","3/17/2020 10:14:29 AM","3/29/2023 1:17:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26194","solarwindsdbservice"
"KantechEmail","Weekly Report",,,"Service Account",,,,"4/1/2020 1:43:49 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26612","kantechemail"
"CS","Training",,"CS","Service Account",,,"Client Services, used for booking training for employees.","4/22/2020 9:32:33 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26631","cs.training"
"Intune","NDES",,,"Service Account",,,"Service Account : Corp IT-NDES to enroll certificates from Certificate Authority to AADJ Devices","4/29/2020 11:15:32 AM","10/17/2021 7:24:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24813","IntuneNDES"
"ca","secscan",,,"Service Account",,,"Service Account: Security - This AD Account was created in response to salesforce ticket 01309659 to access QHR servers and report back to the CIS platform dashboard. This account ensure servers are configured Securely.","7/7/2020 2:21:33 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26674","ca_secscan"
"OME","User",,,"Service Account",,,"Service Account: Corp IT - Windows account to access SMB Share on Dell-DRM","8/13/2020 3:02:05 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26682","omeuser"
"SQL-EMRDATASQL02",,,,"Service Account",,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2019 MSSQL instance.","9/9/2020 12:22:48 PM","8/10/2022 9:04:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24900","SQL-EMRDATASQL02"
"OXIDIZEDSABASH",,,,"Service Account",,,,"1/5/2021 11:39:11 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26745","OXIDIZEDSABASH"
"Service-Now","Blob-Reader",,,"Service Account",,,"Service Account: Corp IT - Used for Azure blob storage and Service-Now Prod impersonation connection","2/5/2021 12:49:32 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26331","servicenow.blob"
"sa-paloalto","userid",,,"Service Account",,,"Service Account: Networking - used by corp palo alto firewalls to perform user-id lookups","2/16/2021 1:56:14 PM","1/15/2022 4:24:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26753","sa-paloalto-userid"
"DGPSQLServiceUser",,,,"Service Account",,,"Ran on QTFINSQL for sql service","10/4/2022 9:21:00 AM","3/23/2023 3:24:22 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26499","DGPSQLServiceUser"
"DGPeConnectService",,,,"Service Account",,,"Econnect service on QTFINSQL and QTFINRDS","10/4/2022 9:25:15 AM","3/27/2023 3:46:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26500","DGPeConnectService"
"jira-","serviceaccount",,,"Service Account",,,,"1/10/2023 11:29:44 AM","3/29/2023 2:56:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26970","jira-serviceaccount"
"Greg","Harshenin","<EMAIL>",,,,,,"2/19/2007 10:56:21 AM","3/28/2023 5:05:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12624","da-gharshenin"
"Malcolm","Kennedy",,,,,,,"7/18/2019 3:33:50 PM","9/28/2020 11:52:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24632","da-mkennedy"
"Domain Admin","Nyel English","<EMAIL>",,,,,,"12/10/2018 7:50:15 AM","3/29/2023 11:52:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,"1/14/2019 11:33:33 AM","3/2/2023 10:33:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,"12/11/2017 10:37:39 AM","1/24/2023 2:52:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,"2/27/2017 8:37:17 AM","1/26/2023 9:29:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,"2/9/2010 6:16:34 PM","3/2/2021 2:53:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15393","da-amehl"
"Domain Admin -","Andrew Stavert","<EMAIL>",,,,,,"3/20/2017 9:14:43 AM","11/25/2021 5:49:46 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22643","da-astavert"
"Domain Admin -","Andrew McFadden","<EMAIL>",,,,,,"3/8/2017 11:41:26 AM","12/30/2022 1:21:19 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22274","da-amcfadden"
"Domain Admin -","Kevin Kendall","<EMAIL>",,,,,,"5/30/2018 2:23:28 PM","1/31/2023 2:03:27 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,"2/11/2019 2:40:19 PM","3/27/2023 7:52:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24060","da-smay"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,"4/26/2019 11:20:55 AM","3/21/2023 1:00:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,"6/27/2013 3:07:02 PM","1/21/2020 3:05:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19009","da-nated"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,"6/27/2013 3:08:33 PM","4/16/2021 8:46:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,"5/16/2019 2:51:57 PM","3/22/2023 8:29:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,"12/19/2018 2:57:51 PM","2/27/2023 11:16:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,"6/27/2012 1:46:05 PM","3/8/2023 12:04:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,"8/2/2016 10:33:47 AM","2/10/2023 2:45:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22187","da-sbradford"
"da-","mhoppe","<EMAIL>",,,,,,"8/25/2016 2:13:41 PM","12/21/2021 1:48:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23206","da-mhoppe"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,"11/13/2019 10:52:32 AM","8/23/2022 11:35:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25149","da-tdrescher"
"Domain Admin","Craig Hounsham","<EMAIL>",,,,,,"12/6/2019 8:22:00 AM","3/24/2023 6:38:34 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24706","da-chounsham"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,"12/18/2019 11:13:45 AM","2/15/2023 5:05:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24717","da-jbell"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,"3/11/2020 5:40:42 AM","9/13/2021 12:09:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26188","da-mkandy"
"Arnold","Nzailu","<EMAIL>",,,,,,"3/30/2020 9:28:23 AM","4/11/2022 11:24:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26610","da-anzailu"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,"6/2/2020 8:08:38 AM","12/16/2020 11:48:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24838","da-balbrecht"
"Domain Admin -","Simon Ummard","<EMAIL>",,,,,,"9/11/2020 2:41:50 PM","11/23/2021 11:18:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24911","da-summard"
"Jarrid","Pond",,,,,,,"4/19/2021 10:46:03 AM","3/29/2023 9:05:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26768","da-jpond"
"Nirmol","Bajwa",,,,,,,"4/29/2021 7:30:19 AM","11/25/2021 2:42:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26775","da-nbajwa"
"Jordan","Pinske",,,,,,,"4/29/2021 7:32:07 AM","3/16/2023 12:36:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26776","da-jpinske"
"Tayo","Aruleba",,,,,,,"5/10/2021 12:19:55 PM","3/15/2023 4:02:42 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26381","DA-TAruleba"
"Jeff","Fleming",,,,,,,"6/14/2021 5:41:05 AM","3/23/2023 11:58:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26391","da-jfleming"
"Domain Admin","Dilcia Torres","<EMAIL>",,,,,,"6/14/2021 12:38:47 PM","3/29/2023 11:36:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26392","da-dtorres"
"Domain Admin","Fred Xiao",,,,,,,"9/28/2021 11:32:34 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26411","da-fxiao"
"Paul","Farry",,,,,,,"12/10/2021 8:07:13 AM","3/29/2023 11:30:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26431","da-pfarry"
"Chrisaine","Brown-Humphrey",,,,,,,"1/28/2022 9:29:21 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26873","da-cbrown-humphrey"
"Domain Admin - Sudeep","Mool",,,,,,,"4/13/2022 3:30:37 PM","11/17/2022 2:51:20 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26892","da-smool"
"Domain Admin -","Kent Ellis",,,,,,,"5/3/2022 4:03:10 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26480","da-kellens"
"Domain Admin","Justin Germain",,,,,,,"5/6/2022 11:13:13 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26481","da-jgermain"
"Domain Admin -","Muhammad Ali","<EMAIL>",,,,,,"5/16/2022 8:17:54 AM","5/16/2022 1:46:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26483","da-mali"
"Nathan","Taylor",,,,,,,"8/23/2022 6:46:59 AM","3/24/2023 9:46:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26929","da-ntaylor"
"Domain Admin","Rushik Panchal",,,,,,,"12/8/2022 1:56:52 PM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26967","da-rpanchal"
"Domain Admin","Brad Keefe","<EMAIL>",,,,,,"1/4/2023 2:54:24 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26532","da-bkeefe"
"Domain Admin","Chandra Potharaju","<EMAIL>",,,,,,"1/4/2023 3:16:28 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26533","cpotharaju"
"Test","Passwriteback","<EMAIL>",,,,,"For Password Writeback and MDM testing Azure","11/22/2019 1:07:16 PM","9/15/2020 8:38:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24700","Testpw"
"Test","Phoneforward",,,,,,"Service Account: Corp IT - Testing Teams Phone Forwarding","7/29/2021 9:16:15 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26793","test.phoneforward"
"Test","User",,"Client Services","Client Services Analyst",,,,"11/22/2022 4:02:57 PM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26523","test.user"
"Dynamics MR","Service",,,"Service Account",,,"Service Account: add-on to GP that provides/creates financial reports","5/8/2015 12:33:10 PM","10/27/2022 3:31:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20096","mrsvc"
"svc","Flexapp",,,,,,"Service Account:Do not change - service account for Flexapp","8/17/2020 10:33:18 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-24884","svc-flexapp"
"SVC","Jenkins",,,,,,"DO not change - Jenkins slave account for Flexapp","8/17/2020 1:07:10 PM","1/16/2023 3:48:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24885","svc-jenkins"
"CommVault","Backup Admin",,,,,,,"12/18/2020 7:47:26 AM","1/24/2023 8:39:33 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26725","svc-commvault"
"MDI","sensor",,,,,,"Service Account: Security - Microsoft Defender for Identity Sensor for QTDC01/02 and QTADFS2","4/14/2021 11:46:11 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26767","svc-mdisensor"
"Splunk","Sync",,,,,,"Service Account: Security - Used for Splunk Syncing","10/27/2021 10:29:07 AM",,"<EMAIL>","S-1-5-21-**********-**********-*********-26840","svc_splunksync"
"svc-sqlbackups",,,,,,,,"2/17/2022 10:19:43 AM","3/29/2023 2:30:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26449","svc-sqlbackups"
"svc-vmbackups",,,,,,,,"3/22/2022 8:25:10 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26454","svc-vmbackups"
"svc-mstrueup",,,,,,,,"3/29/2022 10:18:27 AM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26456","svc-mstrueup"
"svc-adbackups","account AD backups",,,,,,,"3/9/2023 10:25:44 AM","3/28/2023 10:00:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26983","svc-adbackups"
