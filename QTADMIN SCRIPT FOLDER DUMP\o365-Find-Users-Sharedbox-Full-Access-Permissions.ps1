﻿Try {
  $connected = Get-mailbox <EMAIL>
  Write-Host "Already Connected to SharePoint Online!`n`n" -ForegroundColor Green
}
Catch {
  $LiveCred = Get-Credential

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
 }

cls
$mail = get-mailbox -Filter '(RecipientTypeDetails -eq "shared")'
$mailboxes = $mail.name
$user = Read-Host "Enter username to check, example first.last "
$user = "$<EMAIL>"

#$mailboxes = Get-MailboxPermission -Identity *
foreach ($mailbox in $mailboxes)
    {
    Get-MailboxPermission -Identity $mailbox -user "$user" 
    }