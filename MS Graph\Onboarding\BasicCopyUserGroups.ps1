# Install the Microsoft Graph module if not installed
if (-not (Get-Module -ListAvailable -Name Microsoft.Graph)) {
    Install-Module Microsoft.Graph -Scope CurrentUser -Force
}
# Connect to Microsoft Graph
Connect-MgGraph -Scopes "Group.ReadWrite.All", "User.Read.All"
# Define the users and groups
# These should be either Object IDs or UPNs (for users) and Group IDs (for groups)
# Example: Array of user UPNs (email addresses)
$users = @(
    "<EMAIL>"
)
# Example: Array of Group Object IDs
$groups = @(
    "51c0fafe-e08f-4be8-bfaf-1ff26bef24f3",     #AIP-Client-Services
    "0851679f-5a5a-4075-859b-8cdbe8e42b53",     #azure-adp
    "fd6a4bd7-55cc-42dd-af45-63963dde00c1",     #BeyondTrust - Client Services - SSO
    "9e4ce4a5-35c0-44cf-b3ee-ac8d6ebbe02a",     #CloudSync Admin Tool
    "d77e987e-bed3-45e5-b3dc-8b1dc5eb0e24",     #Conditional Access: Risky sign-in multifactor authentication (a4ea6c0f-b8fb-4d29-91f1-9f8cf0601e98)
    "cf38bb07-d8e3-4397-b731-63ba900d5e88",     #CXONE - Screen Agent Pilot
    "512c83cd-5ca2-4cfa-8b81-76da8fcdb09d",     #CXONE Screen Recording
    "42f37f68-b870-43ff-a6e2-2aced4c85c7e",     #MDM Enroll
    "24581a52-eb5f-4066-b46e-89d87a79e890",     #MDM Users
    "21f0d0ca-8953-4c1c-b526-c44ebdc3535c",     #Okta p - Accuro Cloud Users
    "f16482d8-b0fa-4ae9-bab6-d2947e71daee",     #Okta p - Central Admin Users
    "ea15c460-0f89-4c23-9616-caae7c924403",     #Okta p - Help Desk Administrators
    "c9016fbd-aa49-42c3-b3a1-60a1bcf70e50",     #Okta p - Medeo Admin Tools
    "ad8d08d9-1f2d-4f10-88c3-41e218e5fab9",     #Salesforce-Client Services
    "b2e54ed5-3cbe-46dd-bad3-5eb601a55b0f",     #Sendgrid - SSO
    "01297227-9ef9-440a-966c-65ff75482077",     #tm-CS inbound
    "a9682169-3b89-4428-b7dc-d9494632f1ab",     #Zscaler-Client-Services
    "8a40accc-2c0a-4b66-b681-d3b63675fe2c"      #ZSCALER-USER
)
# Loop through each group
foreach ($group in $groups) {
    foreach ($user in $users) {
        try {
            # Get the user's object
            $userObject = Get-MgUser -UserId $user
            # Check if the user is already a member
            $isMember = Get-MgGroupMember -GroupId $group -All | Where-Object { $_.Id -eq $userObject.Id }
            if (-not $isMember) {
                # Add user to group
                New-MgGroupMember -GroupId $group -DirectoryObjectId $userObject.Id
                Write-Output "Added $($user) to Group $($group)"
            }
            else {
                Write-Output "User $($user) is already a member of Group $($group)"
            }
        }
        catch {
            Write-Warning "Failed processing $($user) for Group $($group): $_"
        }
    }
}
# Disconnect when done
Disconnect-MgGraph