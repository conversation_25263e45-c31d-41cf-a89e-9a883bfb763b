﻿Import-Module ActiveDirectory

$missingDescriptions = @()
$DescriptionsNotUpdated = @()
$oldcomputers = (Get-Date).AddDays(-30)


$computers = Get-ADComputer -Properties * -Filter{
    Enabled -eq $True -and
    name -notlike "emrqaslave*"-and
    name -notlike "qtfile*"-and
    name -like "emr*" -or
    name -like "qt*" 
    
} |where-object { ($_.DistinguishedName -like "*OU=Computers*") -and 
($_.DistinguishedName -notlike "*OU=Virtual Machines*") -and 
($_.DistinguishedName -notlike "*OU=Servers*") -and
($_.LastLogonDate -gt $oldcomputers)
} | foreach {$_.Name}

foreach ($computer in $computers) {

    
    
    $adcomputerDescription = Get-ADComputer $computer -Properties description | select Description | foreach {$_.Description}
    
    if ($adcomputerDescription -eq $null){
    
        $missingDescriptions += $computer
    }
    else {
        write-host "$computer already has AD description" -ForegroundColor Yellow
    }



}


foreach ($computerMissingDescription in $missingDescriptions) {

    #if (Test-Connection -Computername $computerMissingDescription -BufferSize 16 -Count 1 -Quiet) {

        #write-host "$computerMissingDescription Online" -ForegroundColor Green
        $localComputerDescription = "" # Clears Local Description so wrong description isn't applied if RPC server is unavailable

        try {
            $localComputerDescription = (Get-WMIObject Win32_OperatingSystem -ComputerName $computerMissingDescription -ErrorAction Stop).description

            if ($localComputerDescription -eq "") {
            
                write-host "$comptuerMissingDescription is ONLINE but local computer description is missing" -ForegroundColor Red
                $DescriptionsLog += "$comptuerMissingDescription is ONLINE but local computer description is missing"
            }

            else {
                    
                write-host "Updated $computerMissingDescription AD description too $localComputerDescription" -ForegroundColor Green
                Set-ADComputer $computerMissingDescription -Description $localComputerDescription

                $DescriptionsLog += "Updated $computerMissingDescription AD description too $localComputerDescription"
            }
        }
        catch {
            Write-Host "PC unavailable or RPC server unavailable for $computerMissingDescription, probably blocked firewall port" -ForegroundColor Red
            $DescriptionsLog += "PC unavailable or RPC server unavailable for $computerMissingDescription, probably blocked firewall port"
        }

    #}

    #else {
    #    write-host "$computerMissingDescription can't be reached" -ForegroundColor Red
    #    $DescriptionsLog += "$computerMissingDescription can't be reached on QHR network"
    #}
}

$DescriptionsLog | Out-File C:\temp\ADDescriptionsLog.txt
write-host "Output Log file created at C:\temp\ADDescriptionsLog.txt" -ForegroundColor Yellow