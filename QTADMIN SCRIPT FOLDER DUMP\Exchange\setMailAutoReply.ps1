﻿Connect-ExchangeOnline
$ident = Read-host "Enter email of mailbox"
$start = Read-host "Enter date to start [M/DD/YYYY HH:MM:SS]"
$end = Read-host "Enter date to end [M/DD/YYYY HH:MM:SS]"
$message = Read-host "Enter message"
Set-MailboxAutoReplyConfiguration -Identity $ident -AutoReplyState Scheduled -StartTime $start -EndTime $end -InternalMessage $message
Get-MailboxAutoReplyConfiguration -Identity $ident
Disconnect-ExchangeOnline

