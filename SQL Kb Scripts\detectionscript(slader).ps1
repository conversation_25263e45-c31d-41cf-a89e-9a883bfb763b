try {
    # Attempt to load the SQL Server SMO assembly
    $smoAssembly = [Reflection.Assembly]::LoadWithPartialName('Microsoft.SqlServer.Smo')

    # Check if the assembly was loaded successfully
    if ($smoAssembly -ne $null) {
        # Check if the loaded SMO version is equal to ********
        if ($smoAssembly.GetName().Version -eq '********') {
            #Write-Output "SMO version is equal to ********. Continuing with the script."

            # Connect to the local SQL Server instance
            $server = New-Object Microsoft.SqlServer.Management.Smo.Server("(local)")

            # Get the SQL Server version
            $version = $server.Information.VersionString
            #Write-Output "Installed SQL Server Version: $version"

            # Check if the version is 13.0.7029.3 or greater
            $requiredVersion = [Version]"13.0.7029.3"

            if ($version -ge $requiredVersion) {
                #Write-Output "SQL Server version $version is equal to or greater than $requiredVersion. Exiting with code 0."
                exit 0
            } else {
                #Write-Output "SQL Server version $version is lower than $requiredVersion. Exiting with code 1."
                exit 1
            }
        } else {
            #Write-Output "SMO version is not equal to ********. Exiting with code 0."
            exit 0
        }
    } else {
        #Write-Output "SMO assembly not found. Exiting with code 0."
        exit 0
    }
} catch {
    # Catch the error silently and exit with code 0
    exit 0
}
