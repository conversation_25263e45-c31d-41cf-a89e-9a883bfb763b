$userinput = Read-Host "what is the CN of the cert you are looking for?"
$items = Get-ChildItem -Recurse Cert: | Select-String $userinput 

if ($items) {
write-host $items
$userInput = Read-Host "Are you sure you want to remove all found items? (yes/no)"
if ($userInput -eq "yes") {
$items | Remove-Item
Write-Host "All found items have been removed."
} elseif ($userInput -eq "no") {
Write-Host "Exiting script without removing items."
} else {
Write-Host "Invalid input. Please enter either 'yes' or 'no'."
}
} else {
Write-Host "No items found."
}