$TaskName = "Winget-AutoUpdate"
$WAUInstallPath = "C:\ProgramData\Winget-AutoUpdate" 
$WAUExePath = "$WAUInstallPath\ServiceUI.exe"
$WAURegKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Winget-AutoUpdate"

# 1. Check for Registry Key
if (Test-Path $WAURegKey) {
    #"Winget-AutoUpdate (WAU) is installed (registry key found)."
    exit 0
}

# 2. Check for Scheduled Task
$TaskExists = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
if ($TaskExists) {
    #"Winget-AutoUpdate (WAU) is installed (task found)."
    exit 0
}

# 3. Check for WAU Executable
if (Test-Path $WAUExePath) {
    #"Winget-AutoUpdate (WAU) is installed (executable found)."
    exit 0
}

# No signs of WAU found, assume not installed
#"Winget-AutoUpdate (WAU) is not installed."
exit 1
