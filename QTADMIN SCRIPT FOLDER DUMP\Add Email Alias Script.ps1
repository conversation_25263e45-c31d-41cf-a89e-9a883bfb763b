Import-Module activedirectory

$Username = Read-Host -prompt "Enter Username, all lower case (eg. firstname.lastname)"
$user = Get-ADUser $Username

$SMTP0 = "@QHRtech.com"
$smtp1 = "@qhrtech.mail.onmicrosoft.com"
$smtp2 = ".<EMAIL>"
$smtp3 = ".<EMAIL>"
$smtp4 = ".<EMAIL>"
$smtp5 = ".<EMAIL>"
$smtp6 = ".<EMAIL>"
$smtp7 = "@medeoprovider2.com"
$smtp8 = "@medeoassistant.com"
$smtp9 = "@medeo.ca"
$smtp10 = "@medeohealth.com"
$smtp11 = "@QHRtechnologies.com"
$smtp12 = "@OptiMedSoftware.com"
$smtp13 = ".<EMAIL>"

$Smtp ='smtp:'
$SMTPPrimary ='SMTP:'

        $fname=$($user.givenname)
        $lname=$($user.surname)
	$proxyaddresses0=$SMTPPrimary + $fname + '.' +$lname + $SMTP0
	$proxyaddresses1=$Smtp + $fname + '.' +$lname + $smtp1
        $proxyaddresses2=$Smtp + $fname + '.' +$lname + $smtp2
        $proxyaddresses3=$Smtp + $fname + '.' +$lname + $smtp3
        $proxyaddresses4=$Smtp + $fname + '.' +$lname + $smtp4
	$proxyaddresses5=$Smtp + $fname + '.' +$lname + $smtp5
        $proxyaddresses6=$Smtp + $fname + '.' +$lname + $smtp6
        $proxyaddresses7=$Smtp + $fname + '.' +$lname + $smtp7
	$proxyaddresses8=$Smtp + $fname + '.' +$lname + $smtp8
        $proxyaddresses9=$Smtp + $fname + '.' +$lname + $smtp9
        $proxyaddresses10=$Smtp + $fname + '.' +$lname + $smtp10
	$proxyaddresses11=$Smtp + $fname + '.' +$lname + $smtp11
        $proxyaddresses12=$Smtp + $fname + '.' +$lname + $smtp12
        $proxyaddresses13=$Smtp + $fname + '.' + $lname + $smtp13
 	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses0}
       	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses1}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses2}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses3}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses4}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses5}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses6}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses7}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses8}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses9}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses10}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses11}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses12}
	Set-ADUser -Identity $user.samaccountname -add @{proxyaddresses = $proxyaddresses13}
   
   
   