<#
.SYNOPSIS
    Automates Dell Command | Update (DCU) processes followed by Windows Updates (excluding drivers).
.DESCRIPTION
    This script integrates DCU and Windows Update processes.
    1. Ensures DCU is installed and attempts to configure recommended settings.
    2. Runs DCU scan and applies available Dell updates (BIOS, Firmware, Applications, Drivers via DCU).
    3. Handles BIOS password prompts when needed for updates applied by DCU.
    4. Suspends BitLocker via DCU settings if applicable.
    5. Runs Windows Update using PSWindowsUpdate, EXCLUDING driver updates.
    6. Logs the entire process.
.NOTES
    Author: Slader Sheppard
    Version: 1.4
    Date: April 9, 2025

    Requirements:
    - PowerShell 5.1 or later.
    - Dell Command | Update (will be installed if missing).
    - PSWindowsUpdate module (will be installed if missing).
    - Run as Administrator.
    - expand.exe (Standard Windows utility) must be available in the system PATH.

    Changes in v1.4:
    - Changed expand.exe syntax in Get-DellBIOSUpdates to specify the output file path explicitly, matching known working examples.

    WARNING: Passing the BIOS password via the command line to dcu-cli.exe might expose it in process memory or logs. Use with caution.
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$LogDirectory = "C:\temp\LaptopUpdateLogs",

    [Parameter(Mandatory = $false)]
    [switch]$AutoRebootIfNeeded
)

#==============================================================================
# Initial Setup
#==============================================================================
Write-Host "Checking for Administrator privileges..." -ForegroundColor Cyan
if (-not ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Error "This script must be run as Administrator."
    Exit 1 # Use non-zero exit code for error
}
Write-Host "Administrator privileges confirmed." -ForegroundColor Green

# Create Log Directory if it doesn't exist
if (-not (Test-Path -Path $LogDirectory -PathType Container)) {
    Try {
        New-Item -Path $LogDirectory -ItemType Directory -Force -ErrorAction Stop | Out-Null
        Write-Host "Log directory created at $LogDirectory" -ForegroundColor Green
    } Catch {
        Write-Error "Failed to create log directory '$LogDirectory': $($_.Exception.Message)"
        Exit 1
    }
}

# Create Temp Directory for Downloads within Log Directory
$Global:DellTempDirectory = Join-Path $LogDirectory "DellTemp"
if (-not (Test-Path -Path $Global:DellTempDirectory -PathType Container)) {
    Try {
        New-Item -Path $Global:DellTempDirectory -ItemType Directory -Force -ErrorAction Stop | Out-Null
    } Catch {
        Write-Error "Failed to create temporary directory '$Global:DellTempDirectory': $($_.Exception.Message)"
        Exit 1
    }
}

# Start Transcription
$transcriptLog = Join-Path $LogDirectory "UpdateScript_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
Try {
    Start-Transcript -Path $transcriptLog -Append -ErrorAction Stop
} Catch {
    Write-Error "Failed to start transcript log at '$transcriptLog': $($_.Exception.Message)"
    Write-Warning "Transcript logging failed. Script will continue without detailed log file."
}

# Global variable for DCU Path
$Global:DCUCLIPath = $null

#==============================================================================
# Function Definitions
#==============================================================================

Function Get-DCUInstallDetails {
    # .SYNOPSIS Retrieves details about the installed Dell Command | Update (DCU) application.
    # .DESCRIPTION Checks registry for DCU installation details.
    # .OUTPUTS PSCustomObject with DCU installation details or $null if DCU is not installed.
    Write-Host "Checking for existing Dell Command | Update installation..." -ForegroundColor Cyan
    $uninstallKeys = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
    )
    $dcuProduct = $null

    foreach ($keyPath in $uninstallKeys) {
        if (Test-Path $keyPath) {
            # Added -ErrorAction SilentlyContinue for cases where keys exist but are inaccessible
            $dcuProduct = Get-ChildItem -Path $keyPath -ErrorAction SilentlyContinue | ForEach-Object { Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue } | Where-Object { $_.DisplayName -like "Dell Command | Update*" } | Select-Object -First 1
            if ($dcuProduct) { break }
        }
    }

    if ($dcuProduct) {
        Write-Host "Found DCU: $($dcuProduct.DisplayName), Version: $($dcuProduct.DisplayVersion)" -ForegroundColor Green
        # Try to determine dcu-cli.exe path
        $installLocation = $dcuProduct.InstallLocation
        $potentialCLIPath = $null
        if ($installLocation -and (Test-Path (Join-Path $installLocation "dcu-cli.exe"))) {
             $potentialCLIPath = Join-Path $installLocation "dcu-cli.exe"
        } elseif (Test-Path "C:\Program Files\Dell\CommandUpdate\dcu-cli.exe") {
             $potentialCLIPath = "C:\Program Files\Dell\CommandUpdate\dcu-cli.exe"
        } elseif (Test-Path "C:\Program Files (x86)\Dell\CommandUpdate\dcu-cli.exe") {
             $potentialCLIPath = "C:\Program Files (x86)\Dell\CommandUpdate\dcu-cli.exe"
        }

        if ($potentialCLIPath) {
            $Global:DCUCLIPath = $potentialCLIPath
            Write-Host "Using DCU CLI Path: $Global:DCUCLIPath" -ForegroundColor Cyan
        } else {
             Write-Warning "Could not reliably determine dcu-cli.exe path. DCU configuration and execution might fail."
             $Global:DCUCLIPath = $null # Explicitly set to null if not found
        }

        return [PSCustomObject]@{
            Name            = $dcuProduct.DisplayName
            Version         = $dcuProduct.DisplayVersion
            InstallLocation = $dcuProduct.InstallLocation
            CLIPath         = $Global:DCUCLIPath # Return the found path or null
        }
    } else {
        Write-Host "Dell Command | Update not found." -ForegroundColor Yellow
        $Global:DCUCLIPath = $null # Ensure it's null if DCU not found
        return $null
    }
}

Function Install-DCU {
    # .SYNOPSIS Installs the latest version of Dell Command | Update (DCU). (Hardcoded URL)
    # .DESCRIPTION Downloads and silently installs a specific version of DCU.
    # NOTE: The download URL is hardcoded and may become outdated.
    # Hardcoded URL - Check for updates periodically
    $DCUDownloadUrl = "https://dl.dell.com/FOLDER12925773M/1/Dell-Command-Update-Windows-Universal-Application_P4DJW_WIN64_5.5.0_A00.EXE"
    $DCUInstaller = Split-Path -Leaf $DCUDownloadUrl
    $DCUInstallerPath = Join-Path $Global:DellTempDirectory $DCUInstaller

    Write-Host "Downloading Dell Command | Update installer from $DCUDownloadUrl..." -ForegroundColor Cyan
    Try {
        Invoke-WebRequest -Uri $DCUDownloadUrl -OutFile $DCUInstallerPath -ErrorAction Stop
        Write-Host "Download complete." -ForegroundColor Green
    } Catch {
        Write-Error "Failed to download DCU installer: $($_.Exception.Message)"
        if (Test-Path $DCUInstallerPath) { Remove-Item $DCUInstallerPath -Force }
        return $false # Indicate failure
    }

    Write-Host "Installing Dell Command | Update silently..." -ForegroundColor Cyan
    Try {
        $process = Start-Process -FilePath $DCUInstallerPath -ArgumentList "/s" -Wait -PassThru -ErrorAction Stop
        if ($process.ExitCode -ne 0) {
             Throw "DCU installation failed with exit code $($process.ExitCode)."
        }
        Write-Host "Dell Command | Update installed successfully." -ForegroundColor Green
        Start-Sleep -Seconds 5 # Give installer time to finish processes
        Get-DCUInstallDetails | Out-Null # Refresh details after install
        return $true
    } Catch {
        Write-Error "Failed to install DCU: $($_.Exception.Message)"
        return $false # Indicate failure
    } Finally {
        if (Test-Path $DCUInstallerPath) { Remove-Item -Path $DCUInstallerPath -Force }
    }
}

Function Set-DCUSettings {
    # .SYNOPSIS Configures Dell Command | Update (DCU) settings using dcu-cli.exe.
    # .DESCRIPTION Sets recommended settings using Start-Process.
    Write-Host "Configuring Dell Command | Update settings via dcu-cli.exe..." -ForegroundColor Cyan

    if (-not $Global:DCUCLIPath -or -not (Test-Path $Global:DCUCLIPath)) {
        Write-Error "dcu-cli.exe path ('$($Global:DCUCLIPath)') not found or not set. Cannot configure DCU."
        return $false
    }

    $configSuccess = $true
    # Define arguments separately
    $configArgsList = @(
        "/configure -autoSuspendBitLocker=enable",
        "/configure -scheduleAction=DownloadInstallAndNotify",
        # "/configure -userConsent=Never", # Commented out: Exit Code 107 in logs (Likely invalid for DCU 5.4.0)
        "/configure -advancedDriverRestore=enable"#,
        # "/configure -driverLibrarySource=DellSupport" # Commented out: Exit Code 106 in logs (Likely invalid for DCU 5.4.0)
    )

    foreach ($args in $configArgsList) {
        Write-Host "Executing: `"$Global:DCUCLIPath`" $args" -ForegroundColor Gray
        Try {
            $process = Start-Process -FilePath $Global:DCUCLIPath -ArgumentList $args -Wait -PassThru -ErrorAction Stop
            if ($process.ExitCode -ne 0) {
                 Write-Warning "DCU configuration command (`"$Global:DCUCLIPath`" $args) completed with exit code $($process.ExitCode)."
                 # Treat any non-zero as a potential issue for configuration
                 $configSuccess = $false
            }
        } Catch {
            Write-Warning "Failed to execute configuration command `"$Global:DCUCLIPath`" $args : $($_.Exception.Message)"
            $configSuccess = $false
        }
    }

    # Registry fallback/redundancy for AutoSuspendBitLocker
    $regKey = "HKLM:\SOFTWARE\Dell\CommandUpdate\Preferences"
    Try {
        if (-not (Test-Path $regKey)) { New-Item -Path $regKey -Force -ErrorAction Stop | Out-Null }
        Set-ItemProperty -Path $regKey -Name "AutoSuspendBitLocker" -Value 1 -Type DWord -Force -ErrorAction Stop
        Write-Host "Registry setting 'AutoSuspendBitLocker=1' confirmed." -ForegroundColor Green
    } Catch {
        Write-Warning "Failed to set registry key for AutoSuspendBitLocker: $($_.Exception.Message)"
    }

    if ($configSuccess) {
        Write-Host "DCU settings configuration attempted via CLI. Check warnings for specific command results." -ForegroundColor Green
    } else {
        Write-Warning "One or more DCU configuration commands reported non-zero exit codes or failed. Check transcript log."
    }
    # Return overall status
    return $configSuccess
}

Function Get-DellBIOSUpdates {
    # .SYNOPSIS Checks for available Dell BIOS updates using the Dell Catalog.
    # .DESCRIPTION Downloads and extracts catalog using expand.exe (explicit file target), then compares versions. ONLY CHECKS.
    # .OUTPUTS Boolean: $true if a newer BIOS update is available, $false otherwise or on error.

    $SystemSKU = $null
    Try {
        $SystemSKU = (Get-CimInstance -ClassName Win32_ComputerSystem -ErrorAction Stop).SystemSKUNumber
        if (-not $SystemSKU) { Throw "Could not retrieve System SKU." }
         Write-Host "System SKU detected: $SystemSKU" -ForegroundColor Cyan
    } Catch {
         Write-Error "Failed to get System SKU: $($_.Exception.Message)"
         return $false
    }

    $CatalogUrl = "https://downloads.dell.com/catalog/CatalogIndexPC.cab"
    $CatalogPath = Join-Path $Global:DellTempDirectory "CatalogIndexPC.cab"
    # Define the explicit path for the target XML file
    $CatalogXmlFile = Join-Path $Global:DellTempDirectory "CatalogIndexPC.xml"

    Write-Host "Downloading Dell catalog from $CatalogUrl..." -ForegroundColor Cyan
    Try { Invoke-WebRequest -Uri $CatalogUrl -OutFile $CatalogPath -ErrorAction Stop } Catch {
        Write-Error "Failed to download Dell Catalog: $($_.Exception.Message)"
        if (Test-Path $CatalogPath) { Remove-Item $CatalogPath -Force }
        return $false
    }

    Write-Host "Extracting Dell catalog using expand.exe..." -ForegroundColor Cyan
    Try {
        # Ensure the target XML file doesn't already exist from a previous failed run
        if (Test-Path $CatalogXmlFile) { Remove-Item $CatalogXmlFile -Force }

        # Use expand.exe with explicit source CAB and destination FILE path
        # Syntax: expand <source> <destination_file>
        $expandArgs = "`"$CatalogPath`" `"$CatalogXmlFile`"" # Quote paths
        Write-Host "Running: expand.exe $expandArgs" -ForegroundColor Gray
        $process = Start-Process -FilePath "expand.exe" -ArgumentList $expandArgs -Wait -PassThru -WindowStyle Hidden
        if ($process.ExitCode -ne 0) {
            Throw "expand.exe failed with exit code $($process.ExitCode)."
        }

        # Check if the XML file exists after extraction attempt
        if (-not (Test-Path $CatalogXmlFile)) {
            Throw "Catalog XML file '$CatalogXmlFile' not found after running expand.exe."
        }
        Write-Host "Catalog extracted successfully to '$CatalogXmlFile'." -ForegroundColor Green
    } Catch {
        Write-Error "Failed to extract Dell Catalog using expand.exe: $($_.Exception.Message)"
        # Cleanup extracted file if it exists but was potentially bad
        if (Test-Path $CatalogXmlFile) { Remove-Item $CatalogXmlFile -Force }
        return $false
    }

    # Load and Parse Catalog XML
    [xml]$Catalog = $null
    Try { $Catalog = Get-Content -Path $CatalogXmlFile -Raw -Encoding UTF8 -ErrorAction Stop } Catch {
         Write-Error "Failed to read Catalog XML file '$CatalogXmlFile': $($_.Exception.Message)"
         return $false
    }

    # Find the latest applicable BIOS update in the catalog
    $BIOSUpdate = $null
    Try {
        $BIOSUpdate = $Catalog.Manifest.SoftwareComponent | Where-Object {
             ($_.ComponentType.Display.'#cdata-section' -eq "BIOS" -or $_.ComponentType.Display.'#text' -eq "BIOS" -or $_.ComponentType -match 'BIOS') -and
             $_.SupportedSystems.Brand.Model.SystemID -contains $SystemSKU
        } | Sort-Object -Property @{Expression={ [datetime]$_.ReleaseDate }} -Descending | Select-Object -First 1
    } Catch { Write-Warning "Error parsing BIOS updates from catalog for SKU $SystemSKU{}: $($_.Exception.Message)" }

    if ($BIOSUpdate) {
        $LatestBIOSVersionStr = $BIOSUpdate.Version
        if (-not $LatestBIOSVersionStr) { $LatestBIOSVersionStr = $BIOSUpdate.VendorVersion }
        $BIOSUpdateName = $null
        if ($BIOSUpdate.Name.Display.'#cdata-section') { $BIOSUpdateName = $BIOSUpdate.Name.Display.'#cdata-section' }
        elseif ($BIOSUpdate.Name.Display.'#text') { $BIOSUpdateName = $BIOSUpdate.Name.Display.'#text' }
        else { $BIOSUpdateName = $BIOSUpdate.Name }

        if (-not $LatestBIOSVersionStr) {
             Write-Warning "Could not determine latest BIOS version from catalog entry for '$BIOSUpdateName'."
             return $false
        }
        Write-Host "Latest BIOS Version found in Catalog: $LatestBIOSVersionStr ($BIOSUpdateName)" -ForegroundColor Green

        $CurrentBIOSVersionStr = $null
        Try {
            $CurrentBIOSVersionStr = (Get-CimInstance -ClassName Win32_BIOS -ErrorAction Stop).SMBIOSBIOSVersion
             Write-Host "Current System BIOS Version: $CurrentBIOSVersionStr" -ForegroundColor Cyan
        } Catch { Write-Error "Failed to get current BIOS version: $($_.Exception.Message)"; return $false }

        Try {
            if ([version]$CurrentBIOSVersionStr -lt [version]$LatestBIOSVersionStr) {
                Write-Host "BIOS update IS available (Current: $CurrentBIOSVersionStr, Available: $LatestBIOSVersionStr)." -ForegroundColor Yellow
                return $true
            } else {
                Write-Host "System BIOS is up-to-date or newer." -ForegroundColor Green
                return $false
            }
        } Catch {
             Write-Warning "Could not compare BIOS versions ('$CurrentBIOSVersionStr' vs '$LatestBIOSVersionStr'): $($_.Exception.Message)"
             Write-Warning "Assuming NO update is available due to version comparison error."
             return $false
        }
    } else {
        Write-Host "No specific BIOS updates found for System SKU '$SystemSKU' in the catalog." -ForegroundColor Green
        return $false
    }
}

Function Invoke-DCU {
    # .SYNOPSIS Runs Dell Command Update CLI to scan or apply available updates.
    # .DESCRIPTION Executes dcu-cli.exe and handles specific exit codes (0, 2, 500) as success.
    # .PARAMETER scan Perform scan for updates.
    # .PARAMETER applyUpdates Apply downloaded/available updates.
    # .PARAMETER Password SecureString containing the BIOS password, if required for an update.
    param(
        [Parameter(Mandatory=$true, ParameterSetName='Scan')]
        [switch]$scan,
        [Parameter(Mandatory=$true, ParameterSetName='Apply')]
        [switch]$applyUpdates,
        [Parameter(ParameterSetName='Apply')]
        [SecureString]$Password
    )

    if (-not $Global:DCUCLIPath -or -not (Test-Path $Global:DCUCLIPath)) {
        Write-Error "dcu-cli.exe path ('$($Global:DCUCLIPath)') not found or not set. Cannot run DCU."
        return $false
    }

    $LogFileName = if ($scan) { "DCUScan_$(Get-Date -Format 'yyyyMMdd_HHmmss').log" } else { "DCUApplyUpdates_$(Get-Date -Format 'yyyyMMdd_HHmmss').log" }
    $LogPath = Join-Path $LogDirectory $LogFileName

    $ArgumentList = New-Object System.Collections.Generic.List[string]
    if ($scan) { $ArgumentList.Add("/scan") }
    elseif ($applyUpdates) {
        $ArgumentList.Add("/applyUpdates")
        $ArgumentList.Add("-autoSuspendBitLocker=enable")
        $ArgumentList.Add("-reboot=disable")
    } else { Write-Error "Invalid parameters for Invoke-DCU."; return $false }
    $ArgumentList.Add("-outputlog=`"$LogPath`"")

    if ($applyUpdates -and $Password) {
        Write-Host "BIOS Password provided, adding to dcu-cli command." -ForegroundColor Yellow
        Write-Warning "Security Risk: Converting SecureString to plain text for dcu-cli /password argument."
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password)
        $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        $ArgumentList.Add("/password=$PlainPassword")
        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR); $PlainPassword = $null
        Register-ObjectEvent -InputObject $pscmdlet -EventName 'Disposed' -Action { $PlainPassword = $null } | Out-Null
    } elseif ($applyUpdates) { Write-Host "No BIOS password provided to Invoke-DCU." -ForegroundColor Gray }

    $ArgumentString = $ArgumentList -join ' '
    Write-Host "Executing: `"$Global:DCUCLIPath`" $ArgumentString" -ForegroundColor Gray
    $exitCode = -1

    Try {
        $process = Start-Process -FilePath $Global:DCUCLIPath -ArgumentList $ArgumentString -Wait -PassThru -ErrorAction Stop
        $exitCode = $process.ExitCode
        Write-Host "DCU Process exited with code: $exitCode" -ForegroundColor Cyan

        if ($exitCode -in @(0, 2, 500)) {
             if ($exitCode -eq 0) { Write-Host "DCU operation completed successfully." -ForegroundColor Green }
             if ($exitCode -eq 2) { Write-Host "DCU operation completed, reboot required (Exit Code 2)." -ForegroundColor Yellow; $script:RebootNeededByDCU = $true }
             if ($exitCode -eq 500) { Write-Host "DCU operation completed: No applicable updates found or system is up-to-date (Exit Code 500)." -ForegroundColor Green }
             return $true
        } else { Write-Warning "DCU operation failed or completed with unexpected warnings. Exit Code: $exitCode. Check DCU log: $LogPath"; return $false }
    } Catch { Write-Error "Failed to execute dcu-cli.exe: $($_.Exception.Message)"; return $false }
}

#==============================================================================
# --- Main Script Workflow ---
#==============================================================================

# --- Dell Update Workflow ---
Write-Host "--- Starting Dell Update Workflow ---" -ForegroundColor Magenta

$dcuDetails = Get-DCUInstallDetails
if (-not $dcuDetails) {
    Write-Host "Dell Command | Update not found. Attempting installation..." -ForegroundColor Yellow
    if (-not (Install-DCU)) { Write-Error "DCU Installation failed. Skipping Dell update process." }
    else {
         $dcuDetails = Get-DCUInstallDetails
         if (-not $dcuDetails -or -not $Global:DCUCLIPath) { Write-Error "DCU installed but details/CLI path could not be retrieved. Skipping Dell update process." }
    }
}

# Configure DCU Settings
if ($dcuDetails -and $Global:DCUCLIPath) { Set-DCUSettings }
else { Write-Warning "Skipping DCU configuration because DCU or its CLI path is not available." }

# Check for BIOS Updates and Prompt for Password if needed
$BIOSUpdateAvailable = $false
$SecureBIOSPassword = $null
if ($Global:DCUCLIPath) { # Only check/apply if DCU CLI is available
    Write-Host "Checking if BIOS update is available via Dell Catalog..." -ForegroundColor Cyan
    $BIOSUpdateAvailable = Get-DellBIOSUpdates

    if ($BIOSUpdateAvailable) {
        Write-Host "Potential BIOS update detected by catalog check." -ForegroundColor Yellow
        $isPasswordPresent = $false
        Try {
            $biosInfo = Get-CimInstance -ClassName Win32_BIOS -ErrorAction Stop
            if ($biosInfo.PSObject.Properties.Name -contains 'IsPasswordPresent') { $isPasswordPresent = $biosInfo.IsPasswordPresent }
            else { Write-Warning "Cannot determine if BIOS password is set (WMI property 'IsPasswordPresent' not found)." }
        } Catch { Write-Warning "Could not query Win32_BIOS for password status: $($_.Exception.Message)" }

        if ($isPasswordPresent) {
            Write-Host "System BIOS password is set." -ForegroundColor Yellow
            if ($Host.Name -eq 'ConsoleHost') { $SecureBIOSPassword = Read-Host "Please enter the current BIOS Admin/Setup Password" -AsSecureString }
            else { Write-Error "Cannot prompt for BIOS password in non-console host."; $SecureBIOSPassword = $null }
        } else { Write-Host "System BIOS password is not detected or status unknown." -ForegroundColor Cyan }
    } else { Write-Host "No pending BIOS update found via catalog check, or unable to verify." -ForegroundColor Green }

    # Run DCU Scan and Apply Updates
    Write-Host "Running DCU Scan..." -ForegroundColor Cyan
    Invoke-DCU -scan
    Write-Host "Running DCU Apply Updates..." -ForegroundColor Cyan
    Invoke-DCU -applyUpdates -Password $SecureBIOSPassword

} else { Write-Warning "Skipping BIOS check, DCU Scan and Apply Updates because DCU CLI path is not available." }

Write-Host "--- Dell Update Workflow Finished ---" -ForegroundColor Magenta

# --- Windows Update Workflow (Excluding Drivers) ---
Write-Host "--- Starting Windows Update Workflow (Excluding Drivers) ---" -ForegroundColor Magenta

Try {
    Write-Host "Checking/Installing PSWindowsUpdate Module..." -ForegroundColor Cyan
    if (-not (Get-Module -ListAvailable -Name PSWindowsUpdate)) {
        Write-Host "PSWindowsUpdate module not found. Installing (Scope: CurrentUser)..." -ForegroundColor Yellow
        Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force -Scope CurrentUser -Confirm:$false -ErrorAction SilentlyContinue
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        Install-Module PSWindowsUpdate -Force -AllowClobber -Scope CurrentUser -SkipPublisherCheck -Confirm:$false -ErrorAction Stop
        Write-Host "PSWindowsUpdate module installed." -ForegroundColor Green
    } else { Write-Host "PSWindowsUpdate module already available." -ForegroundColor Green }
    Import-Module PSWindowsUpdate -Force -ErrorAction Stop

    Write-Host "Checking for and Installing Windows updates (excluding Drivers)..." -ForegroundColor Cyan
    Get-WindowsUpdate -NotCategory 'Drivers' -AcceptAll -Install -AutoReboot:$AutoRebootIfNeeded -Verbose -ErrorAction Continue
    Write-Host "Windows Update check/install command executed (excluding Drivers)." -ForegroundColor Green

} Catch { Write-Error "An error occurred during the Windows Update workflow: $($_.Exception.Message)" }

Write-Host "--- Windows Update Workflow Finished ---" -ForegroundColor Magenta

#==============================================================================
# Reboot Handling
#==============================================================================
$RebootPending = $false
Try {
   $pendingRebootReg = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager"
   $pendingFileOps = Get-ItemProperty -Path $pendingRebootReg -Name PendingFileRenameOperations -ErrorAction SilentlyContinue
   if ($pendingFileOps) { Write-Host "Registry (PendingFileRenameOperations) indicates a pending reboot is required." -ForegroundColor Yellow; $RebootPending = $true }
   if (-not $RebootPending) {
       $pendingRebootRegWU = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired"
       if (Test-Path $pendingRebootRegWU) { Write-Host "Registry (Windows Update) indicates a pending reboot is required." -ForegroundColor Yellow; $RebootPending = $true }
   }
   if ($script:RebootNeededByDCU) { Write-Host "DCU indicated a reboot might be required (Exit Code 2)." -ForegroundColor Yellow; $RebootPending = $true }
} Catch { Write-Warning "Could not reliably determine pending reboot status via registry: $($_.Exception.Message)" }

if ($AutoRebootIfNeeded -and $RebootPending) {
    Write-Host "Reboot needed and -AutoRebootIfNeeded specified. Rebooting system in 1 minute..." -ForegroundColor Yellow
    Restart-Computer -Force -Delay 1
} elseif ($RebootPending) { Write-Warning "Updates completed, but a REBOOT IS REQUIRED. Please reboot the system manually." }
else { Write-Host "Updates completed. No pending reboot detected or auto-reboot not specified." -ForegroundColor Green }

#==============================================================================
# Finalize and Exit
#==============================================================================
Write-Host "Script execution finished." -ForegroundColor Green
Write-Host "Check transcript log '$transcriptLog' and DCU logs in '$LogDirectory' for details." -ForegroundColor Green

if (Test-Path $Global:DellTempDirectory) {
    Write-Host "Cleaning up temporary directory: $Global:DellTempDirectory" -ForegroundColor Cyan
    Remove-Item -Path $Global:DellTempDirectory -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "Exiting script."
Stop-Transcript

Exit 0