﻿$identity = Read-Host 'Enter Mailbox name - ie: <EMAIL> - to list users with Full Control access'
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
get-mailbox -Identity $identity | Get-MailboxPermission | where {$_.user.tostring() -ne "NT AUTHORITY\SELF" -and $_.IsInherited -eq $false} | Select Identity,User,@{Name='Access Rights';Expression={[string]::join(', ', $_.AccessRights)}}  | Export-Csv -NoTypeInformation c:\scripts\checkmailboxpermission.csv
Invoke-Item "C:\scripts\checkmailboxpermission.csv"