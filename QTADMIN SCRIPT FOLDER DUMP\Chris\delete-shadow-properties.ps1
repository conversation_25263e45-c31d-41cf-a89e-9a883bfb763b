﻿get-aduser -filter * -Properties MsExchShadowProxyAddresses | ? {$_.MsExchShadowProxyAddresses.count -gt 0 -and $_.enabled -eq $true -and $_.samaccountname -match '\.'} | select samaccountname | sort samaccountname
get-aduser -filter * -Properties MsExchShadowProxyAddresses | ? {$_.MsExchShadowProxyAddresses.count -eq 0 -and $_.enabled -eq $true -and $_.samaccountname -match '\.'} | select samaccountname | sort samaccountname