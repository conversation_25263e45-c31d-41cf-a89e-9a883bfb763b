# Import the Active Directory module
Import-Module ActiveDirectory

# Define the paths to the CSV files
$groupsToRemoveFile = "C:\Scripts\Under Construction\Remove and add local groups Transfer\CSVsgroups_to_remove.csv"
$groupsToAddFile = "C:\Scripts\Under Construction\Remove and add local groups Transfer\CSVsgroups_to_add.csv"

# Prompt for the username
$userName = Read-Host -Prompt "Enter the username"

# Import the CSV files
$groupsToRemove = Import-Csv -Path $groupsToRemoveFile
$groupsToAdd = Import-Csv -Path $groupsToAddFile

# Iterate through each group in the groups to remove file
foreach ($group in $groupsToRemove) {
    $groupName = $group.GroupName
    try {
        # Remove the user from the group
        Remove-ADGroupMember -Identity $groupName -Members $userName -Confirm:$false -ErrorAction Stop
        Write-Host "Removed '$userName' from '$groupName'"
    } catch {
        Write-Host "Failed to remove '$userName' from '$groupName'. Error: $_"
    }
}

# Iterate through each group in the groups to add file
foreach ($group in $groupsToAdd) {
    $groupName = $group.GroupName
    try {
        # Add the user to the group
        Add-ADGroupMember -Identity $groupName -Members $userName -ErrorAction Stop
        Write-Host "Added '$userName' to '$groupName'"
    } catch {
        Write-Host "Failed to add '$userName' to '$groupName'. Error: $_"
    }
}
