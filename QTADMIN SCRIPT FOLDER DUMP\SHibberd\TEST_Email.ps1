$location = "Corporate"
# ASP
#$sender = "<EMAIL>"
# Corp
$sender = "vCenter Admin <<EMAIL>>"
$subject = "Test Email From $location"
$recipient = "<EMAIL>"
# Works Corp
$smtp_server = "ca-smtp-outbound-1.mimecast.com"
# Fails Corp
#$smtp_server = "us-smtp-outbound-1.mimecast.com"
# Works Corp
#$smtp_server = "mail.qhrtech.com"
$port = "25"

#write-host $latest_file
$body = "Test Email Body - $location $smtp_server"

Send-MailMessage -To $recipient -from $sender -Subject $subject -Body $body -SmtpServer $smtp_server -Port 25