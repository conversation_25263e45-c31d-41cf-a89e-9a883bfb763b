https://joostgelijsteen.com/get-an-epm-elevation-request-notification/ > Screenshots here for reference


Guide: Automated EPM Request Notifications in Teams via Azure Logic Apps
Introduction & Goal
This guide provides detailed, step-by-step instructions to create an automated workflow that monitors for new Intune Endpoint Privilege Management (EPM) elevation requests and sends an interactive approval card to a Microsoft Teams channel.

The final result will be a serverless process where:

An EPM request is submitted by a user.

Within minutes, a card appears in a designated Teams channel with request details.

An admin can click "Approve" or "Deny" directly in Teams and provide a justification.

The Logic App securely communicates the decision back to Intune, completing the request.

Part 1: Prerequisites
Before you begin, ensure you have the following:

Azure Subscription: You need an active Azure subscription with permissions to create resources like Logic Apps and App Registrations.

Intune Suite or EPM License: EPM must be licensed and configured in your tenant.

Administrator Access: You'll need sufficient permissions in Azure AD and Intune (e.g., Global Administrator or Endpoint Security Manager) to grant API permissions.

Microsoft Teams: A Team and a dedicated channel where notifications will be sent. Based on your setup, this will be the "EPM Notifications" Team and its "General" channel.

Part 2: Create the Azure Logic App Resource
First, we'll create the core resource that will host our workflow.

Navigate to the Azure Portal: Go to https://portal.azure.com.

Click + Create a resource.

Search for Logic App in the marketplace and select it.

Click Create.

On the Create Logic App screen, configure the following:

Subscription: Choose your Azure subscription.

Resource Group: Click Create new and provide a name following your convention, for example: RG-Intune-EPM-Notifications.

Logic App name: Provide a descriptive name following your convention, for example: LA-Intune-EPM-Notifications.

Region: Select the Azure region closest to you.

Plan type: Select Consumption. This is a pay-per-execution model that is extremely cost-effective for this type of workload.

Navigate to the Identity tab at the top.

Under System assigned, set the Status to On. This is the most critical step for creating a secure, passwordless connection to the Graph API. A system-assigned managed identity will be created with the same name as your Logic App.

Click Review + create, and then click Create after validation completes.

Part 3: Assign Graph API Permissions to the Logic App
Now, we need to grant our new Logic App's Managed Identity permission to read and update EPM requests in Intune. We will do this using Script1: Grant-EpmPermissions.ps1.

Prepare the Script: Save the script provided below this guide (Script1: Grant-EpmPermissions.ps1) to a file named Grant-EpmPermissions.ps1 on your local machine.

Open PowerShell: On your local machine, open a PowerShell 7 console as an Administrator.

Install Required Module: If you don't have it installed, run the following command. The -AllowClobber and -Force switches help avoid installation issues.

Install-Module Microsoft.Graph.Applications -AllowClobber -Force

Configure and Run the Permission Script:

Navigate to the directory where you saved Grant-EpmPermissions.ps1.

Open the Grant-EpmPermissions.ps1 file in an editor (like VS Code or Notepad) and change the value of the $logicAppName variable on line 18 to match the exact name of the Logic App you created in Part 2 (e.g., LA-Intune-EPM-Notifications). Save the file.

In your PowerShell console, run the script by executing: .\Grant-EpmPermissions.ps1

Authenticate and Consent: You will be prompted to sign in via a browser window. Use a global administrator account to consent to the permissions. The script will find your Logic App's identity and assign it the correct permissions to manage EPM requests, printing its progress in the console.

Part 4: Build the Logic App Workflow
Now we will build the actual workflow in the Logic App Designer.

In the Azure Portal, go to the Logic App you created.

From the left menu, select Logic App designer.

You will see a blank canvas. We will now add the steps.

Step 4.1: The Recurrence Trigger
This starts the workflow on a schedule.

Under "Start with a common trigger", select Recurrence.

Set the Interval to 5.

Set the Frequency to Minute.

Step 4.2: Get Pending Elevation Requests
This step calls the Graph API to get the data.

Click + New step.

Search for the HTTP connector and select the HTTP action.

Configure the HTTP action as follows:

Method: GET

URI: https://graph.microsoft.com/beta/deviceManagement/privilegeManagementElevationRequests?$filter=status eq 'pending'

Click Add new parameter and check the box for Authentication.

Configure the Authentication section:

Authentication type: Managed Identity

Managed identity: System-assigned managed identity

Audience: https://graph.microsoft.com

Step 4.3: Parse the JSON Response
This makes the data from the API call easy to use in subsequent steps.

Click + New step.

Search for Parse JSON and select the action.

In the Content field, click inside it and select Body from the Dynamic Content pane (it's the output from the HTTP step).

Click the Use sample payload to generate schema link.

Paste the following JSON sample into the box and click Done.

{
    "value": [
        {
            "id": "e4a42426-57a9-4623-a9a3-d02c2e07e6b8",
            "requestedByUserId": "c6fae2a5-42f3-462c-81b4-2b6228e93294",
            "requestedOnDeviceId": "54b86c35-2230-4e2a-8924-42b43b40e791",
            "requestedByUserPrincipalName": "<EMAIL>",
            "deviceName": "SLADER-PC",
            "requestCreatedDateTime": "2025-06-24T18:30:00Z",
            "status": "pending",
            "justification": "Need to install new software for a project.",
            "applicationDetail": {
                "fileName": "setup.exe",
                "filePath": "C:\\Users\\<USER>\\Downloads\\",
                "productName": "Awesome Utility",
                "publisherName": "Contoso Inc.",
                "fileHash": "A94A8FE5CCB19BA61C4C0873D391E987982FBBD3"
            }
        }
    ]
}

Step 4.4: Loop Through Each Request
This creates a loop to process each pending request one by one.

Click + New step.

Select the Control connector, then choose the For each action.

In the Select an output from previous steps field, select value from the Dynamic Content pane (it's the output from the "Parse JSON" step).

Step 4.5: Post the Adaptive Card to Teams
Inside the "For each" loop, we will add the action to send the notification.

Inside the loop, click Add an action.

Search for Teams and select the Post adaptive card and wait for a response action.

Sign in to Teams if prompted.

Configure the action:

Post as: Flow bot

Post in: Channel

Team: Select the EPM Notifications team from the dropdown list.

Channel: Select the General channel from the dropdown list.

Message: Delete the existing content and paste the entire JSON adaptive card code below.

{
    "type": "AdaptiveCard",
    "version": "1.4",
    "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
    "body": [
        {
            "type": "TextBlock",
            "text": "New EPM Elevation Request",
            "weight": "Bolder",
            "size": "Large"
        },
        {
            "type": "TextBlock",
            "text": "A new request for application elevation requires review.",
            "wrap": true,
            "isSubtle": true
        },
        {
            "type": "FactSet",
            "facts": [
                {
                    "title": "User:",
                    "value": "@{items('For_each')?['requestedByUserPrincipalName']}"
                },
                {
                    "title": "Device:",
                    "value": "@{items('For_each')?['deviceName']}"
                },
                {
                    "title": "File Name:",
                    "value": "@{items('For_each')?['applicationDetail']?['fileName']}"
                },
                {
                    "title": "Publisher:",
                    "value": "@{items('For_each')?['applicationDetail']?['publisherName']}"
                },
                {
                    "title": "Request Time:",
                    "value": "@{formatDateTime(items('For_each')?['requestCreatedDateTime'], 'yyyy-MM-dd hh:mm tt')}"
                },
                {
                    "title": "Justification:",
                    "value": "@{items('For_each')?['justification']}"
                }
            ],
            "spacing": "Large"
        },
        {
            "type": "Input.Text",
            "id": "reviewerJustification",
            "placeholder": "Provide a reason for approval or denial (required)",
            "isMultiline": true,
            "label": "Reviewer Justification"
        }
    ],
    "actions": [
        {
            "type": "Action.Submit",
            "title": "Approve",
            "style": "positive",
            "data": {
                "action": "approve"
            }
        },
        {
            "type": "Action.Submit",
            "title": "Deny",
            "style": "destructive",
            "data": {
                "action": "deny"
            }
        }
    ]
}

Step 4.6: Check the Admin's Response
Still inside the "For each" loop, after the Teams card, we add a condition to see which button was clicked.

Click Add an action.

Select the Control connector, then choose the Condition action.

Configure the condition:

In the first box, select submitActionId from the dynamic content (it's an output from the "Post adaptive card" step).

Change the operator to is equal to.

In the third box, type Approve.

Step 4.7: Approve or Deny the Request
The condition creates two paths: "If true" and "If false".

In the "If true" (Approve) branch:

Click Add an action.

Add an HTTP action.

Configure it:

Method: POST

URI: https://graph.microsoft.com/beta/deviceManagement/privilegeManagementElevationRequests/@{items('For_each')?['id']}/approve

Body:

{
  "justification": "@{outputs('Post_adaptive_card_and_wait_for_a_response')?['body/data/reviewerJustification']}"
}

Authentication: Set it to Managed Identity just as you did in Step 4.2.

In the "If false" (Deny) branch:

Click Add an action.

Add an HTTP action.

Configure it:

Method: POST

URI: https://graph.microsoft.com/beta/deviceManagement/privilegeManagementElevationRequests/@{items('For_each')?['id']}/deny

Body:

{
  "justification": "@{outputs('Post_adaptive_card_and_wait_for_a_response')?['body/data/reviewerJustification']}"
}

Authentication: Set it to Managed Identity.

Part 5: Save and Test
Click Save at the top of the Logic App Designer.

To test, go to a client machine and submit an EPM elevation request that requires support approval.

Within 5 minutes, the Logic App will run, and the adaptive card should appear in your EPM Notifications -> General channel in Teams.

Interact with the card to approve or deny the request and check the Intune portal to confirm the status has changed. You can also view the Runs history in the Logic App's main blade to troubleshoot any errors.