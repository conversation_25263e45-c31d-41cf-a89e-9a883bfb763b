﻿#Files Path to CSV file
$FilePath = "C:\Scripts\Signature\signaturegroupmembers.csv"

$csv = Import-CSV $FilePath


#Select a Signature Template
$selection = read-host "Select template number to apply signature attribute to group members. `n (1) Name Title CS Toll Free `n (2) Name Title Mobile `n (3) Name Title Direct Line `n (4) Name Title Direct Line Mobile `n (5) Name Title Direct Line Mobile Stock `n"
 
Foreach ($user in $csv)

{
SET-ADUSER $user.Usernames –replace @{extensionAttribute1=”$selection”}
}


