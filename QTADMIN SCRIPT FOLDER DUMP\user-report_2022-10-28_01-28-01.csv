﻿"First Name","Last Name","Email Address","Department","Title","Mobile","Extension attribute 10","Extension attribute 11","Description","Last Log-on Date","Full User Logon","Unique Account ID","User Logon"
"<PERSON>","<PERSON><PERSON><PERSON><PERSON>","<EMAIL>",,,,,,,"10/27/2022 3:23:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12624","da-gharshenin"
,,,,,,,,,,,"S-1-5-21-**********-**********-*********-16650","CLINICARE$"
,,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Built-in account for administering the computer/domain/enterprise","10/31/2019 4:30:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-500","$DUPLICATE-1f4"
"MDT",,,,"Service Account",,,,"Service Account: Windows deployment account","10/28/2022 9:43:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-13593","MDT"
"Brandon","Chesley","<EMAIL>","Data and Software Architecture","Junior Site Reliability Engineer",,,,,"10/25/2022 12:34:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22578","brandon.chesley"
"Malcolm","Kennedy",,,,,,,,"9/28/2020 11:52:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24632","da-mkennedy"
"Callow","Associates",,,"Dynamics Contractors",,,,"Contractor Account -  Finance - Dynamics Consultants Has to use this OU because of finance permissions","10/28/2022 11:47:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23935","callow"
"Domain Admin","Nyel English","<EMAIL>",,,,,,,"10/27/2022 1:06:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23507","da-nenglish"
"Domain Admin","Preet Kainth","<EMAIL>",,,,,,,"8/18/2022 6:10:50 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22518","da-pkainth"
"Domain Admin","Nick Janzen","<EMAIL>",,,,,,,"1/13/2022 11:06:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22341","da-njanzen"
"Domain Admin","Kevin Rosal","<EMAIL>",,,,,,,"9/19/2022 8:52:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23281","da-krosal"
"Alex","Mehl","<EMAIL>",,,,,,,"3/2/2021 2:53:17 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15393","da-amehl"
"Nan","Adams","<EMAIL>","CHN Central","Director, CHN Central","+1 (204) 898-5371",,,,"2/25/2022 12:48:44 PM","<EMAIL>","S-1-5-21-**********-**********-*********-13396","nan.adams"
"Miguel","Hernandez","<EMAIL>","Technology","Network Team Lead","+1 (250) 307-3323",,,,"10/28/2022 1:13:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22827","miguel.hernandez"
"JIRA","LDAP SYNC",,,"Service Account",,,,"Service Account: Corp IT - Jira and Confluence Service Account","11/8/2021 12:29:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22624","jirasync"
"Domain Admin -","Andrew McFadden","<EMAIL>",,,,,,,"2/7/2022 8:36:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22274","da-amcfadden"
"Domain Admin -","Kevin Kendall","<EMAIL>",,,,,,,"10/3/2022 11:17:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23928","da-kkendall"
"Domain Admin -","Scott May","<EMAIL>",,,,,,,"9/16/2022 8:06:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24060","da-smay"
"Domain Admin -","Chris Roseberry","<EMAIL>",,,,,,,"10/28/2022 6:33:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22564","da-croseberry"
"Domain Admin -","Devin Nate","<EMAIL>",,,,,,,"1/21/2020 3:05:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19009","da-nated"
"Mark","McLean (DA)",,,"Sr. Manager Cloud Technology",,,,,"4/16/2021 8:46:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19010","da-mmclean"
"Domain Admin -","Peter Laudenklos","<EMAIL>",,,,,,,"10/28/2022 11:56:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24118","Da-Plaudenklos"
"Domain Admin -","Miguel Hernandez","<EMAIL>",,,,,,,"10/28/2022 10:31:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22506","da-mhernandez"
"Domain Admin -","Robert Armstrong","<EMAIL>",,,,,,,"10/25/2022 2:39:53 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18347","da-rarmstrong"
"Domain Admin -","Sam Bradford","<EMAIL>",,,,,,,"10/28/2022 10:52:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22187","da-sbradford"
"QTADMIN1 service",,,,"Service Account",,,,"Service Account: Corp IT - Account used to run scheduled scripts on qtadmin1; Admin monitoring scripts","10/28/2022 8:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22370","qtadmin1service"
"da-","mhoppe","<EMAIL>",,,,,,,"12/21/2021 1:48:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23206","da-mhoppe"
"Mike","Checkley","<EMAIL>","Executive","President","+1 (250) 870-6888",,,,"5/6/2022 4:00:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1432","mike.checkley"
"Optimed","Sales","<EMAIL>","Sales","Service Account",,,,"Service Account:Email Account for Optimed Software Sales",,"<EMAIL>","S-1-5-21-**********-**********-*********-1456","OPT_Sales"
"Brian","Ellis","<EMAIL>","Development","Senior Director of Product Development","+1 (250) 870-6875",,,,"10/4/2022 4:20:41 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1428","brian.ellis"
"Lisa","St Laurent","<EMAIL>","Data and Software Architecture","Director of Software Architecture and Data",,,,,"10/27/2022 4:02:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1431","lisa.stlaurent"
"Opt","Development",,,"Service Account",,,,"Service Account: Development - SQL Server Account","9/21/2021 9:10:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1426","opt_development"
"Marion","Sherback","<EMAIL>","CHN West","Director of CHN West","+1 (604) 314-5067",,,,"10/27/2022 7:40:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-11242","marion.sherback"
"QHR Technologies","Support","<EMAIL>","Client Services","Service Account","+1 (250) 801-4274",,,"Service Account: Corp IT - Email / GTA no screen saver / After hours BB Support","10/16/2022 3:50:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-1454","OPT_Support"
"Greg","Harshenin","<EMAIL>","Technology","Information Security Officer","+1 (250) 215-0861",,,,"10/28/2022 8:43:07 AM","<EMAIL>","S-1-5-21-**********-**********-*********-12613","greg.harshenin"
"Blake","Dickie","<EMAIL>","Development","Principal Software Developer",,,,,"10/28/2022 1:25:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-8785","blake.dickie"
"Cheryl","Cain","<EMAIL>","Finance","Senior Accountant",,,,,"10/26/2022 3:40:52 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1156","cheryl.cain"
"Alfred","Loh","<EMAIL>","Data and Software Architecture","Developer Advocate",,,,,"10/8/2022 8:43:03 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15743","alfred.loh"
"Mark","McLean","<EMAIL>","Technology","Director of Enterprise Architecture","+1 (403) 617-8489",,,,"1/20/2021 7:55:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-16718","mark.mclean"
"Stefanie","Giddens","<EMAIL>","Marketing","Senior Director of Marketing & Product Management","+1 (250) 826-4426",,,,"10/27/2021 10:24:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-15740","stefanie.giddens"
"Ron","Hughes","<EMAIL>","CHN West","Account Executive","+1 (250) 550-6270",,,,"10/28/2022 11:00:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-15549","ron.hughes"
"Dayna","McInnis","<EMAIL>","Revenue Management","Revenue Management Clerk","+1 (250) 718-1271",,,,"10/25/2022 12:55:45 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18127","dayna.mcinnis"
"Amanda","Korecki","<EMAIL>","Revenue Management","Revenue Management Lead",,,,,"10/27/2022 8:13:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-14635","amanda.korecki"
"Bob","Gemmell","<EMAIL>","Finance","Accountant",,,,,"10/28/2022 9:32:06 AM","<EMAIL>","S-1-5-21-**********-**********-*********-14608","bob.gemmell"
"Neil","Hylton","<EMAIL>","Technology","Vendor and Partner Program Specialist","+1 (403) 512-0225",,,,"1/21/2021 9:38:46 AM","<EMAIL>","S-1-5-21-**********-**********-*********-16719","neil.hylton"
"Matti","Kalijarvi","<EMAIL>","CHN East","Account Executive","+1 (705) 931-4333",,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-12865","matti.kalijarvi"
"QHR Tech ","Admin","<EMAIL>",,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16670","QTAdmin"
"Brad","Paffe","<EMAIL>","Implementations","Senior Technical Services Specialist","+1 (416) 220-7464",,,,"10/27/2022 11:07:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-16722","brad.paffe"
"Jennifer","Davidoff","<EMAIL>","Implementations","Systems Integration Analyst","+1 (250) 826-0578",,,,"10/27/2022 6:47:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18728","jennifer.davidoff"
"Lesley","Beamond","<EMAIL>","Implementations","Implementation Consultant","+1 (604) 816-0301",,,,"10/28/2022 1:14:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18659","lesley.beamond"
"e","learning","<EMAIL>",,"Service Account",,,,"Service Account:For learning and Development","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-1260","elearning"
"Daryl","Laverdure","<EMAIL>","Product Development","Director of Product Development","+1 (250) 486-0392",,,,"10/26/2022 5:53:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18204","daryl.laverdure"
"Devin","Nate","<EMAIL>","Technology","Senior Director of Technology","+1 (403) 650-0871",,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16716","devin.nate"
"IT","Admin","<EMAIL>",,"Service Account",,,,"Service Account:  Corp IT - Shared email inbox",,"<EMAIL>","S-1-5-21-**********-**********-*********-18118","itadmin"
"Louise","Richardson","<EMAIL>","Product Operations","Client Escalation Manager","+1 (250) 215-1709",,,,"10/25/2022 1:16:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15581","louise.richardson"
"Chris","Hollman","<EMAIL>","Implementations","Systems Manager, Implementations","+1 (250) 808-0865",,,,"10/27/2022 12:19:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18706","chris.hollman"
"Claire","Blaker","<EMAIL>","Implementations","Senior Manager, Implementations & Data Solutions","+1 (250) 870-6871",,,,"10/28/2022 11:28:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-15817","claire.blaker"
"Shaun","O'Grady","<EMAIL>","Product Operations","Lead Project Manager","+1 (778) 363-1771",,,,"10/24/2022 6:59:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-12831","shaun.ogrady"
"Elton","Mahabir","<EMAIL>","CHN East","Account Executive-ON","+1 (647) 269-5773",,,,"9/14/2022 9:57:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-15655","elton.mahabir"
"Brad","Reibin","<EMAIL>","Product Operations","Associate Project Manager",,,,,"10/27/2022 12:53:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18727","brad.reibin"
"Tony","Elumir","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 10:34:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-14659","tony.elumir"
"Luba","O'Brien","<EMAIL>","Implementations","Implemention Consultant","+1 (416) 993-8343",,,,"10/12/2022 12:50:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15597","luba.obrien"
"Brenda","Undiri","<EMAIL>","Product Management","Project Manager Lead","+1 (416) 938-0377",,,,"10/20/2022 5:49:23 AM","<EMAIL>","S-1-5-21-**********-**********-*********-15429","brenda.undiri"
"Angie","Jarabe","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (647) 210-8788",,,,"10/25/2022 10:27:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18651","angie.jarabe"
"Christie","Magee","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/28/2022 10:23:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-17183","christie.magee"
"Roxanne","Geiger","<EMAIL>","Implementations","Implementation Team Lead","+1 (403) 608-5027",,,,"10/27/2022 3:11:53 PM","<EMAIL>","S-1-5-21-**********-**********-*********-13582","roxanne.geiger"
"Jeff","VanDenHeuvel","<EMAIL>","Finance","Director of Finance","+1 (250) 869-5637",,,,"10/28/2022 1:21:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16712","jeff.vandenheuvel"
"Alex","Mehl","<EMAIL>","Technology","Lead Systems Administrator","+1 (250) 718-2892",,,,"7/15/2022 1:39:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15368","alex.mehl"
"Jen","Danchuk","<EMAIL>","Product Operations","Senior Product Owner",,,,,"10/27/2022 4:11:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-15541","jen.danchuk"
"Adele","Williams","<EMAIL>","Revenue Management","Senior Project Manager, Revenue Management","+1 (250) 862-0894",,,,"10/28/2022 1:04:30 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18176","adele.williams"
"Paul","Wait","<EMAIL>","Implementations","Technical Services Specialist","+1 (250) 801-9950",,,,"10/28/2022 12:44:29 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18669","paul.wait"
"Wayne","Knorr","<EMAIL>","Development","Senior QA Analyst",,,,,"10/28/2022 10:01:23 AM","<EMAIL>","S-1-5-21-**********-**********-*********-15448","wayne.knorr"
"Janet","Hatfield","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (905) 630-2543",,,,"10/28/2022 6:39:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-16690","janet.hatfield"
"Susan","Poisson","<EMAIL>","Training","Training Team Lead","+1 (613) 889-4449",,,,"10/27/2022 10:21:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18234","susan.poisson"
"Ravi","Anandarajah","<EMAIL>","Implementations","Operations Manager, Implementations","+1 (647) 456-5702",,,,"3/10/2021 7:39:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18761","ravi.anandarajah"
"Colin","Greenway","<EMAIL>","Sales National","Senior Manager, Business Services","+1 (416) 220-7401",,,,"1/8/2021 12:35:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18755","colin.greenway"
"Aron","Ashmead","<EMAIL>","Operations","Privacy Officer","+1 (778) 214-0350",,,,"10/26/2022 2:20:41 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17387","aron.ashmead"
"Michael","Hall","<EMAIL>","Sales National","QHR General Manager","+1 (416) 220-7417",,,,"10/25/2022 11:00:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18754","michael.hall"
"Christopher","Cadieux","<EMAIL>","Development","Senior Software Developer",,,,,"5/7/2021 2:57:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18316","christopher.cadieux"
"Jonathan","Chalaturnyk","<EMAIL>","Development","Senior QA Analyst",,,,,"8/29/2022 3:30:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18342","jonathan.chalaturnyk"
"Temi","Beckley","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/20/2022 3:27:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17258","temi.beckley"
"Products","EMR","<EMAIL>",,"Service Account",,,,"shared email address monitored by the Product Operations Support Team (POST), for notifications from government bodies regarding lists that need to be regularly updated in Accuro.",,"<EMAIL>","S-1-5-21-**********-**********-*********-18339","Products"
"Dylan","Wood","<EMAIL>","Implementations","Lead Technical Services","+1 (647) 880-0650",,,,"10/24/2022 1:36:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17005","dylan.wood"
"Shawn","Manary","<EMAIL>","CHN East","Account Executive","+1 (416) 797-7178",,,,"6/28/2021 2:18:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18753","shawn.manary"
"Stevan","Christiansen","<EMAIL>","Implementations","Data Services Representative",,,,,"10/28/2022 11:41:24 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18749","stevan.christiansen"
"EMRcslt",,"<EMAIL>","Client Services","Service Account",,,,"Client Services Leadership Team email box, for communication between CSLT with CS staff.",,"<EMAIL>","S-1-5-21-**********-**********-*********-17324","Optcslt"
"Robert","Armstrong","<EMAIL>","Technology","Manager, Infrastructure","+1 (250) 317-6585",,,,"10/28/2022 10:54:24 AM","<EMAIL>","S-1-5-21-**********-**********-*********-17375","robert.armstrong"
"Jerry","Diener","<EMAIL>","Executive","Senior Director of Corporate Development","+1 (778) 214-1382",,,,"1/24/2022 12:54:15 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17014","jerry.diener"
"Rebecca","Ferrie","<EMAIL>","Product Management","Product Manager","+1 (416) 992-0453",,,,"10/26/2022 12:55:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-16901","rebecca.ferrie"
"Viktor","Velkovski","<EMAIL>","CHN East","Account Executive-ON","+1 (416) 220-7376",,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-18307","viktor.velkovski"
"Cody","Cudmore","<EMAIL>","Implementations","Lead Business Analyst, Data Solutions","+1 (250) 870-2639",,,,"10/27/2022 2:08:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18314","cody.cudmore"
"OptimedImpW",,"<EMAIL>","Implementations","Service Account",,,,"Linked to an outlook calendar for implementer booking",,"<EMAIL>","S-1-5-21-**********-**********-*********-17018","OptimedImpW"
"Adam","Sinai","<EMAIL>","CHN East","Director, CHN East","+1 (416) 220-7454",,,,"10/27/2022 12:02:01 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18752","adam.sinai"
"Optimed","Development After Hours","<EMAIL>","Development","Service Account",,,,"Service Account: This Account is for after hours support so if CS needs to get a hold of DEV that's what they use it for.",,"<EMAIL>","S-1-5-21-**********-**********-*********-18797","osdevah"
"Accounting",,"<EMAIL>",,"Service Account",,,,"Service Account: Finance - 	User account with an active email address that was used on qtfin2 to send invoices to clients","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18796","accounting"
"Optimed","Admin","<EMAIL>",,"Service Account",,,,"Master inbox that is used for receiving sales e-fax and general sales related emails.",,"<EMAIL>","S-1-5-21-**********-**********-*********-14774","OSAdmin"
"Shelby","Laidlaw","<EMAIL>","Implementations","Senior Business Analyst, Data Solutions","+1 (778)-903-0889",,,,"10/28/2022 11:53:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18330","shelby.laidlaw"
"Robert","Thornton","<EMAIL>","Implementations","Senior Technical Services Specialist","+1 (905) 327-9221",,,,"10/28/2022 11:19:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18231","robert.thornton"
"Justin","Hebert","<EMAIL>","Development","Software Development Manager",,,,,"10/28/2022 12:53:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18280","justin.hebert"
"Shelly","Arsenault","<EMAIL>","Sales National","Sales Coordinator","+1 (647) 202-9000",,,,"10/28/2022 12:56:22 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18757","shelly.arsenault"
"EMR","Implementations","<EMAIL>","Implementations","Service Account",,,,"Service Account: PM's check that inbox routinely for faxes and or emails from client",,"<EMAIL>","S-1-5-21-**********-**********-*********-1458","Implementations"
"Craig","Hounsham","<EMAIL>","Technology","Jr Systems Administrator",,,,,"10/28/2022 6:33:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18760","craig.hounsham"
"Kevin","Kendall","<EMAIL>","Data and Software Architecture","Lead Site Reliability Engineer",,,,,"11/30/2021 10:18:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18837","kevin.kendall"
"Lucy","Montagnese","<EMAIL>","Implementations","eRx Adoption Specialist","+1 (705) 229-8260",,,,"10/26/2022 3:11:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-18225","lucy.montagnese"
"Jessica","Severiano","<EMAIL>","Implementations","Implementation Specialist","+1 (416) 937-8848",,,,"10/26/2022 10:23:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18865","jessica.severiano"
"Aaron","Hartnell","<EMAIL>","Development","Senior Software Developer",,,,,"10/24/2022 4:32:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17559","aaron.hartnell"
"EMR","Development Inbox","<EMAIL>","Development","Service Account",,,,"Service Account: central developer account for app stores and alerts","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-9650","optdev"
"Nicol","Solomonides","<EMAIL>","Revenue Management","Sr Manager, Revenue Management","+1 (778) 363-6654",,,,"10/27/2022 6:57:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17495","nicol.solomonides"
"Erik","Adamson","<EMAIL>","Implementations","Technical Services Specialist","+1 (250) 717-7314",,,,"10/27/2022 1:45:58 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17441","erik.adamson"
"Zsolt","Kiss","<EMAIL>","Implementations","Data Analyst",,,,,"10/28/2022 10:16:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-17569","zsolt.kiss"
"Lisa","Gunnlaugson","<EMAIL>","Training","Training Manager","+1 (250) 801-7274",,,,"10/28/2022 8:39:21 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18459","lisa.gunnlaugson"
"Tim","Melmoth","<EMAIL>","Implementations","Director of Implementations","+1 (613) 408-0110",,,,"10/28/2022 11:52:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-17590","tim.melmoth"
"Amanda","Tubello","<EMAIL>","Product Management","Project Manager","+1 (778) 214-1940",,,,"10/27/2022 3:22:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17098","amanda.tubello"
"Sandra","Baker","<EMAIL>","Training","Training Team Lead","+1 (519) 280-0251",,,,"10/24/2022 4:23:08 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19729","sandra.baker"
"Alan","Zantingh","<EMAIL>","CHN East","Account Executive","+1 (416) 992-0574",,,,"10/24/2022 11:25:35 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18533","alan.zantingh"
"Nyel","English","<EMAIL>","Technology","Helpdesk Team Lead","+1 (250) 681-4603",,,,"10/18/2022 5:01:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20137","nyel.english"
"Judy","Zeeben","<EMAIL>","CHN West","Practice Consultant Manager, CHN West","+1 (250) 300-1125",,,,"10/21/2022 8:45:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20136","judy.zeeben"
"Kevan","Poeschek","<EMAIL>","Data Analysis","Data Systems Administrator",,,,,"10/27/2022 4:26:06 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20230","kevan.poeschek"
"Vicki","Henckel","<EMAIL>","Training","Trainer",,,,,"9/2/2022 3:50:28 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19896","vicki.henckel"
"Colleen","Piotrowski","<EMAIL>","Implementations","Senior Implementation Specialist","+****************",,,,"10/27/2022 2:26:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20274","colleen.piotrowski"
"Kyle","Newton","<EMAIL>","Development","Senior Software Developer",,,,,"10/27/2022 12:22:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20179","kyle.newton"
"qtmitelccm",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Service account on qtmitelcsm, also has scripts running with those credentials; Mitel Services","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-17022","qtmitelccm"
"Bryan","Bergen","<EMAIL>","Data and Software Architecture","Application Architect",,,,,"1/6/2021 3:27:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20228","bryan.bergen"
"Tawny","Rother","<EMAIL>","Implementations","Systems Integration Analyst","+****************",,,,"10/28/2022 8:49:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20341","tawny.rother"
"Chris","Roseberry","<EMAIL>","Data and Software Architecture","Junior Site Reliability Engineer","+****************",,,,"10/28/2022 12:12:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20413","chris.roseberry"
"Alison","Cooney","<EMAIL>","Product Management","Product Manager","+****************",,,,"8/9/2021 3:32:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19969","alison.cooney"
"Katherine","Awad","<EMAIL>","Product Operations","Senior Technial Writer","+****************",,,,"10/27/2022 9:37:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20036","katherine.awad"
"Scott","Chipman","<EMAIL>","Implementations","Senior Data Analyst",,,,,"10/27/2022 8:51:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19885","scott.chipman"
"Wayne","Bullock","<EMAIL>","Product Operations","Product Owner",,,,,"10/26/2022 1:04:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23106","wayne.bullock"
"Nancy","Chapeskie","<EMAIL>","Sales National","National Account Manager","+1 (519) 535-3110",,,,"10/28/2022 11:31:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19889","nancy.chapeskie"
"Kamran","Khan","<EMAIL>","Platform Integrations","Platform Integration Manager","+1 (416) 993-3144",,,,"10/28/2022 12:25:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23111","kamran.khan"
"Pavan","Brar","<EMAIL>","Human Resources","Human Resources Manager","+1 (250) 689-1522",,,,"10/28/2022 1:18:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20480","pavan.brar"
"Curtis","Rose","<EMAIL>","Development","Software Development Manager",,,,,"10/27/2022 1:01:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20334","curtis.rose"
"Kevin","Koehler","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 11:00:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20436","kevin.koehler"
"Shelley","Watson","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,"Finance","10/28/2022 10:57:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19922","shelley.watson"
,,,,"Service Account",,,,"Service Account: Corp IT - Account created by the Windows Azure Active Directory Sync tool with installation identifier '0e6855de790d47ebaefa653faf59f481' running on computer 'QTADFS2' configured to synchronize to tenant 'qhrtech.onmicrosoft.com'. This account must have directory replication permissions in the local Active Directory and write permission on certain attributes to enable Hybrid Deployment.","10/27/2022 3:44:59 AM",,"S-1-5-21-**********-**********-*********-20390","MSOL_0e6855de790d"
"Ryan","Wood","<EMAIL>","Marketing","Director of Product Marketing","+1 (250) 870-1770",,,,"10/5/2022 2:49:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20557","ryan.wood"
"Candus","Hunter","<EMAIL>","Marketing","Senior Demand Generation Manager","+1 (250) 469-1513",,,,"10/28/2022 12:48:42 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20538","candus.hunter"
"Steven","Mathers","<EMAIL>","Development","Principal Software Developer",,,,,"8/30/2022 7:21:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22192","Steven.Mathers"
"Cali","Rendulic","<EMAIL>","Revenue Management","Revenue Management Assistant Manager",,,,,"10/28/2022 1:03:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22157","cali.rendulic"
"Samuel","Bradford","<EMAIL>","Technology","Network Analyst","+1 (250) 212-2353",,,,"12/3/2021 10:22:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23188","samuel.bradford"
"Jeff","Wimmer","<EMAIL>","Human Resources","Director of Human Resources & Client Services",,,,,"10/28/2022 11:41:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23645","jeff.wimmer"
"Kelley","Mullen","<EMAIL>","Client Services","Client Services Analyst, Expert",,,,,"10/28/2022 10:35:46 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23124","kelley.mullen"
"Sam","Mullen","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"10/27/2022 10:43:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23121","sam.mullen"
"Felix","Lau","<EMAIL>","Technology","Systems Administrator","+1 (647) 465-0353",,,,"10/24/2022 11:42:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20503","felix.lau"
"Michal","Hoppe","<EMAIL>","Technology","Cloud Systems Administrator","+1 (778) 581-6979",,,,"10/28/2022 1:02:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20560","Michal.Hoppe"
"Preston","Cooper","<EMAIL>","Development","Software Developer",,,,,"10/27/2022 5:45:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23138","preston.cooper"
"Fan","Jin","<EMAIL>","Development","Senior Software Developer",,,,,"9/15/2020 9:05:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22171","Fan.Jin"
"Sudha","Verma","<EMAIL>","Development","Senior Software Developer",,,,,"10/27/2022 4:05:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23176","Sudha.verma"
"Steve","Bailey","<EMAIL>","Product Management","Product Manager",,,,,"10/27/2022 12:20:21 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23172","stephen.bailey"
"Chakks","Paramasivam","<EMAIL>","Development","Software Development Manager",,,,,"7/29/2022 3:41:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23648","chakks.paramasivam"
"Lucas","Shoesmith","<EMAIL>","Development","Senior Software Development Manager",,,,,"10/18/2022 12:46:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23632","lucas.shoesmith"
"Tanya","Winsor","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 12:47:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20559","tanya.winsor"
"Dejan","Gudjevski","<EMAIL>","Implementations","Business Analyst, Data Solutions",,,,,"9/13/2022 11:22:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23143","dejan.gudjevski"
"Alison","Moore","<EMAIL>","IT","GRC Analyst","+1 (250) 718-5687",,,,"4/14/2022 8:32:11 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23620","alison.moore"
"bomgarIC",,,,"Service Account",,,,"Service Account: Corp IT - User for Bomgar Integration Client Scheduler service running on QTAdmin1","9/30/2022 2:51:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23635","bomgarIC"
"Ted","Sorensen","<EMAIL>","Development","Senior Software Developer",,,,,"10/26/2022 2:25:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20572","ted.sorensen"
"Scott","Johnston","<EMAIL>","Marketing","Marketing Web Developer",,,,,"4/14/2021 7:53:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22605","scott.johnston"
"Md","Mishu","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 1:25:11 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22618","Md.Mishu"
"David","Huang","<EMAIL>","Development","Software Developer",,,,,"10/24/2022 4:07:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22252","David.Huang"
"Scott","May","<EMAIL>","Technology","Junior Database Administrator","+1 (250) 878-5068",,,,"10/27/2022 2:44:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22217","scott.may"
"Andrew","McFadden","<EMAIL>","Development","Senior DevOps Engineer","+1 (250) 575-1878",,,,"10/27/2022 3:28:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23279","andrew.mcfadden"
"Parth","Bhatt","<EMAIL>","Sales National","Team Lead, Sales Engineering","+1 (416) 668-4153",,,,"11/23/2020 9:47:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20574","parth.bhatt"
"Adam","Peacock","<EMAIL>","Product Operations","Lead Support Coordinator",,,,,"7/15/2021 6:59:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23236","adam.peacock"
"Oniel","Wilson","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (778) 392-5640",,,,"9/19/2022 2:04:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23210","Oniel.Wilson"
"Melissa","DeLeon","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 10:08:37 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23231","melissa.DeLeon"
"Chris","Spinov","<EMAIL>","Client Services","Manager, Client Experience",,,,,"9/29/2022 12:34:53 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23233","Chris.spinov"
"Cole","Senger","<EMAIL>","Product Operations","Product Owner",,,,,"10/26/2022 4:19:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23208","cole.senger"
"Kevin","Rosal","<EMAIL>","Technology","Cloud Systems Administrator","+1 (250) 718-6717",,,,"9/21/2022 4:11:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22603","Kevin.rosal"
"Daniel","Moon","<EMAIL>","Client Services","Manager, Client Services Operations",,,,,"10/27/2022 12:17:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23234","Daniel.Moon"
"Nina","Chnek","<EMAIL>","Development","QA Analyst",,,,,"10/28/2022 12:10:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23711","nina.chnek"
"Dave","Munday","<EMAIL>","Client Services","Client Services Analyst, Expert",,,,,"10/28/2022 9:38:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23209","dave.munday"
"Kyle","Somogyi","<EMAIL>","Development","Software Development Manager",,,,,"12/3/2019 11:35:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20595","kyle.somogyi"
"Stefan","Richardson","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 10:59:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22615","stefan.richardson"
"Veronika","Havelkova","<EMAIL>","Marketing","Graphic Designer",,,,,"9/3/2020 12:22:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23804","veronika.havelkova"
"Stephen","Dobrozsi","<EMAIL>","Development","Senior QA Analyst",,,,,"10/28/2022 12:53:45 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22617","Stephen.Dobrozsi"
"Jesse","Pasos","<EMAIL>","Development","Software Developer",,,,,"10/25/2022 2:44:20 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22244","jesse.pasos"
"Amelia","Lang","<EMAIL>","Development","Software Development Manager",,,,,"10/28/2022 12:46:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23232","Amelia.Lang"
"Corey","Doty","<EMAIL>","Data and Software Architecture","Lead Site Reliability Engineer","250-859-1472",,,,"10/27/2022 3:51:44 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23276","corey.doty"
"Mychal","Hackman","<EMAIL>","Development","Lead Software Developer",,,,,"10/28/2022 9:12:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22259","mychal.hackman"
"Graeme","Mcivor","<EMAIL>","Implementations","Technical Services Specialist.","+1 (250) 869-6114",,,,"10/27/2022 1:24:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23237","Graeme.Mcivor"
"Richelle","Ferguson","<EMAIL>","Marketing","Senior Marketing Manager",,,,,"10/27/2022 4:06:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23813","richelle.ferguson"
"Deanna","Gourley","<EMAIL>","Administration","Office Assistant",,,,,"9/19/2021 10:55:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23300","deanna.gourley"
"Sofi","Mondesir","<EMAIL>","Implementations","Implementer","+1 (416) 892-8059",,,,"10/7/2022 12:18:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23810","sofi.mondesir"
"Joanne","Spatola","<EMAIL>","Marketing","Lead Generation Specialist - Team Lead",,,,,"4/5/2022 1:43:22 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22657","joanne.spatola"
"Odette","Roy","<EMAIL>","Data and Software Architecture","Application Architect",,,,,"10/26/2022 1:49:35 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22692","odette.roy"
"Liam","Anderson","<EMAIL>","Development","Senior Software Development Manager",,,,,"2/9/2021 1:01:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23825","liam.anderson"
"Christine","Karpinsky","<EMAIL>","Training","Instructional Designer",,,,,"10/28/2022 11:31:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22672","christine.karpinsky"
"Preet","Kainth","<EMAIL>","Technology","Systems Administrator","+1 (647) 529-5656",,,,"10/28/2022 11:30:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22299","preet.kainth"
"Iram","Hussain","<EMAIL>","Client Services","Client Experience Team Lead",,,,,"10/28/2022 1:11:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22673","iram.hussain"
"Tim","Sylvester","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"9/27/2022 6:21:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22675","tim.sylvester"
"Carminda","Fernandez","<EMAIL>","Implementations","Business Analyst, Data Solutions",,,,,"10/28/2022 12:59:05 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23296","carminda.fernandez"
"Carson","Milligen","<EMAIL>","Development","Software Development Manager",,,,,"10/13/2022 10:56:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23817","carson.milligen"
"Darcy","Senger","<EMAIL>","Marketing","Production Manager",,,,,"10/5/2020 8:14:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22646","darcy.senger"
"Demetri","Tsoycalas","<EMAIL>","Data Analysis","Data Analyst",,,,,"10/28/2022 7:26:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20414","demetri.tsoycalas"
"Justin","Harrington","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (416) 527-2301",,,,"10/17/2022 4:58:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22667","justin.harrington"
"Paolo","Aquino","<EMAIL>","Data and Software Architecture","Software Architecture Manager",,,,,"10/28/2022 8:25:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23293","paolo.aquino"
"Jessica","Burtney","<EMAIL>","CHN East","Practice Consultant","+1 (416) 801-3759",,,,"10/27/2022 12:06:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22663","jessica.burtney"
"Jeffrey","Bell","<EMAIL>","Technology","Systems Specialist",,,,,"10/21/2022 1:12:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22662","jeffrey.bell"
"Nicolas","Wourms","<EMAIL>","Development","Software Development Manager",,,,,"10/28/2022 1:10:15 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22283","nicolas.wourms"
"Rohith","Mannem","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 12:09:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23301","rohith.mannem"
"Thomas","Laehren","<EMAIL>","Product Operations","Product Owner",,,,,"10/28/2022 8:03:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23815","thomas.laehren"
"Graham","Pomfret","<EMAIL>","Implementations","eRx Adoption Specialist",,,,,"10/28/2022 6:33:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22683","graham.pomfret"
"Cara","Dwyer","<EMAIL>","Implementations","Senior Business Analyst, Data Solutions","905-449-4851",,,,"10/28/2022 12:13:10 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22681","cara.dwyer"
"Nick","Janzen","<EMAIL>","Technology","Manager, Corporate IT","+1 (403)-809-5584",,,,"3/1/2021 6:49:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22685","nick.janzen"
"Sergiu","Barsa","<EMAIL>","Development","Senior Software Developer",,,,,"10/25/2022 11:32:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22337","sergiu.barsa"
"Sviatlana","Vinnikava","<EMAIL>","Development","QA Analyst",,,,,"10/28/2022 7:23:23 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22338","sviatlana.vinnikava"
"Danielle","Semple","<EMAIL>","CHN East","Practice Consultant","+1 (416) 992-3873",,,,"10/24/2022 7:18:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22342","danielle.semple"
"Jo","Yoshida","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 7:05:28 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22344","jo.yoshida"
"James","Michaud","<EMAIL>","Development","Senior Software Developer",,,,,"5/6/2022 6:59:30 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22757","james.michaud"
"Reilly","Harper","<EMAIL>","Client Services","Client Services Team Lead, Senior","+1 (236) 457-5829",,,,"10/21/2022 1:30:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23391","reilly.harper"
"Taylor","Drescher","<EMAIL>","Technology","Information Security Officer","+1 (250) 859-4524",,,,"10/24/2022 10:17:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23398","taylor.drescher"
"Rakesh","Jammula","<EMAIL>","Development","Senior QA Analyst",,,,,"9/9/2022 10:00:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23323","rakesh.jammula"
"Yu Zhi","Xing","<EMAIL>","Development","Software Developer",,,,,"9/22/2020 4:07:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23399","yuzhi.xing"
"Lubna","Shahid","<EMAIL>","Implementations","Associate Project Manager",,,,,"6/23/2021 12:01:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23343","lubna.shahid"
"Shabnam","Ahmmed","<EMAIL>","Development","Senior QA Analyst",,,,,"10/28/2022 6:38:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23848","shabnam.ahmmed"
"Joshua","Abaloyan","<EMAIL>","Development","Lead Software Developer",,,,,"10/24/2022 8:41:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22721","joshua.abaloyan"
"Ryan","Cotter","<EMAIL>","Product Operations","Senior Product Operations Manager",,,,,"10/28/2022 1:05:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22320","ryan.cotter"
"Sam","Bassett","<EMAIL>","Development","Senior Software Developer",,,,,"12/10/2021 2:23:41 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22707","sam.bassett"
"Shannon","Parent","<EMAIL>","Training","Trainer","+1 (807) 861-0612",,,,"10/13/2022 12:29:01 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22720","shannon.kennelly"
"Heather","Gardiner","<EMAIL>","Client Services","Client Services Knowledge and Information Specialist",,,,,"10/27/2022 5:55:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22718","heather.gardiner"
"Raquel","Teixeira","<EMAIL>","Implementations","Lead Systems Integration Analyst","+1 (647) 355-4781",,,,"10/27/2022 1:56:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22711","raquel.teixeira"
"Carly","Rigg","<EMAIL>","Product Operations","Associate Product Owner",,,,,"10/26/2022 1:06:27 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22713","carly.rigg"
"Kendre","Roseberry","<EMAIL>","Training","Instructional Designer",,,,,"10/28/2022 12:47:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23350","kendre.scott"
"Holli","Gordon","<EMAIL>","Training","Training Solutions Designer",,,,,"6/7/2022 2:57:05 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23347","holli.gordon"
"Kailyn","Pederson","<EMAIL>","Product Development","Product Owner",,,,,"10/13/2022 9:03:39 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23388","kailyn.pederson"
"Ashika","Balakrishnan","<EMAIL>","Implementations","Project Coordinator",,,,,"10/28/2022 10:49:23 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23904","ashika.balakrishnan"
"Divya","Chhabra","<EMAIL>","Implementations","Senior Systems Integration Analyst",,,,,"9/28/2022 5:05:05 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22710","divya.chhabra"
"Richard","Welsh","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 8:28:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22348","richard.welsh"
"Paige","O'Hearn","<EMAIL>","CHN East","Practice Consultant",,,,,"10/28/2022 10:40:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23903","paige.ohearn"
"Sienna","Kohn","<EMAIL>","Product Operations","Lead Technical Writer",,,,,"10/27/2022 4:05:28 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23392","sienna.kohn"
"Megan","Owens","<EMAIL>","Implementations","Implementation Consultant","+1 (403) 796-7460",,,,"10/17/2022 1:56:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22733","megan.owens"
"Mark","Coutts","<EMAIL>","Implementations","Data Analyst",,,,,"10/26/2022 5:28:56 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22758","mark.coutts"
"Karley","Davis","<EMAIL>","Client Services","Enterprise Relationship Analyst","+1 (250) 864-4295",,,,"10/27/2022 5:57:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23349","karley.davis"
"Richard","Millard","<EMAIL>","Development","Senior Software Developer",,,,,"7/6/2022 4:30:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23396","richard.millard"
"Jonathan","Chapman","<EMAIL>","Product Management","Project Manager","+1 (250) 469-2926",,,,"10/28/2022 1:25:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23402","jonathan.chapman"
"Randy","Lewis","<EMAIL>","Quality Assurance","QA Analyst",,,,,"10/27/2022 2:00:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23390","randy.lewis"
"Zohra","Charaniya","<EMAIL>","CHN East","Practice Consultant","+1 (416) 435-8524",,,,"6/17/2021 9:49:06 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22725","zohra.charaniya"
"Srinivas","Vemulapalli","<EMAIL>","Development","Software Developer",,,,,"10/7/2022 12:16:47 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22331","srinivas.vemulapalli"
"Steve","Lewis","<EMAIL>","Development","Senior BI Developer",,,,,"10/28/2022 11:28:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22787","steve.lewis"
"Sharlene","Quinn","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/28/2022 10:36:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23417","sharlene.quinn"
"Caitlin","Slavik","<EMAIL>","Client Services","Senior Manager, Client Services","+1 (250) 808-8699",,,,"10/28/2022 12:54:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22413","caitlin.slavik"
"Christina","Bye","<EMAIL>","Revenue Management","Enterprise Revenue Manager",,,,,"10/28/2022 9:15:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22414","christina.bye"
"Shannon","Nebert","<EMAIL>","Product Operations","Support Coordinator",,,,,"7/15/2022 12:36:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22419","shannon.nebert"
"Sally","Nimmo","<EMAIL>","Development","Software Developer",,,,,"9/29/2022 8:21:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22430","sally.nimmo"
"Steve","Forsythe","<EMAIL>","Implementations","Project Coordinator",,,,,"10/28/2022 11:02:02 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23955","steve.forsythe"
"David","Smekal","<EMAIL>","Development","Software Developer",,,,,"10/25/2022 1:24:35 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22816","david.smekal"
"Malcolm","Kennedy","<EMAIL>","Technology","Citrix Administrator","+1 (250) 859-6318",,,,"10/28/2022 9:10:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23405","malcolm.kennedy"
"Damian","Hamilton","<EMAIL>","Development","Software Developer",,,,,"10/11/2022 10:51:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23464","damian.hamilton"
"Mallory","Conn","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"10/28/2022 11:59:06 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22452","mallory.conn"
"Brian","Matte","<EMAIL>","Development","Junior Software Developer",,,,,"10/26/2022 5:19:19 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23960","brian.matte"
"Levi","Miller","<EMAIL>","Development","Software Developer",,,,,"3/17/2022 12:17:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22428","levi.miller"
"Eliana","Wardle","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 9:08:58 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23961","eliana.wardle"
"Cassandra","Rose","<EMAIL>","Development","QA Manager",,,,,"10/28/2022 1:01:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22446","cassandra.rose"
"Brett","Evans","<EMAIL>","Development","QA Manager",,,,,"8/17/2022 6:18:38 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23463","brett.evans"
"Brad","Fuller","<EMAIL>","Development","QA Analyst",,,,,"10/28/2022 12:37:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23959","brad.fuller"
"Dan","Thiessen","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 4:32:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23953","dan.thiessen"
"Lyndsay","Mokonen","<EMAIL>","Development","Junior QA Analyst",,,,,"10/28/2022 7:20:50 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22460","lyndsay.mokonen"
"Bib","Patel","<EMAIL>","Revenue Management","Special Projects Manager, Revenue Management",,,,,"10/28/2022 9:34:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22445","bib.patel"
"Abhishek","Dutta","<EMAIL>","Development","QA Manager",,,,,"10/27/2022 4:56:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23949","abhishek.dutta"
"Parker","Burns","<EMAIL>","Client Services","Client Services Training Lead",,,,,"10/28/2022 10:30:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22719","parker.burns"
"Brittany","Koehler","<EMAIL>","Client Services","Client Services Team Lead",,,,,"10/1/2021 4:55:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22412","brittany.koehler"
"Katie","Light","<EMAIL>","IT","GRC Analyst","+****************",,,,"10/27/2022 2:31:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22450","katie.light"
"Anett","Kalmanczhey","<EMAIL>","Client Services","Client Services Team Lead, Senior",,,,,"10/28/2022 8:19:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22410","anett.kalmanczhey"
"Ryan","Prevost","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/28/2022 11:51:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22456","ryan.prevost"
"Carlene","Williams","<EMAIL>","Implementations","Systems Integration Analyst","+1 (416) 574-4035",,,,"4/29/2022 12:28:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23458","carlene.williams"
"Christina","VandenBrink","<EMAIL>","Implementations","Associate Project Manager",,,,,"10/28/2022 12:15:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22415","christina.vandenbrin"
"Courtney","Stokman","<EMAIL>","Human Resources","Human Resources Business Partner","+1 (250) 681-3669",,,,"10/28/2022 1:19:11 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23448","courtney.stokman"
"Crystal","Benoit","<EMAIL>","IT","GRC Team Lead","+1 (250) 469-2175",,,,"10/27/2022 2:29:53 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23952","crystal.benoit"
"Divya","Manyala","<EMAIL>","Product Management","Product Manager",,,,,"1/24/2022 1:46:58 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23446","divya.manyala"
"Punita","Gosar","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 10:32:33 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23465","punita.gosar"
"QHR backup","smtp","<EMAIL>",,"Service Account",,,,"Service Account: Tech Infrastructure - Backup notifications to C. Hounsham (SF Case ********)",,"<EMAIL>","S-1-5-21-**********-**********-*********-23228","qhrbackupsmtp"
"QHR Technologies IT",,"<EMAIL>",,"Service Account",,,,"Service Account: Corp IT - Contact for IT assistance",,"<EMAIL>","S-1-5-21-**********-**********-*********-13309","IT"
"David","Braaten","<EMAIL>","Development","Lead Software Developer",,,,".","10/24/2022 2:42:15 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23449","david.braaten"
"Greg","Harpell","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","General visibility into documentation related to both initiatives",,"<EMAIL>","S-1-5-21-**********-**********-*********-23407","greg.harpell"
"Alan","McNaughton","<EMAIL>","Technology","Director of Technology Operations","+1 (403) 805-9002",,,,"10/4/2022 1:48:06 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22829","alan.mcnaughton"
"Jolanda","Kondrak","<EMAIL>","Marketing","Marketing Manager","+1 (250) 826-3866",,,,"10/27/2022 8:58:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23478","jolanda.kondrak"
"KT","Nguyen","<EMAIL>","Development","Software Developer",,,,,"10/27/2022 3:17:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23987","kt.nguyen"
"Sandeep","Singh","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 8:32:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23994","sandeep.singh"
"Carson","Judd","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 12:52:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22847","carson.judd"
"Tricia","Nason","<EMAIL>","Implementations","Implementer","+1 (902) 240-0811",,,,"10/21/2022 6:02:39 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23497","tricia.nason"
"Chris","Bremmer","<EMAIL>","Data and Software Architecture","Database Architect",,,,,"10/28/2022 11:01:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23998","Chris.Bremmer"
"Gee Mary","Tan","<EMAIL>","Development","Senior QA Analyst",,,,,"10/28/2022 6:25:40 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23477","geemary.tan"
"Mark","Ramsden","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"10/28/2022 12:27:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23481","mark.ramsden"
"Yuliya","Voytsekhivska","<EMAIL>","Development","Lead QA Analyst",,,,,"10/28/2022 11:02:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23483","yuliya.voytsekhivska"
"Ryan","Kleiber","<EMAIL>","Development","Lead QA Analyst",,,,,"10/27/2022 2:47:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24011","ryan.kleiber"
"Stacey","Tovey","<EMAIL>","Implementations","Senior Data Analyst",,,,,"10/27/2022 11:43:44 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23823","stacey.tovey"
"Tanya","Peixoto","<EMAIL>","Implementations","Implementer","+1 (416) 801-7445",,,,"10/28/2022 6:12:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24018","tanya.peixoto"
"Rebekka","Augustine","<EMAIL>","Marketing","Marketing Content Writer",,,,,"10/25/2022 3:59:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23513","rebekka.augustine"
"Mohammad","Kandy","<EMAIL>","Technology","Cloud Systems Administrator",,,,,"9/22/2022 1:18:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23516","mohammad.kandy"
"Sara","Burgess","<EMAIL>","Technology","Knowledge and Information Lead","+1 (250) 870-7621",,,,"6/18/2021 8:53:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23515","sara.burgess"
"Ashley","DeLaney","<EMAIL>","Training","Instructional Designer","+1 (403) 463-8346",,,,"5/6/2022 10:47:02 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24027","ashley.delaney"
"Lorenn","Floor","<EMAIL>","Product Operations","Associate Product Owner",,,,,"6/28/2022 11:22:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24031","lorenn.floor"
"Harleen","Kohli","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"10/27/2022 8:31:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24043","harleen.kohli"
"Megan","Bowker","<EMAIL>","Product Operations","Project Coordinator",,,,,"10/27/2022 2:17:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24044","megan.bowker"
"Nathan","Poehlke","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"10/27/2022 2:27:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24045","nathan.poehlke"
"Rick","Poor","<EMAIL>","Client Services","Client Services Team Lead, Senior","+1 (250) 470-9636",,,,"10/21/2022 3:13:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24047","rick.poor"
"Jocelyn","Smith","<EMAIL>","Product Management","Senior UX Researcher",,,,,"10/26/2022 2:35:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23517","jocelyn.smith"
"Davena","Singh","<EMAIL>","Product Management","Senior Product Manager","+1 (416) 708-6649",,,,"10/11/2022 9:57:18 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24038","davena.singh"
"Rene","Kabis","<EMAIL>","Development","Intermediate Software Developer",,,,,"10/28/2022 8:59:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22505","rene.kabis"
"Jill","Sprinkling","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/28/2022 10:07:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24029","jill.sprinkling"
"Charisse","Abaloyan","<EMAIL>","Client Services","Client Experience Analyst, Senior",,,,,"10/26/2022 12:09:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24041","charisse.abaloyan"
"Ryan","Yakiwchuk","<EMAIL>","Product Operations","Senior Product Owner",,,,,"7/7/2020 8:42:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23514","ryan.yakiwchuk"
"Stephane","Chan","<EMAIL>","Implementations","Data Analyst",,,,,"10/28/2022 10:45:29 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22507","stephane.chan"
"Dave","Anderson","<EMAIL>","Implementations","Specialist, SQL",,,,,"10/28/2022 1:04:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22510","dave.anderson"
"Jake","Redekop","<EMAIL>","Product Development","Senior Product Operations Manager",,,,,"3/23/2020 12:43:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24055","jake.redekop"
"Liane","Blake","<EMAIL>","Revenue Management","Accounts Receivable & Collections Administrator",,,,,"10/28/2022 7:50:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22540","liane.blake"
"Tamika","Leslie","<EMAIL>","Product Operations","Product Owner",,,,,"10/27/2022 4:45:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23550","tamika.leslie"
"Alex","Shaw","<EMAIL>","Development","Software Developer",,,,,"10/25/2022 4:21:52 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23563","alex.shaw"
"Jessica","Wright","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/28/2022 7:48:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23569","jessica.wright"
"Hong","He","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 8:21:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23578","hong.he"
"Phil","Campbell","<EMAIL>","Data and Software Architecture","Application Architect",,,,,"1/25/2022 2:21:05 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23579","phil.campbell"
"Sami","Valkama","<EMAIL>","Product Operations","PMO Manager","+1 (250) 575-9376",,,,"10/27/2022 3:14:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23571","sami.valkama"
"Spencer","Shupe","<EMAIL>","Development","Junior Software Developer",,,,,"1/10/2022 12:37:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23545","spencer.shupe"
"Becca","Hembling","<EMAIL>","Development","Software Developer",,,,,"8/25/2022 2:32:56 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23564","becca.hembling"
"Tawfiq","Menad","<EMAIL>","Development","Software Developer",,,,,"1/18/2021 9:10:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23549","tawfiq.menad"
"Mingyuan","Yang","<EMAIL>","Development","QA Analyst",,,,,"10/21/2022 5:28:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23570","mingyuan.yang"
"OXIDIZEDSA",,,,"Service Account",,,,"Service Account: Networking backups and scripts","1/14/2022 3:10:15 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23534","OXIDIZEDSA"
"Rodney","Earl","<EMAIL>","Technology","Application Analyst, Financial Systems","250-826-0405",,,,"10/28/2022 12:41:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22917","rodney.earl"
"Jeff","Brown","<EMAIL>","Infrastructure","Solution Architect",,,,,"2/4/2020 3:33:27 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22919","jeff.brown"
"Victoria","Philips","<EMAIL>","Technology","Technical Writer","+1 (250) 870-1528",,,,"8/31/2022 11:40:18 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22563","victoria.philips"
"Brandon","Unger","<EMAIL>","Development","Software Developer",,,,,"10/27/2022 2:51:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22565","brandon.unger"
"Butch","Albrecht","<EMAIL>","Technology","Sr. Manager Enterprise Architecture & Cloud Services",,,,,"3/15/2022 1:28:28 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22566","butch.albrecht"
"Chase","Jensen","<EMAIL>","Development","Lead Software Developer",,,,,"10/13/2022 1:00:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22661","chase.jensen"
"Shubham","Malik","<EMAIL>","Operations","Privacy Analyst","+1 (250) 869-9189",,,,"2/9/2021 9:00:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22573","shubham.malik"
"Peter","Laudenklos","<EMAIL>","Technology","Virtual Infrastructure Administrator","+1 (403) 829-8214",,,,"10/26/2022 9:09:30 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23593","Peter.Laudenklos"
"Ina","Kebet","<EMAIL>","Technology","Procurement Specialist",,,,,"10/19/2022 7:47:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22576","ina.kebet"
"Jonathan","Dunville","<EMAIL>","Product Management","Launch Operations Manager",,,,,"10/27/2022 1:33:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22580","jonathan.dunville"
"Cintia","Schutt","<EMAIL>","Development","Junior Software Developer",,,,,"7/25/2022 3:58:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24108","cintia.schutt"
"Nishant","Vyas","<EMAIL>","Development","Junior Software Developer",,,,,"1/16/2021 3:15:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22559","nishant.vyas"
"Tyler","Cooney","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"10/27/2022 3:36:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22943","tyler.cooney"
"Liam","Shaw","<EMAIL>","Development","Junior Software Developer",,,,,"10/6/2022 4:46:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24112","liam.shaw"
"Chelsea","Stickney","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"10/25/2022 5:54:39 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20460","chelsea.stickney"
"Vanessa","Stembridge","<EMAIL>","Training","Training Content Developer",,,,,"10/28/2022 12:30:45 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22585","vanessa.stembridge"
"Lindsay","Bronskill","<EMAIL>","Implementations","Senior Implementation Specialist","+1 (403) 472-1772",,,,"10/26/2022 11:38:29 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24606","lindsay.bronskill"
"James","Koss","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 1:01:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24127","james.koss"
"Aditya Kumar","Pothana","<EMAIL>","Development","QA Analyst",,,,,"4/4/2022 3:34:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24128","adityakumar.pothana"
"Elise","Richardson","<EMAIL>","Client Services","Senior Enterprise Relationship Analyst",,,,,"10/25/2022 8:17:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24123","elise.richardson"
"Chris","Heiss","<EMAIL>","Client Services","Senior Client Services Analys",,,,,"10/4/2021 1:29:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24124","chris.heiss"
"Taylor","Floor","<EMAIL>","Implementations","Trainer",,,,,"9/7/2022 6:28:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22587","taylor.floor"
"Vrinda","Monga","<EMAIL>","Development","Senior QA Analyst",,,,,"7/21/2022 4:31:22 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22951","vrinda.monga"
"Ken","Royea","<EMAIL>","Implementations","Senior Business Analyst, Data Solutions",,,,,"10/28/2022 8:55:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22597","ken.royea"
"Matt","Prices","<EMAIL>",,,,,,"Matt from Price's Alarms for Bomgar access to QTKantech VM","11/4/2019 2:41:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23361","matt.prices"
"Andreas","Niemoeller","<EMAIL>","Development","Senior Software Developer",,,,,"10/4/2022 9:49:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-25106","andreas.niemoeller"
"Jordan","Wong","<EMAIL>","Client Services","Client Services Team Lead","+1 (250) 808-7173",,,,"9/1/2021 10:46:03 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22594","jordan.wong"
"Denis","Ivanov","<EMAIL>","Revenue Management","Revenue Management Clerk",,,,,"10/27/2022 1:10:30 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24134","denis.ivanov"
"Rowell","Selvano","<EMAIL>","Implementations","Data Analyst",,,,"Data Analysis","10/28/2022 12:37:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24627","rowell.selvano"
"Andrey","Fedorov","<EMAIL>","Product Operations","Technical Writer",,,,,"10/28/2022 12:57:21 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24160","andrey.fedorov"
"Michael","Jacobs","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"10/13/2022 4:57:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24169","michael.jacobs"
"Kirk","Calvin","<EMAIL>","Implementations","Systems Integration Analyst","2508596912",,,,"10/28/2022 7:15:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22988","kirk.calvin"
"Drew","Hawken","<EMAIL>","Data and Software Architecture","Lead Application Architect",,,,,"9/21/2020 9:58:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22989","drew.hawken"
"Patrick","Nguyen","<EMAIL>",,"LCL - Senior Manager Internal Audit Services",,"Carly Innes","Carly Innes","LCL - IT Internal Audit Manager",,"<EMAIL>","S-1-5-21-**********-**********-*********-24156","patrick.nguyen"
"James","Daniell","<EMAIL>","Product Operations","Senior Product Operations Manager",,,,,"10/28/2022 1:03:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-25129","james.daniell"
"Meera","Babu","<EMAIL>","Quality Assurance","QA Analyst",,,,,"9/21/2022 3:41:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24171","meera.babu"
"Jane","Auyeung","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","ePrescribe Roadmap Review, Freedom Release Process, Document Sharing","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-25124","jane.auyeung"
"Trevor","Trainee","<EMAIL>",,"Service Account",,,,"Service Account:  HR - Used for Training Video/Document",,"<EMAIL>","S-1-5-21-**********-**********-*********-24176","trevor.trainee"
"Tyler","Cossentine","<EMAIL>","Development","Senior Software Developer",,,,,"10/13/2022 7:58:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22996","tyler.cossentine"
"SQL-osqasql1",,,,"Service Account",,,,"Service Account: QA - SQL Service Account","3/23/2021 5:01:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-18681","sql-osqasql1"
"VMware Reporting","Service Account",,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","10/28/2022 1:24:10 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23835","vmwarereport"
"econnect",,,,"Service Account",,,,"Service Account: Finance - for Dynamics running on qtfin3","9/15/2022 2:40:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-23165","econnect"
"ActionTec",,,,"Service Account",,,,"Service Account: User required for wireless connection of ActionTec devices to ""Quadrant Network""",,"<EMAIL>","S-1-5-21-**********-**********-*********-22489","actiontec"
"jenkins",,,,"Service Account",,,,"Service Account: Development/QA - QA Test Account","3/21/2021 4:59:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-19879","jenkins"
"SQL-OSDEVSQL2012",,,,"Service Account",,,,"Service Account: Development - SQL Service Account; used to run OSDEVSQL2012\OSDEVSQL2012
OSDEVSQL2012\MSSQL2014
and OSDEVSQL2012\MSSQL2016
MSSQL instances.","8/10/2022 8:26:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22629","SQL-OSDEVSQL2012"
"Mimecast","LDAPS Sync",,,"Service Account",,,,"Service Account: Security - Secure LDAP account for Mimecast",,"<EMAIL>","S-1-5-21-**********-**********-*********-19814","mimecastsync"
"Splunk","Alerts",,,"Service Account",,,,"Service Account: Pprovide Alerts generated from Splunk service.",,"<EMAIL>","S-1-5-21-**********-**********-*********-22911","splunk.alerts"
"GPeconnect",,,,"Service Account",,,,"Service Account: Finance - Dynamics service account used on qtfin3",,"<EMAIL>","S-1-5-21-**********-**********-*********-23840","GPeconnect"
"SQL-QTVersionOne",,,,"Service Account",,,,"Service Account: Development - SQL Server account for VersionOne. Legacy system - replaced by JIRA - still referred to.","3/4/2021 3:03:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-19680","sql-qtversionone"
"Nirmol","Bajwa","<EMAIL>","Quality Assurance","Junior QA Analyst","+1 (250) 870-1466",,,,"10/27/2022 3:05:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24665","nirmol.bajwa"
"Ashley","Taron","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"10/25/2022 4:57:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24676","ashley.taron"
"Cecilia","McEachern","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"10/27/2022 8:16:18 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24675","cecilia.mceachern"
"Paige","Morelli","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"10/27/2022 11:56:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24666","paige.morelli"
"Donovan","Rogall","<EMAIL>","Development","Software Development Manager",,,,,"6/30/2021 9:14:04 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24677","donovan.rogall"
"sftp","service",,,"Service Account",,,,"Service Account: Corp IT  - Used to mount shares on qtsftp1. Also used by newer Cerberus SFTP service (on QTSFTP1982 that never made it to production) ","4/1/2021 3:01:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-20262","sftp"
"KonicaMinolta",,"<EMAIL>",,"Service Account",,,,"Service Account: LDAP & Email (for scans)","6/30/2020 9:42:15 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24092","konicaminolta"
"rhel-virt-who",,,,"Service Account",,,,"Service Account: RHEL Services Account","10/28/2022 1:01:29 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22598","rhel-virt-who"
"Commvault","vCenter User - cvvcuser",,,"Service Account",,,,"Service Account: Commvault vCenter User","10/28/2022 1:15:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22544","cvvcuser"
"JiraConfshare",,,,"Service Account",,,,"Service Account: Corp IT - Sync account between JIRA servers","10/28/2022 3:47:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22590","JiraConfshare"
"Kace","User",,,"Service Account",,,,"Service Account: Corp IT - Read only user for LDAP imports (do NOT delete)","1/13/2021 3:22:21 PM","<EMAIL>","S-1-5-21-**********-**********-*********-16673","KACE_User"
"SQL-EMRDATASQL01",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2008R2
EMRDATASQL01\SQL2012
and EMRDATASQL01\SQL2017 MSSQL instances.","10/24/2022 1:02:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-22632","SQL-EMRDATASQL01"
"Dynamics MR","Service",,,"Service Account",,,,"Service Account: add-on to GP that provides/creates financial reports","10/27/2022 3:31:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-20096","mrsvc"
"Mitel","Recording",,,"Service Account",,,,"Service Account: Corp IT - User account used on qtmitelscreen and qtmitelcall for accessing shares on those servers","8/24/2022 12:02:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-22799","mitelrecording"
"Brad","Stel","<EMAIL>","Marketing","Marketing Communication Manager",,,,,"10/21/2022 12:40:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23001","brad.stel"
"Domain Admin","Taylor Drescher","<EMAIL>",,,,,,,"8/23/2022 11:35:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25149","da-tdrescher"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Service account for VMWare","10/28/2022 11:38:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-25154","vcenter-skyline"
,,,,"Service Account",,,,"Service Account: Tech Infrastructure - Read only Service account for VMWare","10/28/2022 1:21:57 PM","<EMAIL>","S-1-5-21-**********-**********-*********-23012","vcenter-ro"
"Test","Passwriteback","<EMAIL>",,,,,,"For Password Writeback and MDM testing Azure","9/15/2020 8:38:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24700","Testpw"
"Domain Admin","Craig Hounsham","<EMAIL>",,,,,,,"3/22/2022 12:18:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24706","da-chounsham"
"Steve","Logan","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","Freedom Release Process, Document Sharing",,"<EMAIL>","S-1-5-21-**********-**********-*********-24708","steve.logan"
"George","Papadogoulas","<EMAIL>",,,,"Rebecca Ferrie","Rebecca Ferrie","Freedom Release Process, Document Sharing",,"<EMAIL>","S-1-5-21-**********-**********-*********-24709","george.papadogoulas"
"Khaja","Imran","<EMAIL>","Quality Assurance","QA Analyst",,,,,"10/27/2022 7:35:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24712","khaja.imran"
"Peter","Zeng","<EMAIL>","Development","Junior Software Developer",,,,,"9/2/2022 6:15:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-25162","peter.zeng"
"Domain Admin -","Jeffrey Bell","<EMAIL>",,,,,,,"10/28/2022 11:03:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24717","da-jbell"
"Slava","Ravinsky","<EMAIL>","Development","Software Developer",,,,,"10/28/2022 11:36:04 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26130","slava.ravinsky"
"Mellissa","Senger","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"4/30/2022 7:00:03 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26139","mellissa.senger"
"Amanda","Easton","<EMAIL>","Product Operations","Support Coordinator",,,,,"10/27/2022 3:57:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26143","amanda.easton"
"Charity","Lebedoff","<EMAIL>","Administration","Office Assistant",,,,,"9/17/2021 10:50:08 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26148","Charity.Lebedoff"
"Chantal","Keizer","<EMAIL>","CHN East","Manager Practice Consultant, CHN East","+! (204) 872-4113",,,,"5/20/2022 8:31:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24745","Chantal.Keizer"
"Debra","Steiss","<EMAIL>","CHN Central","Practice Consultant","+****************",,,,"10/27/2022 12:05:21 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24746","Debra.Steiss"
"Erik","Holtom","<EMAIL>","Technology","Technical Writer",,,,,"10/25/2022 1:05:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26160","erik.holtom"
"Charisa","Flach","<EMAIL>","Data and Software Architecture","Business Intelligence Analyst",,,,,"10/26/2022 6:01:07 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24754","charisa.flach"
"Leane","King","<EMAIL>","CHN Central","Account Executive",,,,,"10/28/2022 12:16:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24760","Leane.King"
"Srini","Venkatraman","<EMAIL>","Development","Senior Software Developer",,,,,"9/25/2022 8:28:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24768","srini.venkatraman"
"Greg","Gabelmann","<EMAIL>","Development","Senior Software Development Manager",,,,,"10/28/2022 7:59:50 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24769","greg.gabelmann"
"Gloria","Alla","<EMAIL>","Implementations","Implementation Specialist",,,,,"2/7/2022 10:07:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26173","gloria.alla"
"Jerry","Chuang","<EMAIL>","Development","Junior Software Developer",,,,,"1/12/2022 3:54:21 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26176","jerry.chuang"
"Francisco","Rubio","<EMAIL>","Product Operations","Product Owner",,,,,"10/26/2022 3:58:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26185","francisco.rubio"
"Domain Admin -","Mohammad Kandy","<EMAIL>",,,,,,,"9/13/2021 12:09:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26188","da-mkandy"
"Thomas","Jaeger","<EMAIL>","Product Operations","Project Manager",,,,,"5/18/2021 5:21:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26189","thomas.jaeger"
"Ron","Protacio","<EMAIL>","Technology","Director, Cyber Security West Coast",,"SF: ********","SF: ********",,"5/27/2020 11:06:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26603","ron.protacio"
,,,,"Service Account",,,,"Service Account: AD account that it connects to its database with on infrasql where specific permissions are configured","10/28/2022 12:17:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26194","solarwindsdbservice"
"Tina","Steele","<EMAIL>","Implementations","Implementation Specialist","+1 (604) 354 2678",,,,"7/9/2021 10:06:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26605","tina.steele"
"Dharti","Narayan","<EMAIL>","CHN West","Account Executive","+1 (780) 221-7240",,,,"10/28/2022 1:01:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26608","dharti.narayan"
"Ellen","Doyle","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26196","ellen.doyle"
"Stephanie","Godin","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26197","Stephanie.Godin"
"Cindy","Bekkedam","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26198","Cindy.Bekkedam"
"Chantal","Hayes","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26199","Chantal.Hayes"
"Sophia","Khan","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26200","Sophia.Khan"
"Sophie","Koolen","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26201","Sophie.Koolen"
"Bianca","Santaromita","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26202","Bianca.Santaromita"
"Alison","Caron","<EMAIL>","Implementations","Virtual Care Specialist - LCL",,"Pavan Brar","Pavan Brar",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26203","alison.caron"
"Eric","Bauld","<EMAIL>","Development","Senior Software Developer",,,,,"3/31/2022 8:55:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26204","eric.bauld"
"Karanveer","Khanna","<EMAIL>","Development","Software Developer",,,,,"10/20/2022 2:14:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24793","karanveer.khanna"
"KantechEmail","Weekly Report",,,"Service Account",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26612","kantechemail"
"Padmalatha","Ragunathan","<EMAIL>",,"Microsoft Consultant",,"SF:********","SF:********",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24796","Padmalatha.Ragunatha"
"Mandy","Mann","<EMAIL>","Training","Trainer",,,,,"10/27/2022 12:20:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24798","mandy.mann"
"Manjit","Purewal","<EMAIL>",,"LCL- Senior Manager Internal Audit Services",,"SF:********","SF:********",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26207","Manjit.Purewal"
"Stephanie","Wright","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"10/28/2022 8:02:29 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26211","stephanie.wright"
"Nadia","Hussain","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"10/27/2022 7:12:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26213","nadia.hussain"
"Rachel","Klein","<EMAIL>","Marketing","Senior Product Designer",,,,,"5/3/2021 8:53:06 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26215","rachel.klein"
"Brooke","Laing","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"8/27/2021 2:50:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24804","brooke.laing"
"Jaspreet","Sangha","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/18/2021 2:34:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24805","jaspreet.sangha"
"Ashley","Farrell","<EMAIL>","Human Resources","Learning & Development Specialist",,,,,"5/17/2022 3:19:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24809","ashley.farrell"
"Tona","Mutimura","<EMAIL>",,"LCL - Audit Services",,"Carly.Innes","Carly.Innes",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24811","Tona.Mutimura"
"Jarrid","Pond","<EMAIL>","Technology","Helpdesk Analyst","+1 (778) 392-7070",,,"+1 (778) 738 3327","10/28/2022 12:55:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26620","jarrid.pond"
"Mark","Devries","<EMAIL>","Development","Senior Software Developer",,,,,"10/18/2022 2:13:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26629","Mark.Devries"
"James","Greenwood","<EMAIL>",,,,"Colleen Safinuk","Colleen Safinuk",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26630","james.greenwood"
"CS","Training",,"CS","Service Account",,,,"Client Services, used for booking training for employees.",,"<EMAIL>","S-1-5-21-**********-**********-*********-26631","cs.training"
"Pritpal","Garcha","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 1:01:03 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26235","Pritpal.Garcha"
"Intune","NDES",,,"Service Account",,,,"Service Account : Corp IT-NDES to enroll certificates from Certificate Authority to AADJ Devices","10/17/2021 7:24:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24813","IntuneNDES"
"David","Rivard","<EMAIL>","Development","Junior Software Developer",,,,,"10/24/2022 1:17:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26239","david.rivard"
"Jordan","Pinske","<EMAIL>","Technology","IT Helpdesk Analyst","+1 (250) 317-6996",,,,"10/28/2022 1:16:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26245","jordan.pinske"
"Domain Admin -","Butch Albrecht","<EMAIL>",,,,,,,"12/16/2020 11:48:48 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24838","da-balbrecht"
"Janelle","Prejet","<EMAIL>","CHN Central","Practice Consultant","+! (204) 292-1286",,,,"10/28/2022 12:21:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24839","janelle.prejet"
"Lemuel","Caldito","<EMAIL>","Development","Software Developer",,,,,"10/21/2022 5:28:45 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24840","lemuel.caldito"
"Fred","Xiao","<EMAIL>","Technology","Risk Analyst IAM, Cyber Security West Coast",,"Arnold Nzailu","Arnold Nzailu",,"10/28/2022 12:37:52 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24842","Fred.Xiao"
"Lauri","McCormack","<EMAIL>","Client Services","Client Services Auditor",,"Caitlin Slavik","Caitlin Slavik","Client Services Auditor","7/24/2020 9:30:46 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24844","Lauri.McCormack"
"Akhila","Guttikonda","<EMAIL>","Development","QA Analyst",,,,,"10/27/2022 11:49:58 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24849","akhila.guttikonda"
"Kelsey","Hess","<EMAIL>","Implementations","Implementer","+1 (519) 641-9245",,,,"10/28/2022 5:56:37 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24855","kelsey.hess"
"ca","secscan",,,"Service Account",,,,"Service Account: Security - This AD Account was created in response to salesforce ticket ******** to access QHR servers and report back to the CIS platform dashboard. This account ensure servers are configured Securely.",,"<EMAIL>","S-1-5-21-**********-**********-*********-26674","ca_secscan"
"Carrie","Ng","<EMAIL>","Product Management","Senior Director, Strategy & Planning",,"Shelley Hughes","Shelley Hughes","LCL employee, temporary access",,"<EMAIL>","S-1-5-21-**********-**********-*********-24859","Carrie.Ng"
"John","Wilson","<EMAIL>","Client Services","Client Experience Analyst",,,,,"9/21/2022 8:56:17 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26276","john.wilson"
"Parker","Steadman","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"10/28/2022 12:02:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26279","parker.steadman"
"Lisa","Helin","<EMAIL>","Quality Assurance","Junior QA Analyst",,,,,"10/24/2022 10:46:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26281","lisa.helin"
"Lora","Henriksen","<EMAIL>","Administration","Senior Administrative Lead, EA to the President","**********",,,,"10/28/2022 1:20:42 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24870","lora.henriksen"
"Melissa","Shu","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/14/2022 11:14:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24875","melissa.shu"
"Debbie","Davies","<EMAIL>","Implementations","Project Coordinator",,,,,"9/29/2022 6:10:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24876","debbie.davies"
"OME","User",,,"Service Account",,,,"Service Account: Corp IT - Windows account to access SMB Share on Dell-DRM","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26682","omeuser"
"Stephanie","Koopmans","<EMAIL>","Client Services","Client Experience Analyst, Senior",,,,,"10/27/2022 11:14:49 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24879","stephanie.koopmans"
"Jade","Davies","<EMAIL>","Client Services","Client Experience Analyst",,,,,"10/24/2022 7:02:35 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24881","jade.davies"
"svc","Flexapp",,,,,,,"Service Account:Do not change - service account for Flexapp",,"<EMAIL>","S-1-5-21-**********-**********-*********-24884","svc-flexapp"
"SVC","Jenkins",,,,,,,"DO not change - Jenkins slave account for Flexapp","10/28/2022 3:48:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24885","svc-jenkins"
"Saurabh","Moghe","<EMAIL>","Development","Software Developer",,,,,"10/26/2022 7:15:01 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26292","saurabh.moghe"
"Helder","Necker","<EMAIL>","Development","Software Developer",,,,,"8/5/2022 8:40:46 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26294","helder.necker"
"Simon","Hamilton","<EMAIL>","Product Operations","Product Owner",,,,,"10/28/2022 12:06:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26296","simon.hamilton"
"Olivia","Floyd","<EMAIL>","Product Operations","Associate Project Manager",,,,,"10/28/2022 1:15:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24893","olivia.floyd"
"SQL-EMRDATASQL02",,,,"Service Account",,,,"Service Account: Data - SQL Service Account; used to run EMRDATASQL01\SQL2019 MSSQL instance.","8/10/2022 9:04:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24900","SQL-EMRDATASQL02"
"Justin","Germain",,"Technology","Systems Administrator",,,,,"8/5/2022 1:03:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26693","justin.germain"
"Niloo","Vakili","<EMAIL>","Client Services","Client Services Team Lead, Senior","+1 (250) 899-2919",,,,"10/27/2022 2:37:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26694","niloo.vakili"
"Blaine","Bradley","<EMAIL>","Administration","Facilities Manager","+1 (250)-212-2531",,,,"10/28/2022 1:00:35 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26696","blaine.bradley"
"Shannon","Burns","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"10/27/2022 6:50:23 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26698","shannon.burns"
"Nathan","Taylor","<EMAIL>","Technology","Helpdesk Analyst",,,,,"10/28/2022 1:08:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26700","nathan.taylor"
"Jordan","Levesque","<EMAIL>","Client Services","Client Services Team Lead","250-718-5762",,,,"10/26/2022 1:02:44 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26701","jordan.levesque"
"Selene","Vera","<EMAIL>","Data and Software Architecture","Cloud Cost Analyst",,,,,"10/26/2022 6:24:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24917","selene.vera"
"Muhammad","Ali","<EMAIL>","Technology","Network Analyst","+1 (403) 891-4275",,,,"10/27/2022 1:32:47 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26307","muhammad.ali"
"Lawrence","Lee","<EMAIL>","Data Analysis","Lead Data Analyst",,,,,"10/28/2022 7:14:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26309","lawrence.lee"
"Jennifer","Roseberry","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"10/25/2022 6:31:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26311","jennifer.roseberry"
"Etevaldo","Memoria","<EMAIL>","Development","Software Development Intern",,,,,"10/27/2022 1:29:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26711","etevaldo.memoria"
"Bradley","MacDonald","<EMAIL>","Data Analysis","Senior Data Solutions Specialist",,"Nicola Austin","Nicola Austin","TimeAcct Contractor - Hired via Acq - IMP","9/16/2022 1:28:27 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26718","Bradley.MacDonald"
"Anna","Tam","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"10/27/2022 4:03:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-24923","anna.tam"
"Guinevere","Ashby","<EMAIL>","Training","Training Solutions Lead","+1 (587) 834-7524",,,,"10/27/2022 10:27:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-24925","guinevere.ashby"
"Benie","Tan","<EMAIL>","Quality Assurance","QA Analyst",,,,,"10/28/2022 10:10:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26721","benie.tan"
"Sajid","Syed","<EMAIL>","Product Operations","Scrum Master",,,,,"10/21/2022 7:37:26 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26722","sajid.syed"
"Rob","Lintott","<EMAIL>","Development","Software Developer",,,,,"9/28/2022 7:36:18 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26723","rob.lintott"
"Julie","Allen","<EMAIL>","Implementations","Implementation Specialist","+1 (705) 677-8367",,,,"10/25/2022 6:01:35 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26724","julie.allen"
"CommVault","Backup Admin",,,,,,,,"10/25/2022 2:57:39 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26725","svc-commvault"
"Mahlet","Negussie","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/2/2021 3:00:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26727","mahlet.negussie"
"Jackie","Lin","<EMAIL>","Client Services","Client Services Analyst",,,,,"9/13/2022 5:59:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26728","jackie.lin"
"Dean","McGregor","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/14/2021 3:30:19 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26730","dean.mcgregor"
"Urvashi","Gupta","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 9:35:00 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26733","urvashi.gupta"
"OXIDIZEDSABASH",,,,"Service Account",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26745","OXIDIZEDSABASH"
"Michelle","Pereira","<EMAIL>","Sales National","Inside Sales Representative","+1 (905) 317-6175",,,,"8/31/2022 12:29:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26322","Michelle.Pereira"
"Jennifer","McDougall","<EMAIL>","CHN West","Practice Consultant","+1 (403) 463-6794",,,,"10/28/2022 7:09:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26325","jennifer.mcdougall"
"Kelly","Hanson","<EMAIL>","CHN East","Practice Consultant",,,,,"10/28/2022 12:43:48 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26328","kelly.hanson"
"Service-Now","Blob-Reader",,,"Service Account",,,,"Service Account: Corp IT - Used for Azure blob storage and Service-Now Prod impersonation connection","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26331","servicenow.blob"
"Natasha","Lakhani","<EMAIL>","Sales National","Legal Compliance and Contracts Manager",,,,,"10/27/2022 3:03:02 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26335","natasha.lakhani"
"Paul","Profeta","<EMAIL>","CHN Central","Contractor",,"Audrey Blatz","Audrey Blatz","Contractor - Confluence access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26337","Paul.Profeta"
"Darshini","Trivedi","<EMAIL>","Client Services","Client Services Analyst",,,,,"8/20/2021 3:28:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26340","Darshini.Trivedi"
"Tessa","Tjepkema","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 8:24:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26341","tessa.tjepkema"
"Teagan","King","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 6:51:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26342","teagan.king"
"Patricia","Camara","<EMAIL>","Client Services","Client Services Analyst, Senior",,,,,"10/23/2021 5:58:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26344","patricia.camara"
"Jen","Currier","<EMAIL>","Client Services","Client Services Analyst",,,,,"5/20/2021 12:58:51 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26347","jen.currier"
"Jenna","Slonski","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"10/28/2022 12:42:29 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26348","jenna.slonski"
"Tuba","Tanveer","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/24/2022 7:13:07 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26353","Tuba.Tanveer"
"Claudia","Escanhola","<EMAIL>","Client Services","Client Services Analyst",,,,,"5/25/2022 3:15:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26354","Claudia.Escanhola"
"Derek","Riggs","<EMAIL>","Implementations","Trainer",,,,,"10/7/2022 2:15:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26355","Derek.Riggs"
"Aukse","Braziunaite","<EMAIL>","Client Services","Client Experience Analyst",,,,,"10/24/2022 3:04:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26357","Aukse.Braziunaite"
"Carla","Mendoza","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 1:50:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26358","Carla.Mendoza"
"Chetna","Singh","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26359","chetna.singh"
"Inna","Danilevich","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26360","inna.danilevich"
"Parfait","Kongo","<EMAIL>","Technology","Sr. Specialist, Security",,"Ron protacio","Ron protacio","Sr. Specialist, Security - EXTERNAL","10/25/2021 11:54:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26361","Parfait.Kongo"
"Anthony","Cesario","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26364","Anthony.Cesario"
"Cameron","Sekulin","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26365","Cameron.Sekulin"
"Hyejoong","Kim","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26366","Hyejoong.Kim"
"Kanika","Vig","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"10/28/2022 8:27:11 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26370","kanika.vig"
"Elara","David","<EMAIL>","Client Services","Client Services Analyst",,,,,"2/3/2022 4:59:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26757","elara.david"
"Jack","Fu",,"Client Services","Client Services Analyst",,,,,"10/27/2022 11:11:37 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26759","Jack.Fu"
"Deepa","Sugur",,"Client Services","Client Services Analyst",,,,,"10/14/2021 12:08:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26761","Deepa.Sugur"
"Colleen","Corrigan","<EMAIL>","Client Services","Client Services Analyst, Specialist",,,,,"10/27/2022 6:07:40 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26762","Colleen.corrigan"
"Brenda","Kaweesi","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/24/2022 4:17:52 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26764","Brenda.Kaweesi"
"Ami","Goswami","<EMAIL>","Client Services","Client Services Analyst",,,,,"2/28/2022 5:01:21 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26765","Ami.Goswami"
"James","Calder","<EMAIL>","Marketing","Design Manager","+1 (250) 470-8846",,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26766","james.calder"
"Naaz","Mughal","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"10/27/2022 8:07:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26376","naaz.mughal"
"MDI","sensor",,,,,,,"Service Account: Security - Microsoft Defender for Identity Sensor for QTDC01/02 and QTADFS2",,"<EMAIL>","S-1-5-21-**********-**********-*********-26767","svc-mdisensor"
"Jarrid","Pond",,,,,,,,"10/27/2022 11:00:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26768","da-jpond"
"Nicholas","Braidwood","<EMAIL>","Development","Software Developer",,,,,"10/21/2022 12:29:18 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26771","nicholas.braidwood"
"Abdur","Rafi","<EMAIL>","Implementations","Data Analyst",,,,,"10/27/2022 2:19:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26772","abdur.rafi"
"Jordan","Pinske",,,,,,,,"6/16/2022 2:14:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26776","da-jpinske"
"Rachel","Herzog","<EMAIL>","Marketing","Lead Generation Specialist",,,,,"8/4/2022 7:53:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26377","rachel.herzog"
"Simon","Cohen","<EMAIL>","Sales National","Sales Engineer",,,,,"12/12/2021 8:37:52 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26378","simon.cohen"
"Lidia","Ghobrial-Zaki",,"CHN Central","Contractor",,"Audrey Blatz","Audrey Blatz","MD Practice Solutions External Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26379","lidia.GhobrialZaki"
"Uday","Bhaskar","<EMAIL>","Development","Software Developer",,,,,"5/18/2021 1:01:42 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26777","uday.bhaskar"
"Tayo","Aruleba","<EMAIL>","Technology","Application Analyst, Jira","2505564182",,,,"10/28/2022 9:00:09 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26380","Tayo.Aruleba"
"Tayo","Aruleba",,,,,,,,"10/21/2022 1:17:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26381","DA-TAruleba"
"Dean","Malig","<EMAIL>","Technology","Systems Administrator",,,,,"9/23/2022 4:20:22 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26382","dean.malig"
"Lyndsey","Wong","<EMAIL>","Development","Junior Software Developer",,,,,"5/25/2022 9:16:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26384","lyndsey.wong"
"Gerardo","Marcos","<EMAIL>","Development","Junior Software Developer",,,,,"9/20/2021 8:14:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26385","gerardo.marcos"
"Jane","Ekegbu","<EMAIL>","Development","BI Developer",,,,,"2/6/2022 11:41:34 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26386","jane.ekegbu"
"Jeff","Fleming","<EMAIL>","Technology","IT Helpdesk Analyst","+1 (250) 681-2391",,,,"10/28/2022 1:02:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26387","jeff.fleming"
"Dilcia","Torres","<EMAIL>","Technology","Senior Systems Specialist","4039684700",,,,"10/28/2022 1:11:03 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26388","dilcia.torres"
"David","Markwell",,"Product Management","Senior Director, Strategy & Planning",,"Angela Tam","Angela Tam",,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26389","David.Markwell"
"Jeff","Fleming",,,,,,,,"10/27/2022 9:21:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26391","da-jfleming"
"Domain Admin","Dilcia Torres","<EMAIL>",,,,,,,"10/20/2022 3:21:47 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26392","da-dtorres"
"CAT","Read-Only","<EMAIL>","IT","Deloitte Contractor",,"Lemuel Caldito","Lemuel Caldito","Test account for Central Admin Tools",,"<EMAIL>","S-1-5-21-**********-**********-*********-26779","cat.readonly"
"Eden","Ritchie","<EMAIL>","Product Operations","Project Coordinator",,,,,"10/27/2022 3:41:20 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26781","Eden.Ritchie"
"Bre","Wilson","<EMAIL>","Client Services","Client Services Team Lead","416 320 0463",,,,"10/28/2022 4:59:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26782","Bre.Wilson"
"Harry","Shams","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"9/23/2022 3:43:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26784","Harry.Shams"
"Komal","Kaur","<EMAIL>","CHN West","Practice Consultant",,,,,"10/27/2022 4:34:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26393","Komal.Kaur"
"Nawshin","Tabassum","<EMAIL>","Client Services","Client Services Analyst",,,,,"8/4/2022 8:21:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26395","Nawshin.Tabassum"
"Pierce","Cowan","<EMAIL>","Client Services","Central Desk Client Care Coordinator",,,,,"10/28/2022 5:08:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26396","Pierce.Cowan"
"Eunice","Ndung'u","<EMAIL>","Product Development","RPA Developer",,,,,"10/28/2022 7:14:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26399","eunice.ndungu"
"Brad","Keefe","<EMAIL>","Technology","System Administrator",,,,,"10/27/2022 6:51:30 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26786","Brad.Keefe"
"Omar","Elhalabi","<EMAIL>","Business Development","Practice Consultant",,,,"External - Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26400","omarelhalabi"
"Allan","Holbrook","<EMAIL>","Development","Senior Software Developer",,,,,"10/26/2022 3:30:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26792","allan.holbrook"
"Test","Phoneforward",,,,,,,"Service Account: Corp IT - Testing Teams Phone Forwarding",,"<EMAIL>","S-1-5-21-**********-**********-*********-26793","test.phoneforward"
"Asama","Leduc","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"10/28/2022 12:56:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26794","asama.leduc"
"Cassandra","McAvoy","<EMAIL>","Client Services","Senior Client Services Analyst",,,,,"10/28/2022 7:48:34 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26795","cassandra.mcavoy"
"Chandra","DeLaney","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/8/2022 1:52:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26796","chandra.delaney"
"Gabriela","Parente","<EMAIL>","Client Services","Client Services Analyst",,,,,"6/17/2022 7:01:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26797","gabriela.parente"
"Sam","Kaushal","<EMAIL>","Development","Software Developer Intern",,,,,"10/27/2022 2:54:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26798","sam.kaushal"
"Himali","Lalit","<EMAIL>","Client Services","Client Services Analyst, Specialist",,,,,"10/26/2022 8:00:54 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26799","himali.lalit"
"Joel","Burns","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/14/2022 10:44:33 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26800","joel.burns"
"Natalie","Wilson","<EMAIL>","Revenue Mangement","Revenue Management Data Entry Clerk",,,,,"10/27/2022 7:22:41 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26801","natalie.wilson"
"Cody","Kinzett","<EMAIL>","Development","Software Developer",,,,,"10/20/2022 12:57:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26808","cody.kinzett"
"Iné","Fourie","<EMAIL>","CHN Central","Practice Consultant",,,,,"9/9/2022 2:39:42 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26810","ine.fourie"
"Avi","van Haren","<EMAIL>","Product Management","Director of Product Management",,,,,"10/27/2022 4:48:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26813","avi.vanharen"
"Srija","Yarlagadda","<EMAIL>","Product Operations","Product Owner",,,,,"9/15/2022 1:02:02 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26814","srija.yarlagadda"
"Daniel","Mason","<EMAIL>","Development","Software Developer",,,,,"10/26/2022 2:36:11 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26403","daniel.mason"
"Lisa","Laurent",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26404","pa-llaurent"
"Adnan","Ashfaq","<EMAIL>","Development","Junior Software Developer",,,,,"10/20/2022 2:30:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26405","adnan.ashfaq"
"Caleb","Penman","<EMAIL>","Development","Junior Software Developer",,,,,"6/20/2022 1:07:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26815","caleb.penman"
"Gaku","Jinyama","<EMAIL>","Development","Junior Software Developer",,,,,"9/20/2021 11:07:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26406","gaku.jinyama"
"Domain Admin","Fred Xiao",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26411","da-fxiao"
"Sarah","Ryder","<EMAIL>","Administration","Administrative Coordinator",,,,,"10/26/2022 8:08:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26822","sarah.ryder"
"Timi","Ade-Malomo","<EMAIL>","Product Operations","Project Manager",,,,,"10/27/2022 11:49:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26412","timi.amalomo"
"Janani","Kulanthaiswamy","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"12/16/2021 10:27:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26413","janani.kulanthaiswam"
"Arpita","Brar","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/8/2021 9:40:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26825","arpita.brar"
"Jo","Jraige","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/15/2021 5:32:46 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26829","jo.jraige"
"Samantha","Silverthorne","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/17/2021 2:53:52 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26831","samantha.silverthorn"
"Diego","Silva","<EMAIL>","Development","Senior Software Developer",,,,,"10/5/2022 10:03:18 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26414","diego.silva"
"Ashley","Quigley","<EMAIL>","Implementations","Implementation Specialist",,,,,"12/28/2021 6:39:28 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26834","ashley.quigley"
"Praveen","Kumar Theegala","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 9:03:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26835","praveen.ktheegala"
"Robert","Bro","<EMAIL>","Development","Software Developer",,,,,"10/27/2022 3:23:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26836","robert.bro"
"Chris","Haggard","<EMAIL>","IT","New Rocket (ServiceNow) Vendor",,"Aron Ashmead","Aron Ashmead","Onetrust Vendor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26415","chris.haggard"
"Srikanth","Reddy Surukanti","<EMAIL>","Data and Software Architecture","Site Reliability Engineer",,,,,"9/27/2022 7:56:59 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26837","srikanth.rsurukanti"
"Paul","Farry","<EMAIL>","Technology","System Administrator",,,,,"10/27/2022 8:47:58 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26838","paul.farry"
"Keith","Borgmann","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 9:00:34 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26839","keith.borgmann"
"Splunk","Sync",,,,,,,"Service Account: Security - Used for Splunk Syncing",,"<EMAIL>","S-1-5-21-**********-**********-*********-26840","svc_splunksync"
"Pavan","Mantripragada","<EMAIL>","Data and Software Architecture","Senior Site Reliability Engineer",,,,,"3/23/2022 12:39:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26418","pavan.mantripragada"
"John","Hetherington","<EMAIL>",,,,"Alan Mcnaughton","Alan Mcnaughton","Lumina Contractor, limited confluence access","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26419","john.hetherington"
"Thu","Pystynen","<EMAIL>",,,,"Alan Mcnaughton","Alan Mcnaughton","Lumina Contractor - Limited confluence access","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26420","thu.pystynen"
"Liam","Mowatt","<EMAIL>","Client Services","Client Services Analyst",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26841","liam.mowatt"
"Loanne","Power","<EMAIL>","Client Services","Client Services Analyst",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26842","loanne.power"
"Samantha","Dykeman","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 1:59:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26845","samantha.dykeman"
"Anish","Kumar","<EMAIL>","Development","Junior Software Developer",,,,,"10/14/2022 9:08:04 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26421","anish.kumar"
"Zuhra","Bakhshi","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 1:03:10 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26423","zuhra.bakhshi"
"Stacy","Roemer","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/12/2022 1:25:08 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26426","stacy.roemer"
"Mark","Petersen-Dixon","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/17/2022 6:43:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26427","mark.petersen-dixon"
"Antonio","Cienfuegos","<EMAIL>","IT","Deloitte Contractor",,"Arnold Nzailu","Arnold Nzailu","External user Deloitte Contractor - email access only",,"<EMAIL>","S-1-5-21-**********-**********-*********-26847","antonio.cienfuegos"
"Matt","Ball","<EMAIL>","Implementations","TimeAcct Contractor",,"Stephan Luies","Stephan Luies","TimeAcct Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26430","matt.ball"
"Paul","Farry",,,,,,,,"10/28/2022 8:19:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26431","da-pfarry"
"Jolanta","Gronowski","jolanta,<EMAIL>","CHN Central","Director, CHN Central",,,,,"10/28/2022 11:52:54 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26849","jolanta.gronowski"
"Kathryn","Roseberry","<EMAIL>","Revenue Management","Revenue Management Data Entry Clerk",,,,,"10/28/2022 9:10:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26850","kathryn.roseberry"
"Vuk","Varicak","<EMAIL>","Data Analysis","Data Analyst",,,,,"10/28/2022 9:05:33 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26434","vuk.varicak"
"Jenny","Tieu",,"Marketing","Senior Graphic Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26435","jenny.tieu"
"Mehdi","Noroozi","<EMAIL>","Data and Software Architecture","Site Reliability Engineer","403-402-2294",,,,"10/28/2022 10:36:41 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26436","mehdi.noroozi"
"Bansari","Purohit","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/14/2022 2:04:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26855","Bansari.Purohit"
"Elijah","Lewis","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/21/2022 5:11:40 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26857","Elijah.Lewis"
"Jacinta","Kennedy","<EMAIL>","Client Services","Enterprise Relationship Analyst",,,,,"10/24/2022 6:23:55 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26858","Jacinta.Kennedy"
"Pascal","Swalwell","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/30/2022 5:25:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26860","Pascal.Swalwell"
"Naia","Maird","<EMAIL>","Client Services","Client Services Analyst",,,,,"4/2/2022 11:58:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26861","Naia.Maird"
"Vincent","Tolley","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/6/2022 7:57:16 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26863","Vincent.Tolley"
"Christopher","Lee","<EMAIL>","Data and Software Architecture","Business Intelligence Analyst",,,,,"10/26/2022 12:11:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26866","christopher.lee"
"Chrisaine","Brown-Humphrey","<EMAIL>","Technology","IT Helpdesk Analyst",,,,,"10/27/2022 9:14:29 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26869","chrisaine.bhumphrey"
"Namrata","Jain","<EMAIL>","Product Management","Associate Product Manager",,,,,"10/27/2022 1:41:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26870","namrata.jain"
"Tiffany","Smith","<EMAIL>","Implementations","Implementation Specialist",,,,,"10/28/2022 12:55:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26872","tiffany.smith"
"Chrisaine","Brown-Humphrey",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26873","da-cbrown-humphrey"
"Michael","MacCarthy","<EMAIL>","Development","Junior Software Developer",,,,,"10/21/2022 9:57:22 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26874","michael.maccarthy"
"Aakash","Siddhanti","<EMAIL>","Product Management","Product Manager",,,,,"10/21/2022 10:07:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26875","aakash.siddhanti"
"Yara","Bagh","<EMAIL>","Marketing","Product Designer",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26876","yara.bagh"
"Anthony","Yip","<EMAIL>","Product Operations","Associate Product Owner",,,,,"10/27/2022 6:04:02 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26878","anthony.yip"
"Vanessa","Marchelletta","<EMAIL>","Product Operations","Project Coordinator",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26879","vanessa.marchelletta"
"Connor","Jones","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/25/2022 10:09:42 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26438","connor.jones"
"Fartune","Ahmed","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/20/2022 1:29:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26439","fartune.ahmed"
"Anna-Kay","Dwyer","<EMAIL>","Client Services","Client Services Analyst",,,,,"7/15/2022 5:14:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26443","annakay.dwyer"
"Natalie","Brandon","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 7:48:11 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26445","natalie.brandon"
"Shawn","Mohan","<EMAIL>","Client Services","Client Services Analyst",,,,,"9/28/2022 11:31:20 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26447","shawn.mohan"
"svc-sqlbackups",,,,,,,,,"10/28/2022 12:30:33 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26449","svc-sqlbackups"
"Ana","Macedo","<EMAIL>","Marketing","Product Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26450","ana.macedo"
"Sudeep","Mool","<EMAIL>","Technology","Database Admin Team Lead","403-879-7603",,,,"10/28/2022 9:50:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26880","sudeep.mool"
"Patrick","Badine","<EMAIL>","Data Insights","Junior Application Architect",,,,,"10/12/2022 1:00:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26881","patrick.badine"
"Andrea","Johnston","<EMAIL>","Technology","Knowledge and Information Lead",,,,,"10/27/2022 1:55:19 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26882","andrea.johnston"
"Yetunde","Osanyin","<EMAIL>","Product Operations","Project Manager",,"10","10","..","10/25/2022 7:51:38 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26883","yetunde.osanyin"
"svc-vmbackups",,,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26454","svc-vmbackups"
"Swetha","Mandadi","<EMAIL>","Technology","Senior Application Analyst, Salesforce","250-859-3083",,,,"10/7/2022 9:22:21 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26455","swetha.mandadi"
"svc-mstrueup",,,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26456","svc-mstrueup"
"Satya","Chandran","<EMAIL>","Development","Process Improvement Manager",,,,,"10/25/2022 2:13:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26457","satya.chandran"
"Ronald","Lai","<EMAIL>","IT","Compliance Specialist",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26459","ronald.lai"
"Kurt","Armbruster","<EMAIL>","Development","Junior Software Developer",,,,,"10/25/2022 7:09:12 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26461","kurt.armbruster"
"Monica","Dial","<EMAIL>","Training","Trainer",,,,,"10/27/2022 1:03:56 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26463","monica.dial"
"Benji","Tanner","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 10:52:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26464","benji.tanner"
"Caleb","Marcel","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/11/2022 4:54:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26465","caleb.marcel"
"Cassie","Olivares","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 6:58:37 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26466","cassie.olivares"
"Jordan","DeLaney","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/20/2022 1:03:35 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26468","jordan.delaney"
"Kiran","Gill","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/14/2022 9:51:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26469","kiran.gill"
"Nirav","Chaudhary","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/20/2022 7:58:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26471","nirav.chaudhary"
"Devang","Vansh","<EMAIL>","IT","MetaQuirk Contractor",,"Arnold Nzailu","Arnold Nzailu","External user for MetaQuirk (Contractor)",,"<EMAIL>","S-1-5-21-**********-**********-*********-26888","devang.vansh"
"Matt","Haig","<EMAIL>","IT","MetaQuirk Contractor",,"Arnold Nzailu","Arnold Nzailu","External user for MetaQuirk (Contractor)",,"<EMAIL>","S-1-5-21-**********-**********-*********-26889","matt.haig"
"Scott","Barr","<EMAIL>","IT","MetaQuirk Contractor",,"Taylor Dresher","Taylor Dresher","External user for MetaQuirk (Contractor). Zscaler only,",,"<EMAIL>","S-1-5-21-**********-**********-*********-26890","scott.barr"
"Domain Admin - Sudeep","Mool",,,,,,,,"7/5/2022 11:19:30 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26892","da-smool"
"Nicholas","Brown","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 7:37:25 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26473","nicholas.brown"
"Ashley","Robertson","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 1:00:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26475","ashley.robertson"
"Rajin","Ramjit","<EMAIL>","IT","Sr. Manager - Technology Risk Management",,"Ron Protacio","Ron Protacio","LCL Employee - Needs Confluence, Jira and Sack","10/24/2022 2:39:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26893","rajin.ramjit"
"Rushik","Panchal",,"IT","Sr. Cyber Security Specialist",,"Ron Protacio","Ron Protacio","LCL Employee - Needs Confluence, Jira and Slack","7/21/2022 10:21:58 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26477","rushik.panchal"
"Sam","McGrath","<EMAIL>","CHN East","Practice Consultant",,,,,"10/28/2022 9:45:40 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26478","sam.mcgrath"
"Tully","Johnson","<EMAIL>","Development","Software Developer",,,,,"7/7/2022 1:03:13 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26479","tully.johnson"
"Domain Admin","Justin Germain",,,,,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26481","da-jgermain"
"Domain Admin -","Muhammad Ali","<EMAIL>",,,,,,,"5/16/2022 1:46:32 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26483","da-mali"
"Amanda","Harris","<EMAIL>","CHN Central","Practice Consultant",,,,,"10/26/2022 4:53:36 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26894","amanda.harris"
"Naomi","Brown","<EMAIL>","Implementations","Implementations Specialist",,,,,"10/26/2022 9:25:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26484","naomi.brown"
"Temi","Solanke","<EMAIL>","Client Services","Client Services Analyst",,,,,"9/6/2022 5:36:09 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26895","temi.solanke"
"Simona","Cernanska","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/25/2022 12:08:06 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26896","simona.cernanska"
"Shikha","Batham","<EMAIL>","Product Management","Technical Product Manager",,,,,"10/24/2022 5:44:12 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26485","shikha.batham"
"Sina","Sereshki","<EMAIL>","Product Management","Technical Product Manager",,,,,"10/24/2022 9:39:56 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26486","sina.sereshki"
"Daniel","Bragg","<EMAIL>","Development","Application Architect",,,,,"10/28/2022 8:20:47 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26897","daniel.bragg"
"Andre","Bertram","<EMAIL>","Development","Junior Software Developer",,,,,"10/27/2022 5:00:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26898","andre.bertram"
"Jon","Auger","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/5/2022 8:33:24 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26899","jon.auger"
"Nadine","Dunning","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/17/2022 5:58:06 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26900","nadine.dunning"
"Shantavia","Allerdyce","<EMAIL>","Client Services","Client Services Analyst",,,,,"9/1/2022 3:16:43 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26901","shantavia.allerdyce"
"Ashleigh","Wilson","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/11/2022 6:16:21 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26489","ashleigh.wilson"
"Kennedy","Huizer","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 8:33:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26902","kennedy.huizer"
"Lety","Mitroi","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 6:29:54 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26903","lety.mitroi"
"Viet","Nguyen","<EMAIL>","Development","Software Developer",,,,,"10/4/2022 7:32:31 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26905","viet.nguyen"
"Jody","Kramer","<EMAIL>","CHN West","Practice Consultant Manager, CHN West",,,,,"10/28/2022 12:39:50 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26906","jody.kramer"
"Christine","Downing","<EMAIL>","Product Operations","Lead Project Manager",,,,,"10/26/2022 3:09:04 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26907","christine.downing"
"Andy","Chang","<EMAIL>","Development","Junior Software Developer",,,,,"10/27/2022 4:47:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26491","andy.chang"
"Daniel","Adamic",,"IT","MetaQuirk Contractor",,"Taylor Drescher","Taylor Drescher","External user for MetaQuirk (Contractor). Zscaler only,","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26493","daniel.adamic"
"Nithya","Kumar","<EMAIL>","Development","Senior QA Analyst",,,,,"10/28/2022 8:24:01 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26908","nithya.kumar"
"Stephen","Gu","<EMAIL>","Implementations","Data Analyst",,,,,"10/28/2022 10:38:53 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26909","stephen.gu"
"Andrew","Steed","<EMAIL>","Development","Software Development Manager",,,,,"10/28/2022 7:59:40 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26911","andrew.steed"
"Shweta","Patel","<EMAIL>","Development","Software Developer",,,,,"10/26/2022 12:52:49 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26912","shweta.patel"
"Gaurav","Sharma","<EMAIL>","Marketing","Lead Generation Specialist",,,,,"10/27/2022 2:07:54 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26914","gaurav.sharma"
"Alex","Chow","<EMAIL>","Implementations","Project Coordinator",,,,,"10/26/2022 6:01:56 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26494","alex.chow"
"Rythem","Kaushal","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 8:26:06 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26915","rythem.kaushal"
"Annette","Carlson","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 5:56:10 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26917","annette.carlson"
"Wole","Adeyeye","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/19/2022 5:07:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26918","wole.adeyeye"
"Sheena","Hickey","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/4/2022 8:32:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26920","sheena.hickey"
"Karlene","Nebiyou","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/24/2022 5:15:43 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26923","karlene.nebiyou"
"Kimia","Gholami","<EMAIL>","Marketing","Product Designer",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26924","kimia.gholami"
"Ward","Dixon","<EMAIL>","Development","Software Development Manager",,,,,"10/26/2022 8:24:22 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26925","ward.dixon"
"Nadeer","Peerbocus","<EMAIL>","Development","Senior QA Analyst",,,,,"10/26/2022 1:39:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26926","nadeer.peerbocus"
"Dario","Castro","<EMAIL>","Platform Integrations","Developer Advocate, Platform Integrations",,,,,"10/27/2022 7:59:36 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26927","dario.castro"
"Akash","Bangera","<EMAIL>","Quality Assurance","QA Analyst",,,,,"10/4/2022 2:36:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26928","akash.bangera"
"Nathan","Taylor",,,,,,,,"10/18/2022 7:31:40 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26929","da-ntaylor"
"David","Luu","<EMAIL>","Development","LCL Team Member",,"Daryl Laverdure","Daryl Laverdure","LCL Employee","8/25/2022 3:38:41 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26930","david.luu"
"Susui","Zhu","<EMAIL>","Development","LCL Team Member",,"Daryl Laverdure","Daryl Laverdure","LCL Employee","9/28/2022 8:45:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26931","susui.zhu"
"Brian","Gesch","<EMAIL>","Development","Software Developer Intern",,,,,"10/28/2022 1:03:23 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26496","brian.gesch"
"Amrita","Abhyankar","<EMAIL>","Development","Senior QA Analyst",,,,,"10/7/2022 11:57:05 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26497","amrita.abhyankar"
"Raj","Jampala","<EMAIL>","Technology","Application Analyst",,,,,"10/26/2022 2:08:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26932","raj.jampala"
"Aiobhe","Blue","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/24/2022 8:42:14 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26933","aiobhe.blue"
"Hassan","Alvi","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 4:35:44 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26935","hassan.alvi"
"Laura","Wilson","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 9:16:58 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26936","laura.wilson"
"Makayla","Lansall","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 8:46:13 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26937","makayla.lansall"
"Najib","Ali","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/17/2022 11:30:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26938","najib.ali"
"Phil","Wright","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/20/2022 1:41:59 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26939","phil.wright"
"Stewart","Williams","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 2:27:25 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26940","stewart.williams"
"Brooks","Ovie","<EMAIL>","Administration","Administrative Facilities Assistant","+1 (778) 581-4039",,,,"10/28/2022 1:04:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26944","brooks.ovie"
"Rhosecel","Valerio","<EMAIL>","Development","Business Process Analyst",,,,,"10/26/2022 2:53:37 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26945","rhosecel.valerio"
"Callow","Associates2",,,"Dynamics Contractors",,,,,"10/26/2022 5:35:29 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26946","callow2"
"Callow","Associates3",,,"Dynamics Contractors",,,,"Test User Account for Callow","10/4/2022 11:09:44 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26947","callow3"
"Scott","Refvik","<EMAIL>","CHN Central","Diguno Media Contractor",,"James Calder","James Calder","Diguno Media Contractor","10/25/2022 9:11:03 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26948","scott.refvik"
"Georgina","Heaney","<EMAIL>","Implementations","Project Coordinator",,,,,"10/27/2022 5:47:33 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26949","georgina.heaney"
"DGPSQLServiceUser",,,,"Service Account",,,,"Ran on QTFINSQL for sql service","10/27/2022 3:46:55 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26499","DGPSQLServiceUser"
"DGPeConnectService",,,,"Service Account",,,,"Econnect service on QTFINSQL and QTFINRDS","10/27/2022 3:46:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26500","DGPeConnectService"
"Victoria","Philips",,,,,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26950","pa-vphilips"
"Ian","Heales","<EMAIL>","Development","Junior Software Developer",,,,,"10/28/2022 10:28:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26951","ian.heales"
"Mathew","Levasseur","<EMAIL>","Development","Senior Software Developer",,,,,"10/28/2022 12:50:16 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26952","mathew.levasseur"
"Mike","Fernandez",,"IT","Sr. Cyber Security Specialist West Coast",,"Ron Protacio","Ron Protacio","LCL Employee - Needs Confluence, Jira and Slack",,"<EMAIL>","S-1-5-21-**********-**********-*********-26953","mike.fernandez"
"Weljaa","Karunanithy","<EMAIL>","IT","Senior Specialist, Cybersecurity West Coast",,"Ron Protacio","Ron Protacio","LCL Employee - Needs Confluence, Jira and Slack","10/18/2022 8:05:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26501","weljaa.karunanithy"
"Michael","Colange","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 10:19:45 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26502","michael.colange"
"Walter","Matte","<EMAIL>","Implementations","Senior Data Analyst",,,,,"10/24/2022 2:26:01 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26503","walter.matte"
"Babatunde","Ojo","<EMAIL>","Implementations","Project Coordinator, Implementations",,,,,"10/26/2022 3:56:39 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26955","babatunde.ojo"
"JR","Asuncion","<EMAIL>","IT","New Rocket Contractor",,"Victoria Philips","Victoria Philips","New Rocket (ServiceNow) Contractor","12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26505","jr.asuncion"
"Sampson","Twihangane","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 3:02:24 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26506","sampson.twihangane"
"Sarah","Jack","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 10:36:51 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26507","sarah.jack"
"Priyanka","Chawla","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/21/2022 3:38:26 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26508","priyanka.chawla"
"Marguerite","du Preez","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 8:35:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26509","marguerite.dupreez"
"Ariba","Ara","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/25/2022 10:57:11 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26510","ariba.ara"
"Aasim","Shaikh","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/25/2022 5:52:31 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26511","aasim.shaikh"
"Kevin","Torres","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 8:51:19 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26512","kevin.torres"
"Furo","Ugo","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/27/2022 8:46:27 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26513","furo.ugo"
"Amber","Kaur","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/26/2022 3:02:14 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26514","amber.kaur"
"Anthony","Headley","<EMAIL>","Client Services","Client Services Analyst",,,,,"12/31/1600 4:00:00 PM","<EMAIL>","S-1-5-21-**********-**********-*********-26515","anthony.headley"
"Ana","Torres","<EMAIL>","Client Services","Client Services Analyst",,,,,"10/28/2022 9:28:57 AM","<EMAIL>","S-1-5-21-**********-**********-*********-26516","ana.torres"
"Phil","Leduc","<EMAIL>","IT","GRC Analyst",,,,,,"<EMAIL>","S-1-5-21-**********-**********-*********-26956","phil.leduc"
