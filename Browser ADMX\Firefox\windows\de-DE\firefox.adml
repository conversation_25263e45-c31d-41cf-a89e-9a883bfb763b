<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="7.1" schemaVersion="1.0">
  <displayName/>
  <description/>
  <resources>
    <stringTable>
      <string id="SUPPORTED_WINXPSP2">Microsoft Windows XP SP2 oder höher</string>
      <string id="UNSUPPORTED">Nicht mehr unterstützt.</string>
      <string id="SUPPORTED_FF60">Firefox 60 oder höher, Firefox 60 ESR oder höher</string>
      <string id="SUPPORTED_FF62">Firefox 62 oder höher, Firefox 60.2 ESR oder höher</string>
      <string id="SUPPORTED_FF63">Firefox 63 oder höher</string>
      <string id="SUPPORTED_FF64">Firefox 64 oder höher, Firefox 60.4 ESR oder höher</string>
      <string id="SUPPORTED_FF66">Firefox 66 oder höher, Firefox 60.6 ESR oder höher</string>
      <string id="SUPPORTED_FF67">Firefox 67 oder höher, Firefox 60.7 ESR oder höher</string>
      <string id="SUPPORTED_FF68">Firefox 68 oder höher, Firefox 68 ESR oder höher</string>
      <string id="SUPPORTED_FF6801">Firefox 68.0.1 oder höher, Firefox 68.0.1 ESR oder höher</string>
      <string id="SUPPORTED_FF60ESR">Firefox 60 ESR oder höher</string>
      <string id="SUPPORTED_FF68ESR">Firefox 68.5 ESR oder höher</string>
      <string id="SUPPORTED_FF69">Firefox 69 oder höher, Firefox 68.1 ESR oder höher</string>
      <string id="SUPPORTED_FF70">Firefox 70 oder höher, Firefox 68.2 ESR oder höher</string>
      <string id="SUPPORTED_FF71">Firefox 71 oder höher, Firefox 68.3 ESR oder höher</string>
      <string id="SUPPORTED_FF72">Firefox 72 oder höher, Firefox 68.4 ESR oder höher</string>
      <string id="SUPPORTED_FF73">Firefox 73 oder höher, Firefox 68.5 ESR oder höher</string>
      <string id="SUPPORTED_FF74">Firefox 74 oder höher, Firefox 68.6 ESR oder höher</string>
      <string id="SUPPORTED_FF75">Firefox 75 oder höher, Firefox 68.7 ESR oder höher</string>
      <string id="SUPPORTED_FF75_ONLY">Firefox 75 oder höher</string>
      <string id="SUPPORTED_FF76">Firefox 76 oder höher, Firefox 68.8 ESR oder höher</string>
      <string id="SUPPORTED_FF76_ONLY">Firefox 76 oder höher</string>
      <string id="SUPPORTED_FF77">Firefox 77 oder höher, Firefox 68.9 ESR oder höher</string>
      <string id="SUPPORTED_FF77_ONLY">Firefox 77 oder höher</string>
      <string id="SUPPORTED_FF78">Firefox 78 oder höher</string>
      <string id="SUPPORTED_FF79">Firefox 79 oder höher, Firefox 78.1 ESR oder höher</string>
      <string id="SUPPORTED_FF80">Firefox 80 oder höher, Firefox 78.2 ESR oder höher</string>
      <string id="SUPPORTED_FF81">Firefox 81 oder höher, Firefox 78.3 ESR oder höher</string>
      <string id="SUPPORTED_FF82">Firefox 82 oder höher, Firefox 78.4 ESR oder höher</string>
      <string id="SUPPORTED_FF83">Firefox 83 oder höher, Firefox 78.5 ESR oder höher</string>
      <string id="SUPPORTED_FF84">Firefox 84 oder höher, Firefox 78.6 ESR oder höher</string>
      <string id="SUPPORTED_FF85">Firefox 85 oder höher, Firefox 78.7 ESR oder höher</string>
      <string id="SUPPORTED_FF86">Firefox 86 oder höher, Firefox 78.8 ESR oder höher</string>
      <string id="SUPPORTED_FF88">Firefox 88 oder höher, Firefox 78.10 ESR oder höher</string>
      <string id="SUPPORTED_FF89">Firefox 89 oder höher, Firefox 78.11 ESR oder höher</string>
      <string id="SUPPORTED_FF90">Firefox 90 oder höher, Firefox 78.12 ESR oder höher</string>
      <string id="SUPPORTED_FF91">Firefox 91 oder höher</string>
      <string id="SUPPORTED_FF95">Firefox 95 oder höher, Firefox 91.4 ESR oder höher</string>
      <string id="SUPPORTED_FF96">Firefox 96 oder höher, Firefox 91.5 ESR oder höher</string>
      <string id="SUPPORTED_FF96_ONLY">Firefox 96 oder höher</string>
      <string id="SUPPORTED_FF97">Firefox 97 oder höher, Firefox 91.6 ESR oder höher</string>
      <string id="SUPPORTED_FF98_ONLY">Firefox 98 oder höher</string>
      <string id="SUPPORTED_FF99">Firefox 99 oder höher, Firefox 91.8 ESR oder höher</string>
      <string id="SUPPORTED_FF100">Firefox 100 oder höher, Firefox 91.9 ESR oder höher</string>
      <string id="SUPPORTED_FF101">Firefox 101 oder höher, Firefox 91.10 ESR oder höher</string>
      <string id="SUPPORTED_FF102">Firefox 102 oder höher</string>
      <string id="SUPPORTED_FF104">Firefox 104 oder höher, Firefox 102.2 ESR oder höher</string>
      <string id="SUPPORTED_FF105">Firefox 105 oder höher, Firefox 102.3 ESR oder höher</string>
      <string id="SUPPORTED_FF106">Firefox 106 oder höher, Firefox 102.4 ESR oder höher</string>
      <string id="SUPPORTED_FF107">Firefox 107 oder höher, Firefox 102.5 ESR oder höher</string>
      <string id="SUPPORTED_FF108">Firefox 108 oder höher, Firefox 102.6 ESR oder höher</string>
      <string id="SUPPORTED_FF109">Firefox 109 oder höher, Firefox 102.7 ESR oder höher</string>
      <string id="SUPPORTED_FF110">Firefox 110 oder höher, Firefox 102.8 ESR oder höher</string>
      <string id="SUPPORTED_FF112_ONLY">Firefox 112 oder höher</string>
      <string id="SUPPORTED_FF113_ONLY">Firefox 113 oder höher</string>
      <string id="SUPPORTED_FF114">Firefox 114 oder höher, Firefox 102.12 ESR oder höher</string>
      <string id="SUPPORTED_FF118">Firefox 118 oder höher, Firefox 115.3 ESR oder höher</string>
      <string id="SUPPORTED_FF120">Firefox 120 oder höher, Firefox 115.5 ESR oder höher</string>
      <string id="SUPPORTED_FF121">Firefox 121 oder höher, Firefox 115.6 ESR oder höher</string>
      <string id="SUPPORTED_FF122">Firefox 122 oder höher, Firefox 115.7 ESR oder höher</string>
      <string id="SUPPORTED_FF123">Firefox 123 oder höher, Firefox 115.8 ESR oder höher</string>
      <string id="SUPPORTED_FF124">Firefox 124 oder höher, Firefox 115.9 ESR oder höher</string>
      <string id="SUPPORTED_FF124_ONLY">Firefox 124 oder höher</string>
      <string id="SUPPORTED_FF125">Firefox 125 oder höher, Firefox 115.10 ESR oder höher</string>
      <string id="SUPPORTED_FF126_ONLY">Firefox 126 oder höher</string>
      <string id="SUPPORTED_FF127_ONLY">Firefox 127 oder höher</string>
      <string id="SUPPORTED_FF128">Firefox 128 oder höher</string>
      <string id="SUPPORTED_FF129">Firefox 129 oder höher, Firefox 128.1 ESR oder höher</string>
      <string id="SUPPORTED_FF130">Firefox 130 oder höher, Firefox 128.2 ESR oder höher</string>
      <string id="SUPPORTED_FF130_ONLY">Firefox 130 oder höher</string>
      <string id="SUPPORTED_FF131">Firefox 131 oder höher, Firefox 128.3 ESR oder höher</string>
      <string id="SUPPORTED_FF137_ONLY">Firefox 137 oder höher</string>
      <string id="SUPPORTED_FF138_ONLY">Firefox 138 oder höher</string>
      <string id="SUPPORTED_FF138">Firefox 138 oder höher, Firefox 128.10 ESR oder höher</string>
      <string id="firefox">Firefox</string>
      <string id="Permissions_group">Berechtigungen</string>
      <string id="Camera_group">Kamera</string>
      <string id="Microphone_group">Mikrofon</string>
      <string id="Location_group">Standort</string>
      <string id="Notifications_group">Benachrichtigungen</string>
      <string id="Autoplay_group">Automatische Wiedergabe</string>
      <string id="VirtualReality_group">Virtual Reality</string>
      <string id="Authentication_group">Authentifizierung</string>
      <string id="Bookmarks_group">Lesezeichen</string>
      <string id="Certificates_group">Zertifikate</string>
      <string id="Popups_group">Popups</string>
      <string id="Cookies_group">Cookies</string>
      <string id="Addons_group">Add-ons</string>
      <string id="Extensions_group">Erweiterungen</string>
      <string id="Flash_group">Flash</string>
      <string id="Homepage_group">Startseite</string>
      <string id="Search_group">Suche</string>
      <string id="Preferences_group">Einstellungen (Veraltet)</string>
      <string id="UserMessaging_group">Benutzer-Benachrichtigungen</string>
      <string id="DisabledCiphers_group">Deaktivierte Verschlüsselungsverfahren</string>
      <string id="EncryptedMediaExtensions_group">DRM-Medien Erweiterungen</string>
      <string id="PDFjs_group">PDF.js</string>
      <string id="PictureInPicture_group">Bild in Bild</string>
      <string id="ProxySettings_group">Proxy</string>
      <string id="SecurityDevices_group">Sicherheitsmodule</string>
      <string id="FirefoxSuggest_group">Firefox Suggest (nur USA)</string>
      <string id="ContentAnalysis_group">Verhinderung von Datenverlust (DLP)</string>
      <string id="InterceptionPoints_group">Abfangpunkte</string>
      <string id="InterceptionPoints_Clipboard_group">Zwischenablage</string>
      <string id="InterceptionPoints_DragAndDrop_group">Drag-and-Drop</string>
      <string id="Allow">Erlaubte Seiten</string>
      <string id="AllowSession">Erlaubte Seiten (Session Only)</string>
      <string id="Block">Gesperrte Seiten</string>
      <string id="AppAutoUpdate">Automatisches Update</string>
      <string id="AppAutoUpdate_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird Firefox automatisch ohne Zustimmung des Benutzers aktualisiert.

Wenn diese Richtlinieneinstellung deaktiviert ist, werden Firefox-Updates heruntergeladen, aber der Benutzer startet die Update-Installation manuell. Die Schaltfläche zur Installation erscheint bei Verfügbarkeit des Updates.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, kann der Benutzer wählen, ob Firefox automatisch aktualisiert wird oder nicht.</string>
      <string id="AppUpdateURL">Benutzerdefinierte Update-URL</string>
      <string id="AppUpdateURL_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine URL zu einem Update-Server setzen, die von der Standard-URL abweicht. Dies kann hilfreich sein, falls Sie einen eigenen Update-Server in Ihrem Netzwerk betreiben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standard-URL verwendet.</string>
      <string id="Authentication_SPNEGO">SPNEGO</string>
      <string id="Authentication_SPNEGO_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, dürfen Webseiten die SPNEGO-Authentifizierung verwenden. Erlaubte Formate für die Einträge in der Liste sind mydomain.com oder https://myotherdomain.com.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, verwenden Webseiten die SPNEGO-Authentifizierung nicht.

Weitere Informationen finden Sie unter https://developer.mozilla.org/en-US/docs/Mozilla/Integrated_authentication.</string>
      <string id="Authentication_Delegated">Delegiert</string>
      <string id="Authentication_Delegated_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, kann der Browser die Benutzerautorisierung für die angegebenen Webseiten an den Server delegieren. Einträge in der Liste sind als mydomain.com oder https://myotherdomain.com formatiert.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden für keine Webseite Benutzerautorisierung delegiert.

Für weitere Informationen, besuchen Sie https://developer.mozilla.org/en-US/docs/Mozilla/Integrated_authentication.</string>
      <string id="Authentication_NTLM">NTLM</string>
      <string id="Authentication_NTLM_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, verwenden die angegebenen Webseiten NTLM-Authentifizierung. Erlaubte Formate für die Einträge in der Liste sind mydomain.com oder https://myotherdomain.com.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, verwenden Webseiten die NTLM-Authentifizierung nicht.

Für weitere Informationen, besuchen Sie https://developer.mozilla.org/en-US/docs/Mozilla/Integrated_authentication.</string>
      <string id="Authentication_AllowNonFQDN">Authentifiziert auch auf Nicht-FQDN</string>
      <string id="Authentication_AllowNonFQDN_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden SPNEGO und NTLM auf Nicht-FQDN (Fully Qualified Domain Name) Adressen aktiviert sein.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, sind NTLM und SPNEGO auf Nicht-FQDN Adressen nicht aktiviert.</string>
      <string id="Authentication_AllowProxies">Authentifiziert auch auf Proxies</string>
      <string id="Authentication_AllowProxies_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, findet keine SPNEGO- oder NTLM-Authentifizierung gegenüber einem Proxy statt.

Wenn diese Richtlinieneinstellung aktiviert (und das Kontrollkästchen aktiviert) oder nicht konfiguriert ist, findet SPNEGO- und NTLM-Authentifizierung gegenüber einem Proxy statt.</string>
      <string id="Authentication_Locked">Änderungen an Authentifizierungseinstellungen verbieten</string>
      <string id="Authentication_Locked_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, können Nutzer Änderungen an den Einstellungen zur Authentifizierung vornehmen.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, können Nutzer Einstellungen zur Authentifizierung nicht ändern.</string>
      <string id="Authentication_PrivateBrowsing">Authentifizierung in Privaten Fenstern</string>
      <string id="Authentication_PrivateBrowsing_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird die Interne Authentifizierung (via SPNEGO oder NTLM) auch in privaten Fenstern benutzt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die interne Authentifizierung in privaten Fenstern nicht benutzt.</string>
      <string id="BlockAboutAddons">Zugriff auf Add-ons Manager verhindern</string>
      <string id="BlockAboutAddons_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer nicht auf den Add-on Manager oder about:addons zugreifen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer auf den Add-ons Manager und about:addons zugreifen.</string>
      <string id="BlockAboutConfig">Zugriff auf about:config verhindern</string>
      <string id="BlockAboutConfig_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer nicht auf about:config zugreifen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer auf about:config zugreifen.</string>
      <string id="BlockAboutProfiles">Zugriff auf about:profiles verhindern</string>
      <string id="BlockAboutProfiles_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer nicht auf about:profiles zugreifen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer auf about:profiles zugreifen.</string>
      <string id="BlockAboutSupport">Zugriff auf Informationen zur Fehlerbehebung verhindern</string>
      <string id="BlockAboutSupport_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer nicht auf Informationen zur Fehlerbehebung oder about:support zugreifen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer auf Informationen zur Fehlerbehebung und about:support zugreifen.</string>
      <string id="DisableSetDesktopBackground">Als Hintergrundbild einrichten deaktivieren</string>
      <string id="DisableSetDesktopBackground_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer Bilder nicht mit Hilfe von "Als Hintergrundbild einrichten…" als Desktophintergrund festlegen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer Bilder mit Hilfe von "Als Hintergrundbild einrichten.." als Desktophintergrund festlegen.</string>
      <string id="CaptivePortal">Captive Portal Unterstützung</string>
      <string id="CaptivePortal_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, wird die Unterstützung für Captive Portals deaktiviert.

Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, wird die Unterstützung für Captive Portals aktiviert.</string>
      <string id="Certificates_ImportEnterpriseRoots">Windows-Zertifikatsspeicher benutzen</string>
      <string id="Certificates_ImportEnterpriseRoots_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, verwendet Firefox den Windows-Zertifikatsspeicher.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, verwendet Firefox den eigenen Zertifikatsspeicher.</string>
      <string id="Certificates_Install">Zertifikate installieren</string>
      <string id="Certificates_Install_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, wird Firefox die gelisteten Zertifikate installieren. Es wird in folgende Verzeichnisse geschaut %USERPROFILE%\AppData\Local\Mozilla\Certificates und %USERPROFILE%\AppData\Roaming\Mozilla\Certificates.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird Firefox keine weiteren Zertifikate installieren.</string>
      <string id="DefaultDownloadDirectory">Standard-Download-Verzeichnis</string>
      <string id="DefaultDownloadDirectory_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie das Standard-Verzeichnis für Downloads definieren. ${home} kann für den nativen Profilpfad verwendet werden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird das Standard-Download-Verzeichnis von Firefox benutzt.</string>
      <string id="DownloadDirectory">Download-Verzeichnis</string>
      <string id="DownloadDirectory_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie das Standard-Verzeichnis für Downloads definieren und sperren. ${home} kann für den nativen Profilpfad verwendet werden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird das Standard-Download-Verzeichnis von Firefox benutzt und der Benutzer kann dies ändern.</string>
      <string id="DNSOverHTTPS_group">DNS über HTTPS</string>
      <string id="DNSOverHTTPS_Enabled">Aktiviert</string>
      <string id="DNSOverHTTPS_Enabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, ist DNS über HTTPS deaktiviert.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, ist DNS über HTTPS aktiviert.</string>
      <string id="DNSOverHTTPS_ProviderURL">Provider-URL</string>
      <string id="DNSOverHTTPS_ProviderURL_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird die angegebene URL als Provider-URL verwendet.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird der Standardanbieter verwendet.</string>

      <string id="DNSOverHTTPS_Locked">Gesperrt</string>
      <string id="DNSOverHTTPS_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die DNS-über-HTTPS-Einstellungen vom Benutzer nicht geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können die DNS-über-HTTPS-Einstellungen vom Benutzer geändert werden.</string>
      <string id="DNSOverHTTPS_ExcludedDomains">Ausgeschlossene Domains</string>
      <string id="DNSOverHTTPS_ExcludedDomains_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden die angegebenen Domains von DNS über HTTPS ausgeschlossen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden keine Domains von DNS über HTTPS ausgeschlossen.</string>
      <string id="DNSOverHTTPS">DNS über HTTPS konfigurieren (Verschoben)</string>
      <string id="DNSOverHTTPS_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können die Standardeinstellungen für DNS über HTTPS geändert werden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden von Mozilla Firefox die Standardeinstellungen zu DNS über HTTPS benutzt.</string>
      <string id="DNSOverHTTPS_Fallback">Fallback</string>
      <string id="DNSOverHTTPS_Fallback_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, greift Firefox nicht auf Ihren Standard-DNS-Server zurück, wenn ein Problem mit dem sicheren DNS-Anbieter auftritt.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, verwendet Firefox Ihren Standard-DNS-Server, wenn ein Problem mit dem sicheren DNS-Anbieter auftritt.</string>
      <string id="DisableMasterPasswordCreation">Master-Passwort verwenden deaktivieren</string>
      <string id="DisableMasterPasswordCreation_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer kein Master-Passwort vergeben.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer ein Master-Passwort vergeben.</string>
      <string id="DisableAppUpdate">Update deaktivieren</string>
      <string id="DisableAppUpdate_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, wird nicht nach Updates gesucht.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden Updates automatisch installiert.</string>
      <string id="DisableBuiltinPDFViewer">PDF-Ansicht (PDF.js) deaktivieren</string>
      <string id="DisableBuiltinPDFViewer_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, werden PDF-Dateien nicht in Firefox angezeigt.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden PDF-Dateien in Firefox angezeigt.</string>
      <string id="DisableDefaultBrowserAgent">Deaktivieren Sie den Standard-Browser-Agent</string>
      <string id="DisableDefaultBrowserAgent_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist der Standard-Browser-Agent deaktiviert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, ist der Standard-Browser-Agent aktiviert.

Weitere Informationen über den Standard-Browser-Agent finden Sie unter https://firefox-source-docs.mozilla.org/toolkit/mozapps/defaultagent/default-browser-agent/index.html</string>
      <string id="DisableDeveloperTools">Werkzeuge für Webentwickler deaktivieren</string>
      <string id="DisableDeveloperTools_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können die Werkzeuge für Webentwickler in Firefox nicht benutzt werden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können die Werkzeuge für Webentwickler in Firefox benutzt werden.</string>
      <string id="DisableFeedbackCommands">Feedback Commands deaktivieren</string>
      <string id="DisableFeedbackCommands_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, sind "Feedback senden..." und "Betrügerische Website melden…" im Hilfemenü nicht verfügbar.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, sind "Feedback senden..." und "Betrügerische Website melden.." im Hilfemenü verfügbar.</string>
      <string id="DisableFirefoxAccounts">Firefox-Konto deaktivieren</string>
      <string id="DisableFirefoxAccounts_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, steht Firefox-Konto nicht zur Verfügung, dies beinhaltet auch die Synchronisierung.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, stehen Firefox-Konto und Synchronisierung zur Verfügung.</string>
      <string id="DisableFirefoxScreenshots">Firefox Screenshots deaktivieren</string>
      <string id="DisableFirefoxScreenshots_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, Firefox Screenshots stehen nicht zur Verfügung.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, Firefox Screenshots stehen zur Verfügung.</string>
      <string id="DisableFirefoxStudies">Firefox Studien deaktivieren</string>
      <string id="DisableFirefoxStudies_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, führt Firefox keine SHIELD-Studien durch oder fragt nach Bewertungen zu Ihren Firefox-Erfahrungen (Heartbeat).

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können SHIELD-Studien oder Firefox-Erfahrungen (Heartbeat) vom Benutzer ausgewählt werden.

Für weitere Informationen, besuchen Sie https://support.mozilla.org/en-US/kb/shield und https://wiki.mozilla.org/Firefox/Shield/Heartbeat</string>
      <string id="DisableForgetButton">Vergessen Button deaktivieren</string>
      <string id="DisableForgetButton_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, steht der "Vergessen" Button nicht zur Verfügung.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, steht der "Vergessen" Button zur Verfügung.</string>
      <string id="DisableFormHistory">Formular- und Suchverlaufhistorie deaktivieren</string>
      <string id="DisableFormHistory_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, wird Firefox keine Formular- oder Suchverläufe speichern.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird Firefox Formular- und Suchverläufe speichern.</string>
      <string id="DisablePasswordReveal">Verbieten, dass Nutzer Passwörter in Gespeicherte Zugangsdaten anzeigen können</string>
      <string id="DisablePasswordReveal_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Nutzer Gespeicherte Zugangsdaten nicht anzeigen lassen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer Gespeicherte Zugangsdaten anzeigen lassen.</string>
      <string id="DisablePocket">Pocket für Firefox deaktivieren (Veraltet)</string>
      <string id="DisablePocket_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, steht Pocket nicht zur Verfügung.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, steht Pocket zur Verfügung.</string>
      <string id="DisablePrivateBrowsing">Privates Surfen deaktivieren</string>
      <string id="DisablePrivateBrowsing_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, ist privates Surfen nicht erlaubt.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, ist privates Surfen erlaubt.</string>
      <string id="DisableProfileImport">Profil-Import deaktivieren</string>
      <string id="DisableProfileImport_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, steht die Option "Daten von einen anderen Browser importieren.." in der Bibliothek nicht zur Verfügung.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, steht die Option "Daten von einen anderen Browser importieren.." in der Bibliothek zur Verfügung.</string>
      <string id="DisableProfileRefresh">Firefox bereinigen deaktivieren</string>
      <string id="DisableProfileRefresh_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, steht der "Firefox bereinigen..." Button auf der about:support Seite oder auf support.mozilla.org nicht zur Verfügung.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, steht der "Firefox bereinigen..." Button auf der about:support Seite oder auf support.mozilla.org zur Verfügung.</string>
      <string id="DisableSafeMode">Abgesicherten Modus deaktivieren</string>
      <string id="DisableSafeMode_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer die Option "Mit deaktivierten Add-ons neu starten..." (Abgesicherter Modus) im Hilfemenü nicht auswählen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer die Option "Mit deaktivierten Add-ons neu starten..." (Abgesicherter Modus) im Hilfemenü auswählen.</string>
      <string id="DisableSecurityBypass_InvalidCertificate">Ausnahme hinzufügen verhindern bei unsicheren Zertifikaten</string>
      <string id="DisableSecurityBypass_InvalidCertificate_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, steht "Ausnahme hinzufügen" nicht zur Verfügung, wenn ein Zertifikat ungültig ist. Dies verhindert, dass ein Benutzer Zertifikatsfehler überschreibt.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, steht "Ausnahme hinzufügen" zur Verfügung, wenn ein Zertifikat ungültig ist.</string>
      <string id="DisableSecurityBypass_SafeBrowsing">Ausnahme hinzufügen verhindern bei betrügerischen oder schädlichen Weseiten</string>
      <string id="DisableSecurityBypass_SafeBrowsing_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer Warnungen zu Betrugsversuch- und Schadprogrammschutz nicht umgehen um eine schädliche Webseite zu besuchen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer Warnungen zu Betrugsversuch- und Schadprogrammschutz umgehen um eine schädliche Webseite zu besuchen.</string>
      <string id="DisableSystemAddonUpdate">System Add-on Updates deaktivieren</string>
      <string id="DisableSystemAddonUpdate_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, werden neue System-Add-ons nicht installiert und installierte Add-ons werden nicht mit Updates versorgt.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden System Add-ons installiert und mit Updates versorgt.</string>
      <string id="DisableTelemetry">Telemetrie deaktivieren</string>
      <string id="DisableTelemetry_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, werden keine Telemetriedaten versendet.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden Telemetriedaten versendet.

Mozilla empfiehlt, die Telemetrie nicht zu deaktivieren. Durch Telemetrie gesammelte Informationen helfen uns, ein besseres Produkt für Unternehmen wie Ihres zu erstellen.</string>
      <string id="DisplayBookmarksToolbar">Lesezeichenleiste anzeigen (Veraltet)</string>
      <string id="DisplayBookmarksToolbar_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, wird die Lesezeichenleiste standardmäßig in der Menüleiste angezeigt, der Benutzer kann diese wieder ausblenden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Lesezeichenleiste nicht in der Menüleiste angzeigt.</string>
      <string id="DisplayBookmarksToolbar_Enum">Lesezeichen-Symbolleiste anzeigen</string>
      <string id="DisplayBookmarksToolbar_Enum_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, kann die Standardanzeige der Lesezeichen-Symbolleiste konfiguriert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Lesezeichen-Symbolleiste standardmäßig auf der neuen Registerkarte angezeigt.</string>
      <string id="DisplayBookmarksToolbar_Always">Immer</string>
      <string id="DisplayBookmarksToolbar_Never">Niemals</string>
      <string id="DisplayBookmarksToolbar_NewTab">Neuer Tab</string>
      <string id="DisplayMenuBar">Menüleiste anzeigen (Veraltet)</string>
      <string id="DisplayMenuBar_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, wird die Menüleiste standardmäßig angezeigt, der Benutzer kann diese wieder ausblenden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Menüleiste standrdmäßig nicht angezeigt.</string>
      <string id="DisplayMenuBar_Enum">Menüleiste anzeigen</string>
      <string id="DisplayMenuBar_Enum_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie wählen, ob die Menüleiste angezeigt wird oder nicht und ob der Benutzer die Menüleiste ein- und ausblenden kann oder nicht.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Menüleiste standardmäßig nicht angezeigt.</string>
      <string id="DisplayMenuBar_Always">Immer</string>
      <string id="DisplayMenuBar_Never">Niemals</string>
      <string id="DisplayMenuBar_Default_On">Standardmäßig eingeschaltet</string>
      <string id="DisplayMenuBar_Default_Off">Standardmäßig ausgeschaltet</string>
      <string id="DontCheckDefaultBrowser">Firefox-Standardbrowser-Überprüfung deaktivieren</string>
      <string id="DontCheckDefaultBrowser_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, überprüft Firefox nicht beim Starten ob er als Standardbrowser gesetzt ist.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, überprüft Firefox beim Starten, ob er als Standardbrowser gesetzt ist.</string>
      <string id="Extensions_Install">Erweiterungen installieren</string>
      <string id="Extensions_Install_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine Liste mit Erweiterungs-URLs oder -pfaden angeben, die beim Start von Firefox installiert werden.
Jedes Mal, wenn diese Liste geändert wird, werden die Erweiterungen erneut installiert.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden keine Erweiterungen installiert.</string>
      <string id="Extensions_Uninstall">Erweiterungen deinstallieren</string>
      <string id="Extensions_Uninstall_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine Liste mit Erweiterungs-IDs angeben, die beim Start deinstalliert werden.
Jedes Mal, wenn diese Liste geändert wird, werden die Erweiterungen deinstalliert.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden keine Erweiterungen deinstalliert.</string>
      <string id="Extensions_Locked">Verhindern, dass Erweiterungen deaktiviert oder gelöscht werden</string>
      <string id="Extensions_Locked_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine Liste mit Erweiterungs-IDs angeben, die den Benutzer hindert die angegebenen Erweiterungen zu deinstallieren oder zu deaktivieren.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden keine Erweiterungen gesperrt.</string>
      <string id="ExtensionUpdate">Add-on Updates</string>
      <string id="ExtensionUpdate_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, werden Add-ons nicht automatisch aktualisiert.

Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, werden Add-ons automatisch aktualisiert.</string>
      <string id="ExtensionSettingsOneLine">Erweiterungen mit JSON verwalten (Einzeiliger JSON-Code)</string>
      <string id="ExtensionSettings">Erweiterungen mit JSON verwalten</string>
      <string id="ExtensionSettings_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden die Einstellungen zu Erweiterungen mit einer JSON-Datei verwaltet.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Erweiterungen nicht mit einer Richtlinie verwaltet.

Für detaillierte Informationen bitte die Dokumentation (in englischer Sprache) lesen: https://github.com/mozilla/policy-templates/blob/master/README.md#extensionsettings.</string>
      <string id="HardwareAcceleration">Hardware-Beschleunigung</string>
      <string id="HardwareAcceleration_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, wird die Hardware-Beschleunigung deaktiviert.

Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, wird die Hardware-Beschleunigung aktiviert.</string>
      <string id="LegacyProfiles">Separate Profile</string>
      <string id="LegacyProfiles_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird Firefox keine separaten Profile bei mehreren parallelen Installationen anlegen. Diese Einstellung ist equivalent zu der Umgebungsvariable MOZ_LEGACY_PROFILES.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird Firefox separate Profile für jede Installation anlegen.</string>
      <string id="LegacySameSiteCookieBehaviorEnabled">Wiederherstellung des alten SameSite-Verhaltens</string>
      <string id="LegacySameSiteCookieBehaviorEnabled_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, kehrt Firefox zum alten Verhalten von SameSite zurück. Das bedeutet, dass Cookies, die nicht explizit ein SameSite-Attribut angeben, so behandelt werden, als ob sie SameSite=None wären.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, erzwingt Firefox SameSite=lax.</string>
      <string id="LegacySameSiteCookieBehaviorEnabledForDomainList">Wiederherstellung des alten SameSite-Verhaltens für bestimmte Domains</string>
      <string id="LegacySameSiteCookieBehaviorEnabledForDomainList_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, kehrt Firefox für die angegebenen Domains zum alten SameSite-Verhalten zurück. Das bedeutet, dass Cookies, die nicht explizit ein SameSite-Attribut angeben, so behandelt werden, als ob sie SameSite=None wären.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, erzwingt Firefox für alle Domains SameSite=lax.</string>
      <string id="LocalFileLinks">Links zu lokalen Dateien</string>
      <string id="LocalFileLinks_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie die Quellen angeben, von denen Links auf lokale Dateien erlaubt sind.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Websites nicht auf lokale Dateien verlinken.</string>
      <string id="NetworkPrediction">Netzwerkvorhersage</string>
      <string id="NetworkPrediction_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, wird die Netzwerkvorhersage (DNS-Prefetching) deaktiviert.

Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, wird die Netzwerkvorhersage (DNS-Prefetching) aktiviert.</string>
      <string id="NewTabPage">Neuer Tab Seite</string>
      <string id="NewTabPage_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird die Neuer Tab Seite leer sein.

Wenn die Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird die Neuer Tab Seite standardmäßig aktiviert sein.</string>
      <string id="OfferToSaveLogins">Zugangsdaten und Passwörter für Webseiten speichen</string>
      <string id="OfferToSaveLogins_Explain">Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, fordert Firefox zum speichern von Logindaten und Passwörtern auf.

Wenn Sie die Richtlinieneinstellung deaktivieren, fordert Firefox nicht zum Speichern von Logindaten und Passwörtern auf.</string>
      <string id="OfferToSaveLoginsDefault">Zugangsdaten und Passwörter für Webseiten speichen (Standard)</string>
      <string id="OfferToSaveLoginsDefault_Explain">Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, fordert Firefox zum speichern von Logindaten und Passwörtern auf.

Wenn Sie die Richtlinieneinstellung deaktivieren, fordert Firefox nicht zum Speichern von Logindaten und Passwörtern auf.

In beiden Fällen kann der Benutzer den Wert ändern (er ist nicht gesperrt).</string>
      <string id="PopupBlocking_Allow_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, sind Popup-Fenster für die angegebenen URLs immer erlaubt. Wenn eine Top-Level-Domain angegeben ist (http://example.org), sind Popup-Fenster für alle Subdomains ebenfalls zulässig.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Popup-Fenster Richtlinie angewendet.</string>
      <string id="PopupBlocking_Default">Pop-ups von Webseiten blockieren</string>
      <string id="PopupBlocking_Default_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, sind Popup-Fenster standardmäßig von Webseiten zugelassen.

Wenn Sie die Richtlinieneinstellung aktivieren oder nicht konfigurieren, sind Popup-Fenster standardmäßig von Webseiten nicht erlaubt.</string>
      <string id="PopupBlocking_Locked">Pop-up-Blocker Einstellungen sperren</string>
      <string id="PopupBlocking_Locked_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Popup Einstellungen nicht vom Benutzer geändert werden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Popup-Fenster Einstellungen vom Benutzer geändert werden.</string>
      <string id="InstallAddonsPermission_Allow_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, sind Add-Ons immer für die angegebenen URLs zulässig, sofern die Add-On-Installation nicht deaktiviert ist. Wenn eine Top-Level-Domain angegeben ist (http://example.org), sind Add-Ons für alle Subdomains ebenfalls zulässig.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Add-On Richtlinie verwendet.</string>
      <string id="InstallAddonsPermission_Default">Add-On Installation aktivieren</string>
      <string id="InstallAddonsPermission_Default_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, können keine Add-ons installiert werden.

Wenn Sie die Richtlinieneinstellungen nicht konfigurieren oder aktivieren, können Add-ons installiert werden.</string>
      <string id="Cookies_Allow_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, sind Cookies immer für die angegebenen URLs erlaubt. Wenn eine Top-Level-Domain angegeben ist (http://example.org), sind Cookies auch für alle Sub-Domains zulässig.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Cookie Richtlinie verwendet.</string>
      <string id="Cookies_AllowSession_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, sind Cookies immer für die angegebenen URLs erlaubt, werden aber beim Schließen von Firefox gelöscht. Wenn eine Top-Level-Domain angegeben ist (http://example.org), sind Cookies auch für alle Sub-Domains zulässig.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Cookie Richtlinie verwendet.</string>
      <string id="Cookies_Block_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, sind Cookies für die angegebenen URLs gesperrt. Wenn eine Top-Level-Domain angegeben ist (http://example.org), werden Cookies von allen Sub-Domains ebenfalls blockiert.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, sind Cookies standardmäßig nicht blockiert.</string>
      <string id="Cookies_Default">Cookies und Website-Daten annehmen (Veraltet)</string>
      <string id="Cookies_Default_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren, werden Cookies standardmäßig nicht von Webseiten akzeptiert.

Wenn Sie die Richtlinieneinstellung nicht konfigurieren oder aktivieren, werden Cookies von Websiten akzeptiert.</string>
      <string id="Cookies_AcceptThirdParty">Cookies und Website-Daten von Drittanbietern akzeptieren (Veraltet)</string>
      <string id="Cookies_AcceptThirdParty_Explain">Wenn Sie die Richtlinieneinstellung aktivieren und Cookies erlauben, können Sie einstellen wie Sie Cookies von Drittanbietern akzeptieren.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren oder wenn Cookies nicht erlaubt sind.</string>
      <string id="Cookies_AcceptThirdParty_All">Immer</string>
      <string id="Cookies_AcceptThirdParty_None">Nie</string>
      <string id="Cookies_AcceptThirdParty_FromVisited">Nur von besuchten Drittanbietern</string>
      <string id="Cookies_ExpireAtSessionEnd">Cookies behalten, bis Firefox geschlossen ist</string>
      <string id="Cookies_ExpireAtSessionEnd_Explain">Wenn Sie die Richtlinieneinstellung aktivieren und Cookies erlaubt sind, werden diese gelöscht sobald Firefox geschlossen wird.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren oder wenn "Cookies und Website-Daten annehmen" deaktiviert ist wird diese Richtlinieneinstellung ignoriert.</string>
      <string id="Cookies_RejectTracker">Tracker ablehnen (Veraltet)</string>
      <string id="Cookies_RejectTracker_Explain">Wenn diese Richtlinieneinstellung aktiviert ist und Cookies aktiviert sind, werden Tracker-Cookies standardmäßig abgewiesen.

Diese Einstellung wird ignoriert wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist oder Cookies durch eine andere Richtlinie oder Einstellung nicht erlaubt sind.</string>
      <string id="Cookies_Locked">Cookie Einstellungen sperren</string>
      <string id="Cookies_Locked_Explain">Wenn Sie die Richtlinieneinstellung aktivieren können Benutzer die Cookie-Einstellungen nicht ändern.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer die Cookie-Einstellungen ändern.</string>
      <string id="Cookies_Behavior">Cookie-Verhalten</string>
      <string id="Cookies_Behavior_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie das Cookie-Verhalten konfigurieren.

Wenn diese Richtlinieneinstellung nicht konfiguriert oder deaktiviert ist, werden Cookies für bekannte Tracker abgelehnt.</string>
      <string id="Cookies_BehaviorPrivateBrowsing">Cookie-Verhalten beim privaten Surfen</string>
      <string id="Cookies_BehaviorPrivateBrowsing_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie das Cookie-Verhalten beim privaten Surfen konfigurieren.

Wenn diese Richtlinieneinstellung nicht konfiguriert oder deaktiviert ist, werden beim privaten Surfen Cookies für bekannte Tracker abgelehnt und Cookies von Drittanbietern werden aufgeteilt.</string>
      <string id="Cookies_Behavior_Accept">Alle Cookies akzeptieren</string>
      <string id="Cookies_Behavior_RejectForeign">Cookies von Drittanbietern ablehnen</string>
      <string id="Cookies_Behavior_Reject">Alle Cookies ablehnen</string>
      <string id="Cookies_Behavior_LimitForeign">Cookies von Drittanbietern für Websites, die Sie nicht besucht haben, ablehnen</string>
      <string id="Cookies_Behavior_RejectTracker">Cookies für bekannte Tracker ablehnen</string>
      <string id="Cookies_Behavior_RejectTrackerAndPartitionForeign">Ablehnung von Cookies für bekannte Tracker und Partitionierung von Drittanbieter-Cookies (Vollständiger Cookie-Schutz)</string>
      <string id="Camera_Allow_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird die Kamera für die genannten Quellen automatisch freigegeben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Camera_Block_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird der Kamerazugriff für die genannten Quellen immer geblockt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Camera_BlockNewRequests">Neue Anfragen zur Kameranutzung blockieren</string>
      <string id="Camera_BlockNewRequests_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, sind Seiten, die nicht in der Erlauben-Richtlinie sind, daran gehindert nach der Berechrechtigung für den Kamera-Zugang zu fragen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann jede Seite, die nicht in der Blockieren-Richtlinie ist, nach der Berechtigung zur Kamera-Nutzung fragen.</string>
      <string id="Camera_Locked">Änderung der Einstellungen verbieten</string>
      <string id="Camera_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Nutzer nicht die Einstellungen für Kamera Zugriff ändern.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer die Einstellungen für den Kamera Zugriff ändern.</string>
      <string id="Microphone_Allow_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird das Mikrofon für die genannten Quellen automatisch freigegeben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Microphone_Block_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird der Mikrofonzugriff für die genannten Quellen immer geblockt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Microphone_BlockNewRequests">Neue Anfragen zur Mikrofon Nutzung blockieren</string>
      <string id="Microphone_BlockNewRequests_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Seiten, denen es nicht explizit erlaubt ist, das Mikrofon zu nutzen, keine Anfragen zur Mikrofon-Nutzung mehr stellen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann jede Seite, der es nicht verboten ist, das Mikrofon zu nutzen, die Erlaubnis zur Mikrofon-Nutzung zu erbitten.</string>
      <string id="Microphone_Locked">Änderung der Einstellungen verbieten</string>
      <string id="Microphone_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Einstellungen zur Mikrofon-Nutzung nicht vom Anwender geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer seine Einstellungen zur Mikrofon-Nutzung ändern.</string>
      <string id="Location_Allow_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird der Standort für die genannten Quellen automatisch freigegeben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Location_Block_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird der Standort für die genannten Quellen immer geblockt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Location_BlockNewRequests">Neue Anfragen zum Standort blockieren</string>
      <string id="Location_BlockNewRequests_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Seiten, denen es nicht explizit erlaubt ist, den Standort zu nutzen, keine Anfragen zum Standort mehr stellen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann jede Seite, der es nicht verboten ist, den Standort zu nutzen, die Erlaubnis zur Standort-Nutzung zu erbitten.</string>
      <string id="Location_Locked">Änderung der Einstellungen verbieten</string>
      <string id="Location_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Einstellungen zur Standort-Nutzung nicht vom Anwender geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer seine Einstellungen zur Standort-Nutzung ändern.</string>
      <string id="Notifications_Allow_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Benachrichtigungen für die genannten Quellen automatisch versendet werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Notifications_Block_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die genannten Quellen keine Benachrichtigungen versenden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung befolgt.</string>
      <string id="Notifications_BlockNewRequests">Neue Anfragen zum Senden von Benachrichtigungen blockieren</string>
      <string id="Notifications_BlockNewRequests_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Seiten, denen es nicht explizit erlaubt ist, Benachrichtigungen zu versenden, nicht mehr um Erlaubnis bitten, Benachrichtigungen senden zu dürfen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann jede Seite der es nicht verboten ist, Benachrichtigung zu senden, nach der Erlaubnis fragen, Benachrichtigungen zu versenden.</string>
      <string id="Notifications_Locked">Änderung der Einstellungen verbieten</string>
      <string id="Notifications_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Einstellungen zu Benachrichtigungen nicht vom Anwender geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer ihre Einstellungen zu Benachrichtigungen ändern.</string>
      <string id="Autoplay_Allow_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist die Automatische Wiedergabe von Medien für die angegebenen Quellen immer aktiviert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standardeinstellung zur Automatischen Wiedergabe von Medien befolgt.</string>
      <string id="Autoplay_Block_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist die Automatische Wiedergabe für die genannten Quellen immer deaktiviert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert, wird die Standardeinstellung zur Automatischen Wiedergabe von Medien befolgt.</string>
      <string id="Autoplay_Default">Standardmäßige Autoplay-Einstellungen</string>
      <string id="Autoplay_Default_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, sind die Einstellungen zu Autoplay festgesetzt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, ist die Automatische Audiowiedergabe standardmäßig blockiert.

Wichtig: Diese Richtlinieneinstellung funktioniert nicht mit Mozilla Firefox ESR.</string>
      <string id="Autoplay_Locked">Änderung der Einstellungen verbieten</string>
      <string id="Autoplay_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Einstellungen zu Autoplay nicht vom Anwender geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer ihre Einstellungen zu Autoplay ändern.</string>
      <string id="AllowAudioVideo">Automatische Audio- und Videowiedergabe erlauben</string>
      <string id="BlockAudio">Automatische Audiowiedergabe blockieren</string>
      <string id="BlockAudioVideo">Automatische Audio- und Videowiedergabe blockieren</string>
      <string id="VirtualReality_Allow_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist der Zugriff auf Virtual-Reality-Geräte für die angegebenen Quellen immer aktiviert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Standard-Richtlinie für Virtual Reality verwendet.</string>
      <string id="VirtualReality_Block_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist der Zugriff auf Virtual-Reality-Geräte für die angegebenen Quellen immer deaktiviert.

Wenn diese Richtlinieneinstellung deaktiviert, wird der Zugriff auf Virtual-Reality-Geräte nicht deaktiviert.</string>
      <string id="VirtualReality_BlockNewRequests">Neue Anfragen für den Zugriff auf Virtual-Reality-Geräte blockieren</string>
      <string id="VirtualReality_BlockNewRequests_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Seiten, denen es nicht explizit erlaubt ist, nicht mehr um Erlaubnis bitten, auf Virtual-Reality-Geräte zugreifen zu dürfen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann jede Seite, der es nicht verboten ist, nach der Erlaubnis fragen, auf Virtual-Reality-Geräte zuzugreifen.</string>
      <string id="VirtualReality_Locked">Änderung der Einstellungen verbieten</string>
      <string id="VirtualReality_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Einstellungen zu Virtual Reality nicht vom Anwender geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Nutzer ihre Einstellungen zu Virtual Reality ändern.</string>
      <string id="FirefoxHome">Firefox Home anpassen</string>
      <string id="FirefoxHome_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Sektionen festgelegt werden, die der Anwender sieht und der Anwender kann diese nicht mehr anpassen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden dem Anwender die standardmäßigen Sektionen angezeigt und der Anwender kann diese anpassen.</string>
      <string id="FlashPlugin_Allow_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, ist Flash standardmäßig für die angegebenen URLs aktiviert, es sei denn, Flash ist vollständig deaktiviert. Wenn eine Top-Level-Domäne angegeben ist (http://example.org), ist Flash auch für alle Sub-Domains zulässig.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Flash Richtlinie verwendet.</string>
      <string id="FlashPlugin_Block_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, ist Flash für die angegebenen URLs gesperrt. Wenn eine Top-Level-Domain angegeben ist (http://example.org), wird Flash von allen Sub-Domains ebenfalls blockiert.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Flash Richtlinie verwendet.</string>
      <string id="FlashPlugin_Default">Flash aktivieren</string>
      <string id="FlashPlugin_Default_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, ist Flash immer auf Websiten aktiviert.

Wenn Sie die Richtlinieneinstellung deaktivieren, wird Flash niemals auf Webseiten aktiviert, selbst wenn sie in der Zulassen-Liste aufgeführt sind.

Wenn Sie die Richtlinieneinstellung nicht konfigurieren, ist Flash ausführbar.</string>
      <string id="FlashPlugin_Locked">Flash-Einstellungen sperren</string>
      <string id="FlashPlugin_Locked_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer die Flash-Einstellungen nicht ändern.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer die Flash-Einstellungen ändern.</string>
      <string id="OverrideFirstRunPage">Willkommensseite ändern</string>
      <string id="OverrideFirstRunPage_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine URL angeben, die als Willkommensseite beim ersten Start von Firefox verwendet werden soll. Wenn Sie die URL leer lassen, wird keine Willkommensseite angezeigt.

Ab Firefox 83 oder Firefox ESR 78.5 lassen sich mehrere Willkommensseiten festlegen. Diese müssen durch das Zeichen | getrennt sein.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Standard Willkommensseite angezeigt.</string>
      <string id="OverridePostUpdatePage">Upgrade Seite ändern</string>
      <string id="OverridePostUpdatePage_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine URL angeben, die nach der Aktualisierung von Firefox angezeigt wird. Wenn Sie die URL leer lassen, wird keine Upgrade-Seite angezeigt.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren wird eine Upgrade Seite angezeigt.</string>
      <string id="SanitizeOnShutdown">Die Chronik löschen, wenn Firefox geschlossen wird (Verschoben)</string>
      <string id="SanitizeOnShutdown_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, werden alle Daten der Chronik gelöscht, wenn Firefox geschlossen wird. Dies beinhaltet Browsing &amp; Download Verlauf, Cookies, Aktive Logins, Cache, Form &amp; Suchverlauf, Website-Einstellungen und Offline-Website-Daten.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die keine Daten der Chronik gelöscht.</string>
      <string id="SanitizeOnShutdown_group">Browserdaten löschen, wenn der Browser geschlossen wird</string>

      <string id="SanitizeOnShutdown_Cache">Cache</string>
      <string id="SanitizeOnShutdown_Cache_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird der Cache gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird der Cache nicht gelöscht wenn der Browser geschlossen wird.</string>
      <string id="SanitizeOnShutdown_Cookies">Cookies</string>
      <string id="SanitizeOnShutdown_Cookies_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Cookies gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden die Cookies nicht gelöscht wenn der Browser geschlossen wird.</string>
      <string id="SanitizeOnShutdown_Downloads">Download-Historie (Veraltet)</string>
      <string id="SanitizeOnShutdown_Downloads_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird die Download-Historie gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Download-Historie nicht gelöscht, wenn der Browser geschlossen wird.</string>
      <string id="SanitizeOnShutdown_FormData">Eingegebene Suchbegriffe und Formulardaten (Veraltet)</string>
      <string id="SanitizeOnShutdown_FormData_Explain">Wenn diese Gruppenrichtlinieneinstellung aktiviert ist, werden eingegebene Suchbegriffe und Formulardaten gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden eingegebene Suchbegriffe und Formulardaten nicht gelöscht, wenn der Browser geschlossen wird.</string>
      <string id="SanitizeOnShutdown_History">Chronik</string>
      <string id="SanitizeOnShutdown_History_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird die Chronik gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Chronik nicht gelöscht, wenn der Browser geschlossen wird.</string>
      <string id="SanitizeOnShutdown_Sessions">Gespeicherte Logins</string>
      <string id="SanitizeOnShutdown_Sessions_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden gespeicherte Logins gelöscht, nachdem der Browser geschlossen wurde.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden gespeicherte Logins nicht gelöscht, nachdem der Browser geschlossen wurde.</string>
      <string id="SanitizeOnShutdown_SiteSettings">Website-Einstellungen</string>
      <string id="SanitizeOnShutdown_SiteSettings_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Website-Einstellungen gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Website-Einstellungen nicht gelöscht, nachdem der Browser geschlossen wurde.</string>
      <string id="SanitizeOnShutdown_OfflineApps">Offline Website Daten (Veraltet)</string>
      <string id="SanitizeOnShutdown_OfflineApps_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Offline Website-Daten gelöscht, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Offline Website-Daten nicht gelöscht, wenn der Browser geschlossen wird.</string>
      <string id="SanitizeOnShutdown_Locked">Gesperrt</string>
      <string id="SanitizeOnShutdown_Locked_Explain">Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert sind, können alle Einstellungen vom Nutzer geändert werden.

Wenn diese Richtlinieneinstellung aktiviert ist, können Einstellungen vom Nutzer nicht geändert werden.</string>
      <string id="WebsiteFilter_Block">Gesperrte Webseiten</string>
      <string id="WebsiteFilter_Block_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie die Übereinstimmungsmuster für zu blockierende Webseiten angeben. Die Übereinstimmungsmuster sind unter https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns dokumentiert. Es gibt eine Beschränkung von 1000 Einträgen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden keine Webseiten gesperrt.</string>
      <string id="WebsiteFilter_Exceptions">Ausnahmen zu gesperrten Webseiten</string>
      <string id="WebsiteFilter_Exceptions_Explain">Wenn Sie die Richtlinieneinstellung aktivieren und der Webseiten-Filter aktiviert ist, können Sie Übereinstimmungsmuster für Webseiten angeben, die nicht blockiert werden sollen. Die Übereinstimmungsmuster sind unter https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns dokumentiert. Es gibt eine Beschränkung von 1000 Einträgen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, gibt es keine Ausnahmen für den Webseiten-Filter.</string>
      <string id="WebsiteFilterOneLine">Webseiten-Filter (Einzeiliger JSON-Code)</string>
      <string id="WebsiteFilter">Webseiten-Filter (JSON)</string>
      <string id="WebsiteFilter_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie blockierte Webseiten und Ausnahmen über JSON angeben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Webseiten nicht gefiltert.</string>
      <string id="Bookmark01">Lesezeichen 01</string>
      <string id="Bookmark02">Lesezeichen 02</string>
      <string id="Bookmark03">Lesezeichen 03</string>
      <string id="Bookmark04">Lesezeichen 04</string>
      <string id="Bookmark05">Lesezeichen 05</string>
      <string id="Bookmark06">Lesezeichen 06</string>
      <string id="Bookmark07">Lesezeichen 07</string>
      <string id="Bookmark08">Lesezeichen 08</string>
      <string id="Bookmark09">Lesezeichen 09</string>
      <string id="Bookmark10">Lesezeichen 10</string>
      <string id="Bookmark11">Lesezeichen 11</string>
      <string id="Bookmark12">Lesezeichen 12</string>
      <string id="Bookmark13">Lesezeichen 13</string>
      <string id="Bookmark14">Lesezeichen 14</string>
      <string id="Bookmark15">Lesezeichen 15</string>
      <string id="Bookmark16">Lesezeichen 16</string>
      <string id="Bookmark17">Lesezeichen 17</string>
      <string id="Bookmark18">Lesezeichen 18</string>
      <string id="Bookmark19">Lesezeichen 19</string>
      <string id="Bookmark20">Lesezeichen 20</string>
      <string id="Bookmark21">Lesezeichen 21</string>
      <string id="Bookmark22">Lesezeichen 22</string>
      <string id="Bookmark23">Lesezeichen 23</string>
      <string id="Bookmark24">Lesezeichen 24</string>
      <string id="Bookmark25">Lesezeichen 25</string>
      <string id="Bookmark26">Lesezeichen 26</string>
      <string id="Bookmark27">Lesezeichen 27</string>
      <string id="Bookmark28">Lesezeichen 28</string>
      <string id="Bookmark29">Lesezeichen 29</string>
      <string id="Bookmark30">Lesezeichen 30</string>
      <string id="Bookmark31">Lesezeichen 31</string>
      <string id="Bookmark32">Lesezeichen 32</string>
      <string id="Bookmark33">Lesezeichen 33</string>
      <string id="Bookmark34">Lesezeichen 34</string>
      <string id="Bookmark35">Lesezeichen 35</string>
      <string id="Bookmark36">Lesezeichen 36</string>
      <string id="Bookmark37">Lesezeichen 37</string>
      <string id="Bookmark38">Lesezeichen 38</string>
      <string id="Bookmark39">Lesezeichen 39</string>
      <string id="Bookmark40">Lesezeichen 40</string>
      <string id="Bookmark41">Lesezeichen 41</string>
      <string id="Bookmark42">Lesezeichen 42</string>
      <string id="Bookmark43">Lesezeichen 43</string>
      <string id="Bookmark44">Lesezeichen 44</string>
      <string id="Bookmark45">Lesezeichen 45</string>
      <string id="Bookmark46">Lesezeichen 46</string>
      <string id="Bookmark47">Lesezeichen 47</string>
      <string id="Bookmark48">Lesezeichen 48</string>
      <string id="Bookmark49">Lesezeichen 49</string>
      <string id="Bookmark50">Lesezeichen 50</string>
      <string id="Bookmark_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie ein Lesezeichen konfigurieren, das zu Firefox hinzugefügt wird. Aufgrund eines Fehlers müssen Sie den Speicherort auswählen. Beachten Sie, dass Sie die Lesezeichen in der Reihenfolge angeben müssen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden keine Lesezeichen zu Firefox hinzugefügt.</string>
      <string id="BookmarkPlacementToolbar">Werkzeugleiste</string>
      <string id="BookmarkPlacementMenu">Menü</string>
      <string id="NoDefaultBookmarks">Keine Standard-Lesezeichen</string>
      <string id="NoDefaultBookmarks_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, werden die Standardlesezeichen und Smart-Lesezeichen (meist besuchte, neueste Tags) nicht erstellt.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden die Standardlesezeichen und Smart-Lesezeichen (meist besuchte, neueste Tags) erstellt.

Hinweis: Diese Richtlinieneinstellung ist nur wirksam, wenn Sie vor der ersten Ausführung von Firefox verwendet wurde.</string>
      <string id="HomepageURL">Startseite</string>
      <string id="HomepageURL_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine Startseite festlegen. Sie können die Startseite auch sperren.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Benutzer eine Startseite festlegen.</string>
      <string id="HomepageAdditional">Zusätzliche Startseiten</string>
      <string id="HomepageAdditional_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie zusätzliche Startseiten festlegen. Diese werden in mehreren Registerkarten geöffnet.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird nur eine Startseite angezeigt.</string>
      <string id="HomepageStartPage">Startseite beim Start</string>
      <string id="HomepageStartPage_Explain">Mit dieser aktivierten Richtlinieneinstellung kann definiert werden, ob die Startseite, die vorherige Sitzung oder eine leere Seite beim Start angezeigt wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird standardmäßig die letzte Sitzung beim Start geöffnet.</string>
      <string id="None">Leere Seite</string>
      <string id="Homepage">Startseite</string>
      <string id="PreviousSession">Letzte Sitzung</string>
      <string id="HomepageLocked">Homepage (Gesperrt)</string>
      <string id="Homepage_ShowHomeButton">Home-Button zur Symbolleiste hinzufügen</string>
      <string id="Homepage_ShowHomeButton_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, erscheint der Home-Button standardmäßig in der Symbolleiste.

Wenn diese Richtlinieneinstellung deaktiviert ist, erscheint der Home-Button standardmäßig nicht in der Symbolleiste.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, entscheidet Firefox selbst, ob der Home-Button standardmäßig in der Symbolleiste erscheint oder nicht.</string>
      <string id="PasswordManagerEnabled">Passwort-Manager</string>
      <string id="PasswordManagerEnabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, ist der Passwort-Manager nicht über die Einstellungen verfügbar.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, ist der Passwort-Manager über die Einstellungen verfügbar.</string>
      <string id="PasswordManagerExceptions">Password Manager Ausnahmen</string>
      <string id="PasswordManagerExceptions_Explain">Wenn diese Richtlinieeinstellung aktiviert ist, können Sie Webseiten angeben, auf denen Firefox das Speichern von Passwörtern nicht anbietet.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, bietet Firefox die Speicherung von Passwörtern auf allen Webseiten an.</string>
      <string id="PromptForDownloadLocation">Abfrage des Download-Verzeichnisses</string>
      <string id="PromptForDownloadLocation_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird der Nutzer nicht nach einem Verzeichnis für den Download gefragt.

Wenn diese Richtlinieneinstellung aktiviert ist, wird der Nutzer immer nach einem Verzeichnis für den Download gefragt.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, wird der Nutzer nach einem Verzeichnis für den Download gefragt aber kann die Einstellung ändern.</string>
      <string id="Proxy">Proxy-Einstellungen</string>
      <string id="Proxy_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie die Netzwerkeinstellungen ändern und sperren.

Wählen Sie den Verbindungstyp und füllen Sie die entsprechenden Abschnitte aus. Aufgrund eines Fehlers müssen Sie einen Wert für die SOCKS-Proxy-Version auswählen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden die Standard Netzwerk-Einstellungen verwenden. Diese können vom Benutzer verändert werden.</string>
      <string id="SOCKSVersion4">SOCKS v4</string>
      <string id="SOCKSVersion5">SOCKS v5</string>
      <string id="AutoConfigURL">Automatische Proxy-Konfigurations-Adresse</string>
      <string id="AutoConfigURL_Explain">Dies sollten Sie nur einstellen, wenn Sie autoConfig ausgewählt haben</string>
      <string id="Passthrough">Proxy-Ausnahmen</string>
      <string id="Passthrough_Explain">Dies sollten Sie nur einstellen, wenn Sie manuelle Proxy-Konfiguration ausgewählt haben</string>
      <string id="Connection">Verbindungs-Einstellungen</string>
      <string id="NoProxy">Kein Proxy</string>
      <string id="SystemProxy">Proxy-Einstellungen des Systems verwenden</string>
      <string id="ManualProxy">Manuelle Proxy-Konfiguration</string>
      <string id="AutoDetectProxy">Die Proxy-Einstellungen für dieses Netzwek automatisch erkennen</string>
      <string id="AutoConfigProxy">Automatische Proxy-Konfiguration</string>
      <string id="TrackingProtection">Schutz vor Aktivitätenverfolgung (Verschoben)</string>
      <string id="TrackingProtection_Explain">Wenn Sie die Richtlinieneinstellung nicht konfigurieren, ist der Aktivitäten-Verfolgungsschutz im Browser standardmäßig nicht aktiviert, im privaten Browser ist er jedoch standardmäßig aktiviert und kann vom Benutzer geändert werden.

Wenn Sie die Richtlinieneinstellung deaktivieren, ist der Aktivitäten-Verfolgungsschutz sowohl im Browser als auch im privaten Browser deaktiviert und gesperrt.

Wenn Sie die Richtlinieneinstellung aktivieren, privates Surfen ist sowohl im Browser als auch im privaten Browser standardmäßig aktiviert, und Sie können festlegen, ob der Benutzer die Änderung ändern soll oder nicht</string>
      <string id="TrackingProtection_group">Tracking-Schutz</string>
      <string id="TrackingProtection_Value">Aktiviert</string>
      <string id="TrackingProtection_Value_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist der Schutz vor Aktivitätenverfolgung aktiviert.

Wenn diese Richtlinieneinstellung deaktiviert ist, ist der Schutz vor Aktivitätenverfolgung deaktiviert und kann nicht vom Nutzer aktiviert werden.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, sind die Standardeinstellungen zum Schutz vor Aktivitätenverfolgung aktiviert und der Nutzer kann die Einstellungen ändern.</string>
      <string id="TrackingProtection_Cryptomining">Cryptomining</string>
      <string id="TrackingProtection_Cryptomining_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Cryptomining-Skripte blockiert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Cryptomining-Skripte nicht blockiert.</string>
      <string id="TrackingProtection_Fingerprinting">Identifizierer (Fingerprinter)</string>
      <string id="TrackingProtection_Fingerprinting_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Identifizierer-Skripte blockiert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Identifizierer-Skripte nicht blockiert.</string>
      <string id="TrackingProtection_Exceptions">Ausnahmen</string>
      <string id="TrackingProtection_Exceptions_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Ausnahmen-Seiten definiert werden, bei denen der Schutz vor Aktivitätenverfolgung deaktiviert ist.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, gelten die gesetzten Einstellungen für alle Webseiten.</string>
      <string id="TrackingProtection_Locked">Änderungen an den Einstellungen zum Schutz vor Aktivitätenverfolgung verbieten</string>
      <string id="TrackingProtection_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Nutzer keine Änderungen an den Einstellungen zum Schutz vor Aktivitätenverfolgung vornehmen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann der Nutzer Änderungen an den Einstellungen vornehmen.</string>
      <string id="TrackingProtection_EmailTracking">E-Mail-Verfolgung</string>
      <string id="TrackingProtection_EmailTracking_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden versteckte E-Mail-Pixel und Skripte auf Websites blockiert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden versteckte E-Mail-Pixel und Skripte auf Websites nicht blockiert.</string>
      <string id="RequestedLocales">Angefordertes Gebietsschema</string>
      <string id="RequestedLocalesString">Angefordertes Gebietsschema (Zeichenfolge)</string>
      <string id="RequestedLocales_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine Liste der angeforderten Gebietsschemata für die Anwendung in der Reihenfolge ihrer Präferenz angeben. Dadurch wird das entsprechende Sprachpaket aktiv.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet die Anwendung das Standardgebietsschema.</string>
      <string id="SecurityDevices">Sicherheitsgeräte</string>
      <string id="SecurityDevices_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine Liste mit PKCS #11 Modulen definieren. Module werden mit einem Namen und einem qualifizierten Pfad benannt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden keine weiteren PKCS #11 Module installiert.</string>
      <string id="SecurityDevices_Add">Hinzufügen</string>
      <string id="SecurityDevices_Delete">Entfernen</string>
      <string id="SecurityDevices_Delete_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie die Namen der zu entfernenden PKCS #11-Module angeben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden keine PKCS #11-Module entfernt.</string>
      <string id="SearchBar">Position der Suchleiste</string>
      <string id="SearchBar_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie festlegen, ob die Suchleiste von der Adressleiste getrennt ist.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, erhalten neue Benutzer eine einheitliche Suchleiste, Benutzer, die ab Firefox 56 und darunter aktualisieren, erhalten eine separate Suchleiste.</string>
      <string id="SearchEngines_1">Suchmaschine 1</string>
      <string id="SearchEngines_2">Suchmaschine 2</string>
      <string id="SearchEngines_3">Suchmaschine 3</string>
      <string id="SearchEngines_4">Suchmaschine 4</string>
      <string id="SearchEngines_5">Suchmaschine 5</string>
      <string id="SearchEngines_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie eine Suchmaschine zu Firefox hinzufügen. Verwenden Sie {searchTerms}, um anzugeben, wo der Suchbegriff platziert wird. Aufgrund eines Fehlers müssen Sie die Methode auswählen (normalerweise GET). Beachten Sie, dass Sie die Suchmaschinen der Reihe nach angeben müssen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird keine neue Suchmaschine hinzugefügt.</string>
      <string id="SearchBar_Unified">Adressleiste für Suche und Seitenaufrufe verwenden</string>
      <string id="SearchBar_Separate">Suchleiste zur Symbolleiste hinzufügen</string>
      <string id="SearchEngine_Method_GET">GET</string>
      <string id="SearchEngine_Method_POST">POST</string>
      <string id="SearchEngines_Default">Standardsuchmaschine</string>
      <string id="SearchEngines_Default_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie den Namen einer Suchmaschine festlegen, die als Standard verwendet werden soll.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, wird die Firefox Standard-Suchmaschine verwendet.</string>
      <string id="SearchEngines_PreventInstalls">Installation von Suchmaschinen verhindern</string>
      <string id="SearchEngines_PreventInstalls_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Benutzer keine Suchmaschinen hinzufügen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, können Suchmaschinen hinzugefügt werden.</string>
      <string id="SearchEngines_Remove">Suchmaschinen entfernen</string>
      <string id="SearchEngines_Remove_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, können Sie die Namen der Suchmaschinen definieren welche entfernt oder ausgeblendet werden sollen.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, werden keine Suchmaschinen entfernt bzw. ausgeblendet.</string>
      <string id="SearchSuggestEnabled">Suchvorschläge</string>
      <string id="SearchSuggestEnabled_Explain">Wenn Sie die Richtlinieneinstellung deaktivieren werden die Suchvorschläge abgeschaltet.

Wenn Sie die Richtlinieneinstellung aktivieren, werden Suchvorschläge aktiviert.

Wenn Sie die Richtlinieneinstellung nicht konfigurieren, werden Suchvorschläge aktiviert, aber der Benutzer kann diese abschalten.</string>
      <string id="SSLVersionMin">Minimale SSL-Version</string>
      <string id="SSLVersionMin_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, Firefox wird keine SSL/TLS-Version unter der angegebenen Version verwenden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, verwendet Firefox die Standardeinstellung von TLS 1.2.</string>
      <string id="SSLVersionMax">Maximale SSL-Version</string>
      <string id="SSLVersionMax_Explain">Wenn Sie die Richtlinieneinstellung aktivieren, Firefox wird keine höhere SSL/TLS-Version als der angegebenen Version verwenden.

Wenn Sie die Richtlinieneinstellung deaktivieren oder nicht konfigurieren, verwendet Firefox die Standardeinstellung von TLS 1.3.</string>
      <string id="TLS1">TLS 1.0</string>
      <string id="TLS1_1">TLS 1.1</string>
      <string id="TLS1_2">TLS 1.2</string>
      <string id="TLS1_3">TLS 1.3</string>
      <string id="SupportMenu">Support Menü</string>
      <string id="SupportMenu_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird ein Menüeintrag mit spezifizierten Supportinformationen zum "Hilfe"-Menü hinzugefügt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird kein Menüeintrag hinzugefügt.</string>
      <string id="UserMessaging_WhatsNew">Neue Funktionen und Änderungen (Veraltet)</string>
      <string id="UserMessaging_WhatsNew_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden das Symbol und der Menüpunkt "Neue Funktionen und Änderungen" nicht angezeigt.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden das Symbol und der Menüpunkt "Neue Funktionen und Änderungen" angezeigt.</string>
      <string id="UserMessaging_ExtensionRecommendations">Empfehlungen zur Erweiterung</string>
      <string id="UserMessaging_ExtensionRecommendations_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden Erweiterungen nicht empfohlen, wenn der Benutzer Webseiten besucht.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden Erweiterungen empfohlen, wenn der Benutzer Webseiten besucht.</string>
      <string id="UserMessaging_FeatureRecommendations">Funktions-Empfehlungen</string>
      <string id="UserMessaging_FeatureRecommendations_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden keine Firefox-Funktionen empfohlen, wenn der Benutzer Firefox verwendet.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden Firefox-Funktionen empfohlen, wenn der Benutzer Firefox verwendet.</string>
      <string id="UserMessaging_UrlbarInterventions">Adressleisten-Empfehlungen</string>
      <string id="UserMessaging_UrlbarInterventions_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden basierend darauf, was der Benutzer in die Adressleiste eingibt, keine Aktionen empfohlen.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden Aktionen empfohlen, die darauf basieren, was der Benutzer in die Adressleiste eingibt.</string>
      <string id="UserMessaging_SkipOnboarding">Onboarding überspringen</string>
      <string id="UserMessaging_SkipOnboarding_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden auf der neuen Registerkarte keine Onboarding-Nachrichten angezeigt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden auf der neuen Registerkarte Onboarding-Nachrichten angezeigt.</string>
      <string id="UserMessaging_MoreFromMozilla">Mehr von Mozilla</string>
      <string id="UserMessaging_MoreFromMozilla_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird der Abschnitt "Mehr von Mozilla" in den Einstellungen nicht angezeigt.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird der Abschnitt "Mehr von Mozilla" in den Einstellungen angezeigt.</string>
      <string id="UserMessaging_FirefoxLabs">Firefox Labs</string>
      <string id="UserMessaging_FirefoxLabs_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird der Abschnitt "Firefox Labs" in den Einstellungen nicht angezeigt.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird der Abschnitt "Firefox Labs" in den Einstellungen angezeigt.</string>
      <string id="UserMessaging_Locked">Keine Änderung der Einstellungen für die Nachrichtenübermittlung zulassen</string>
      <string id="UserMessaging_Locked_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, können die Einstellungen für die Nachrichtenübermittlung vom Benutzer geändert werden.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, können die Einstellungen für die Nachrichtenübermittlung nicht vom Benutzer geändert werden.</string>
      <string id="DisabledCiphers_TLS_DHE_RSA_WITH_AES_128_CBC_SHA">TLS_DHE_RSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_DHE_RSA_WITH_AES_256_CBC_SHA">TLS_DHE_RSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA">TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA">TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256">TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256">TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_128_CBC_SHA">TLS_RSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_256_CBC_SHA">TLS_RSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_3DES_EDE_CBC_SHA">TLS_RSA_WITH_3DES_EDE_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_128_GCM_SHA256">TLS_RSA_WITH_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_RSA_WITH_AES_256_GCM_SHA384">TLS_RSA_WITH_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA">TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA">TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384">TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256">TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384">TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256">TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256</string>
      <string id="DisabledCiphers_TLS_CHACHA20_POLY1305_SHA256">TLS_CHACHA20_POLY1305_SHA256</string>
      <string id="DisabledCiphers_TLS_AES_128_GCM_SHA256">TLS_AES_128_GCM_SHA256</string>
      <string id="DisabledCiphers_TLS_AES_256_GCM_SHA384">TLS_AES_256_GCM_SHA384</string>
      <string id="DisabledCiphers_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist die ausgewählte Verschlüsselung deaktiviert.

Wenn diese Richtlinieneinstellung deaktiviert ist, ist der ausgewählte Verschlüsselung aktiviert.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, werden die Standardeinstellungen in Firefox verwendet.</string>
      <string id="EncryptedMediaExtensions_Enabled">DRM-Medien Erweiterungen</string>
      <string id="EncryptedMediaExtensions_Enabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden DRM-Medien Erweiterungen (wie z.B. Widevine) nicht durch Mozilla Firefox heruntergeladen außer der Nutzer stimmt dem Download explizit zu.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden DRM-Medien Erweiterungen (wie z.B. Widevine) nicht durch Mozilla Firefox heruntergeladen und benutzt.</string>
      <string id="EncryptedMediaExtensions_Locked">Einstellung zu DRM-Medien Erweiterungen sperren</string>
      <string id="EncryptedMediaExtensions_Locked_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, können Nutzer Änderungen an der Einstellung zu DRM-Medien Erweiterungen vornehmen.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, können Nutzer die Einstellung zu DRM-Medien Erweiterungen nicht ändern.</string>
      <string id="PDFjs_Enabled">PDF.js (integrierter PDF-Betrachter) aktivieren</string>
      <string id="PDFjs_Enabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird der integrierte PDF-Betrachter nicht verwendet.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird der integrierte PDF-Betrachter verwendet.</string>
      <string id="PDFjs_EnablePermissions">PDF-Berechtigungen aktivieren</string>
      <string id="PDFjs_EnablePermissions_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Berechtigungen des PDF-Dokuments (wie z.B. Markieren und Kopieren von Text verhindern) angewendet.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Berechtigungen im PDF-Dokument ignoriert.</string>
      <string id="PictureInPicture_Enabled">Aktiviert</string>
      <string id="PictureInPicture_Enabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, ist die Bild-in-Bild Funktion deaktiviert.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, ist die Bild-in-Bild Funktion verfügbar.</string>
      <string id="PictureInPicture_Locked">Gesperrt</string>
      <string id="PictureInPicture_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Bild-in-Bild-Einstellungen vom Benutzer nicht geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können die Bild-in-Bild-Einstellungen vom Benutzer geändert werden.</string>
      <string id="PrimaryPassword">Masterpasswort</string>
      <string id="PrimaryPassword_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist das Setzen eines Masterpasswortes erforderlich.

Wenn diese Richtlinieneinstellung deaktiviert ist, ist das Setzen eines Masterpasswortes nicht möglich.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, können Nutzer ein Masterpasswort setzen, dies ist aber nicht erforderlich.</string>
      <string id="HandlersOneLine">Handlers (Einzeiliger JSON-Code)</string>
      <string id="Handlers">Handlers</string>
      <string id="Handlers_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie das Anwendungsverhalten (beim Anklicken eines Links oder beim Download) mittels JSON beschreiben.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden die Standardeinstellungen verwendet und können vom Nutzer modifiziert werden.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#handlers (Englisch)</string>
      <string id="PreferencesOneLine">Einstellungen (Einzeiliger JSON-Code)</string>
      <string id="Preferences">Einstellungen</string>
      <string id="Preferences_Explain">Hinweis: Um diese Richtlinieneinstellungen nutzen zu können, müssen Sie zunächst alle Einstellungen im Abschnitt Einstellungen (Veraltet) löschen.

Wenn diese Richtlinieneinstellung aktiviert ist, können Sie Einstellungen mit Hilfe einer JSON-Datei konfigurieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden die Einstellungen nicht verändert.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#preferences (Englisch)</string>
      <string id="BookmarksOneLine">Lesezeichen (Einzeiliger JSON-Code)</string>
      <string id="Bookmarks">Lesezeichen (JSON)</string>
      <string id="Bookmarks_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie Lesezeichen mit JSON konfigurieren. Mit [] können Sie alle Lesezeichen löschen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden die Richtlinien zu individuellen Lesezeichen verwendet.

Wenn diese Richtlinieneinstellung zusammen mit der für die individuellen Lesezeichen verwendet wird, werden die individuellen Lesezeichen nicht hinzugefügt.

Diese Richtlinieneinstellung wirkt sich nicht auf verwaltete Lesezeichen aus.

Für Informationen über die JSON-Struktur, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#bookmarks (Englisch)</string>
      <string id="ManagedBookmarksOneLine">Verwaltete Lesezeichen (Einzeiliger JSON-Code)</string>
      <string id="ManagedBookmarks">Verwaltete Lesezeichen</string>
      <string id="ManagedBookmarks_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie verwaltete Lesezeichen mit Hilfe einer JSON-Datei konfigurieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden keine verwalteten Lesezeichen hinzugefügt.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#managedbookmarks (Englisch)</string>
      <string id="AllowedDomainsForApps">Domains, die auf Google Workspace zugreifen dürfen</string>
      <string id="AllowedDomainsForApps_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Anwender nur für definierte Domains auf Google Workspace zugreifen. Domains werden durch Kommas getrennt. Um den Zugriff auf Gmail zu erlauben, fügen Sie consumer_accounts hinzu.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, können Anwender auf jedes Konto bei Google Workspace und auch auf Gmail zugreifen.</string>
      <string id="BackgroundAppUpdate">Hintergrund-Updates</string>
      <string id="BackgroundAppUpdate_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird die Anwendung keine Updates im Hintergrund installieren, wenn sie geschlossen ist.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, können Updates ohne Genehmigung durch den Anwender im Hintergrund installiert werden, selbst wenn die Anwendung nicht gestartet ist. Das Betriebssystem kann jedoch den Anwender um Genehmigung fragen.</string>
      <string id="AutoLaunchProtocolsFromOriginsOneLine">Externe Protokolle automatisch ausführen (Einzeiliger JSON-Code)</string>
      <string id="AutoLaunchProtocolsFromOrigins">Externe Protokolle automatisch ausführen</string>
      <string id="AutoLaunchProtocolsFromOrigins_Explain">Wenn diese Richtlinieneinstellung aktiviert, können Sie eine Liste von externen Protokollen definieren, die bei bestimmten URLs ohne Nachfrage beim Benutzer automatisch ausgeführt werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird jede Seite, die ein externes Protokoll ausführt, den Benutzer um Erlaubnis fragen.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#autolaunchprotocolsfromorigins (Englisch)</string>
      <string id="WindowsSSO">Windows SSO</string>
      <string id="WindowsSSO_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, verwendet Firefox in Windows gespeicherte Zugangsdaten, um Sie auf Webseiten von Microsoft anzumelden, einschließlich Outlook, Office 365 sowie allen anderen Geschäfts- oder Schulkonten, die Microsofts Authentifizierung verwenden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, müssen die Zugangsdaten manuell eingegeben werden.</string>
      <string id="UseSystemPrintDialog">Druckdialog des Betriebssystems verwenden</string>
      <string id="UseSystemPrintDialog_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, verwendet Firefox den Druckdialog des Betriebssystems, anstatt vorher eine Druckvorschau anzuzeigen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, zeigt Firefox vor dem Drucken eine Druckvorschau an.</string>
      <string id="ExemptDomainFileTypePairsFromFileTypeDownloadWarningsOneLine">Deaktivieren von Warnungen auf der Grundlage von Dateierweiterungen für bestimmte Dateitypen in Domänen (Einzeiliger JSON-Code)</string>
      <string id="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings">Deaktivieren von Warnungen auf der Grundlage von Dateierweiterungen für bestimmte Dateitypen in Domänen</string>
      <string id="ExemptDomainFileTypePairsFromFileTypeDownloadWarnings_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine Liste von Domänen und Dateierweiterungen definieren, die von Warnungen ausgenommen sind.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden Warnungen für alle ausführbaren Dateitypen angezeigt.

Ausführliche Informationen zur Erstellung der Richtlinie finden Sie unter https://github.com/mozilla/policy-templates/blob/master/README.md#exemptdomainfiletypepairsfromfiletypedownloadwarnings.</string>
      <string id="StartDownloadsInTempDirectory">Downloads im temporären Verzeichnis starten</string>
      <string id="StartDownloadsInTempDirectory_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, startet Firefox Downloads in einem temporären Verzeichnis und löscht sie automatisch, wenn der Browser geschlossen wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, startet Firefox Downloads im Download-Ordner und sie werden nicht automatisch gelöscht, wenn der Browser geschlossen wird.</string>
      <string id="GoToIntranetSiteForSingleWordEntryInAddressBar">Erzwingen einer direkten Intranet-Seitennavigation bei der Eingabe einzelner Wörter in der Adresszeile</string>
      <string id="GoToIntranetSiteForSingleWordEntryInAddressBar_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird bei der Eingabe von einzelnen Wörtern in die Adressleiste versucht, zuerst zu Intranetsites zu navigieren, und erst auf die Suche zurückgegriffen, wenn die DNS-Anfrage fehlschlägt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird bei der Eingabe von einzelnen Wörtern in die Adressleiste eine Suche durchgeführt.</string>
      <string id="AppUpdatePin">Aktualisierungen auf eine bestimmte Version begrenzen</string>
      <string id="AppUpdatePin_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine Firefox-Version als xx. oder xx.xx. angeben und Firefox wird nicht über diese Haupt- oder Nebenversion hinaus aktualisiert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird Firefox normal aktualisiert.</string>
      <string id="Proxy_Locked">Keine Änderung der Proxy-Einstellungen zulassen</string>
      <string id="Proxy_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Proxy-Einstellungen vom Benutzer nicht geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann der Benutzer seine Proxy-Einstellungen ändern.</string>
      <string id="Proxy_ConnectionType">Verbindungstyp</string>
      <string id="Proxy_ConnectionType_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie den Verbindungstyp festlegen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox standardmäßig den Systemproxy.</string>
      <string id="Proxy_HTTPProxy">HTTP Proxy</string>
      <string id="Proxy_HTTPProxy_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie den HTTP-Proxy festlegen, der verwendet wird, wenn eine manuelle Proxy-Konfiguration angegeben ist.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox keinen HTTP-Proxy.</string>
      <string id="Proxy_UseHTTPProxyForAllProtocols">HTTP-Proxy für HTTPS verwenden</string>
      <string id="Proxy_UseHTTPProxyForAllProtocols_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird der HTTP-Proxy für HTTPS verwendet, wenn eine manuelle Proxy-Konfiguration angegeben ist.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox keinen HTTPS-Proxy, es sei denn, er ist angegeben.</string>
      <string id="Proxy_SSLProxy">HTTPS Proxy</string>
      <string id="Proxy_SSLProxy_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie den HTTPS-Proxy festlegen, der verwendet wird, wenn eine manuelle Proxy-Konfiguration angegeben ist.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox keinen HTTPS-Proxy.</string>
      <string id="Proxy_SOCKSProxy">SOCKS Host</string>
      <string id="Proxy_SOCKSProxy_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie den SOCKS-Host und die Version festlegen, die verwendet werden, wenn eine manuelle Proxy-Konfiguration angegeben wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox keinen SOCKS-Host.</string>
      <string id="Proxy_AutoConfigURL">URL für automatische Proxy-Konfiguration</string>
      <string id="Proxy_AutoConfigURL_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie die URL für die automatische Proxy-Konfiguration festlegen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox keine URL für die automatische Proxy-Konfiguration.</string>
      <string id="Proxy_Passthrough">Proxy Passthrough</string>
      <string id="Proxy_Passthrough_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden die Proxy-Einstellungen für die angegebenen Standorte umgangen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, umgeht Firefox den Proxy nicht.</string>
      <string id="Proxy_AutoLogin">Keine Aufforderung zur Authentifizierung, wenn das Passwort gespeichert ist</string>
      <string id="Proxy_AutoLogin_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird Firefox nicht zur Proxy-Authentifizierung auffordern, wenn ein Kennwort gespeichert wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird Firefox immer zur Proxy-Authentifizierung auffordern.</string>
      <string id="Proxy_UseProxyForDNS">Proxy DNS bei Verwendung von SOCKS</string>
      <string id="Proxy_UseProxyForDNS_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird bei Verwendung eines SOCKS-Proxies die DNS-Anfragen vom Proxy-Server aufgelöst.

Wenn diese Richtlinieneinstellung deaktiviert ist, wird DNS bei der Verwendung von SOCKS nicht über einen Proxy geleitet.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, wird DNS bei der Verwendung von SOCKS v4 nicht über einen Proxy geleitet, bei SOCKS v5 wird die DNS-Anfrage vom Proxy-Server aufgelöst</string>

      <string id="DisableThirdPartyModuleBlocking">Blockierung von Drittanbietermodulen deaktivieren</string>
      <string id="DisableThirdPartyModuleBlocking_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, dürfen Benutzer keine Module von Drittanbietern auf der Seite about:third-party blockieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, dürfen Benutzer Module von Drittanbietern auf der Seite about:third-party blockieren.</string>
      <string id="ContainersOneLine">Container (Einzeiliger JSON-Code)</string>
      <string id="Containers">Container</string>
      <string id="Containers_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie JSON verwenden, um die Standardcontainer zu konfigurieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden die integrierten Standardwerte verwendet.

Ausführliche Informationen zur Erstellung der Richtlinie finden Sie unter https://github.com/mozilla/policy-templates/blob/master/README.md#containers.</string>
      <string id="FirefoxSuggest_WebSuggestions">Vorschläge aus dem Internet</string>
      <string id="FirefoxSuggest_WebSuggestions_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, erhalten Sie von Firefox Vorschläge, die mit Ihrer Suche zusammenhängen.

Wenn diese Richtlinieneinstellung deaktiviert ist, erhalten Sie diese Vorschläge nicht.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, erhalten Sie von Firefox Vorschläge für Ihre Suche.</string>
      <string id="FirefoxSuggest_SponsoredSuggestions">Vorschläge von Sponsorens</string>
      <string id="FirefoxSuggest_SponsoredSuggestions_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden Sie die Entwicklung von Firefox mit gelegentlichen gesponserten Vorschlägen unterstützen.

Wenn diese Richtlinieneinstellung deaktiviert ist, werden Sie diese Vorschläge nicht erhalten.

Wenn diese Richtlinieneinstellung nicht konfiguriert ist, erhalten Sie gelegentlich gesponserte Vorschläge.</string>
      <string id="FirefoxSuggest_ImproveSuggest">Verbessern Sie Firefox Suggest</string>
      <string id="FirefoxSuggest_ImproveSuggest_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, tragen Sie zu einem besseren Sucherlebnis bei, indem Sie Mozilla erlauben, Ihre Suchanfragen zu verarbeiten.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, erlauben Sie Mozilla nicht, Ihre Suchanfragen zu verarbeiten.</string>
      <string id="FirefoxSuggest_Locked">Keine Änderung der Firefox Suggest-Einstellungen zulassen</string>
      <string id="FirefoxSuggest_Locked_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können die Firefox Suggest-Einstellungen nicht vom Benutzer geändert werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, kann der Benutzer seine Firefox Suggest-Einstellungen ändern..</string>
      <string id="PrintingEnabled">Drucken</string>
      <string id="PrintingEnabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, ist das Drucken deaktiviert.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, ist das Drucken aktiviert.</string>
      <string id="ManualAppUpdateOnly">Nur manuelle Updates</string>
      <string id="ManualAppUpdateOnly_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden die Benutzer nicht aufgefordert, Updates zu installieren, und Firefox sucht nicht im Hintergrund nach Updates. Der Benutzer muss Updates manuell über das Dialogfeld "Info" überprüfen und installieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, erhält der Browser Updates.

Diese Richtlinie wird für die meisten Benutzer nicht empfohlen.</string>
      <string id="AllowFileSelectionDialogs">Dateiauswahldialoge zulassen</string>
      <string id="AllowFileSelectionDialogs_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, können Benutzer keine Dateiauswahldialoge öffnen. In den meisten Fällen verhält sich Firefox so, als ob der Benutzer auf die Schaltfläche Abbrechen geklickt hätte.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, können Benutzer Dateiauswahldialoge öffnen.</string>
      <string id="AutofillAddressEnabled">Autovervollständigung für Adressen aktivieren</string>
      <string id="AutofillAddressEnabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden Adressen für Firefox-Versionen und Regionen, die dies unterstützen, nicht automatisch ausgefüllt.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden Adressen für Firefox-Versionen und -Regionen, die dies unterstützen, automatisch ausgefüllt.</string>
      <string id="AutofillCreditCardEnabled">Autovervollständigung für Zahlungsarten aktivieren</string>
      <string id="AutofillCreditCardEnabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden Zahlungsmethoden für Firefox-Versionen und Regionen, die dies unterstützen, nicht automatisch ausgefüllt.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, werden Zahlungsmethoden für Firefox-Versionen und -Regionen, die dies unterstützen, automatisch ausgefüllt.</string>
      <string id="TranslateEnabled">Übersetzung von Webseiten aktivieren</string>
      <string id="TranslateEnabled_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, ist die Übersetzung von Webseiten nicht verfügbar.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, ist die Übersetzung von Webseiten verfügbar.

Hinweis: Die Übersetzung von Webseiten erfolgt vollständig auf dem Client, um den Datenschutz zu gewährleisten.</string>
      <string id="DisableEncryptedClientHello">Encrypted Client Hello (ECH) deaktivieren</string>
      <string id="DisableEncryptedClientHello_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird die TLS-Funktion Encrypted Client Hello (ECH) deaktiviert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die TLS-Funktion Encrypted Client Hello (ECH) aktiviert.</string>
      <string id="PostQuantumKeyAgreementEnabled">Post-quantum Key Agreement aktivieren</string>
      <string id="PostQuantumKeyAgreementEnabled_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird Post-Quantum Key Agreement für TLS aktiviert.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird Post-Quantum Key Agreement für TLS deaktiviert.</string>
      <string id="HttpsOnlyMode">Nur-HTTPS Modus</string>
      <string id="HttpsOnlyMode_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie das Standardverhalten für den Nur-HTTPS Modus festlegen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, ist der Nur-HTTPS Modus nicht aktiviert.</string>
      <string id="HttpsOnlyMode_Allowed">Standardmäßig ausgeschaltet</string>
      <string id="HttpsOnlyMode_Disallowed">Ausgeschaltet und gesperrt</string>
      <string id="HttpsOnlyMode_Enabled">Standardmäßig eingeschaltet</string>
      <string id="HttpsOnlyMode_ForceEnabled">Eingeschaltet und gesperrt</string>
      <string id="HttpAllowlist">HTTP Erlaubnisliste</string>
      <string id="HttpAllowlist_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine Liste von Verbindungen angeben, die nicht auf HTTPS hochgestuft werden sollen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden alle Verbindungen auf HTTPS hochgestuft, wenn der Nur-HTTPS Modus aktiviert ist.</string>
      <string id="PrivateBrowsingModeAvailability">Verfügbarkeit des privaten Browsing-Modus</string>
      <string id="PrivateBrowsingModeAvailability_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie die Verfügbarkeit des privaten Browsing-Modus festlegen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, ist der private Browsing-Modus verfügbar.</string>
      <string id="PrivateBrowsingModeAvailability_0">Privaten Browsing-Modus zulassen</string>
      <string id="PrivateBrowsingModeAvailability_1">Privaten Browsing-Modus deaktivieren</string>
      <string id="PrivateBrowsingModeAvailability_2">Privaten Browsing-Modus erzwingen</string>
      <string id="ContentAnalysis_AgentName">Name des DLP-Agenten</string>
      <string id="ContentAnalysis_AgentName_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie den Namen des DLP-Agenten angeben, der in Dialogen und Benachrichtigungen über DLP-Vorgänge verwendet wird.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird der Agentenname „A DLP Agent“ verwendet.</string>
      <string id="ContentAnalysis_AgentTimeout">Zeitüberschreitung des DLP-Agenten</string>
      <string id="ContentAnalysis_AgentTimeout_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie die Zeitüberschreitung in Sekunden angeben, nachdem eine DLP-Anfrage an den Agenten gesendet wurde. Nach Ablauf dieser Zeitspanne wird die Anfrage abgelehnt, es sei denn, „Standardergebnis“ ist auf 1 oder 2 eingestellt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, beträgt die Zeitüberschreitung 30 Sekunden.</string>
      <string id="ContentAnalysis_AllowUrlRegexList">Url-Regex-Zulassungsliste</string>
      <string id="ContentAnalysis_AllowUrlRegexList_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine durch Leerzeichen getrennte Liste regulärer Ausdrücke angeben, die URLs angibt, für die DLP-Vorgänge ohne Rücksprache mit dem Agenten immer zugelassen werden. Die Standardeinstellung ist „^about:(?!blank|srcdoc).*“, was bedeutet, dass alle Seiten, die mit „about:“ beginnen, von DLP ausgenommen sind, mit Ausnahme von „about:blank“ und „about:srcdoc“, da diese durch Webinhalte kontrolliert werden können.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird immer der DLP-Agent konsultiert.</string>
      <string id="ContentAnalysis_BypassForSameTabOperations">DLP auf der selben Registerkarte</string>
      <string id="ContentAnalysis_BypassForSameTabOperations_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, lässt Firefox automatisch DLP-Anfragen zu, deren Daten von derselben Registerkarte und demselben Frame stammen - zum Beispiel, wenn Daten in die Zwischenablage kopiert und dann auf derselben Seite eingefügt werden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, leitet Firefox DLP-Anfragen, deren Daten von der gleichen Registerkarte und dem gleichen Frame stammen, nicht wie üblich an den DLP-Agenten weiter.</string>
      <string id="ContentAnalysis_ClientSignature">Signatur der Pipe</string>
      <string id="ContentAnalysis_ClientSignature_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie die erforderliche Signatur des mit der Pipe verbundenen DLP-Agenten festlegen. Wenn dies eine nicht leere Zeichenkette ist und der DLP-Agent keine Signatur mit einem Betreff-Namen hat, der genau mit diesem Wert übereinstimmt, wird Firefox keine Verbindung zur Pipe herstellen.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Signatur nicht überprüft.</string>
      <string id="ContentAnalysis_DefaultResult">Standardergebnis</string>
      <string id="ContentAnalysis_DefaultResult_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie das gewünschte Verhalten für DLP-Anfragen angeben, wenn ein Problem bei der Verbindung mit dem DLP-Agenten auftritt.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden DLP-Anfragen abgelehnt, wenn ein Problem bei der Verbindung mit dem Agenten auftritt.</string>
      <string id="ContentAnalysis_DefaultResult_0">Anfrage ablehnen</string>
      <string id="ContentAnalysis_DefaultResult_1">Warnen Sie den Benutzer und lassen Sie ihm die Wahl, ob er zulassen oder verweigern möchte</string>
      <string id="ContentAnalysis_DefaultResult_2">Anfrage zulassen</string>
      <string id="ContentAnalysis_DenyUrlRegexList">Url-Regex-Verweigerungsliste</string>
      <string id="ContentAnalysis_DenyUrlRegexList_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie eine durch Leerzeichen getrennte Liste mit regulären Ausdrücken angeben, die URLs angibt, für die DLP-Vorgänge immer verweigert werden, ohne den Agenten zu konsultieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird der DLP-Agent immer konsultiert.</string>
      <string id="ContentAnalysis_Enabled">DLP aktivieren</string>
      <string id="ContentAnalysis_Enabled_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, wird Firefox DLP verwenden.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, verwendet Firefox kein DLP.

Hinweis: Wenn diese Richtlinieneinstellung aktiviert ist und kein DLP-Agent ausgeführt wird, werden alle DLP-Anfragen abgelehnt, es sei denn, das Standardergebnis ist auf 1 oder 2 eingestellt.</string>
      <string id="ContentAnalysis_IsPerUser">Pipe pro Benutzer</string>
      <string id="ContentAnalysis_IsPerUser_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, erstellt der DLP-Agent die Pipe pro System.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird die Pipe vom DLP-Agenten pro Benutzer erstellt.</string>
      <string id="ContentAnalysis_PipePathName">Name der Pipe</string>
      <string id="ContentAnalysis_PipePathName_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie den Namen der Pipe für den DLP-Agenten ändern.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird der Standard-Pipe-Name „path_user“ verwendet.</string>
      <string id="ContentAnalysis_ShowBlockedResult">Benachrichtigung bei Ablehnung einer DLP-Anfrage</string>
      <string id="ContentAnalysis_ShowBlockedResult_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, zeigt Firefox keine Benachrichtigung an, wenn eine DLP-Anfrage abgelehnt wird.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, zeigt Firefox eine Benachrichtigung an, wenn eine DLP-Anfrage abgelehnt wird.</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard">Aktiviert</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird bei Vorgängen in der Zwischenablage kein DLP verwendet.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird bei Vorgängen in der Zwischenablage DLP verwendet.</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly">Nur reiner Text</string>
      <string id="ContentAnalysis_InterceptionPoints_Clipboard_PlainTextOnly_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden alle Formate in der Zwischenablage analysiert, was einige DLP-Agenten möglicherweise nicht erwarten.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird nur das Text/Plain-Format in der Zwischenablage analysiert.</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop">Aktiviert</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird bei Drag-and-Drop-Vorgängen kein DLP verwendet.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, verwenden Drag-and-Drop-Vorgänge DLP.</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly">Nur reiner Text</string>
      <string id="ContentAnalysis_InterceptionPoints_DragAndDrop_PlainTextOnly_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, werden alle Formate in den abgelehnten Daten analysiert, was einige DLP-Agenten möglicherweise nicht erwarten.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird nur das Text/Plain-Format in den abgelehnten Daten analysiert.</string>
      <string id="ContentAnalysis_InterceptionPoints_FileUpload">Datei-Upload</string>
      <string id="ContentAnalysis_InterceptionPoints_FileUpload_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird bei Datei-Upload-Vorgängen kein DLP verwendet.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird bei Datei-Upload-Vorgängen DLP verwendet.</string>
      <string id="ContentAnalysis_InterceptionPoints_Print">Drucken</string>
      <string id="ContentAnalysis_InterceptionPoints_Print_Explain">Wenn diese Richtlinieneinstellung deaktiviert ist, wird bei Druckvorgängen kein DLP verwendet.

Wenn diese Richtlinieneinstellung aktiviert oder nicht konfiguriert ist, wird bei Druckvorgängen DLP verwendet.</string>
      <string id="ContentAnalysis_TimeoutResult">Ergebnis der Zeitüberschreitung</string>
      <string id="ContentAnalysis_TimeoutResult_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, können Sie das gewünschte Verhalten für DLP-Anfragen angeben, wenn der DLP-Agent auf eine Anfrage nicht in weniger als AgentTimeout-Sekunden antwortet. 

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, wird die Anfrage abgelehnt.</string>
      <string id="SkipTermsOfUse">Nutzungsbedingungen überspringen</string>
      <string id="SkipTermsOfUse_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, werden die Firefox-Nutzungsbedingungen (https://www.mozilla.org/about/legal/terms/firefox/) und der Datenschutzhinweis (https://www.mozilla.org/privacy/firefox/) beim Starten nicht angezeigt.

Sie versichern, dass Sie die Nutzungsbedingungen im Namen aller Personen, denen Sie Zugang zu diesem Browser gewähren, akzeptieren und befugt sind, diese zu akzeptieren.

Wenn diese Richtlinieneinstellung deaktiviert oder nicht konfiguriert ist, werden die Firefox-Nutzungsbedingungen und der Datenschutzhinweis beim Starten angezeigt.</string>
      <string id="Preferences_Boolean_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist die Einstellung auf true gesperrt. Wenn diese Richtlinieneinstellung deaktiviert ist, ist die Einstellung auf false gesperrt.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#preferences (Englisch)</string>
      <string id="Preferences_String_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist die Einstellung auf den spezifizierten String-Wert gesperrt. Wenn diese Richtlinieneinstellung deaktiviert ist, hat sie keinen Effekt.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#preferences (Englisch)</string>
      <string id="Preferences_Enum_Explain">Wenn diese Richtlinieneinstellung aktiviert ist, ist sie auf den spezifizierten Wert gesperrt. Wenn diese Richtlinieneinstellung deaktiviert ist, hat sie keinen Effekt.

Für eine Beschreibung der Einstellung, siehe:
https://github.com/mozilla/policy-templates/blob/master/README.md#preferences (Englisch)</string>
      <string id="Preferences_Unsupported_Explain">Diese Richtlinieneinstellung ist nicht mehr unterstützt. Wir arbeiten an einer Lösung.</string>
      <string id="Preferences_accessibility_force_disabled_auto">Automatisch (0)</string>
      <string id="Preferences_accessibility_force_disabled_off">Immer Aus (1)</string>
      <string id="Preferences_security_default_personal_cert_Ask_Every_Time">Jedes Mal nachfragen</string>
      <string id="Preferences_security_default_personal_cert_Select_Automatically">Automatisch auswählen</string>
      <string id="accessibility_force_disabled">accessibility.force_disabled</string>
      <string id="app_update_auto">app.update.auto (Veraltet)</string>
      <string id="browser_bookmarks_autoExportHTML">browser.bookmarks.autoExportHTML</string>
      <string id="browser_bookmarks_file">browser.bookmarks.file</string>
      <string id="browser_bookmarks_restore_default_bookmarks">browser.bookmarks.restore_default_bookmarks</string>
      <string id="browser_cache_disk_enable">browser.cache.disk.enable</string>
      <string id="browser_fixup_dns_first_for_single_words">browser.fixup.dns_first_for_single_words</string>
      <string id="browser_places_importBookmarksHTML">browser.places.importBookmarksHTML</string>
      <string id="browser_safebrowsing_phishing_enabled">browser.safebrowsing.phishing.enabled</string>
      <string id="browser_safebrowsing_malware_enabled">browser.safebrowsing.malware.enabled</string>
      <string id="browser_search_update">browser.search.update</string>
      <string id="browser_tabs_warnOnClose">browser.tabs.warnOnClose</string>
      <string id="browser_cache_disk_parent_directory">browser.cache.disk.parent_directory</string>
      <string id="browser_slowStartup_notificationDisabled">browser.slowStartup.notificationDisabled</string>
      <string id="browser_taskbar_previews_enable">browser.taskbar.previews.enable</string>
      <string id="browser_urlbar_suggest_bookmark">browser.urlbar.suggest.bookmark</string>
      <string id="browser_urlbar_suggest_history">browser.urlbar.suggest.history</string>
      <string id="browser_urlbar_suggest_openpage">browser.urlbar.suggest.openpage</string>
      <string id="datareporting_policy_dataSubmissionPolicyBypassNotification">datareporting.policy.dataSubmissionPolicyBypassNotification</string>
      <string id="dom_allow_scripts_to_close_windows">dom.allow_scripts_to_close_windows</string>
      <string id="dom_disable_window_flip">dom.disable_window_flip</string>
      <string id="dom_disable_window_move_resize">dom.disable_window_move_resize</string>
      <string id="dom_event_contextmenu_enabled">dom.event.contextmenu.enabled</string>
      <string id="dom_keyboardevent_keypress_hack_dispatch_non_printable_keys_addl">dom.keyboardevent.keypress.hack.dispatch_non_printable_keys.addl</string>
      <string id="dom_keyboardevent_keypress_hack_use_legacy_keycode_and_charcode_addl">dom.keyboardevent.keypress.hack.use_legacy_keycode_and_charcode.addl</string>
      <string id="dom_xmldocument_load_enabled">dom.xmldocument.load.enabled</string>
      <string id="dom_xmldocument_async_enabled">dom.xmldocument.async.enabled</string>
      <string id="extensions_blocklist_enabled">extensions.blocklist.enabled</string>
      <string id="geo_enabled">geo.enabled</string>
      <string id="extensions_getAddons_showPane">extensions.getAddons.showPane</string>
      <string id="intl_accept_languages">intl.accept_languages</string>
      <string id="media_eme_enabled">media.eme.enabled (Veraltet)</string>
      <string id="media_gmp-gmpopenh264_enabled">media.gmp-gmpopenh264.enabled</string>
      <string id="media_gmp-widevinecdm_enabled">media.gmp-widevinecdm.enabled</string>
      <string id="network_dns_disableIPv6">network.dns.disableIPv6</string>
      <string id="network_IDN_show_punycode">network.IDN_show_punycode</string>
      <string id="places_history_enabled">places.history.enabled</string>
      <string id="print_save_print_settings">print.save_print_settings</string>
      <string id="security_default_personal_cert">security.default_personal_cert</string>
      <string id="security_ssl_errorReporting_enabled">security.ssl.errorReporting.enabled</string>
      <string id="security_mixed_content_block_active_content">security.mixed_content.block_active_content</string>
      <string id="ui_key_menuAccessKeyFocuses">ui.key.menuAccessKeyFocuses</string>
      <string id="browser_newtabpage_activity-stream_default_sites">browser.newtabpage.activity-stream.default.sites</string>
      <string id="extensions_htmlaboutaddons_recommendations_enabled">extensions.htmlaboutaddons.recommendations.enabled</string>
      <string id="media_peerconnection_enabled">media.peerconnection.enabled</string>
      <string id="media_peerconnection_ice_obfuscate_host_addresses_whitelist">media.peerconnection.ice.obfuscate_host_addresses.whitelist (Veraltet)</string>
      <string id="media_peerconnection_ice_obfuscate_host_addresses_blocklist">media.peerconnection.ice.obfuscate_host_addresses.blocklist</string>
      <string id="security_osclientcerts_autoload">security.osclientcerts.autoload</string>
      <string id="security_tls_hello_downgrade_check">security.tls.hello_downgrade_check</string>
      <string id="widget_content_gtk-theme-override">widget.content.gtk-theme-override</string>
    </stringTable>
    <presentationTable>
      <presentation id="AppUpdateURL">
        <textBox refId="AppUpdateURL">
          <label>URL:</label>
        </textBox>
      </presentation>
      <presentation id="Authentication">
        <listBox refId="Authentication"/>
      </presentation>
      <presentation id="Authentication_AllowNonFQDN">
        <checkBox refId="Authentication_AllowNonFQDN_NTLM">NTLM immer auf Nicht-FQDNs zulassen</checkBox>
        <checkBox refId="Authentication_AllowNonFQDN_SPNEGO">SPNEGO immer auf Nicht-FQDNs zulassen</checkBox>
      </presentation>
      <presentation id="Authentication_AllowProxies">
        <checkBox refId="Authentication_AllowProxies_NTLM">Erlauben Sie NTLM, sich automatisch bei Proxy-Servern zu authentifizieren</checkBox>
        <checkBox refId="Authentication_AllowProxies_SPNEGO">Erlauben Sie SPNEGO, sich automatisch bei Proxy-Servern zu authentifizieren</checkBox>
      </presentation>
      <presentation id="Certificates_Install">
        <listBox refId="Certificates_Install"/>
      </presentation>
      <presentation id="RequestedLocales">
        <listBox refId="RequestedLocales"/>
      </presentation>
      <presentation id="SecurityDevices">
        <listBox refId="SecurityDevices"/>
      </presentation>
      <presentation id="Extensions">
        <listBox refId="Extensions"/>
      </presentation>
      <presentation id="WebsiteFilter">
        <listBox refId="WebsiteFilter"/>
      </presentation>
      <presentation id="Permissions"><listBox refId="Permissions"/></presentation>
      <presentation id="PopupsAllow"><listBox refId="PopupsAllowDesc">Pop-up-Blocker deaktivieren</listBox></presentation>
      <presentation id="Cookies_AcceptThirdParty">
        <dropdownList refId="Cookies_AcceptThirdParty"/>
      </presentation>
      <presentation id="Cookies_Behavior">
        <dropdownList refId="Cookies_Behavior"/>
      </presentation>
      <presentation id="Cookies_BehaviorPrivateBrowsing">
        <dropdownList refId="Cookies_BehaviorPrivateBrowsing"/>
      </presentation>
      <presentation id="SearchBar">
        <dropdownList refId="SearchBar"/>
      </presentation>
      <presentation id="TrackingProtection">
        <checkBox refId="TrackingProtectionLocked">Änderungen am Schutz vor Aktivitätenverfolgung nicht erlauben</checkBox>
        <checkBox refId="Cryptomining">Cryptomining-Skripte blockieren.</checkBox>
        <checkBox refId="Fingerprinting">Identifizierer-Skripte (Fingerprinter) blockieren.</checkBox>
        <text>Ausnahmen:</text>
        <listBox refId="TrackingProtection_Exceptions"/>
      </presentation>
      <presentation id="TrackingProtection_Exceptions">
        <listBox refId="TrackingProtection_Exceptions"/>
      </presentation>
      <presentation id="OverridePage">
        <textBox refId="OverridePage">
          <label>URL:</label>
        </textBox>
      </presentation>
      <presentation id="HomepageURL">
        <text>URL:</text>
        <textBox refId="HomepageURL">
          <label/>
        </textBox>
        <checkBox refId="HomepageLocked">Änderung der Startseite nicht erlauben</checkBox>
      </presentation>
      <presentation id="HomepageAdditional">
        <listBox refId="HomepageAdditional">Weitere Startseiten</listBox>
      </presentation>
      <presentation id="StartPage">
        <dropdownList refId="StartPage"/>
      </presentation>
      <presentation id="Bookmark">
        <text>Titel:</text>
        <textBox refId="BookmarkTitle">
          <label/>
        </textBox>
        <text>Adresse:</text>
        <textBox refId="BookmarkURL">
          <label/>
        </textBox>
        <text>Favicon Adresse:</text>
        <textBox refId="BookmarkFavicon">
          <label/>
        </textBox>
        <text>Speicherort:</text>
        <dropdownList refId="BookmarkPlacement"/>
        <text>Ordner:</text>
        <textBox refId="BookmarkFolder">
          <label/>
        </textBox>
      </presentation>
      <presentation id="SearchEngine">
        <textBox refId="SearchEngine_Name">
          <label>Name:</label>
        </textBox>
        <textBox refId="SearchEngine_URLTemplate">
          <label>URL Template:</label>
        </textBox>
        <text>Methode:</text>
        <dropdownList refId="SearchEngine_Method"/>
        <textBox refId="SearchEngine_IconURL">
          <label>Icon URL:</label>
        </textBox>
        <textBox refId="SearchEngine_Alias">
          <label>Alias:</label>
        </textBox>
        <textBox refId="SearchEngine_Description">
          <label>Beschreibung:</label>
        </textBox>
        <textBox refId="SearchEngine_SuggestURLTemplate">
          <label>Vorlagen Suchvorschläge:</label>
        </textBox>
        <textBox refId="SearchEngine_PostData">
          <label>POST data:</label>
        </textBox>
        <textBox refId="SearchEngine_Encoding">
          <label>Encoding:</label>
        </textBox>
      </presentation>
      <presentation id="SearchEngines_Default">
        <textBox refId="SearchEngines_Default">
          <label/>
        </textBox>
      </presentation>
      <presentation id="SearchEngines_Remove">
        <listBox refId="SearchEngines_Remove"/>
      </presentation>
      <presentation id="Proxy">
        <checkBox refId="ProxyLocked">Ändern der Proxy-Einstellungen nicht erlauben</checkBox>
        <text>Verbindungs-Einstellungen</text>
        <dropdownList refId="ConnectionType"/>
        <text>HTTP Proxy:</text>
        <textBox refId="HTTPProxy">
          <label/>
        </textBox>
        <checkBox refId="UseHTTPProxyForAllProtocols">Für alle Protokolle diesen Proxy-Server verwenden.</checkBox>
        <text>SSL Proxy:</text>
        <textBox refId="SSLProxy">
          <label/>
        </textBox>
        <text>FTP Proxy:</text>
        <textBox refId="FTPProxy">
          <label/>
        </textBox>
        <text>SOCKS Host:</text>
        <textBox refId="SOCKSProxy">
          <label/>
        </textBox>
        <text>SOCKS Version:</text>
        <dropdownList refId="SOCKSVersion"/>
        <text>Kein Proxy für:</text>
        <textBox refId="Passthrough">
          <label/>
        </textBox>
        <text>Beispiel: .mozilla.org, .net.nz, ***********/24</text>
        <text>Automatische Proxy-Konfigurations-Adresse:</text>
        <textBox refId="AutoConfigURL">
          <label/>
        </textBox>
        <checkBox refId="AutoLogin">Keine Authentifizierungsanfrage bei gespeichertem Passwort.</checkBox>
        <checkBox refId="UseProxyForDNS">Bei Verwendung von SOCKS v5 den Proxy für DNS-Anfragen verwenden.</checkBox>
      </presentation>
      <presentation id="DNSOverHTTPS">
        <text>Provider URL:</text>
        <textBox refId="ProviderURL">
          <label/>
        </textBox>
        <checkBox refId="DNSOverHTTPSEnabled">DNS über HTTPS aktivieren.</checkBox>
        <checkBox refId="DNSOverHTTPSLocked">Änderungen an den DNS-über-HTTPS Einstellungen nicht erlauben.</checkBox>
      </presentation>
      <presentation id="SSLVersionMin">
        <dropdownList refId="SSLVersion" defaultItem="2"/>
      </presentation>
      <presentation id="SSLVersionMax">
        <dropdownList refId="SSLVersion" defaultItem="3"/>
      </presentation>
      <presentation id="SupportMenu">
        <text>Title:</text>
        <textBox refId="SupportMenuTitle">
          <label/>
        </textBox>
        <text>URL:</text>
        <textBox refId="SupportMenuURL">
          <label/>
        </textBox>
        <text>Taste:</text>
        <textBox refId="SupportMenuAccessKey">
          <label/>
        </textBox>
      </presentation>
      <presentation id="Preferences_String">
        <textBox refId="Preferences_String">
          <label/>
        </textBox>
      </presentation>
      <presentation id="Preferences_accessibility_force_disabled">
        <dropdownList refId="Preferences_accessibility_force_disabled"/>
      </presentation>
      <presentation id="Preferences_security_default_personal_cert">
        <dropdownList refId="Preferences_security_default_personal_cert"/>
      </presentation>
      <presentation id="LegacySameSiteCookieBehaviorEnabledForDomainList">
        <listBox refId="LegacySameSiteCookieBehaviorEnabledForDomainList"/>
      </presentation>
      <presentation id="LocalFileLinks">
        <listBox refId="LocalFileLinks"/>
      </presentation>
      <presentation id="SanitizeOnShutdown">
        <checkBox refId="SanitizeOnShutdown_Cache">Cache</checkBox>
        <checkBox refId="SanitizeOnShutdown_Cookies">Cookies</checkBox>
        <checkBox refId="SanitizeOnShutdown_Downloads">Download-Chronik</checkBox>
        <checkBox refId="SanitizeOnShutdown_FormData">Eingegebene Suchbegriffe &amp; Formulardaten</checkBox>
        <checkBox refId="SanitizeOnShutdown_History">Besuchte Seiten</checkBox>
        <checkBox refId="SanitizeOnShutdown_Sessions">Aktive Logins</checkBox>
        <checkBox refId="SanitizeOnShutdown_SiteSettings">Website-Einstellungen</checkBox>
        <checkBox refId="SanitizeOnShutdown_OfflineApps">Offline-Website-Daten</checkBox>
      </presentation>
      <presentation id="FirefoxHome">
        <checkBox refId="FirefoxHome_Search">Suche</checkBox>
        <checkBox refId="FirefoxHome_TopSites">Top Seiten</checkBox>
        <checkBox refId="FirefoxHome_SponsoredTopSites">Gesponserte Top-Seiten</checkBox>
        <checkBox refId="FirefoxHome_Highlights">Download Verlauf</checkBox>
        <checkBox refId="FirefoxHome_Pocket">Von Pocket empfohlen</checkBox>
        <checkBox refId="FirefoxHome_SponsoredPocket">Gesponserte Pocket Stories</checkBox>
        <checkBox refId="FirefoxHome_Snippets">Snippets</checkBox>
        <checkBox refId="FirefoxHome_Locked">Es ist nicht erlaubt, dass Einstellungen geändert werden.</checkBox>
      </presentation>
      <presentation id="ExtensionSettings">
        <multiTextBox refId="ExtensionSettings"/>
      </presentation>
      <presentation id="Handlers">
        <multiTextBox refId="Handlers"/>
      </presentation>
      <presentation id="DisplayMenuBar">
        <dropdownList refId="DisplayMenuBar"/>
      </presentation>
      <presentation id="DisplayBookmarksToolbar">
        <dropdownList refId="DisplayBookmarksToolbar"/>
      </presentation>
      <presentation id="String">
        <textBox refId="String">
          <label/>
        </textBox>
      </presentation>
      <presentation id="List">
        <listBox refId="List"/>
      </presentation>
      <presentation id="Autoplay_Default">
        <dropdownList refId="Autoplay_Default"/>
      </presentation>
      <presentation id="JSON">
        <multiTextBox refId="JSON"/>
      </presentation>
      <presentation id="JSONOneLine">
        <textBox refId="JSONOneLine">
          <label/>
        </textBox>
      </presentation>
      <presentation id="Proxy_ConnectionType">
        <dropdownList refId="Proxy_ConnectionType"/>
      </presentation>
      <presentation id="Proxy_HTTPProxy">
        <textBox refId="Proxy_HTTPProxy">
          <label>Host einschließlich Port:</label>
        </textBox>
      </presentation>
      <presentation id="Proxy_SSLProxy">
        <textBox refId="Proxy_SSLProxy">
          <label>Host einschließlich Port:</label>
        </textBox>
      </presentation>
      <presentation id="Proxy_SOCKSProxy">
        <text>Host einschließlich Port:</text>
        <textBox refId="Proxy_SOCKSProxy">
          <label/>
        </textBox>
        <text>SOCKS Version:</text>
        <dropdownList refId="Proxy_SOCKSVersion"/>
      </presentation>
      <presentation id="Proxy_AutoConfigURL">
        <textBox refId="Proxy_AutoConfigURL">
          <label>URL:</label>
        </textBox>
      </presentation>
      <presentation id="Proxy_Passthrough">
        <text>Kein Proxy für</text>
        <textBox refId="Proxy_Passthrough">
          <label/>
        </textBox>
        <text>Example: .mozilla.org, .net.nz, ***********/24</text>
        <text>Connections to localhost, 127.0.0.1/8, and ::1 are never proxied.</text>
      </presentation>
      <presentation id="HttpsOnlyMode">
        <dropdownList refId="HttpsOnlyMode"/>
      </presentation>
      <presentation id="PrivateBrowsingModeAvailability">
        <dropdownList refId="PrivateBrowsingModeAvailability"/>
      </presentation>
      <presentation id="ContentAnalysis_DefaultResult">
        <dropdownList refId="ContentAnalysis_DefaultResult"/>
      </presentation>
      <presentation id="Number">
        <decimalTextBox refId="Number"/>
      </presentation>
      <presentation id="ContentAnalysis_TimeoutResult">
        <dropdownList refId="ContentAnalysis_TimeoutResult"/>
      </presentation>
    </presentationTable>
  </resources>
</policyDefinitionResources>
