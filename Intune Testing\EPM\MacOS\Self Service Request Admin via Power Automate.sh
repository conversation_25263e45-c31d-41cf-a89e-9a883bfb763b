#!/bin/zsh
#================================================================================================
#   Author: <PERSON><PERSON> Sheppard
#   Purpose: This script is run from Self Service to request temporary admin rights.
#================================================================================================
# This URL is specific to your Power Automate flow and includes the required signature.
webhook_url="https://prod-135.westus.logic.azure.com:443/workflows/268d97705c3d481db7b3b2f9cca2b190/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=3LsL1tR-SpqNlFGehk7bpOuI_0jQEom65dCJt1QfIfs"

log_message() { echo "$(date +"%Y-%m-%d %H:%M:%S") - $1"; }
log_message "Starting admin request script..."
current_user=$(/bin/ls -l /dev/console | /usr/bin/awk '{ print $3 }')
serial_number=$(system_profiler SPHardwareDataType | awk '/Serial Number/{print $4}')
computer_name=$(scutil --get ComputerName)

justification=$(osascript -e 'tell application "System Events" to display dialog "Please provide a detailed business justification for needing temporary administrator rights." with title "Admin Access Request - Step 1 of 3" default answer "" buttons {"Cancel", "Next"} default button "Next"' -e 'text returned of result')
if [ $? -ne 0 ]; then log_message "User cancelled at justification prompt. Exiting."; exit 0; fi

duration_choice=$(osascript -e 'tell application "System Events" to choose from list {"15 minutes", "30 minutes", "1 hour", "4 hours", "8 hours"} with prompt "Select the duration you need admin rights for:" with title "Admin Access Request - Step 2 of 3" default items {"30 minutes"}')
if [ $? -ne 0 ]; then log_message "User cancelled at duration prompt. Exiting."; exit 0; fi

app_choice=$(osascript -e 'tell application "System Events" to choose from list {"System Settings", "Terminal (for sudo)", "Install a specific application", "Other (specify below)"} with prompt "Which application or task requires elevation?" with title "Admin Access Request - Step 3 of 3" default items {"System Settings"}')
if [ $? -ne 0 ]; then log_message "User cancelled at application prompt. Exiting."; exit 0; fi

if [[ "$app_choice" == "Other (specify below)" ]] || [[ "$app_choice" == "Install a specific application" ]]; then
    specific_app=$(osascript -e 'tell application "System Events" to display dialog "Please specify the application name or task:" with title "Specify Application/Task" default answer "" buttons {"Cancel", "Submit Request"} default button "Submit Request"' -e 'text returned of result')
    if [ $? -ne 0 ]; then log_message "User cancelled at specific application prompt. Exiting."; exit 0; fi
    app_choice="$app_choice: $specific_app"
fi

# Sanitize user input for the JSON payload
sanitized_justification=$(echo -n "$justification" | perl -p -e 's/\\/\\\\/g; s/"/\\"/g; s/\n/\\n/g;')
sanitized_app_choice=$(echo -n "$app_choice" | perl -p -e 's/\\/\\\\/g; s/"/\\"/g; s/\n/\\n/g;')

json_payload=$(printf '{"computerName": "%s", "serialNumber": "%s", "requestUser": "%s", "justification": "%s", "duration": "%s", "application": "%s"}' "$computer_name" "$serial_number" "$current_user" "$sanitized_justification" "$duration_choice" "$sanitized_app_choice")

response_file=$(mktemp)
response_code=$(curl -s -i -o "$response_file" -w "%{http_code}" -X POST -H "Content-Type: application/json" -d "$json_payload" "$webhook_url")

if [ "$response_code" -eq 202 ]; then
    log_message "Request successfully submitted (HTTP 202)."
    osascript -e 'display dialog "Your request for administrator access has been submitted for review. You will be notified once it is approved or denied." with title "Request Submitted" buttons {"OK"} default button "OK"'
else
    log_message "Error submitting request. The webhook returned a non-202 response."
    log_message "--- Begin Server Error Response ---"
    body=$(sed '1,/^\r$/d' "$response_file")
    log_message "SERVER BODY: $body"
    log_message "--- End Server Error Response ---"
    osascript -e "display dialog \"An error occurred while submitting your request (HTTP Code: $response_code). Please contact Helpdesk.\" with title \"Submission Error\" buttons {\"OK\"} default button \"OK\" with icon stop"
fi
rm "$response_file"
log_message "Script finished."
exit 0