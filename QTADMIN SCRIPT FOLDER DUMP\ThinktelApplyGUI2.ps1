﻿# Thinktel Gui Script
# This script will show a GUI with textboxes and buttons to apply or remove Thinktel policy.

Add-Type -AssemblyName System.Windows.Forms

$form = New-Object System.Windows.Forms.Form
$form.Text = "Thinktel Policy"

$emailLabel = New-Object System.Windows.Forms.Label
$emailLabel.Text = "Email:"
$emailLabel.AutoSize = $true
$emailLabel.Location = New-Object System.Drawing.Point(10, 20)

$emailBox = New-Object System.Windows.Forms.TextBox
$emailBox.Width = 150
$emailBox.Location = [System.Drawing.Point]::new($emailLabel.Right + 5, $emailLabel.Top)

$phoneNumberLabel = New-Object System.Windows.Forms.Label
$phoneNumberLabel.Text = "Phone Number:"
$phoneNumberLabel.Location = New-Object System.Drawing.Point(10, 40)

$phoneNumberBox = New-Object System.Windows.Forms.TextBox
$phoneNumberBox.Width = 150
$phoneNumberBox.Location = [System.Drawing.Point]::new($phoneNumberLabel.Right + 5, $phoneNumberLabel.Top)

$submitButton = New-Object System.Windows.Forms.Button
$submitButton.Text = "Submit"
$submitButton.Height = 40
$submitButton.Location = New-Object System.Drawing.Point(10, 60)
$submitButton.add_Click({
    $Identity = $emailBox.Text
    $phonenumber = $phoneNumberBox.Text

    Connect-MicrosoftTeams 

    Set-CsPhoneNumberAssignment -Identity $Identity -EnterpriseVoiceEnabled $true
    Grant-CsOnlineVoiceRoutingPolicy -Identity $Identity -PolicyName thinktel 
    Set-CsPhoneNumberAssignment -Identity $Identity -PhoneNumber $phonenumber -PhoneNumberType DirectRouting
    $data = Get-CsOnlineUser -identity $Identity | Select-Object EnterpriseVoiceEnabled, OnlineVoiceRoutingPolicy, LineUri
    
    Disconnect-MicrosoftTeams

    # create a new section to display the response from Get-CsOnlineUser cmdlet
    $outputLabel = New-Object System.Windows.Forms.Label
    $outputLabel.Text = "EnterpriseVoiceEnabled: " + $data.EnterpriseVoiceEnabled + ", OnlineVoiceRoutingPolicy: " + $data.OnlineVoiceRoutingPolicy + ", LineURI: " + $data.LineUri 
    $outputLabel.AutoSize = $true
    $outputLabel.Location = New-Object System.Drawing.Point(10, 120)

    $form.Controls.Add($outputLabel)
})

$removeButton = New-Object System.Windows.Forms.CheckBox
$removeButton.Text = "Remove all Thinktel Policy"
$removeButton.Height = 30
$removeButton.Location = New-Object System.Drawing.Point(170, 80)
$removeButton.add_CheckStateChanged({
    if ($removeButton.Checked) {
        $Identity = $emailBox.Text

        Connect-MicrosoftTeams 

        Set-CsPhoneNumberAssignment -Identity $Identity -EnterpriseVoiceEnabled $false
        Remove-CsPhoneNumberAssignment -Identity $Identity -RemoveAll
        Grant-CsOnlineVoiceRoutingPolicy -Identity $Identity -PolicyName $null

        Disconnect-MicrosoftTeams

        # create a new section to display the response from Get-CsOnlineUser cmdlet
        $outputLabel = New-Object System.Windows.Forms.Label
        $outputLabel.Text = "All Thinktel policy removed."
        $outputLabel.AutoSize = $true
        $outputLabel.Location = New-Object System.Drawing.Point(10, 120)

        $form.Controls.Add($outputLabel)
    }
})

$form.Controls.Add($emailLabel)
$form.Controls.Add($emailBox)
$form.Controls.Add($phoneNumberLabel)
$form.Controls.Add($phoneNumberBox)
$form.Controls.Add($submitButton)
$form.Controls.Add($removeButton)

$form.ShowDialog() | Out-Null
