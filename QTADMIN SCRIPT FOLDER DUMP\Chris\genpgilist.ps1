﻿$pgiusers = Get-Content 'C:\Scripts\Chris\pgiusers.txt'

foreach ($pgiuser in $pgiusers) {
    $userdetails = get-aduser -Filter {Name -eq $pgiuser} -Properties * | select Name,telephonenumber,mobile,office,userprincipalname,title
    if ($userdetails.office -eq "Kelowna") {
      $userdetails.office = "300 - 1620 Dickson Ave, Kelowna, BC V1Y 9Y2"
    }
    $userdetails | Export-Csv c:\scripts\chris\pgigend.csv -Append -NoTypeInformation
}