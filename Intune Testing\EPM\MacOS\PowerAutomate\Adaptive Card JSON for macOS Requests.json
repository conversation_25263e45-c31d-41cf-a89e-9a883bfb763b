{"type": "AdaptiveCard", "version": "1.4", "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "body": [{"type": "TextBlock", "text": "New macOS Privilege Elevation Request", "weight": "Bolder", "size": "Large", "color": "Warning"}, {"type": "TextBlock", "text": "A new request for temporary macOS administrator rights requires review.", "wrap": true, "isSubtle": true}, {"type": "FactSet", "facts": [{"title": "User:", "value": "@{triggerBody()?['requestUser']}"}, {"title": "Device Name:", "value": "@{triggerBody()?['computerName']}"}, {"title": "Serial Number:", "value": "@{triggerBody()?['serialNumber']}"}, {"title": "Duration:", "value": "@{triggerBody()?['duration']}"}, {"title": "Application/Task:", "value": "@{triggerBody()?['application']}"}, {"title": "Justification:", "value": "@{triggerBody()?['justification']}"}], "spacing": "Large"}], "actions": [{"type": "Action.Submit", "title": "Approve", "style": "positive", "data": {"action": "approve"}}, {"type": "Action.Submit", "title": "<PERSON><PERSON>", "style": "destructive", "data": {"action": "deny"}}]}