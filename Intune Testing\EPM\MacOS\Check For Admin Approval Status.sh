#!/bin/zsh
#=================================================================================
#   Author: <PERSON><PERSON>ppard
#   Purpose: Checks the 'Temp Admin - Action Status' EA and triggers the
#            appropriate grant or deny policy.
#   Version: Final 4.2
#=================================================================================
log() { echo "$(date '+%Y-%m-%d %H:%M:%S') | $1"; }
log "--- SCRIPT START: Check for Admin Approval Status ---"

apiClientID="$4"; apiClientSecret="$5"; jamfProURL="$6"; ACTION_EA_ID="3"
if [[ -z "$apiClientID" || -z "$apiClientSecret" || -z "$jamfProURL" ]]; then log "FATAL ERROR: API credentials or Jamf Pro URL not passed to script. Exiting."; exit 1; fi

apiTokenResponse=$(/usr/bin/curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" --data-urlencode "client_id=${apiClientID}" --data-urlencode "client_secret=${apiClientSecret}" --data-urlencode "grant_type=client_credentials" "${jamfProURL}/api/oauth/token")
bearerToken=$(echo "$apiTokenResponse" | /usr/bin/plutil -extract "access_token" raw -)
if [[ -z "$bearerToken" ]]; then log "FATAL ERROR: Could not retrieve bearer token."; exit 1; fi

serialNumber=$(/usr/sbin/system_profiler SPHardwareDataType | /usr/bin/awk '/Serial Number/{print $4}')
computerDetails=$(/usr/bin/curl -s -H "Authorization: Bearer ${bearerToken}" -H "accept: application/json" "${jamfProURL}/api/v1/computers-inventory?filter=hardware.serialNumber==%22${serialNumber}%22")
if [[ -z "$computerDetails" ]] || echo "$computerDetails" | /usr/bin/grep -q '"httpStatus"'; then log "FATAL ERROR: API returned an error."; exit 1; fi

# Minify the JSON first, then parse the single line with sed for reliability.
minified_json=$(echo "${computerDetails}" | tr -d ' \n\r')
actionStatus=$(echo "${minified_json}" | sed -n 's/.*"definitionId":"'"${ACTION_EA_ID}"'".*"values":\["\([^"]*\)"\].*/\1/p')
log "Extracted Action Status: '${actionStatus}'"

case "$actionStatus" in
    "approved")
        log "ACTION: 'approved' status found. Triggering grant policy (event: grantAdminApproval)."
        /usr/local/bin/jamf policy -event grantAdminApproval
        ;;
    "denied")
        log "ACTION: 'denied' status found. Triggering deny policy (event: denyAdminRequest)."
        /usr/local/bin/jamf policy -event denyAdminRequest
        ;;
    *)
        log "INFO: No 'approved' or 'denied' status flag found. No action required."
        ;;
esac
log "--- SCRIPT END ---"; exit 0
