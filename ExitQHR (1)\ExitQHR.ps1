﻿<#
Author <PERSON><PERSON><PERSON>

Description
This script will offboard and disable access for a QHR Employee


#>

<#
Prerequesites
Running script from QTADMIN1 in an administrator powershell window.
Install-Module ActiveDirectory
Install-Module AzureAD
Install-Module ExchangeOnlineManagement
Install-Module MSOnlineExtended
Imports
#>
Import-Module ActiveDirectory
Import-Module ExchangeOnlineManagement
import-module MSOnline
Import-Module MSOnlineExtended

#Get Today's Date
$Today = Get-Date 
$Today
#Read who we will be exiting today
$Emp = Read-Host -Prompt 'Who will we be removing from the virtual premises?'
$Emp
#Find manager of employee
Connect-AzureAD 
Connect-MsolService
Get-AzureADUser -Filter "startswith(UserPrincipalName,'$Emp')" | Select UserPrincipalName, ObjectId, State, Department, AccountEnabled, DisplayName, GivenName, Surname -OutVariable UserData
$Identity = $Emp


    try {
Get-AzureADUserManager -ObjectId $UserData.ObjectId | Select  UserPrincipalName, ObjectId, State, DisplayName  -OutVariable ManagerData
    }
    catch {write-host "manager is not valid"}


#Find location of employee, Remote or other
$UserData.State
#Read Reason for termination
$TermReason = Read-Host -Prompt 'Enter Reason for Exit'



#Initiate Sign out for Office 365 (Block sign in)
try{
Set-AzureADUser -ObjectID $UserData.ObjectId -AccountEnabled $false
}catch{write-host "Already disabled"}

#Search local active directory for regular and domain admin accounts
# Prompt to disable
$dausername = ""
$dausername =  Read-Host -Prompt 'Enter your DA Username for get-aduser'



Get-ADuser -Identity $Identity.ToString() -Server QTDC01 -Credential $dausername

try {
$daIdentity = 'da-' + $UserData.DisplayName.Substring(0,1).toLower() + $UserData.Surname.toLower()
Get-ADuser -Identity $daIdentity -Server QTDC01
Get-AzureADUser -Filter "startswith(displayName,'$daIdentity')" |  Select  UserPrincipalName, ObjectId, State, Department, AccountEnabled, DisplayName, GivenName, Surname    -OutVariable DAData

$da? = $true
}
catch {
write-host 'No DA Account'
$da? = $false
}

#Remove organiation attributes

Set-ADUser -Identity  $Identity -Title $null -Department $null -Company $null -Manager $null  -Enabled $false 
if($da?){
Set-ADUser -Identity $daIdentity -Title $null -Department $null -Company $null -Manager $null  -Enabled $false 
}
# Reset password
$reppwd = Get-Random
$reppwd = $reppwd.ToString() + "Ab#$"

Set-ADAccountPassword -Identity $Identity.ToString() -Reset -NewPassword (ConvertTo-SecureString -AsPlainText $reppwd  -Force)
if ($da?){
Set-ADAccountPassword -Identity $daIdentity.ToString() -Reset -NewPassword (ConvertTo-SecureString -AsPlainText $reppwd  -Force)
}

# Add new group (No Access -Identity "S-1-5-21-**********-**********-*********-9623) Set as primary
Add-ADGroupMember -Identity "S-1-5-21-**********-**********-*********-9623" -Members $Identity
$noaccess = Get-ADGroup -Identity "S-1-5-21-**********-**********-*********-9623" -properties @("primaryGroupToken")


get-aduser $Identity | set-aduser -replace @{primaryGroupID=$noaccess.primaryGroupToken}

if ($da?) {
get-aduser $daIdentity | set-aduser -replace @{primaryGroupID=$noaccess.primaryGroupToken}
}


# Remove all other groups.

try {

$GroupsRemoved = Get-ADPrincipalGroupMembership -Identity $Identity | where {$_.Name -ne “no access”}
foreach ($grp in $GroupsRemoved) {
                        try {
                    Remove-ADPrincipalGroupMembership -Identity $Identity -MemberOf $grp -Confirm:$false
                    }catch {
                    Write-Host "Could not remove " $grp
                    }
                                }
if($da?)
    {
$daGroupsRemoved = Get-ADPrincipalGroupMembership -Identity $daIdentity | where {$_.Name -ne “no access”}
    foreach ($grp in $daGroupsRemoved) 
        {
            Remove-ADPrincipalGroupMembership -Identity $daIdentity -MemberOf $grp -Confirm:$false
        }
    }
}
catch {
Write-Host "Some Groups could not be removed" 

}

#Disable the slack account, initiate sign out

#Disable the CDSASP account and accuro cloud

#Disable 1password account (suspend)

#Disable Mitel extension in the MAS, delete mac address

#Message in Networking channel to terminate zscaler sessions.

#Freeze Salesforce Account

#Disable Jira, remove from groups

#Check if akamai account exists
#Generate <NAME_EMAIL> if exists.

#Disable OPT Account

#Remove from enterprise Github

#Does the manager need email or onedrive?

#Set mailbox to shared
Connect-ExchangeOnline -CommandName Set-Mailbox
try{
Set-Mailbox $UserData.userprincipalname -Type Shared
write-host "Mailbox set to shared now"
}catch{write-host "set mailbox to shared failed"}
#Remove licenses


try{
(get-MsolUser -UserPrincipalName $UserData.userprincipalname).licenses.AccountSkuId |
foreach{
    Set-MsolUserLicense -UserPrincipalName $UserData.userprincipalname -RemoveLicenses $_
}
}catch{write-host "License Removal Did not complete"}

#Remove azure AD Groups

$AZGroupsRemoved =   Get-AzureADUserMembership –ObjectId $UserData.ObjectId | Select-Object * | Where-Object {-not ($_.DirSyncEnabled)}
foreach ($group in $AZGroupsRemoved)
{
try {
 Remove-AzureADGroupMember –ObjectId $group.ObjectId –MemberId $UserData.ObjectId
 }
 catch{
 Write-host "Could not remove " $group.ObjectId
 }
}

if ($da?) {
 
$AZGroupsRemovedDA =   Get-AzureADUserMembership –ObjectId $DAData.ObjectId | Select-Object * | Where-Object {-not ($_.DirSyncEnabled)}
foreach ($group in $AZGroupsRemovedDA)
{
try{
 Remove-AzureADGroupMember –ObjectId $group.ObjectId –MemberId $DAData.ObjectId
    }catch{
    Write-host "Could not remove " $group.ObjectId}


}}


#Set out of office message if required

#msExchHideFromAddressLists -> True
try{
Set-ADUser -Identity $Identity -Add @{msExchHideFromAddressLists="TRUE"}
}catch{write-host "msExchHideFromAddressList was not successful"}
if ($da?)
{
    try{
Set-ADUser -Identity $daIdentity -Add @{msExchHideFromAddressLists="TRUE"}
    }catch{write-host "DA msExchHideFromAddressList was not successful"}
}

#remove email forwarding unless required.

#Reset voice mail password for landline phone in lm3 controller

# Does call forwarding need to be checked?

#Disable Goto meeting, premiere global and myfax

#Check for VMs in Vsphere

#Add to former employee distro list

#Send message to cloud to disable GCP

#Any additional vendor or datacenter access?

#Create a SNOW Ticket for cloud team. 

#Dev exit procedure complete?

#Move AD Account on prem to graveyard OU
#Delete asset from MDM MAchine group

#Set asset to retired in SNOW 

#Delete Asset from Intune
Disconnect-ExchangeOnline
Disconnect-AzureAD
