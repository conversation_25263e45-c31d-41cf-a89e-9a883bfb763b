﻿<#
Title: Check employees without an all-staff distro group.
DESC: This script checks employees at the company for membership in "All Staff" groups. Those without membership are highlighted.
Prereqs:
 - PowerShell modules: ExchangeOnlineManagement, AzureAD
#>

# Connect to services
Write-Host "Connecting to Exchange Online..."
Connect-ExchangeOnline
Write-Host "Connecting to Azure AD..."
Connect-AzureAD

# Initialize variables (best practice for clarity)
$IsNotInAllStaff = @()
$inAllStaff = @()
$AllStaffMembers = @()

# Get all users (excluding service accounts, etc.)
Write-Host "Collecting user list..."
try {
    $userList = Get-AzureADUser -Filter 'AccountEnabled eq true and (UserType eq "Member" or UserType eq "Guest")' | 
                Where-Object { $_.DisplayName -notlike "*Domain*" -and $_.DisplayName -notlike "*test*" -and $_.DisplayName -notlike "*svc*" } |
                Select-Object UserPrincipalName
}
catch {
    Write-Error "Error retrieving Azure AD users. Ensure you have the necessary permissions."
}

# Get all "All Staff" distribution groups
Write-Host "Generating All Staff group list..."
$allStaffGroups = Get-EXOMailbox -RecipientTypeDetails MailUniversalDistributionGroup -Filter 'DisplayName -like "*qhrallstaff*"' | 
                  Select-Object DisplayName, PrimarySmtpAddress, ExternalDirectoryObjectId

# Collect members of all "All Staff" groups
Write-Host "Collecting members of the groups..."
ForEach ($group in $allStaffGroups) {
    $AllStaffMembers += Get-AzureADGroupMember -ObjectId $group.ExternalDirectoryObjectId -All $true | Select -ExpandProperty UserPrincipalName
}

# Compare user list with members of "All Staff" groups
Write-Host "Checking group membership..."
ForEach ($Employee in $userList) {
    if ($AllStaffMembers -contains $Employee.UserPrincipalName) {
        Write-Host "Employee $($Employee.UserPrincipalName) is in an all-staff group."
        $inAllStaff += $Employee.UserPrincipalName
    } else {
        $IsNotInAllStaff += $Employee.UserPrincipalName
        Write-Host "$($Employee.UserPrincipalName) is not part of an All Staff group."
    }
}

# Display results
Write-Host "The following employees have no all-staff group:"
$IsNotInAllStaff

# Disconnect from services
Write-Host "Disconnecting..."
Disconnect-ExchangeOnline
Disconnect-AzureAD
