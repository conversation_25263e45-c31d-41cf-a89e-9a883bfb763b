<#
.SYNOPSIS
Checks the Mailbox Type and Place Attributes for a list of room mailboxes.

.DESCRIPTION
This script takes a list of room email addresses and performs two checks for each:
1. Verifies that the RecipientTypeDetails is 'RoomMailbox' using Get-Mailbox.
2. Retrieves Place attributes (Building, Capacity, Tags) using Get-Place, which are
   important for the New Outlook Room Finder functionality.

.NOTES
Run this script after connecting to Exchange Online PowerShell.
The list of room emails is pre-populated based on the provided CSV.
Ensure you have the necessary permissions to run Get-Mailbox and Get-Place cmdlets.
#>

#Requires -Modules ExchangeOnlineManagement

# List of Room Email Addresses from Resources.csv
$roomEmails = @(
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
)



Write-Host "Checking Room Mailbox Type and Place Attributes..." -ForegroundColor Yellow
Write-Host "-------------------------------------------------"

if ($roomEmails.Count -eq 0) {
    Write-Host "No room email addresses to process. Check the \$roomEmails list in the script." -ForegroundColor Red
} else {
    foreach ($email in $roomEmails) {
        Write-Host "Processing: $email"

        # Check Mailbox Type
        try {
            $mailbox = Get-Mailbox -Identity $email -ErrorAction Stop
            Write-Host "  [Mailbox Check] Identity: $($mailbox.PrimarySmtpAddress), RecipientTypeDetails: $($mailbox.RecipientTypeDetails)" -ForegroundColor Green
            if ($mailbox.RecipientTypeDetails -ne "RoomMailbox") {
                Write-Host "    WARNING: RecipientTypeDetails is not 'RoomMailbox'." -ForegroundColor Magenta
            }
        } catch {
            Write-Host "  [Mailbox Check] Error retrieving mailbox details for $email`: $($_.Exception.Message)" -ForegroundColor Red
        }

        # Check Place Attributes (Building, Capacity, Tags)
        try {
            # Use -ErrorAction SilentlyContinue for Get-Place as it might fail if the room isn't fully configured as a place
            $place = Get-Place -Identity $email -ErrorAction SilentlyContinue
            if ($place) {
                Write-Host "  [Place Check]   Building: $($place.Building)"
                Write-Host "                  Capacity: $($place.Capacity)"
                # Tags property might be a collection, join it for display
                $tagString = if ($place.Tags) { $place.Tags -join ', ' } else { $null }
                Write-Host "                  Tags: $($tagString)"

                # Highlight if Building is missing, as it's often key for Room Finder
                if (-not $place.Building) {
                    Write-Host "    INFO: 'Building' attribute is not set. This is often required for New Outlook Room Finder." -ForegroundColor Cyan
                }
            } else {
                 Write-Host "  [Place Check]   Could not retrieve Place information. Room might not be configured as a Place, or lacks attributes." -ForegroundColor Yellow
                 Write-Host "                  Consider using 'Set-Place -Identity $email -Building ""Your Building Name"" ...' etc. to configure." -ForegroundColor Yellow
            }
        } catch {
            # Catch potential errors even with SilentlyContinue (though less likely)
            Write-Host "  [Place Check]   Error retrieving Place details for $email`: $($_.Exception.Message)" -ForegroundColor Red
        }

        Write-Host "-------------------------------------------------"
    }
}

Write-Host "Script finished." -ForegroundColor Yellow