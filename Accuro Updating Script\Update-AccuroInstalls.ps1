<#
.SYNOPSIS
    Updates and tests all local Accuro installations.

.DESCRIPTION
    This script automates the process of updating Accuro installations for
    various provinces, then launches each installation for testing.

.NOTES
    Author:      [<PERSON><PERSON>, <PERSON>]
    Created:     [ADD NATHAN CREATED DATE]
    Revised:     [5/29/2024]
    Version:     0.2
    PowerShell:  Requires PowerShell 7+ (for automatic installation)
#>

# Ensure PowerShell 7+ is installed
try {
    winget install --id Microsoft.Powershell --source winget -h
} catch {
    Write-Warning "PowerShell 7+ installation failed. Proceeding with existing version."
}

# Function to update and test a single Accuro installation
function Update-AndTestAccuro {
    param(
        [string]$Province
    )

    $installPath = "C:\Program Files (x86)\Accuro - $Province"
    $updateExe = "$installPath\UpdateAccuro.exe"
    $accuroExe = "$installPath\Accuro.exe"

    Write-Host "Updating Accuro - $Province..."
    if (Test-Path $updateExe) {
        Start-Process $updateExe -Wait
    } else {
        Write-Warning "UpdateAccuro.exe not found for Accuro - $Province."
    }

    Write-Host "Launching Accuro - $Province... Close window to continue."
    if (Test-Path $accuroExe) {
        Start-Process $accuroExe -Wait
    } else {
        Write-Warning "Accuro.exe not found for Accuro - $Province."
    }
}

# Array of provinces with Accuro installations
$provinces = "AB", "BC", "MB", "NS", "ON", "SK"

# Update and test each installation
foreach ($province in $provinces) {
    Update-AndTestAccuro $province
}
