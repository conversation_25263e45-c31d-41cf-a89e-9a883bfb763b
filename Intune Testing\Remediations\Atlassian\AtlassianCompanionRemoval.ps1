# Define log file path
$logFilePath = "C:\temp\atlassianremoval.log"

# Ensure the temp directory exists
if (-not (Test-Path -Path "C:\temp")) {
    New-Item -ItemType Directory -Path "C:\temp" | Out-Null
}

# Initialize the log file
Add-Content -Path $logFilePath -Value "Atlassian Removal Script Log - $(Get-Date)" -Encoding UTF8

# Define specific paths for Atlassian directories
$directoriesToCheck = @(
    "C:\Program Files (x86)\Atlassian Companion",
    "$env:LOCALAPPDATA\atlassian-desktop-companion",
    "$env:APPDATA\Atlassian Companion",
    "C:\Users\<USER>\.atlassian-companion"
)

# Define specific registry keys for Atlassian
$regKeys = @(
    "HKCR\atlassian-companion",
    "HKCU\Software\Classes\atlassian-companion",
    "HKCU\Software\Microsoft\atlassian-desktop-companion"
)

# Initialize a flag to track if any errors occur
$global:errorsEncountered = $false

# Function to write to the log file
function Log-Action {
    param (
        [string]$message
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Add-Content -Path $logFilePath -Value "$($timestamp): $($message)"
}

# Function to check for admin privileges
function Check-Admin {
    if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
        Log-Action "Script does not have administrative privileges. Exiting."
        exit 1
    } else {
        Log-Action "Confirmed script is running with administrative privileges."
    }
}

# Ensure script is run as admin
Check-Admin

# Start logging script details
Log-Action "Starting the Atlassian uninstallation script."

# Function to remove specified directories
function Remove-AtlassianDirectories {
    param (
        [array]$dirPaths
    )
    foreach ($dirPath in $dirPaths) {
        if (Test-Path -Path $dirPath) {
            Log-Action "Found directory: $dirPath. Attempting to delete."
            try {
                Remove-Item -Path $dirPath -Recurse -Force -ErrorAction Stop
                Log-Action "Successfully deleted directory: $dirPath"
            } catch {
                Log-Action "ERROR: Failed to delete directory: $dirPath - $_"
                $global:errorsEncountered = $true
            }
        } else {
            Log-Action "Directory not found: $dirPath"
        }
    }
}

# Function to remove specified registry keys
function Remove-AtlassianRegistryKeys {
    param (
        [array]$regKeys
    )
    foreach ($regKey in $regKeys) {
        if (Test-Path -Path "Registry::$regKey") {
            Log-Action "Found registry key: $regKey. Attempting to delete."
            try {
                Remove-Item -Path "Registry::$regKey" -Recurse -Force -ErrorAction Stop
                Log-Action "Successfully deleted registry key: $regKey"
            } catch {
                Log-Action "ERROR: Failed to delete registry key: $regKey - $_"
                $global:errorsEncountered = $true
            }
        } else {
            Log-Action "Registry key not found: $regKey"
        }
    }
}

# Remove specified directories
Remove-AtlassianDirectories -dirPaths $directoriesToCheck

# Remove specified registry keys
Remove-AtlassianRegistryKeys -regKeys $regKeys

# Final log message and exit
if (-not $global:errorsEncountered) {
    Log-Action "Complete uninstallation finished successfully."
    exit 0
} else {
    Log-Action "Errors encountered during uninstallation."
    exit 1
}
