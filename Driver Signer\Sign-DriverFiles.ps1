<#
.SYNOPSIS
    Automatically signs all driver files (.sys and .inf) within a specified folder, providing verbose output and verification.

.DESCRIPTION
    This enhanced PowerShell script thoroughly documents the signing process for driver files 
    within a target folder. It lists each .sys and .inf file encountered, displays the signing command used,
    and confirms the successful application of the digital signature. 

    If no suitable certificate is provided, it generates a self-signed certificate for testing purposes.
    It also temporarily adjusts security settings to allow the self-signed certificate to modify system files.

    The script now checks if files are locked before signing, and attempts to close open handles if necessary.
    Finally, it verifies the signatures of the signed files and displays the verification results.

.PARAMETER TargetFolder
    The full path to the folder containing the driver files to be signed.

.PARAMETER CertificateThumbprint
    (Optional) The thumbprint of the code signing certificate to use for signing. 
    If not provided, a self-signed certificate will be generated.

.EXAMPLE
    .\Sign-DriverFiles.ps1 -TargetFolder "C:\MyDriverFiles" -CertificateThumbprint "1234567890ABCDEF1234567890ABCDEF123456"
    .\Sign-DriverFiles.ps1 -TargetFolder "C:\MyDriverFiles" # Generates a self-signed certificate

.NOTES
    Created by slader

#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$TargetFolder,

    [Parameter()]
    [string]$CertificateThumbprint
)

# Check if the required module is installed
if (-not (Get-Module -ListAvailable -Name Microsoft.PowerShell.Security)) {
    # Install the module if it's not present
    Write-Host "Installing Microsoft.PowerShell.Security module..."
    Install-Module -Name Microsoft.PowerShell.Security -Force
}

# Import the module
Import-Module Microsoft.PowerShell.Security -ErrorAction Stop

# Generate a self-signed certificate if no thumbprint is provided
if ([string]::IsNullOrEmpty($CertificateThumbprint)) {
    Write-Host "No certificate provided. Generating a self-signed certificate for testing..." -ForegroundColor Yellow
    $subject = "CN=SelfSignedTestCert"
    $cert = New-SelfSignedCertificate -Subject $subject -Type CodeSigningCert -CertStoreLocation Cert:\CurrentUser\My
    $CertificateThumbprint = $cert.Thumbprint
    Write-Host "Self-signed certificate generated with thumbprint: $CertificateThumbprint" -ForegroundColor Green
}

# Get the certificate object based on thumbprint from ALL stores
$storeNames = "My", "Root", "TrustedPeople", "TrustedPublisher"
$signingCert = $null

foreach ($storeName in $storeNames) {
    $certStore = New-Object System.Security.Cryptography.X509Certificates.X509Store($storeName, [System.Security.Cryptography.X509Certificates.StoreLocation]::CurrentUser) 
    $certStore.Open([System.Security.Cryptography.X509Certificates.OpenFlags]::ReadOnly)
    $tempCert = $certStore.Certificates.Find([System.Security.Cryptography.X509Certificates.X509FindType]::FindByThumbprint, $CertificateThumbprint, $false)
    if ($tempCert.Count -gt 0) {
        $signingCert = $tempCert
        break 
    }
    $certStore.Close()
}

if ($null -eq $signingCert) {
    Write-Error "No certificate found with the specified thumbprint in any store." -ForegroundColor Red
    exit 1
}

# Get all .sys and .inf files in the target folder
$driverFiles = Get-ChildItem -Path $TargetFolder -Include "*.sys", "*.inf" -Recurse

# Temporarily allow self-signed certificates for driver signing (if applicable)
if ([string]::IsNullOrEmpty($CertificateThumbprint)) {
    Write-Host "Temporarily adjusting security settings to allow self-signed certificate..." -ForegroundColor Yellow
    Set-ExecutionPolicy -ExecutionPolicy Unrestricted -Scope Process -Force
}

# Sign each driver file with verbose output and handle file locks with retry
foreach ($file in $driverFiles) {
    Write-Host "File found: $($file.FullName)" -ForegroundColor Cyan

    $retryCount = 0
    $maxRetries = 3 
    $signedSuccessfully = $false

    while ($retryCount -lt $maxRetries) {
        try {
            $command = "Microsoft.PowerShell.Security\Set-AuthenticodeSignature -FilePath '$($file.FullName)' -Certificate $signingCert[0] -TimestampServer http://timestamp.digicert.com"
            Write-Host "Executing command: $command" -ForegroundColor DarkGray
            Microsoft.PowerShell.Security\Set-AuthenticodeSignature -FilePath $file.FullName -Certificate $signingCert[0] -TimestampServer http://timestamp.digicert.com
            $signedSuccessfully = $true
            break 
        } catch [System.IO.IOException] {
            Write-Host "File is locked. Attempting to close open handles..." -ForegroundColor Yellow
            # Get the processes locking the file
            $processes = Get-Process | Where-Object { $_.Handles | Where-Object {$_.Path -eq $file.FullName} }
            foreach ($process in $processes) {
                Write-Host "Closing handles held by process $($process.Id) - $($process.ProcessName)" -ForegroundColor Yellow
                # Close the handles forcefully
                $process.Handles | Where-Object {$_.Path -eq $file.FullName} | Close-Handle -Force
            }
            
            if ($retryCount -lt $maxRetries - 1){ # Retry only if we haven't reached the max retries
                Write-Host "Retrying in 2 seconds... (Attempt $($retryCount + 1) of $maxRetries)" -ForegroundColor Yellow
                Start-Sleep -Seconds 2
                $retryCount++
            } else {
                break # Exit the loop if we've reached max retries
            }
        }
    }

    if ($signedSuccessfully) {
        Write-Host "Successfully signed $($file.FullName)" -ForegroundColor Green
    } else {
        Write-Host "Failed to sign $($file.FullName) after $maxRetries attempts. Please check the certificate and file permissions." -ForegroundColor Red
    }

    Write-Host "" # Add a blank line for better readability
}

# Verify the signatures of the signed files and handle file locks with retry
foreach ($file in $driverFiles) {
    Write-Host "Verifying signature for: $($file.FullName)"  -ForegroundColor Cyan

    $retryCount = 0
    $maxRetries = 3
    $verificationSuccessful = $false

    while ($retryCount -lt $maxRetries) {
        try {
            $signature = Get-AuthenticodeSignature -FilePath $file.FullName
            $verificationSuccessful = $true
            break
        } catch [System.IO.IOException] {
            Write-Host "File is locked. Attempting to close open handles..." -ForegroundColor Yellow
            # Get the processes locking the file
            $processes = Get-Process | Where-Object { $_.Handles | Where-Object {$_.Path -eq $file.FullName} }
            foreach ($process in $processes) {
                Write-Host "Closing handles held by process $($process.Id) - $($process.ProcessName)" -ForegroundColor Yellow
                # Close the handles forcefully
                $process.Handles | Where-Object {$_.Path -eq $file.FullName} | Close-Handle -Force
            }

            if ($retryCount -lt $maxRetries - 1){ # Retry only if we haven't reached the max retries
                Write-Host "Retrying in 2 seconds... (Attempt $($retryCount + 1) of $maxRetries)" -ForegroundColor Yellow
                Start-Sleep -Seconds 2
                $retryCount++
            } else {
                break # Exit the loop if we've reached max retries
            }
        }
    }

    if ($verificationSuccessful) {
        if ($signature.Status -eq "Valid") {
            Write-Host "Signature is valid." -ForegroundColor Green
        } else {
            Write-Host "Signature is NOT valid. Status: $($signature.Status)" -ForegroundColor Red
        }
    } else {
        Write-Host "Failed to verify signature for $($file.FullName) after $maxRetries attempts. Please check file permissions" -ForegroundColor Red
    }

    Write-Host "" 
}

Write-Host "Signing and verification process completed." -ForegroundColor Magenta