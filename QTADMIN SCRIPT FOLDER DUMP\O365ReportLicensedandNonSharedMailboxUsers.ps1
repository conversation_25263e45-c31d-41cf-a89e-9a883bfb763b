﻿#This Script runs as a scheduled task weekly. Do not use for anything but the scheduled task.

#Connect to Office365
#only works if running as the qtadmin1service account
$LiveCred = Import-CliXml -Path "C:\Scripts\Credentials\office365scripts-qtadmin1service.cred"

#Use below instead if running manually but remember to comment it back after 
#$LiveCred = Get-Credential <EMAIL>

Clear
Write-Host "Connecting to Exchange Online..."

$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/PowerShell-LiveID?PSVersion=4.0 -Credential $LiveCred -Authentication Basic -AllowRedirection
Import-PSSession $Session -AllowClobber
Connect-MsolService -Credential $LiveCred

$DLUM = ""
$DLSM = ""

#$disabledlic = get-msoluser -all | ? {$_.blockcredential -eq $true -and $_.islicensed -eq $true }| select UserPrincipalName,isLicensed,BlockCredential
#Write-Host "`n`nDisabled Accounts that are Licensed!!!"

#$disabledlic

$recipients = @("<EMAIL>","<EMAIL>")


$Header = @"
<style>
TABLE {border-width: 1px; padding: 10px; border-style: solid; border-color: black; border-collapse: collapse;}
TD {border-width: 1px; padding: 10px; border-style: solid; border-color: black;}
</style>
"@


$DLU = Get-Mailbox -Filter * | ? recipienttypedetails -eq "UserMailbox" | get-msoluser | ? {$_.blockcredential -eq $true -and $_.islicensed -eq $true}  | select DisplayName

"Disabled Licensed users that are NOT shared mailboxes"
$DLU = $DLU.displayname

$DLS  = Get-Mailbox -Filter * | ? recipienttypedetails -eq "SharedMailbox" | get-msoluser | ? {$_.blockcredential -eq $true -and $_.islicensed -eq $true}  | select DisplayName

"Disabled Licensed users that ARE shared mailboxes"
$DLS = $DLS.displayname

foreach ($user in $DLU)
{
    $DLUM += "<br> $user" 
}

foreach ($user2 in $DLS)
{
    $DLSM += "<br> $user2" 
}

Send-MailMessage -To $recipients -From “<EMAIL>” -SmtpServer mail.qhrtech.com -Subject “Office365 Disabled Users Report” `
    -body "<h5>Disabled Licensed users that are NOT shared mailboxes</h5> $DLUM <br><br><h5>Disabled Licensed users that ARE shared mailboxes</h5> $DLSM` 
    <br><br><br> NOTE: This script runs on QTADMIN1 weekly at 9am (Only Monday) as a scheduled task (o365ReportLicenseandNonSharedMailboxUsers.ps1)." -BodyAsHtml