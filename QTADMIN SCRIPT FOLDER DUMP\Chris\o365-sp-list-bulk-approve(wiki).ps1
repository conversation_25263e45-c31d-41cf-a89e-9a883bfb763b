﻿
$count = 0
$EditMode = $true

#$filenametocheckout = "car"
#$filenametoignore = "car0"
$comment = Read-Host "Enter Checkin/Approval comment"

#Load necessary module to connect to SPOService
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client") | Out-Null
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SharePoint.Client.Runtime") | Out-Null

$creduser = "<EMAIL>"
$encrypted = Get-Content C:\scripts\credentials\admin_qhrtech_onmicrosoft_com.txt | ConvertTo-SecureString
$LiveCred = New-Object System.Management.Automation.PsCredential($creduser, $encrypted)

Connect-SPOService -url https://qhrtech-admin.sharepoint.com -Credential $LiveCred

$WebUrl = "https://qhrtech.sharepoint.com/ca/cs/wiki"

#Connect to SharePoint Online service
Write-Host "Logging into SharePoint online service." -ForegroundColor Green

$Context = New-Object Microsoft.SharePoint.Client.ClientContext($WebUrl)
$Context.Credentials = New-Object Microsoft.SharePoint.Client.SharePointOnlineCredentials($creduser, $encrypted)


#Get the Necessary List
Write-Host "Getting the required list.`n`n" -ForegroundColor Green
$List = $Context.Web.Lists.GetByTitle("Pages")

$Query = [Microsoft.SharePoint.Client.CamlQuery]::CreateAllItemsQuery(1500); 
$Items = $List.GetItems($Query);

$Context.Load($Items);
$Context.ExecuteQuery();

#### WARNING EDITING DONE BELOW THIS LINE!!! ###
foreach($item in $Items)
{
      
   
   if($item["_ModerationStatus"] -eq 3 -and $item.FieldValues.CheckoutUser.LookupValue -eq $null)
    {
        Write-Host "Updating: [$($item["ID"])]: $($item["FileLeafRef"])" -ForegroundColor Green        
        #sleep 3
        $count++

        # Set EditMode to $true at top of file to make actual changes!!! 
        if ($EditMode -eq $true) {
         ### Check out  
         ### $item.file.CheckOut()
         ### $Context.ExecuteQuery()
         ### pause
          
         ### Approve  
          $item["_ModerationStatus"] = 0
          $item.Update()
          $Context.ExecuteQuery()
         
         ### Check in
         ### $item.file.CheckIn($comment,[Microsoft.SharePoint.Client.CheckinType]::MajorCheckIn)
         ### $Context.ExecuteQuery()   

         ### Uncomment below for testing
         ### break
    }
       ### Uncomment below for testing
      ## sleep 1
    }
}

Write-Host "`n`nUpdated $count files." -ForegroundColor Green