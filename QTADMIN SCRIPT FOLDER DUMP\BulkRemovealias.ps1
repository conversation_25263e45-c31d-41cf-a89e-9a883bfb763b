﻿#$DAaccount = Read-Host "Enter your domain admin account without domain, ie. da-ckitella"
#$DAaccount = $DAaccount + "@QHRtech.com"
#$usercredential = Get-Credential $DAaccount
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://qtexch1.quadranthr.com/PowerShell/ -Authentication Kerberos # -Credential $UserCredential
Import-PSSession $Session -AllowClobber

# Get-ClientAccessServer qtexch1 | fl *InternalUri*
# Set-ClientAccessServer qtexch1 -AutoDiscoverServiceInternalUri $null
# Enable-RemoteMailbox "name" -RemoteRoutingAddress <EMAIL>
#			i. Eg. Enable-RemoteMailbox "Chris Kitella" -RemoteRoutingAddress <EMAIL>

# Email Address Policies *** CAPITAL SMTP MEANS DEFAULT PRIMARY EMAIL ADDRESS
# set-emailaddresspolicy softcare.com -enabledemailaddresstemplates smtp:%r`'`'%<EMAIL>,smtp:%r`'`'%<EMAIL>, SMTP:%r`'`'%<EMAIL>, smtp:%<EMAIL>
# set-emailaddresspolicy qhrtechglobal -enabledemailaddresstemplates smtp:%r`'`'%<EMAIL>, smtp:%<EMAIL>,smtp:%r''%<EMAIL>

# Find users who don't have mailbox policies applied
# $ExcludeList = @('delphiuser','osdevah','careers','HR','CDS-BackupStatus','qhrtechprivacyoffice','IT','accuro','Investor','Implementations','qti_events','qtstaffmeetings','OSAdmin','TFSREPORTS','QTAdmin','cds-info','CDS-BackupStatus','ONInvoices','infracalendar','CSR','accounting','opt-graphics','qtmitelccm','expenses','NPUMAdmin','qtmarketing','markevents','FinancialsSupport','Marketing','helpdesk','certification','qhrsoftwareprivacyof','qssales','qstw','FinDev','qscw','supfeedback','ahfsup','afwsup','OPT_Support','optimedprivacyoffice','optdev','cc-support','OSCareers','osse','cwts','HS','hssupport','pictatesupport','TS','Optcslt','publicO','smanager','sadmin','dataanalyst','opttraining')
# Get-RemoteMailbox -ResultSize Unlimited |where {$_.HiddenFromAddressListsEnabled -eq $false -and $_.EmailAddressPolicyEnabled -eq $false -and $_.RecipientTypeDetails -eq "RemoteUserMailbox" -and $ExcludeList -notcontains $_.SAMAccountName} | Select Name, SAMAccountName


# Bulk remove alias
$aliastodelete = Read-Host "Enter email alias (without @ symbol) to BULK DELETE"
$getmailboxes = Get-remoteMailbox -ResultSize unlimited
foreach ($mbx in $getmailboxes) {
$addrs = $mbx.emailaddresses |? {$_ -match $aliastodelete}

    #The command below REMOVES any email addresses that match the $addrs list above
If ($addrs -ne $null){
set-remotemailbox $mbx.Identity -emailaddresses @{Remove=$addrs}
      Write-Host "Removing Addresses: $addrs"
    Write-Output $addrs | Out-File 'C:\users\<USER>\Documents\RemovedEmailAliases(softcare).txt' -Append
    }
 }