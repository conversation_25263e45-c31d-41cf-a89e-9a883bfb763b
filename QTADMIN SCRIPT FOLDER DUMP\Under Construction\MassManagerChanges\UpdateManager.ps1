# Prompt the user for the First.last of the user and the manager
$userIdentity = Read-Host -Prompt 'Enter the First.last of the user you wish to change managers for'
$managerIdentity = Read-Host -Prompt 'Enter the First.last of the manager that you are changing this user to'

# Verbose logging
Write-Verbose "Setting the manager for $userIdentity to $managerIdentity" -Verbose

# Set the manager on the local AD object
Get-ADUser -Identity $userIdentity | Set-ADUser -Manager $managerIdentity

# Verbose logging
Write-Verbose "Retrieving the manager for $userIdentity" -Verbose

# Get the manager of the user
Get-ADUser -Identity $userIdentity -Properties manager | Select-Object -Property manager, name
