﻿#Written by: <PERSON> - Systems Specialist
#Purpose is to populate <NAME_EMAIL> automatically with anyone with manager or director in their title. The Managers are futher filtered by only adding people with direct reports.

Import-Module ActiveDirectory
#Remove all current members from the group
Remove-ADGroupMember "QHR Tech - Managers-Directors" -Members (Get-ADGroupMember "QHR Tech - Managers-Directors") -Confirm:$false
#Search for all users with Manager or Director in their title with a direct report. If this needs to be updated: -or (Title -like 'Manager*') adding this line to the end and changing the title will let you add another title to the filter.
$accounts = Get-ADUser -Filter "(Title -like '*Manager') -and (directReports -like '*') -or (Title -like 'Manager*') -and (directReports -like '*') -or (Title -like 'Director*') -or (Title -like '*Director') -or (Title -like 'Director *') -or (Title -like 'Senior Director *') -or (Title -like 'President')"
#Add each of the above users to the QHR Tech - Managers-Directors distro group. 
ForEach ($account in $accounts) {
    Add-ADGroupMember -Identity "QHR Tech - Managers-Directors" -Members $account
}

exit