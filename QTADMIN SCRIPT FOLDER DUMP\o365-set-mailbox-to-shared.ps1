﻿clear
$usertochange = Read-Host "Enter username to convert to shared (eg. john.doe)"
$usertochange = Get-ADUser $usertochange 

$usertochange | select Name, UserPrincipalName
$confirm = Read-Host "`nIs this correct??? (y/n)"

if ($confirm -eq "y")
    {
    Write-Host "`n`nSetting on-premise attributes..." -ForegroundColor Green
    set-aduser $usertochange.samaccountname -Replace @{msExchRemoteRecipientType = 100; msExchRecipientTypeDetails = ***********}
    Write-Host "Complete!!!" -ForegroundColor Green

    ## Connect to Office 365 (http://technet.microsoft.com/library/jj151815.aspx#BKMK_connect):
    $LiveCred = Get-Credential <EMAIL>
    $Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri https://ps.outlook.com/powershell/ -Credential $LiveCred -Authentication Basic -AllowRedirection
    Import-PSSession $Session -AllowClobber
    Connect-MsolService -Credential $LiveCred

    Write-Host "`n`nSetting cloud attributes..." -ForegroundColor Green
    Set-Mailbox $usertochange.UserPrincipalName -Type shared
    Write-Host "Complete!!!" -ForegroundColor Green

    Write-Host "`n`nSetting active directory attributes..." -ForegroundColor Green
    $samaccount = $usertochange.UserPrincipalName.ToLower().Replace('@qhrtech.com','')
    set-aduser $samaccount -Replace @{msExchRemoteRecipientType = 100; msExchRecipientTypeDetails = ***********}

    Exit-PSSession

    Pause
    }
else
    {break}