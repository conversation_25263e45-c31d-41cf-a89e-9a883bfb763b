# ==============================
# Script: Export AD Users from Specific OU to CSV
# Author: <PERSON>
# ==============================

# ==============================
# Pre-requisites:
# 1. Active Directory PowerShell Module:
#    - Ensure the Active Directory module is installed.
#    - Install it using the following command if not already installed:
#      Install-WindowsFeature -Name RSAT-AD-PowerShell
#
# 2. User Permissions:
#    - The user running this script must have read permissions on the specified OU in Active Directory.
#
# 3. PowerShell Version:
#    - Ensure you are using PowerShell 5.1 or later.
#
# 4. Execution Policy:
#    - If running this script on a system with restricted execution policies, you may need to adjust them using:
#      Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
#
# ==============================

# Import the Active Directory module
Import-Module ActiveDirectory -ErrorAction Stop

# Define the Organizational Unit and output file
$OU = "OU=Disabled Users,OU=Graveyard,DC=QuadrantHR,DC=com"  # Replace with the distinguished name (DN) of your OU
$OutputFile = "C:\Scripts\Under Construction\Graveyard Export\Jan152025.csv"  # Replace with your desired output file path

# Validate the OU
if (-not (Get-ADOrganizationalUnit -Filter "DistinguishedName -eq '$OU'" -ErrorAction SilentlyContinue)) {
    Write-Error "The specified OU '$OU' does not exist. Please check the DN and try again."
    exit
}

# Get the list of users from the specified OU
Write-Host "Fetching users from OU: $OU..." -ForegroundColor Cyan
$Users = Get-ADUser -Filter * -SearchBase $OU -Properties DisplayName, EmailAddress, Title, Department

# Check if users were found
if ($Users.Count -eq 0) {
    Write-Warning "No users found in the specified OU."
    exit
}

# Create an array to store user information
$UserList = @()

# Loop through each user and gather necessary details
Write-Host "Processing user details..." -ForegroundColor Cyan
foreach ($User in $Users) {
    $UserList += [PSCustomObject]@{
        SAMAccountName = $User.SamAccountName
        DisplayName    = $User.DisplayName
        EmailAddress   = $User.EmailAddress
        Title          = $User.Title
        Department     = $User.Department
    }
}

# Export the user list to a CSV file
Write-Host "Exporting user details to CSV file: $OutputFile" -ForegroundColor Cyan
$UserList | Export-Csv -Path $OutputFile -NoTypeInformation -Encoding UTF8

Write-Host "User export completed successfully. File saved to $OutputFile" -ForegroundColor Green
