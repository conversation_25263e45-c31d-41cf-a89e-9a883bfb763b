﻿$csvpath = "c:\csv"
$adgroupname = "confluence-users"

# Import the CSV file containing email addresses
$csvFile = Import-Csv -Path "$csvpath\input.csv"

# Initialize an array to store the usernames
$usernames = @()

# Loop through each row in the CSV file
Write-Output "Creating List of Usernames"
foreach ($row in $csvFile) {
    # Extract the username from the email address
    $username = $row.Email.Split('@')[0]
    $usernames += $username

    # Add the user to the "confluence-users" group
    try {
        #KEEP THIS ADD-ADGroupMember LINE COMMENTED OUT UNTIL YOU ARE READY TO GO LIVE
        Add-ADGroupMember -Identity "CN=$adgroupname,OU=Security Groups,DC=QuadrantHR,DC=com" -Members $username
        Write-Output "Adding $username to '$adgroupname'"
    } catch {
        Write-Output "Error adding $username to '$adgroupname': $_"
    }
   
}
Write-Output "Operation Complete"

# Export the list of usernames to a CSV file
$usernames | ForEach-Object { [PSCustomObject]@{ Username = $_ } } | Export-Csv -Path "$csvpath\output.csv" -NoTypeInformation
