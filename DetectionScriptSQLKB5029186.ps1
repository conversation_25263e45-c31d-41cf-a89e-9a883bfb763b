$fileNAme = "SQLServer2016SP3.exe"
$registryPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\"
$kbNumber = "KB5029186"
$fileNAme = Get-ItemProperty -Path "$registryPath\*" | Where-Object { $_.PSChildName -match $kbNumber }


$displayname = $fileNAme.DisplayName
if ($displayname) {
    Write-Output "$displayname is installed."
    exit 1
} else {
    Write-Output "$displayname is not installed."
    exit 0
}