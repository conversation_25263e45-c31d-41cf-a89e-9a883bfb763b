﻿$user = Read-Host "Enter the Display Name of the Lync user you wish to migrate (<PERSON>)"

Import-Module LyncOnlineConnector 
$cred = Get-Credential <EMAIL>
$CSSession = New-CsOnlineSession -TargetServer admin0a.online.lync.com -Credential $cred 
Import-PSSession $CSSession -AllowClobber 

move-csuser -Confirm:$false -Identity $user -target sipfed.online.lync.com -credential $cred -HostedMigrationOverrideUrl https://admin0a.online.lync.com/HostedMigration/hostedmigrationservice.svc
Write "$user migrated!"
sleep 5
exit