# Find all files containing "log4j" on the C: drive
# ErrorAction SilentlyContinue to ignore permission errors
$log4jFiles = Get-ChildItem -Path C:\ -Recurse -ErrorAction SilentlyContinue -Filter "*log4j*"

# Delete files immediately
foreach ($file in $log4jFiles) {
    try {
        Remove-Item $file.FullName -Force -Recurse
        #Write-Host "Deleted: $($file.FullName)"
    }
    catch {
        #Write-Host "Error deleting: $($file.FullName) - $($_.Exception.Message)" 
        New-Item -Path C:\Temp "Log4j.txt" -ItemType "file" -Value "removed files"
        exit 1
    }
}
New-Item -Path C:\Temp "Log4j.txt" -ItemType "file" -Value "removed files"
exit 0
#Write-Host "Script completed."
