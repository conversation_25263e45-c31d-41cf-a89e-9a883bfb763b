﻿function getDepartmentList {
    $department = read-host "Enter department name"
    $department2 = read-host "Enter additional department name (Leave blank if none)"

    $departmentusers = Get-ADUser -Filter {Enabled -eq $true -and PasswordNeverExpires -eq $False} -Properties * | Where-Object {$_.UserPrincipalName -match "@qhrtech.com" -and $_.UserPrincipalName -notmatch "da-" -and $_.Department -eq $department} | select -Property UserPrincipalName
    $departmentusers

    $answer = read-host "`nIs the list correct? y/n"
    "`n"

    if ($answer -eq "y") 
        {
        foreach ($dname in $departmentusers)
            {
            $name = $dname.userPrincipalname
            $namefix = $name -replace "@QHRtech.com", ""
            SET-ADUSER $namefix –replace @{extensionAttribute1=”0”}
            write-host "$namefix ✓" -ForegroundColor "Green"
            
            }
        }
    else 
        {"Going back to Department list"; getDepartmentList}
}

getDepartmentList