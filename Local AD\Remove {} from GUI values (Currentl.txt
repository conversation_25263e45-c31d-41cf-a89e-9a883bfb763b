Remove {} from GUI values (Currently All Values such as: Province, Department, Manager, and title, show up within the GUI as Province{} when it shouldn't have those curly braces)

Ensure that after clicking Update All Attributes you only have to click OK once, currently it pops up and says "Updated province, Updated Department, etc. > After clicking just say "All attributes updates successfully" > if any errors / manager not found list that in the log as well as re-prompt the user again to enter the correct value

Also change the log tail size to 100

Also, can you add a text field below the log box that says "Scroll down for the latest logs, oldest are at the top" (either do this or find a way to reverse the log file so that the latest entries show up at the top, this would be preferred).

Print the entire script without missing anything so I can copy and test it.
