﻿$match = 0
$findaddress = Read-Host "Enter the email address you wish to find"
$includedisabled = Read-Host "Include DISABLED accounts and/or SHARED mailboxes??? (y/n)"
#$userlist = get-aduser -filter * | select samaccountname,name
$userlist = Get-ADObject -Filter * -Properties *| select samaccountname,DisplayName,DistinguishedName,useraccountcontrol

foreach ($user in $userlist) {
    if ($user.samaccountname -ne $null) {
    $username = $user.samaccountname.ToString()
      if ($includedisabled -eq "y") {
        $proxylist = get-adobject -Filter {samaccountname -eq $username} -Properties * | select -ExpandProperty proxyaddresses
      } else {
        $proxylist = get-adobject -Filter {(samaccountname -eq $username) -and (useraccountcontrol -ne 514)} -Properties * | select -ExpandProperty proxyaddresses
      }
        if ($proxylist -match $findaddress) {
            $addressesfound = $proxylist | ? {$_ -match $findaddress}
            Write-Host "$($user.displayname) ($($user.samaccountname))" -NoNewline # ($($user.DistinguishedName)) is a match"
            if ($user.useraccountcontrol -eq 514) { 
              Write-Host " [DISABLED/SHARED]" -NoNewline
            }
            Write-Host "`n   $addressesfound`n"
            $match = 1
        }
    }
}

if ($match -eq 0) {
    Write-Host "No Match Found!"
}